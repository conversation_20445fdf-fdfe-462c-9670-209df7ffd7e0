import React, { useState, useEffect } from "react";
import { useArticle } from "../../context/ArticleContext";
import { Icons } from "../common";
import ValidationWarning from "./ValidationWarning";
import ManualEntrySection from "./ManualEntrySection";
import { runCompleteValidation } from "../../utils/manuscriptValidator";
import "./FileListViewer.css";

const FileListViewer = ({ onFileSelect, onBack, zipFile = null, onValidationQuerySent = null }) => {
  const { articleData } = useArticle();
  const [selectedFileIndex, setSelectedFileIndex] = useState(null);
  const [validationResult, setValidationResult] = useState(null);
  const [showUnsupportedFiles, setShowUnsupportedFiles] = useState(false);
  const { articleId, zipFiles } = articleData;

  // Run validation whenever files change
  useEffect(() => {
    if (zipFiles && zipFiles.length > 0) {
      const validation = runCompleteValidation(zipFiles, articleId);
      setValidationResult(validation.primary_error || null);
    }
  }, [zipFiles, articleId]);



  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const getFileTypeLabel = (type) => {
    switch (type) {
      case "docx":
      case "doc":
        return "Word Document";
      case "pdf":
        return "PDF Document";
      case "txt":
        return "Text File";
      case "xlsx":
      case "xls":
        return "Excel Spreadsheet";
      case "pptx":
      case "ppt":
        return "PowerPoint Presentation";
      default:
        return "Unknown File";
    }
  };

  const handleFileClick = (file, index) => {
    setSelectedFileIndex(index);
    if (onFileSelect) {
      onFileSelect(file);
    }
  };

  const supportedTypes = ["docx", "doc", "txt", "pdf"];
  const supportedFiles = zipFiles.filter((file) =>
    supportedTypes.includes(file.type)
  );
  const unsupportedFiles = zipFiles.filter(
    (file) => !supportedTypes.includes(file.type)
  );

  return (
    <div className="file-list-container">
      {/* Validation Warning Component */}
      <ValidationWarning
        validationResult={validationResult}
        articleId={articleId}
        zipFile={zipFile}
        onQuerySent={onValidationQuerySent}
      />

      {/* Manual Entry Component */}
      <ManualEntrySection articleId={articleId} />

      {/* File Categories */}
      <div className="file-categories">
        {/* Supported Files */}
        {supportedFiles.length > 0 && (
          <div className="file-category">
            <h3 className="category-title">
              <span className="category-icon">📄</span>
              Previewable Documents ({supportedFiles.length})
            </h3>
            <div className="file-grid">
              {supportedFiles.map((file) => (
                <div
                  key={file.path}
                  className={`file-item ${
                    selectedFileIndex === zipFiles.indexOf(file)
                      ? "selected"
                      : ""
                  }`}
                  onClick={() => handleFileClick(file, zipFiles.indexOf(file))}
                >
                  <div className="file-icon-container">
                    <span className="file-icon">{file.icon}</span>
                  </div>
                  <div className="file-details">
                    <div className="file-name" title={file.name}>
                      {file.name}
                    </div>
                    <div className="file-meta">
                      <span className="file-type">
                        {getFileTypeLabel(file.type)}
                      </span>
                      <span className="file-size">
                        {formatFileSize(file.size)}
                      </span>
                    </div>
                  </div>
                  <div className="file-actions">
                    <button className="preview-button">
                      <Icons.EyeIcon />
                      Preview
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Unsupported Files */}
        {unsupportedFiles.length > 0 && (
          <div className="file-category">
            <h3
              className="category-title clickable"
              onClick={() => setShowUnsupportedFiles(!showUnsupportedFiles)}
              style={{ cursor: 'pointer', userSelect: 'none' }}
            >
              <span className="category-icon">
                {showUnsupportedFiles ? '📂' : '📁'}
              </span>
              Other Files ({unsupportedFiles.length})
              <span className="toggle-icon" style={{ marginLeft: '0.5rem', fontSize: '0.9rem' }}>
                {showUnsupportedFiles ? '▼' : '▶'}
              </span>
            </h3>
            {showUnsupportedFiles && (
              <div className="file-grid">
                {unsupportedFiles.map((file) => (
                  <div key={file.path} className="file-item disabled">
                    <div className="file-icon-container">
                      <span className="file-icon">{file.icon}</span>
                    </div>
                    <div className="file-details">
                      <div className="file-name" title={file.name}>
                        {file.name}
                      </div>
                      <div className="file-meta">
                        <span className="file-type">
                          {getFileTypeLabel(file.type)}
                        </span>
                        <span className="file-size">
                          {formatFileSize(file.size)}
                        </span>
                      </div>
                    </div>
                    <div className="file-actions">
                      <span className="not-supported">Not supported</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="file-instructions">
        <div className="instruction-item">
          <span className="instruction-icon">👆</span>
          <span>Click on a document to preview its contents</span>
        </div>
        <div className="instruction-item">
          <span className="instruction-icon">📋</span>
          <span>Copy references from the preview to process them</span>
        </div>
      </div>
    </div>
  );
};

export default FileListViewer;
