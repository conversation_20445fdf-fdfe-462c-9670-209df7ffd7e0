@echo off
setlocal enabledelayedexpansion

:: Set console colors and title
title TE Automation - Download Phase
color 0A

echo.
echo ========================================
echo     TE AUTOMATION - DOWNLOAD PHASE
echo ========================================
echo.

:ASK_MODE
echo ========================================
echo      SELECT RUN MODE
echo ========================================
echo.
echo 1. Normal (use Target Date + Article Status discovery)
echo 2. download_articles_by_ids (process explicit ARTICLE_IDS)
echo.
set /p "mode_choice=Enter run mode choice (1 or 2): "
echo.

if /i "%mode_choice%"=="1" goto MODE1_NORMAL
if /i "%mode_choice%"=="2" goto MODE2_BY_IDS

echo ERROR: Invalid choice "%mode_choice%". Please enter 1 or 2.
echo.
goto ASK_MODE


:: ---------------------------------------------------------------------
:: MODE 1: NORMAL MODE (Date + Status)
:: ---------------------------------------------------------------------

:MODE1_NORMAL
set "DOWNLOAD_MODE=normal"
set "ARTICLE_IDS=N/A"

echo Selected: Normal discovery mode
echo.

:: Get current date in DD-MM-YYYY format
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value 2^>nul') do set "dt=%%a"
set "YYYY=!dt:~0,4!" & set "MM=!dt:~4,2!" & set "DD=!dt:~6,2!"
set "current_date=!DD!-!MM!-!YYYY!"

echo Current date: !current_date!
echo.
echo Enter the target date (DD-MM-YYYY)
set /p "user_date=Target Date [Press ENTER for !current_date!]: "

if "!user_date!"=="" (
    set "target_date=!current_date!"
) else (
    set "target_date=!user_date!"
)
echo Using date: !target_date!
echo.

:ASK_STATUS
echo ========================================
echo      SELECT ARTICLE STATUS
echo ========================================
echo.
echo 1. Yet-to-Start
echo 2. In Progress
echo 3. ANY
echo.
set /p "status_choice=Status Choice (1, 2, or 3): "

if "!status_choice!"=="1" (
    set "article_status=Yet-to-Start"
) else if "!status_choice!"=="2" (
    set "article_status=In Progress"
) else if "!status_choice!"=="3" (
    set "article_status=ANY"
) else (
    echo.
    echo Invalid choice "%status_choice%"! Please enter 1, 2, or 3.
    goto ASK_STATUS
)
echo Selected status: !article_status!
goto CONTINUE


:: ---------------------------------------------------------------------
:: MODE 2: DIRECT ARTICLE IDS
:: ---------------------------------------------------------------------

:MODE2_BY_IDS
set "DOWNLOAD_MODE=download_articles_by_ids"
set "target_date=N/A"
set "article_status=N/A"

echo Selected: download_articles_by_ids
echo.
echo Enter comma-separated Article IDs (e.g., TE12345, TE67890):
set /p "ARTICLE_IDS=ARTICLE_IDS: "
echo Will process IDs: !ARTICLE_IDS!
goto CONTINUE


:: ---------------------------------------------------------------------
:: CONTINUE EXECUTION — common section (Playwright Setup)
:: ---------------------------------------------------------------------

:CONTINUE
echo.
echo ========================================
echo     STARTING DOWNLOAD AUTOMATION
echo ========================================
echo.
echo 📅 Target Date: !target_date!
echo 📋 Article Status: !article_status!
echo 📝 Mode: !DOWNLOAD_MODE!
echo 🆔 Article IDs: !ARTICLE_IDS!
echo.

:: --- Configuration and Playwright Execution ---

:: Create or update .env file with the target parameters
echo # TE Automation Environment Configuration > .env
echo # Auto-generated by run-download.bat >> .env
echo TE_USERNAME=<EMAIL> >> .env
echo TE_PASSWORD=Editing@1234 >> .env
echo TE_LOGIN_URL=https://production.jow.medknow.com/login >> .env
echo TE_MYTASK_URL=https://production.jow.medknow.com/mytask >> .env
echo INCOMING_DIR=./incoming >> .env
echo DOWNLOADS_DIR=./downloads >> .env
echo TARGET_DATE=!target_date! >> .env
echo ARTICLE_STATUS=!article_status! >> .env
echo DOWNLOAD_MODE=!DOWNLOAD_MODE! >> .env
echo ARTICLE_IDS=!ARTICLE_IDS! >> .env

:: Also set these for current session so child processes see them
set "TARGET_DATE=!target_date!"
set "ARTICLE_STATUS=!article_status!"
set "DOWNLOAD_MODE=!DOWNLOAD_MODE!"
set "ARTICLE_IDS=!ARTICLE_IDS!"

echo ✅ Environment configured with target date: !target_date!
echo ✅ Article status set to: !article_status!
echo.

:: Check if node_modules exists
if not exist "node_modules" (
    echo ⚠️  Node modules not found. Installing dependencies...
    call npm install
    if errorlevel 1 (
        echo ❌ ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully!
)

:: Run Playwright in UI mode
echo 🚀 Launching Playwright UI for Download Phase...

call npx playwright test tests/download.spec.js --ui

:: Check if the command was successful
if errorlevel 1 (
    echo.
    echo ❌ ERROR: Playwright execution failed!
    echo.
    goto END
)

echo.
echo ✅ Download phase completed!
echo.
echo 📁 Check the 'incoming' folder for downloaded articles.
echo.

:: Ask if user wants to run upload test
echo ========================================
echo      UPLOAD TO GOOGLE DRIVE?
echo ========================================
echo.
set /p "upload_choice=Run upload test to Google Drive? (Y/N): "

if /i "%upload_choice%"=="Y" (
    echo.
    echo 🚀 Launching Playwright UI for Drive Upload Phase...
    echo.
    call npx playwright test tests/drive-upload.spec.js --ui
    
    if errorlevel 1 (
        echo.
        echo ❌ ERROR: Upload test failed!
        echo.
    ) else (
        echo.
        echo ✅ Upload phase completed!
        echo.
        echo ☁️  Files uploaded to Google Drive dump folder.
        echo.
    )
) else (
    echo.
    echo ⏭️  Skipping upload phase.
    echo.
)

:END
echo.
echo ========================================
echo          SESSION COMPLETE
echo ========================================
echo.
pause