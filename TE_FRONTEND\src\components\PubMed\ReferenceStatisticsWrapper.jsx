import React from "react";
import { ReferenceStatistics } from "../common";

/**
 * ReferenceStatisticsWrapper component for PubMed interface
 * Wraps the ReferenceStatistics component and handles visibility
 */
const ReferenceStatisticsWrapper = ({
  showStatistics,
  references,
  onFilterChange,
  activeFilter,
}) => {
  if (!showStatistics) return null;

  return (
    <ReferenceStatistics
      references={references}
      onFilterChange={onFilterChange}
      activeFilter={activeFilter}
    />
  );
};

export default ReferenceStatisticsWrapper;