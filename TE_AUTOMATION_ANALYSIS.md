# TE_AUTOMATION System - Complete Analysis

## 🎯 **SYSTEM OVERVIEW**

The **TE_AUTOMATION** system is a comprehensive **Playwright-based automation framework** designed to streamline the **Technical Editing (TE) workflow** for a publishing company. It automates the entire process from task assignment to file delivery.

## 🏗️ **SYSTEM ARCHITECTURE**

### **Core Components:**
1. **Task Discovery & Assignment** (`tasks.spec.js`)
2. **Article Download & Processing** (`assignment.spec.js`) 
3. **File Upload Automation** (`upload.spec.js`)
4. **Email & File Management** (`emailer.js`, utilities)
5. **Google Drive Integration** (`fileUpload.js`)
6. **Google Sheets Integration** (`sheetAuth.js`)

## 📋 **DETAILED WORKFLOW ANALYSIS**

### **1. TASK DISCOVERY PHASE** (`tasks.spec.js`)

**Purpose:** Automatically discover and assign new TE tasks

**Process:**
- Logs into production portal (`https://production.jow.medknow.com`)
- Searches for tasks scheduled for specific date (`todaysDate = '29-06-2024'`)
- Filters tasks with status `'Yet-to-Start'`
- Extracts `articleId` and `journalId` for each task
- **Assignment Logic:** Currently assigns ALL tasks to `Subrata`
- Saves assignments to `data.json`

**Key Features:**
- **Commented Logic:** Previously had 90/10 split between Subrata/Noor
- **Journal-based Assignment:** Specific journals were allocated to different editors
- **Flexible Allocation:** Can handle different assignment strategies

### **2. ARTICLE PROCESSING PHASE** (`assignment.spec.js`)

**Purpose:** Download articles and extract author information

**Process:**
- Reads assignments from `data.json`
- For each article ID:
  - Searches in task list
  - Clicks "Start" to begin task
  - Downloads article files to `./downloadedTE/{date}/{author}/`
  - Extracts author details from "Author Details" section
  - **Copyright Check:** Identifies authors with `copyright = 'NO'`
  - Saves author info to `{author}.txt` files
  - Handles "File Not Found" cases

**Key Features:**
- **Error Handling:** Tracks failed downloads
- **Author Analysis:** Extracts names and emails for copyright issues
- **File Organization:** Date-based folder structure
- **Batch Processing:** Processes multiple articles sequentially

### **3. FILE UPLOAD PHASE** (`upload.spec.js`)

**Purpose:** Upload completed TE files back to the system

**Process:**
- Reads article IDs from `batchToUpload.json`
- For each article:
  - Searches for the task
  - Clicks "Complete Action"
  - Uploads corresponding `.docx` or `.doc` file from `./batchUploader/`
  - Takes screenshot for verification
  - Submits the completed task

**Key Features:**
- **File Format Flexibility:** Handles both .docx and .doc files
- **Visual Verification:** Screenshots of upload process
- **Batch Processing:** Processes multiple uploads automatically

## 🛠️ **UTILITY COMPONENTS**

### **Email System** (`emailer.js`)
- **SMTP Configuration:** Uses GoDaddy secure server
- **HTML Email Templates:** Professional formatting with tables
- **Attachment Handling:** Automatically zips and attaches files
- **Multi-recipient Support:** Sends to multiple stakeholders

### **File Management** (`util/createZip.js`)
- **Folder Compression:** Creates zip files from download folders
- **Unique Naming:** Timestamp-based unique file names
- **Batch Organization:** Moves processed folders to "assigned" status

### **Google Drive Integration** (`util/fileUpload.js`)
- **Service Account Auth:** Uses JWT for authentication
- **Automatic Upload:** Uploads zip files to shared Drive folder
- **Link Generation:** Creates shareable Drive links for emails

### **Google Sheets Integration** (`sheetAuth.js`)
- **OAuth2 Authentication:** JWT-based Google Sheets API access
- **Data Logging:** Appends assignment data to tracking spreadsheet
- **Date Calculations:** Automatically calculates deadlines

### **File Processing** (`fetchFiles.js`)
- **Batch File Preparation:** Processes files in `batchUploader` folder
- **ID Extraction:** Extracts article IDs from filenames
- **File Renaming:** Standardizes file naming conventions
- **JSON Generation:** Creates `batchToUpload.json` for upload automation

## 📊 **DATA FLOW**

```
1. tasks.spec.js → data.json (Task Discovery)
2. data.json → assignment.spec.js → Downloads + Author Info (Processing)
3. assignment.spec.js → emailer.js → Email Delivery (Communication)
4. batchUploader/ → fetchFiles.js → batchToUpload.json (Upload Prep)
5. batchToUpload.json → upload.spec.js → Completed Tasks (Upload)
```

## 🔧 **CONFIGURATION & SETUP**

### **Dependencies:**
- **Playwright:** Browser automation framework
- **Node.js Modules:** nodemailer, googleapis, adm-zip, cheerio
- **Authentication:** Google service account, SMTP credentials

### **Directory Structure:**
- `downloadedTE/` - Downloaded article files organized by date/author
- `batchUploader/` - Files ready for upload
- `allocateTE/` - Zip files for email distribution
- `articlesUploaded/` - Screenshots of upload confirmations

### **Configuration Files:**
- `data.json` - Current task assignments
- `batchToUpload.json` - Files ready for upload
- `util/apikeys.json` - Google Drive service account credentials
- Various `.txt` files - Author information and tracking

## 🎯 **BUSINESS VALUE**

### **Automation Benefits:**
- **Time Savings:** Eliminates manual task discovery and assignment
- **Error Reduction:** Consistent processing and file handling
- **Scalability:** Can handle large batches of articles
- **Traceability:** Complete audit trail of all operations
- **Integration:** Seamless connection with existing systems

### **Workflow Efficiency:**
- **Parallel Processing:** Multiple articles processed simultaneously
- **Automatic Notifications:** Email alerts with file attachments
- **Cloud Storage:** Centralized file access via Google Drive
- **Progress Tracking:** Google Sheets integration for monitoring

## 🚀 **USAGE COMMANDS**

```bash
# Discover and assign new tasks
npx playwright test tasks.spec.js

# Process assigned articles (with UI for monitoring)
npx playwright test assignment.spec.js --ui

# Upload completed files
npx playwright test upload.spec.js

# Prepare files for upload
node fetchFiles.js
```

## 🔍 **SYSTEM INSIGHTS**

This is a **production-grade automation system** that:
- Handles the complete TE workflow lifecycle
- Integrates with multiple external services (Google Drive, Sheets, Email)
- Provides robust error handling and logging
- Supports flexible assignment strategies
- Maintains comprehensive audit trails

The system demonstrates **enterprise-level automation** with proper separation of concerns, modular design, and production-ready error handling.
