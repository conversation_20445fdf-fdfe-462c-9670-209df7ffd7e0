const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:4999';

/**
 * Service for handling author query operations
 */
export const authorQueryService = {
  /**
   * Create a new author query
   * @param {Object} queryData - Query data
   * @param {string} queryData.articleId - Article ID
   * @param {string} queryData.queryType - Type of query
   * @param {string[]} queryData.authors - List of author names
   * @param {string} [queryData.batchId] - Optional batch ID
   * @param {string} [queryData.queryText] - Optional custom query text
   * @returns {Promise<Object>} API response
   */
  async createAuthorQuery(queryData) {
    try {
      const response = await fetch(`${API_BASE}/api/admin/author-query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          article_id: queryData.articleId,
          query_type: queryData.queryType,
          author_names: queryData.authors,
          query_text: queryData.queryText,
          batch_id: queryData.batchId
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create author query');
      }

      return data;
    } catch (error) {
      console.error('Error creating author query:', error);
      throw error;
    }
  },

  /**
   * Get author queries with optional filtering
   * @param {Object} filters - Filter options
   * @param {string} [filters.articleId] - Filter by article ID
   * @param {string} [filters.batchId] - Filter by batch ID
   * @param {string} [filters.status] - Filter by status
   * @param {string} [filters.queryType] - Filter by query type
   * @param {number} [filters.page] - Page number
   * @param {number} [filters.perPage] - Items per page
   * @returns {Promise<Object>} API response with queries and pagination
   */
  async getAuthorQueries(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.articleId) params.append('article_id', filters.articleId);
      if (filters.batchId) params.append('batch_id', filters.batchId);
      if (filters.status) params.append('status', filters.status);
      if (filters.queryType) params.append('query_type', filters.queryType);
      if (filters.page) params.append('page', filters.page);
      if (filters.perPage) params.append('per_page', filters.perPage);

      const response = await fetch(`${API_BASE}/api/admin/author-queries?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch author queries');
      }

      return data;
    } catch (error) {
      console.error('Error fetching author queries:', error);
      throw error;
    }
  },

  /**
   * Get article metadata including author information
   * @param {string} articleId - Article ID
   * @returns {Promise<Object>} API response with article metadata and authors
   */
  async getArticleMetadata(articleId) {
    console.log('🚫 getArticleMetadata called - this should not happen in ZIP workflow!', articleId);
    throw new Error('API call disabled - using JSON data instead');
  }
};

export default authorQueryService;
