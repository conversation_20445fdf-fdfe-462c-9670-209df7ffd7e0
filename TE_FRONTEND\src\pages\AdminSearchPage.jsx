import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Icons } from '../components/common';
import ArticleIdSearchBox from '../components/ArticleIdSearchBox';
import PubMedComponent from '../components/PubMed/PubMedComponent';
import { normalizeReferences } from '../utils/referenceUtils';
import { LOADING_STATES } from '../utils/appUtils';

const AdminSearchPage = () => {
  const [searchParams] = useSearchParams();
  const [terms, setTerms] = useState({
    isLoading: LOADING_STATES.IDLE,
    data: [],
  });
  const [hideSearchSection, setHideSearchSection] = useState(false);

  // Check if we navigated here via clickable Article ID
  useEffect(() => {
    const articleIdParam = searchParams.get('articleId');
    if (articleIdParam) {
      // Hide search section when navigating from dashboard
      setHideSearchSection(true);
    }
  }, [searchParams]);

  const handleArticleIdSearch = (refs, articleId) => {
    const normalizedRefs = normalizeReferences(refs);
    if (normalizedRefs.length > 0) {
      setTerms({
        isLoading: LOADING_STATES.PROCESSING,
        data: normalizedRefs,
        articleId,
        fromDb: true,
      });
    } else {
      setTerms({
        isLoading: LOADING_STATES.IDLE,
        data: [],
        articleId,
        fromDb: false,
      });
    }
  };

  // Function to show search section (when user wants to search for different article)
  const showSearchSection = () => {
    setHideSearchSection(false);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 tracking-tight">
              {hideSearchSection && terms.articleId ? (
                <>
                  References for <span className="text-blue-600 font-mono">{terms.articleId}</span>
                </>
              ) : (
                'Reference Search'
              )}
            </h1>
            <p className="text-gray-600 mt-1">
              {hideSearchSection && terms.articleId ? (
                `Showing ${terms.data.length} references for this article`
              ) : (
                'Search and manage article references by ID'
              )}
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Icons.ClockIcon className="w-4 h-4" />
            <span>Real-time search</span>
          </div>
        </div>
      </div>

      {/* Search Section - Conditionally rendered */}
      {!hideSearchSection ? (
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
          <div className="max-w-2xl mx-auto text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Icons.SearchIcon className="w-8 h-8 text-white" />
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mb-3">
              Search Article References
            </h2>
            <p className="text-gray-600 mb-8">
              Enter an article ID to retrieve all associated references with detailed information
            </p>

            {/* Search Input */}
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
              <ArticleIdSearchBox onFound={handleArticleIdSearch} />
            </div>
          </div>
        </div>
      ) : (
        /* Compact Search Button - When search section is hidden */
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Icons.SearchIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">
                  Search Different Article
                </h3>
                <p className="text-xs text-gray-500">
                  Click to search for references from another article
                </p>
              </div>
            </div>
            <button
              onClick={showSearchSection}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
            >
              <Icons.SearchIcon className="w-4 h-4" />
              <span>New Search</span>
            </button>
          </div>
        </div>
      )}

      {/* Results Section */}
      {terms.data.length > 0 && (
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden animate-fadeIn">
          {/* Results Header */}
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <Icons.CheckIcon className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Search Results
                  </h3>
                  {terms.articleId && (
                    <p className="text-sm text-gray-600">
                      Article ID: <span className="font-medium">{terms.articleId}</span>
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Icons.DocumentIcon className="w-4 h-4" />
                <span>{terms.data.length} references found</span>
              </div>
            </div>
          </div>
          
          {/* Results Content */}
          <div className="p-6">
            <PubMedComponent terms={terms} dbMode={true} />
          </div>
        </div>
      )}

      {/* Empty State */}
      {terms.data.length === 0 && terms.isLoading === 0 && (
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-12">
          <div className="text-center max-w-md mx-auto">
            <div className="w-20 h-20 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Icons.SearchIcon className="w-10 h-10 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              Ready to Search
            </h3>
            <p className="text-gray-600 mb-8">
              Use the search box above to find references for any article ID. 
              Results will appear here with full citation details.
            </p>
            
            {/* Feature Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Icons.SearchIcon className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-1 text-sm">Quick Search</h4>
                <p className="text-xs text-gray-600">Instant article lookup</p>
              </div>
              
              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Icons.DocumentIcon className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-1 text-sm">Full Details</h4>
                <p className="text-xs text-gray-600">Complete citations</p>
              </div>
              
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200">
                <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Icons.StatsIcon className="w-4 h-4 text-white" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-1 text-sm">Analytics</h4>
                <p className="text-xs text-gray-600">Reference insights</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {terms.isLoading > 0 && terms.data.length === 0 && (
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-12">
          <div className="text-center">
            <div className="inline-flex items-center space-x-3 bg-blue-50 px-6 py-4 rounded-xl border border-blue-200">
              <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-blue-700 font-medium">Searching for references...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSearchPage;
