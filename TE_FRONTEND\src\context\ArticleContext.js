import React, { createContext, useContext, useState } from 'react';

const ArticleContext = createContext();

export const useArticle = () => {
  const context = useContext(ArticleContext);
  if (!context) {
    throw new Error('useArticle must be used within an ArticleProvider');
  }
  return context;
};

export const ArticleProvider = ({ children }) => {
  const [articleData, setArticleData] = useState({
    articleId: null,
    zipFiles: [],
    selectedFile: null,
    extractedContent: null,
    references: null,
    isProcessing: false
  });

  const updateArticleData = (updates) => {
    setArticleData(prev => ({ ...prev, ...updates }));
  };

  const resetArticleData = () => {
    setArticleData({
      articleId: null,
      zipFiles: [],
      selectedFile: null,
      extractedContent: null,
      references: null,
      isProcessing: false
    });
  };

  const setArticleId = (id) => {
    updateArticleData({ articleId: id });
  };

  const setZipFiles = (files) => {
    updateArticleData({ zipFiles: files });
  };

  const setSelectedFile = (file) => {
    updateArticleData({ selectedFile: file });
  };

  const setExtractedContent = (content) => {
    updateArticleData({ extractedContent: content });
  };

  const setReferences = (refs) => {
    updateArticleData({ references: refs });
  };

  const setProcessing = (isProcessing) => {
    updateArticleData({ isProcessing });
  };

  const value = {
    articleData,
    updateArticleData,
    resetArticleData,
    setArticleId,
    setZipFiles,
    setSelectedFile,
    setExtractedContent,
    setReferences,
    setProcessing
  };

  return (
    <ArticleContext.Provider value={value}>
      {children}
    </ArticleContext.Provider>
  );
};
