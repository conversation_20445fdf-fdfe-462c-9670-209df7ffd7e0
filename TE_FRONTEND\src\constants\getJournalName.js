const JournalPrompt = (searchTerm = "") => {
  const promptData = {
    instruction: `Extract the journal title from the given text.
    - If the journal title is already abbreviated, return it exactly as provided.
    - If it is not abbreviated, look for a standard abbreviation (e.g., from PubMed, NLM, or ISSN database) and return it.
    - If no official abbreviation is found, return the full journal title exactly as it appears in the input.
    - Do not guess or invent abbreviations.
    
    Expected JSON output format:
    {
      "journal_title": "string"
    }`,
    text_entries: searchTerm,
    output_format: "JSON",
  };

  return JSON.stringify(promptData, null, 2);
};

export default JournalPrompt;
