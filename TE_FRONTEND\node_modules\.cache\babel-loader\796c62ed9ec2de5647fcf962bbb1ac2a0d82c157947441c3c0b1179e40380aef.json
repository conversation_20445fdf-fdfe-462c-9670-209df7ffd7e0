{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Work\\\\MAIN_TE\\\\TE_FRONTEND\\\\src\\\\context\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nconst API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:4999';\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Helper function to check URL parameters (for backward compatibility)\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst hasTestParam = param => {\n  const urlParams = new URLSearchParams(window.location.search);\n  return urlParams.has(param);\n};\n\n// Hook to get current user role\nexport const useRole = () => {\n  _s2();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const getCurrentRole = () => {\n    // If user is authenticated and on admin routes, they are admin\n    if (isAuthenticated && window.location.pathname.startsWith('/admin')) {\n      return 'admin';\n    }\n\n    // Check for TE parameter for backward compatibility\n    if (hasTestParam('te')) {\n      return 'te';\n    }\n\n    // Check for admin parameter for backward compatibility (non-authenticated)\n    if (hasTestParam('admin')) {\n      return 'admin';\n    }\n    return 'default';\n  };\n  const role = getCurrentRole();\n  return {\n    role,\n    isAdmin: role === 'admin',\n    isTe: role === 'te',\n    isDefault: role === 'default',\n    isAuthenticated: isAuthenticated && role === 'admin'\n  };\n};\n_s2(useRole, \"1LGxUrjNz4q7iKM/2JDC9lJQ3xY=\", false, function () {\n  return [useAuth];\n});\nexport const AuthProvider = ({\n  children\n}) => {\n  _s3();\n  const [user, setUser] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check authentication status on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n  const checkAuthStatus = async () => {\n    try {\n      const response = await fetch(`${API_BASE}/api/auth/check`, {\n        method: 'GET',\n        credentials: 'include'\n      });\n      const data = await response.json();\n      if (response.ok && data.authenticated) {\n        setUser(data.user);\n        setIsAuthenticated(true);\n      } else {\n        setUser(null);\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setUser(null);\n      setIsAuthenticated(false);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const login = async credentials => {\n    try {\n      const response = await fetch(`${API_BASE}/api/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(credentials)\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setUser(data.user);\n        setIsAuthenticated(true);\n        return {\n          success: true,\n          user: data.user\n        };\n      } else {\n        return {\n          success: false,\n          error: data.error || 'Login failed'\n        };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      return {\n        success: false,\n        error: 'Network error. Please try again.'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await fetch(`${API_BASE}/api/auth/logout`, {\n        method: 'POST',\n        credentials: 'include'\n      });\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n  const value = {\n    user,\n    isAuthenticated,\n    isLoading,\n    login,\n    logout,\n    checkAuthStatus\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n_s3(AuthProvider, \"BKa16Kz0rM4B0y8AT6EXjU1HOY4=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "API_BASE", "process", "env", "REACT_APP_API_URL", "useAuth", "_s", "context", "Error", "hasTestParam", "param", "urlParams", "URLSearchParams", "window", "location", "search", "has", "useRole", "_s2", "isAuthenticated", "getCurrentRole", "pathname", "startsWith", "role", "isAdmin", "isTe", "isDefault", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s3", "user", "setUser", "isLoading", "setIsLoading", "setIsAuthenticated", "checkAuthStatus", "response", "fetch", "method", "credentials", "data", "json", "ok", "authenticated", "error", "console", "login", "headers", "body", "JSON", "stringify", "success", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Work/MAIN_TE/TE_FRONTEND/src/context/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\n\r\nconst AuthContext = createContext();\r\nconst API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:4999';\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (!context) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\n// Helper function to check URL parameters (for backward compatibility)\r\nconst hasTestParam = (param) => {\r\n  const urlParams = new URLSearchParams(window.location.search);\r\n  return urlParams.has(param);\r\n};\r\n\r\n// Hook to get current user role\r\nexport const useRole = () => {\r\n  const { isAuthenticated } = useAuth();\r\n\r\n  const getCurrentRole = () => {\r\n    // If user is authenticated and on admin routes, they are admin\r\n    if (isAuthenticated && window.location.pathname.startsWith('/admin')) {\r\n      return 'admin';\r\n    }\r\n\r\n    // Check for TE parameter for backward compatibility\r\n    if (hasTestParam('te')) {\r\n      return 'te';\r\n    }\r\n\r\n    // Check for admin parameter for backward compatibility (non-authenticated)\r\n    if (hasTestParam('admin')) {\r\n      return 'admin';\r\n    }\r\n\r\n    return 'default';\r\n  };\r\n\r\n  const role = getCurrentRole();\r\n\r\n  return {\r\n    role,\r\n    isAdmin: role === 'admin',\r\n    isTe: role === 'te',\r\n    isDefault: role === 'default',\r\n    isAuthenticated: isAuthenticated && role === 'admin'\r\n  };\r\n};\r\n\r\nexport const AuthProvider = ({ children }) => {\r\n  const [user, setUser] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n\r\n  // Check authentication status on app load\r\n  useEffect(() => {\r\n    checkAuthStatus();\r\n  }, []);\r\n\r\n  const checkAuthStatus = async () => {\r\n    try {\r\n      const response = await fetch(`${API_BASE}/api/auth/check`, {\r\n        method: 'GET',\r\n        credentials: 'include',\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.authenticated) {\r\n        setUser(data.user);\r\n        setIsAuthenticated(true);\r\n      } else {\r\n        setUser(null);\r\n        setIsAuthenticated(false);\r\n      }\r\n    } catch (error) {\r\n      console.error('Auth check failed:', error);\r\n      setUser(null);\r\n      setIsAuthenticated(false);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const login = async (credentials) => {\r\n    try {\r\n      const response = await fetch(`${API_BASE}/api/auth/login`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        credentials: 'include',\r\n        body: JSON.stringify(credentials),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setUser(data.user);\r\n        setIsAuthenticated(true);\r\n        return { success: true, user: data.user };\r\n      } else {\r\n        return { success: false, error: data.error || 'Login failed' };\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      return { success: false, error: 'Network error. Please try again.' };\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      await fetch(`${API_BASE}/api/auth/logout`, {\r\n        method: 'POST',\r\n        credentials: 'include',\r\n      });\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setUser(null);\r\n      setIsAuthenticated(false);\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    isAuthenticated,\r\n    isLoading,\r\n    login,\r\n    logout,\r\n    checkAuthStatus,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport default AuthContext;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AACnC,MAAMO,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGZ,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACO,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,OAAO;AASpB,MAAMI,YAAY,GAAIC,KAAK,IAAK;EAC9B,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EAC7D,OAAOJ,SAAS,CAACK,GAAG,CAACN,KAAK,CAAC;AAC7B,CAAC;;AAED;AACA,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAM;IAAEC;EAAgB,CAAC,GAAGd,OAAO,CAAC,CAAC;EAErC,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAID,eAAe,IAAIN,MAAM,CAACC,QAAQ,CAACO,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACpE,OAAO,OAAO;IAChB;;IAEA;IACA,IAAIb,YAAY,CAAC,IAAI,CAAC,EAAE;MACtB,OAAO,IAAI;IACb;;IAEA;IACA,IAAIA,YAAY,CAAC,OAAO,CAAC,EAAE;MACzB,OAAO,OAAO;IAChB;IAEA,OAAO,SAAS;EAClB,CAAC;EAED,MAAMc,IAAI,GAAGH,cAAc,CAAC,CAAC;EAE7B,OAAO;IACLG,IAAI;IACJC,OAAO,EAAED,IAAI,KAAK,OAAO;IACzBE,IAAI,EAAEF,IAAI,KAAK,IAAI;IACnBG,SAAS,EAAEH,IAAI,KAAK,SAAS;IAC7BJ,eAAe,EAAEA,eAAe,IAAII,IAAI,KAAK;EAC/C,CAAC;AACH,CAAC;AAACL,GAAA,CA/BWD,OAAO;EAAA,QACUZ,OAAO;AAAA;AAgCrC,OAAO,MAAMsB,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuB,eAAe,EAAEe,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACdsC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEpC,QAAS,iBAAgB,EAAE;QACzDqC,MAAM,EAAE,KAAK;QACbC,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACM,EAAE,IAAIF,IAAI,CAACG,aAAa,EAAE;QACrCZ,OAAO,CAACS,IAAI,CAACV,IAAI,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLH,OAAO,CAAC,IAAI,CAAC;QACbG,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1Cb,OAAO,CAAC,IAAI,CAAC;MACbG,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,SAAS;MACRD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMa,KAAK,GAAG,MAAOP,WAAW,IAAK;IACnC,IAAI;MACF,MAAMH,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEpC,QAAS,iBAAgB,EAAE;QACzDqC,MAAM,EAAE,MAAM;QACdS,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDR,WAAW,EAAE,SAAS;QACtBS,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACX,WAAW;MAClC,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACM,EAAE,EAAE;QACfX,OAAO,CAACS,IAAI,CAACV,IAAI,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxB,OAAO;UAAEiB,OAAO,EAAE,IAAI;UAAErB,IAAI,EAAEU,IAAI,CAACV;QAAK,CAAC;MAC3C,CAAC,MAAM;QACL,OAAO;UAAEqB,OAAO,EAAE,KAAK;UAAEP,KAAK,EAAEJ,IAAI,CAACI,KAAK,IAAI;QAAe,CAAC;MAChE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO;QAAEO,OAAO,EAAE,KAAK;QAAEP,KAAK,EAAE;MAAmC,CAAC;IACtE;EACF,CAAC;EAED,MAAMQ,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMf,KAAK,CAAE,GAAEpC,QAAS,kBAAiB,EAAE;QACzCqC,MAAM,EAAE,MAAM;QACdC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRb,OAAO,CAAC,IAAI,CAAC;MACbG,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMmB,KAAK,GAAG;IACZvB,IAAI;IACJX,eAAe;IACfa,SAAS;IACTc,KAAK;IACLM,MAAM;IACNjB;EACF,CAAC;EAED,oBACEpC,OAAA,CAACC,WAAW,CAACsD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAzB,QAAA,EAChCA;EAAQ;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC7B,GAAA,CAzFWF,YAAY;AAAAgC,EAAA,GAAZhC,YAAY;AA2FzB,eAAe3B,WAAW;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}