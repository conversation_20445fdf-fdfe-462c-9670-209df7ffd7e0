import React, { createContext, useContext, useState, useEffect } from 'react';

const ZipQueueContext = createContext();

export const useZipQueue = () => {
  const context = useContext(ZipQueueContext);
  if (!context) {
    throw new Error('useZipQueue must be used within a ZipQueueProvider');
  }
  return context;
};

export const ZipQueueProvider = ({ children }) => {
  const [zipQueue, setZipQueue] = useState([]);
  const [currentFolder, setCurrentFolder] = useState(null);
  const [processedZips, setProcessedZips] = useState([]);

  // Load queue from localStorage on mount
  useEffect(() => {
    try {
      const savedQueue = localStorage.getItem('zipQueue');
      const savedFolder = localStorage.getItem('currentFolder');
      const savedProcessed = localStorage.getItem('processedZips');

      if (savedQueue) {
        try {
          const parsedQueue = JSON.parse(savedQueue);
          // Note: File objects won't be available after page refresh
          // Users will need to re-upload folders after refresh
          setZipQueue(parsedQueue);
        } catch (e) {
          console.error('Error loading zip queue from localStorage:', e);
          localStorage.removeItem('zipQueue');
        }
      }

      if (savedFolder) {
        try {
          setCurrentFolder(JSON.parse(savedFolder));
        } catch (e) {
          console.error('Error loading current folder from localStorage:', e);
          localStorage.removeItem('currentFolder');
        }
      }

      if (savedProcessed) {
        try {
          setProcessedZips(JSON.parse(savedProcessed));
        } catch (e) {
          console.error('Error loading processed zips from localStorage:', e);
          localStorage.removeItem('processedZips');
        }
      }
    } catch (error) {
      console.error('Error accessing localStorage:', error);
      // Clear all localStorage if there's a persistent issue
      localStorage.removeItem('zipQueue');
      localStorage.removeItem('currentFolder');
      localStorage.removeItem('processedZips');
    }
  }, []);

  // Save to localStorage whenever state changes (excluding file data to avoid quota issues)
  useEffect(() => {
    try {
      // Remove file data before saving to localStorage to avoid quota issues
      const zipQueueForStorage = zipQueue.map(zip => ({
        ...zip,
        file: null, // Don't store the actual file object
        extractedFiles: zip.extractedFiles ? zip.extractedFiles.map(file => ({
          ...file,
          zipEntry: null // Don't store zipEntry objects
        })) : null
      }));
      localStorage.setItem('zipQueue', JSON.stringify(zipQueueForStorage));
    } catch (error) {
      console.warn('Failed to save zipQueue to localStorage:', error);
      // Clear localStorage if quota exceeded
      if (error.name === 'QuotaExceededError') {
        localStorage.removeItem('zipQueue');
        localStorage.removeItem('currentFolder');
        localStorage.removeItem('processedZips');
      }
    }
  }, [zipQueue]);

  useEffect(() => {
    try {
      localStorage.setItem('currentFolder', JSON.stringify(currentFolder));
    } catch (error) {
      console.warn('Failed to save currentFolder to localStorage:', error);
    }
  }, [currentFolder]);

  useEffect(() => {
    try {
      // Remove file data before saving
      const processedZipsForStorage = processedZips.map(zip => ({
        ...zip,
        file: null,
        extractedFiles: zip.extractedFiles ? zip.extractedFiles.map(file => ({
          ...file,
          zipEntry: null
        })) : null
      }));
      localStorage.setItem('processedZips', JSON.stringify(processedZipsForStorage));
    } catch (error) {
      console.warn('Failed to save processedZips to localStorage:', error);
    }
  }, [processedZips]);

  const addZipQueue = (folderData) => {
    setCurrentFolder({
      name: folderData.folderName,
      uploadedAt: new Date().toISOString(),
      totalZips: folderData.totalZips,
      jsonSummaryFile: folderData.jsonSummaryFile // Store JSON summary file for TE assignments
    });
    setZipQueue(folderData.zipQueue);
    setProcessedZips([]); // Reset processed zips for new folder
  };

  const updateCurrentFolderWithBatchSummary = (batchSummary) => {
    setCurrentFolder(prev => ({
      ...prev,
      batchSummary: batchSummary,
      driveUploadComplete: true,
      uploadedAt: new Date().toISOString()
    }));
  };

  const updateZipStatus = (zipId, status, additionalData = {}) => {
    setZipQueue(prevQueue => 
      prevQueue.map(zip => 
        zip.id === zipId 
          ? { 
              ...zip, 
              status, 
              ...additionalData,
              ...(status === 'completed' ? { processedAt: new Date().toISOString() } : {})
            }
          : zip
      )
    );
  };

  const markZipAsProcessed = (zipId) => {
    const zipToProcess = zipQueue.find(zip => zip.id === zipId);

    if (zipToProcess) {
      // Move to processed list with 'ready_for_assignment' status
      setProcessedZips(prev => [...prev, { ...zipToProcess, status: 'ready_for_assignment', processedAt: new Date().toISOString() }]);

      // Remove from pending queue
      setZipQueue(prev => prev.filter(zip => zip.id !== zipId));
    } else {
      console.error('ZIP not found in queue for zipId:', zipId);
    }
  };

  const getZipById = (zipId) => {
    return zipQueue.find(zip => zip.id === zipId) || processedZips.find(zip => zip.id === zipId);
  };

  const getZipByArticleId = (articleId) => {
    return zipQueue.find(zip => zip.articleId === articleId) || processedZips.find(zip => zip.articleId === articleId);
  };

  const getPendingZips = () => {
    return zipQueue.filter(zip => zip.status === 'pending');
  };

  const getProcessingZips = () => {
    return zipQueue.filter(zip => zip.status === 'processing');
  };

  const getCompletedZips = () => {
    return processedZips.filter(zip => zip.status === 'completed');
  };

  const getReadyForAssignmentZips = () => {
    return processedZips.filter(zip => zip.status === 'ready_for_assignment');
  };

  const getAssignedZips = () => {
    return processedZips.filter(zip => zip.status === 'assigned');
  };

  const markZipReadyForAssignment = (zipId) => {
    setProcessedZips(prev =>
      prev.map(zip =>
        zip.id === zipId
          ? { ...zip, status: 'ready_for_assignment' }
          : zip
      )
    );
  };

  const markZipAssigned = (zipId, assignmentDetails = null) => {
    setProcessedZips(prev =>
      prev.map(zip =>
        zip.id === zipId
          ? {
              ...zip,
              status: 'assigned',
              assignedAt: new Date().toISOString(),
              assignmentDetails: assignmentDetails
            }
          : zip
      )
    );
  };

  const markZipCompletelyDone = (zipId) => {
    setProcessedZips(prev =>
      prev.map(zip =>
        zip.id === zipId
          ? { ...zip, status: 'completed' }
          : zip
      )
    );
  };

  const clearQueue = () => {
    setZipQueue([]);
    setProcessedZips([]);
    setCurrentFolder(null);
    clearLocalStorage();
  };

  const clearLocalStorage = () => {
    try {
      localStorage.removeItem('zipQueue');
      localStorage.removeItem('currentFolder');
      localStorage.removeItem('processedZips');
    } catch (error) {
      console.warn('Error clearing localStorage:', error);
    }
  };

  const getQueueStats = () => {
    const pending = getPendingZips().length;
    const processing = getProcessingZips().length;
    const completed = getCompletedZips().length;
    const total = pending + processing + completed;
    
    return {
      pending,
      processing,
      completed,
      total,
      progress: total > 0 ? Math.round((completed / total) * 100) : 0
    };
  };

  const value = {
    // State
    zipQueue,
    currentFolder,
    processedZips,

    // Actions
    addZipQueue,
    updateZipStatus,
    markZipAsProcessed,
    markZipReadyForAssignment,
    markZipAssigned,
    markZipCompletelyDone,
    clearQueue,
    updateCurrentFolderWithBatchSummary,

    // Getters
    getZipById,
    getZipByArticleId,
    getPendingZips,
    getProcessingZips,
    getCompletedZips,
    getReadyForAssignmentZips,
    getAssignedZips,
    getQueueStats
  };

  return (
    <ZipQueueContext.Provider value={value}>
      {children}
    </ZipQueueContext.Provider>
  );
};
