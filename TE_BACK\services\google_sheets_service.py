"""
Google Sheets Service for TE Assignment Updates
Handles updating Google Sheets when articles are assigned to TEs
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Optional
import jwt
import time
import requests

logger = logging.getLogger(__name__)


class GoogleSheetsService:
    """Service for updating Google Sheets with TE assignment information"""
    
    def __init__(self):
        """Initialize Google Sheets service with credentials from environment"""
        self.spreadsheet_id = os.getenv('GOOGLE_SHEETS_ID', '1sdERmtt3I6QDV3Xtw1NzJswQ0MhrbiGg6mf5mTiYlSU')
        self.sheet_page = os.getenv('GOOGLE_SHEETS_PAGE', 'TESTAUTOMATION!A2')
        self.service_account = os.getenv('GOOGLE_SHEETS_SERVICE_ACCOUNT', '115083737441987478188')
        self.private_key = os.getenv('GOOGLE_SHEETS_PRIVATE_KEY', self._get_default_private_key())
        
        # Extract sheet name from page reference (e.g., "TESTAUTOMATION!A2" -> "TESTAUTOMATION")
        self.sheet_name = self.sheet_page.split('!')[0]
        
        logger.info(f"Initialized Google Sheets service for sheet: {self.sheet_name}")
    
    def _get_default_private_key(self) -> str:
        """Get default private key from environment or hardcoded value"""
        return """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    
    def _get_access_token(self) -> str:
        """Generate Google Sheets API access token using JWT"""
        try:
            iat = int(time.time())
            exp = iat + 3600
            
            payload = {
                'iss': self.service_account,
                'scope': 'https://www.googleapis.com/auth/spreadsheets',
                'aud': 'https://accounts.google.com/o/oauth2/token',
                'exp': exp,
                'iat': iat
            }
            
            # Create JWT token
            jwt_token = jwt.encode(payload, self.private_key, algorithm='RS256')
            
            # Exchange JWT for access token
            response = requests.post(
                'https://accounts.google.com/o/oauth2/token',
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                data={
                    'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                    'assertion': jwt_token
                }
            )
            
            if response.status_code != 200:
                raise Exception(f"Failed to get access token: {response.status_code} - {response.text}")
            
            return response.json()['access_token']
            
        except Exception as e:
            logger.error(f"Failed to get Google Sheets access token: {e}")
            raise
    


    def add_te_assignment_row(self, article_data: Dict[str, any], te_name: str, system_query_count: int = 0) -> bool:
        """
        Append a new row to the Google Sheet with TE assignment details

        Columns:
        A: Journal ID
        B: Article ID
        C: Download Date
        D: Due Date
        E: TE Name
        F: Assigning Date
        G: System Queries (count of validation_error queries only)

        Args:
            article_data: Dictionary containing article info (journal_name, journal_code, article_id, created_at)
            te_name: Name of the TE assigned
            system_query_count: Number of system queries (validation_error type only) for this article

        Returns:
            Boolean indicating success
        """
        try:
            access_token = self._get_access_token()

            # Format dates as mm/dd/yyyy
            today = datetime.now().strftime('%m/%d/%Y')

            # Format dates
            download_date = article_data.get('created_at')
            if not download_date:
                download_date = datetime.now()

            if isinstance(download_date, str):
                try:
                    download_date = datetime.fromisoformat(download_date)
                except:
                    download_date = datetime.now()

            # Due date is download date + 4 days
            from datetime import timedelta
            due_date = download_date + timedelta(days=4)

            # Format dates as mmddyyyy
            download_date_str = download_date.strftime('%m/%d/%Y')
            due_date_str = due_date.strftime('%m/%d/%Y')

            # Prepare row data
            values = [[
                '',   # Col A: Journal ID
                article_data.get('article_id', ''),     # Col B: Article ID
                download_date_str,                      # Col C: Download Date (mm/dd/yyyy)
                due_date_str,                           # Col D: Due Date (mm/dd/yyyy)
                te_name,                                # Col E: TE Name
                today,                                  # Col F: Assigning Date (mm/dd/yyyy)
                'QUERY' if system_query_count > 0 else ''  # Col G: System Queries indicator
            ]]

            # STEP 1: Get all data from the sheet to find the last row
            logger.info(f"📊 Finding last row in Google Sheets for article {article_data.get('article_id')}")

            get_url = f"https://sheets.googleapis.com/v4/spreadsheets/{self.spreadsheet_id}/values/{self.sheet_name}"
            get_response = requests.get(
                get_url,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {access_token}'
                }
            )

            if get_response.status_code != 200:
                logger.error(f"❌ Failed to get sheet data: {get_response.status_code} - {get_response.text}")
                return False

            sheet_data = get_response.json()
            rows = sheet_data.get('values', [])

            # Find the last row with data (add 1 because we want the next row, +1 for 1-based indexing)
            last_row = len(rows) + 1

            logger.info(f"📍 Last row with data: {len(rows)}, will append to row: {last_row}")

            # STEP 2: Add a small delay to ensure sheet is ready (100ms)
            import time
            time.sleep(0.1)

            # STEP 3: Write to the specific row after the last row
            # Use UPDATE instead of APPEND to write to a specific row
            target_range = f"{self.sheet_name}!A{last_row}:G{last_row}"
            update_url = f"https://sheets.googleapis.com/v4/spreadsheets/{self.spreadsheet_id}/values/{target_range}?valueInputOption=USER_ENTERED"

            logger.info(f"📝 Writing to specific range: {target_range}")

            response = requests.put(
                update_url,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {access_token}'
                },
                json={'values': values}
            )

            if response.status_code == 200:
                response_data = response.json()
                updated_range = response_data.get('updatedRange', target_range)
                logger.info(f"✅ Added row for {article_data.get('article_id')}: TE={te_name}, Range={updated_range}")
                return True
            else:
                logger.error(f"❌ Failed to write row for {article_data.get('article_id')}: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error adding row to Google Sheet: {e}")
            return False

    def update_te_assignment(self, article_ids: List[str], te_name: str, query_counts: Optional[Dict[str, int]] = None) -> Dict[str, any]:
        """
        DEPRECATED: Use add_te_assignment_row instead.
        Update Google Sheets when articles are assigned to TE
        """
        logger.warning("Using deprecated update_te_assignment method. This should be replaced by add_te_assignment_row.")
        return {'success': False, 'error': 'Deprecated method', 'updated': 0, 'failed': len(article_ids), 'not_found': 0, 'total': len(article_ids)}


def create_google_sheets_service() -> GoogleSheetsService:
    """Factory function to create GoogleSheetsService instance"""
    return GoogleSheetsService()

