{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Work\\\\MAIN_TE\\\\TE_FRONTEND\\\\src\\\\components\\\\zip\\\\DocumentPreview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport mammoth from 'mammoth';\nimport { useArticle } from '../../context/ArticleContext';\nimport { useRole } from '../../context/AuthContext';\nimport { Icons } from '../common';\nimport ArticleMetadataPanel from './ArticleMetadataPanel';\nimport RaiseQueryButton from './RaiseQueryButton';\nimport './DocumentPreview.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DocumentPreview = ({\n  file,\n  onBack,\n  onCopyReferences,\n  zipId,\n  zipFile,\n  authors,\n  articleId,\n  onZipModified,\n  onValidationQuerySent\n}) => {\n  _s();\n  const [content, setContent] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAuthorsSidebar, setShowAuthorsSidebar] = useState(true);\n  const [showScrollToTop, setShowScrollToTop] = useState(false);\n  const navigate = useNavigate();\n  const {\n    setExtractedContent,\n    articleData\n  } = useArticle();\n  const {\n    isAdmin\n  } = useRole();\n  useEffect(() => {\n    if (file) {\n      loadFileContent();\n    }\n  }, [file]);\n\n  // Handle scroll events to show/hide scroll-to-top button\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      setShowScrollToTop(scrollTop > 300); // Show button after scrolling 300px\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const loadFileContent = async () => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('Loading file content for:', file.name);\n      console.log('File object:', file);\n      console.log('ZipEntry exists:', !!file.zipEntry);\n      console.log('ZipEntry type:', typeof file.zipEntry);\n      if (!file.zipEntry) {\n        throw new Error('No zipEntry found in file object');\n      }\n      const arrayBuffer = await file.zipEntry.async('arraybuffer');\n      console.log('ArrayBuffer loaded, size:', arrayBuffer.byteLength);\n      if (file.type === 'docx' || file.type === 'doc') {\n        try {\n          const result = await mammoth.convertToHtml({\n            arrayBuffer\n          });\n          setContent(result.value);\n          setExtractedContent(result.value);\n\n          // Log any warnings from mammoth\n          if (result.messages && result.messages.length > 0) {\n            console.warn('Mammoth conversion warnings:', result.messages);\n          }\n        } catch (docError) {\n          console.error('Error converting document:', docError);\n          // Fallback: try to extract as plain text\n          try {\n            const text = new TextDecoder('utf-8', {\n              ignoreBOM: true\n            }).decode(arrayBuffer);\n            const cleanText = text.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]/g, '').trim();\n            if (cleanText.length > 100) {\n              setContent(`<pre>${cleanText}</pre>`);\n              setExtractedContent(cleanText);\n              console.log('Fallback: Extracted as plain text');\n            } else {\n              throw new Error('Unable to extract meaningful content');\n            }\n          } catch (fallbackError) {\n            throw new Error(`Cannot process ${file.type.toUpperCase()} file. Please try converting to .docx format first.`);\n          }\n        }\n      } else if (file.type === 'txt') {\n        const text = new TextDecoder().decode(arrayBuffer);\n        setContent(`<pre>${text}</pre>`);\n        setExtractedContent(text);\n      } else {\n        setError(`Unsupported file type: ${file.type.toUpperCase()}. Supported formats: .docx, .doc, .txt`);\n      }\n    } catch (err) {\n      console.error('Error loading file content:', err);\n      console.error('Error details:', err.message);\n      console.error('File object at error:', file);\n      let errorMessage = 'Failed to load file content';\n      if (err.message.includes('zipEntry')) {\n        errorMessage = 'File data is not properly loaded. Please try going back and selecting the file again.';\n      } else if (err.message.includes('async')) {\n        errorMessage = 'Unable to extract file from ZIP archive. The file may be corrupted.';\n      }\n      setError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    console.log('Text selection event triggered:', {\n      text,\n      length: text.length\n    });\n    setSelectedText(text);\n  };\n  const processSelectedText = () => {\n    if (selectedText) {\n      // Check multiple indicators that this is an admin session\n      const hasAdminParam = window.location.search.includes('admin=true');\n      const hasAdminPath = window.location.pathname.includes('/admin');\n      const hasAdminSession = sessionStorage.getItem('adminContext') === 'true';\n      const referrerIsAdmin = document.referrer.includes('/admin');\n\n      // Use admin route if any admin indicator is present OR if we're clearly in admin workflow\n      const useAdminRoute = isAdmin || hasAdminParam || hasAdminPath || hasAdminSession || referrerIsAdmin || window.location.href.includes('admin') ||\n      // Current URL has admin\n      document.referrer.includes('admin'); // Came from admin page\n\n      const processRoute = useAdminRoute ? '/admin/process' : '/process';\n      navigate(processRoute, {\n        state: {\n          references: selectedText,\n          articleId: articleData === null || articleData === void 0 ? void 0 : articleData.articleId,\n          fromZipProcessor: true,\n          manualEntry: false,\n          isAdminContext: useAdminRoute,\n          // Pass admin context explicitly\n          zipId: zipId // Pass the zipId for auto-completion\n        }\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"document-preview-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-button\",\n          children: /*#__PURE__*/_jsxDEV(Icons.ChevronLeftIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-file-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"preview-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"file-icon\",\n              children: file.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), file.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"preview-subtitle\",\n            children: [file.type.toUpperCase(), \" \\u2022 Document Preview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-actions\",\n        children: [/*#__PURE__*/_jsxDEV(RaiseQueryButton, {\n          queryType: \"general\",\n          articleId: articleId,\n          zipFile: zipFile,\n          buttonText: \"Raise Query\",\n          buttonIcon: \"\\uD83D\\uDCE7\",\n          variant: \"primary\",\n          size: \"small\",\n          onQuerySent: id => {\n            console.log('Query sent for article:', id);\n            // Mark ZIP with validationQuerySent flag for Leena assignment\n            if (onValidationQuerySent) {\n              onValidationQuerySent(id);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), authors && authors.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAuthorsSidebar(!showAuthorsSidebar),\n          className: \"toggle-sidebar-button\",\n          title: showAuthorsSidebar ? \"Hide Authors\" : \"Show Authors\",\n          children: /*#__PURE__*/_jsxDEV(Icons.SettingsIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666',\n            padding: '4px'\n          },\n          children: [\"Selected: \", selectedText ? `\"${selectedText.substring(0, 20)}...\"` : 'none']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), selectedText && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: processSelectedText,\n          className: \"process-button selected\",\n          children: [/*#__PURE__*/_jsxDEV(Icons.ArrowRightIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), \"Process Selected\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `preview-with-sidebar ${!showAuthorsSidebar ? 'sidebar-hidden' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-content-wrapper\",\n        children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading document...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-error\",\n          children: [/*#__PURE__*/_jsxDEV(Icons.ExclamationIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Error Loading Document\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), error.includes('.DOC') && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-suggestion\",\n              children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tip:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 22\n              }, this), \" For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), !isLoading && !error && content && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-content-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"preview-content\",\n            dangerouslySetInnerHTML: {\n              __html: content\n            },\n            onMouseUp: handleTextSelection,\n            onKeyUp: handleTextSelection\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"instruction-icon\",\n              children: \"\\uD83D\\uDDB1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Select text to process specific sections\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"instruction-icon\",\n              children: \"\\uD83D\\uDD04\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Processing will take you directly to the reference processing screen\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 9\n        }, this), selectedText && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selection-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selection-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"selection-icon\",\n              children: \"\\u2702\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Selected Text (\", selectedText.length, \" characters)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selection-preview\",\n            children: [selectedText.substring(0, 200), selectedText.length > 200 && '...']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), showAuthorsSidebar && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"authors-sidebar\",\n        children: authors && authors.length > 0 && /*#__PURE__*/_jsxDEV(ArticleMetadataPanel, {\n          articleId: articleId,\n          authors: authors,\n          skipApiCall: true,\n          alwaysExpanded: true,\n          zipFiles: (articleData === null || articleData === void 0 ? void 0 : articleData.zipFiles) || [],\n          onZipModified: onZipModified,\n          onQueryCreated: () => {\n            console.log('Query created for article:', articleId);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentPreview, \"VwWn+uPl7NUbNbsGUhusav/OZ3I=\", false, function () {\n  return [useNavigate, useArticle, useRole];\n});\n_c = DocumentPreview;\nexport default DocumentPreview;\nvar _c;\n$RefreshReg$(_c, \"DocumentPreview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "mammoth", "useArticle", "useRole", "Icons", "ArticleMetadataPanel", "RaiseQuery<PERSON><PERSON>on", "jsxDEV", "_jsxDEV", "DocumentPreview", "file", "onBack", "onCopyReferences", "zipId", "zipFile", "authors", "articleId", "onZipModified", "onValidationQuerySent", "_s", "content", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "setIsLoading", "error", "setError", "selectedText", "setSelectedText", "showAuthorsSidebar", "setShowAuthorsSidebar", "showScrollToTop", "setShowScrollToTop", "navigate", "setExtractedContent", "articleData", "isAdmin", "loadFileContent", "handleScroll", "scrollTop", "window", "pageYOffset", "document", "documentElement", "addEventListener", "removeEventListener", "console", "log", "name", "zipEntry", "Error", "arrayBuffer", "async", "byteLength", "type", "result", "convertToHtml", "value", "messages", "length", "warn", "doc<PERSON><PERSON><PERSON>", "text", "TextDecoder", "ignoreBOM", "decode", "cleanText", "replace", "trim", "fallback<PERSON><PERSON>r", "toUpperCase", "err", "message", "errorMessage", "includes", "handleTextSelection", "selection", "getSelection", "toString", "processSelectedText", "hasAdminParam", "location", "search", "has<PERSON>d<PERSON><PERSON><PERSON>", "pathname", "hasAdminSession", "sessionStorage", "getItem", "referrerIsAdmin", "referrer", "useAdminRoute", "href", "processRoute", "state", "references", "fromZipProcessor", "manualEntry", "isAdminContext", "className", "children", "onClick", "ChevronLeftIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "queryType", "buttonText", "buttonIcon", "variant", "size", "onQuerySent", "id", "title", "SettingsIcon", "process", "env", "NODE_ENV", "style", "fontSize", "color", "padding", "substring", "ArrowRightIcon", "ExclamationIcon", "dangerouslySetInnerHTML", "__html", "onMouseUp", "onKeyUp", "skipApiCall", "alwaysExpanded", "zipFiles", "onQ<PERSON>yCreated", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Work/MAIN_TE/TE_FRONTEND/src/components/zip/DocumentPreview.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport mammoth from 'mammoth';\nimport { useArticle } from '../../context/ArticleContext';\nimport { useRole } from '../../context/AuthContext';\nimport { Icons } from '../common';\nimport ArticleMetadataPanel from './ArticleMetadataPanel';\nimport RaiseQueryButton from './RaiseQueryButton';\nimport './DocumentPreview.css';\n\nconst DocumentPreview = ({ file, onBack, onCopyReferences, zipId, zipFile, authors, articleId, onZipModified, onValidationQuerySent }) => {\n  const [content, setContent] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAuthorsSidebar, setShowAuthorsSidebar] = useState(true);\n  const [showScrollToTop, setShowScrollToTop] = useState(false);\n  const navigate = useNavigate();\n  const { setExtractedContent, articleData } = useArticle();\n  const { isAdmin } = useRole();\n\n  useEffect(() => {\n    if (file) {\n      loadFileContent();\n    }\n  }, [file]);\n\n  // Handle scroll events to show/hide scroll-to-top button\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      setShowScrollToTop(scrollTop > 300); // Show button after scrolling 300px\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const loadFileContent = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('Loading file content for:', file.name);\n      console.log('File object:', file);\n      console.log('ZipEntry exists:', !!file.zipEntry);\n      console.log('ZipEntry type:', typeof file.zipEntry);\n\n      if (!file.zipEntry) {\n        throw new Error('No zipEntry found in file object');\n      }\n\n      const arrayBuffer = await file.zipEntry.async('arraybuffer');\n      console.log('ArrayBuffer loaded, size:', arrayBuffer.byteLength);\n      \n      if (file.type === 'docx' || file.type === 'doc') {\n        try {\n          const result = await mammoth.convertToHtml({ arrayBuffer });\n          setContent(result.value);\n          setExtractedContent(result.value);\n\n          // Log any warnings from mammoth\n          if (result.messages && result.messages.length > 0) {\n            console.warn('Mammoth conversion warnings:', result.messages);\n          }\n        } catch (docError) {\n          console.error('Error converting document:', docError);\n          // Fallback: try to extract as plain text\n          try {\n            const text = new TextDecoder('utf-8', { ignoreBOM: true }).decode(arrayBuffer);\n            const cleanText = text.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]/g, '').trim();\n            if (cleanText.length > 100) {\n              setContent(`<pre>${cleanText}</pre>`);\n              setExtractedContent(cleanText);\n              console.log('Fallback: Extracted as plain text');\n            } else {\n              throw new Error('Unable to extract meaningful content');\n            }\n          } catch (fallbackError) {\n            throw new Error(`Cannot process ${file.type.toUpperCase()} file. Please try converting to .docx format first.`);\n          }\n        }\n      } else if (file.type === 'txt') {\n        const text = new TextDecoder().decode(arrayBuffer);\n        setContent(`<pre>${text}</pre>`);\n        setExtractedContent(text);\n      } else {\n        setError(`Unsupported file type: ${file.type.toUpperCase()}. Supported formats: .docx, .doc, .txt`);\n      }\n    } catch (err) {\n      console.error('Error loading file content:', err);\n      console.error('Error details:', err.message);\n      console.error('File object at error:', file);\n\n      let errorMessage = 'Failed to load file content';\n      if (err.message.includes('zipEntry')) {\n        errorMessage = 'File data is not properly loaded. Please try going back and selecting the file again.';\n      } else if (err.message.includes('async')) {\n        errorMessage = 'Unable to extract file from ZIP archive. The file may be corrupted.';\n      }\n\n      setError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    console.log('Text selection event triggered:', { text, length: text.length });\n    setSelectedText(text);\n  };\n\n  const processSelectedText = () => {\n    if (selectedText) {\n      // Check multiple indicators that this is an admin session\n      const hasAdminParam = window.location.search.includes('admin=true');\n      const hasAdminPath = window.location.pathname.includes('/admin');\n      const hasAdminSession = sessionStorage.getItem('adminContext') === 'true';\n      const referrerIsAdmin = document.referrer.includes('/admin');\n\n      // Use admin route if any admin indicator is present OR if we're clearly in admin workflow\n      const useAdminRoute = isAdmin || hasAdminParam || hasAdminPath || hasAdminSession || referrerIsAdmin ||\n                           window.location.href.includes('admin') || // Current URL has admin\n                           document.referrer.includes('admin');      // Came from admin page\n\n      const processRoute = useAdminRoute ? '/admin/process' : '/process';\n\n      navigate(processRoute, {\n        state: {\n          references: selectedText,\n          articleId: articleData?.articleId,\n          fromZipProcessor: true,\n          manualEntry: false,\n          isAdminContext: useAdminRoute, // Pass admin context explicitly\n          zipId: zipId // Pass the zipId for auto-completion\n        }\n      });\n    }\n  };\n\n  return (\n    <div className=\"document-preview-container\">\n      {/* Header */}\n      <div className=\"preview-header\">\n        <div className=\"preview-title-section\">\n          <button onClick={onBack} className=\"back-button\">\n            <Icons.ChevronLeftIcon />\n          </button>\n          <div className=\"preview-file-info\">\n            <h2 className=\"preview-title\">\n              <span className=\"file-icon\">{file.icon}</span>\n              {file.name}\n            </h2>\n            <p className=\"preview-subtitle\">\n              {file.type.toUpperCase()} • Document Preview\n            </p>\n          </div>\n        </div>\n\n        <div className=\"preview-actions\">\n          {/* Raise Query Button */}\n          <RaiseQueryButton\n            queryType=\"general\"\n            articleId={articleId}\n            zipFile={zipFile}\n            buttonText=\"Raise Query\"\n            buttonIcon=\"📧\"\n            variant=\"primary\"\n            size=\"small\"\n            onQuerySent={(id) => {\n              console.log('Query sent for article:', id);\n              // Mark ZIP with validationQuerySent flag for Leena assignment\n              if (onValidationQuerySent) {\n                onValidationQuerySent(id);\n              }\n            }}\n          />\n\n          {authors && authors.length > 0 && (\n            <button\n              onClick={() => setShowAuthorsSidebar(!showAuthorsSidebar)}\n              className=\"toggle-sidebar-button\"\n              title={showAuthorsSidebar ? \"Hide Authors\" : \"Show Authors\"}\n            >\n              <Icons.SettingsIcon />\n            </button>\n          )}\n\n          {/* Debug info - remove after testing */}\n          {process.env.NODE_ENV === 'development' && (\n            <div style={{ fontSize: '12px', color: '#666', padding: '4px' }}>\n              Selected: {selectedText ? `\"${selectedText.substring(0, 20)}...\"` : 'none'}\n            </div>\n          )}\n\n          {selectedText && (\n            <button onClick={processSelectedText} className=\"process-button selected\">\n              <Icons.ArrowRightIcon />\n              Process Selected\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Main Content with Sidebar Layout */}\n      <div className={`preview-with-sidebar ${!showAuthorsSidebar ? 'sidebar-hidden' : ''}`}>\n        {/* Left Side - Document Content */}\n        <div className=\"preview-content-wrapper\">\n        {isLoading && (\n          <div className=\"preview-loading\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading document...</p>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"preview-error\">\n            <Icons.ExclamationIcon />\n            <div>\n              <h3>Error Loading Document</h3>\n              <p>{error}</p>\n              {error.includes('.DOC') && (\n                <div className=\"error-suggestion\">\n                  💡 <strong>Tip:</strong> For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {!isLoading && !error && content && (\n          <div className=\"preview-content-container\">\n            <div\n              className=\"preview-content\"\n              dangerouslySetInnerHTML={{ __html: content }}\n              onMouseUp={handleTextSelection}\n              onKeyUp={handleTextSelection}\n            />\n          </div>\n        )}\n\n        {/* Instructions */}\n        <div className=\"preview-instructions\">\n          <div className=\"instruction-item\">\n            <span className=\"instruction-icon\">🖱️</span>\n            <span>Select text to process specific sections</span>\n          </div>\n          <div className=\"instruction-item\">\n            <span className=\"instruction-icon\">🔄</span>\n            <span>Processing will take you directly to the reference processing screen</span>\n          </div>\n        </div>\n\n        {/* Selection Info */}\n        {selectedText && (\n          <div className=\"selection-info\">\n            <div className=\"selection-header\">\n              <span className=\"selection-icon\">✂️</span>\n              <span>Selected Text ({selectedText.length} characters)</span>\n            </div>\n            <div className=\"selection-preview\">\n              {selectedText.substring(0, 200)}\n              {selectedText.length > 200 && '...'}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Right Side - System Authors Sidebar */}\n      {showAuthorsSidebar && (\n        <div className=\"authors-sidebar\">\n          {authors && authors.length > 0 && (\n            <ArticleMetadataPanel\n              articleId={articleId}\n              authors={authors}\n              skipApiCall={true}\n              alwaysExpanded={true}\n              zipFiles={articleData?.zipFiles || []}\n              onZipModified={onZipModified}\n              onQueryCreated={() => {\n                console.log('Query created for article:', articleId);\n              }}\n            />\n          )}\n        </div>\n      )}\n    </div>\n    </div>\n  );\n};\n\nexport default DocumentPreview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,KAAK,QAAQ,WAAW;AACjC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,gBAAgB;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,SAAS;EAAEC,aAAa;EAAEC;AAAsB,CAAC,KAAK;EAAAC,EAAA;EACxI,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMkC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiC,mBAAmB;IAAEC;EAAY,CAAC,GAAGhC,UAAU,CAAC,CAAC;EACzD,MAAM;IAAEiC;EAAQ,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAE7BJ,SAAS,CAAC,MAAM;IACd,IAAIW,IAAI,EAAE;MACR0B,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC1B,IAAI,CAAC,CAAC;;EAEV;EACAX,SAAS,CAAC,MAAM;IACd,MAAMsC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACJ,SAAS;MAC1EP,kBAAkB,CAACO,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAEDC,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAC/C,OAAO,MAAME,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCb,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFoB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEpC,IAAI,CAACqC,IAAI,CAAC;MACnDF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEpC,IAAI,CAAC;MACjCmC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAACpC,IAAI,CAACsC,QAAQ,CAAC;MAChDH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOpC,IAAI,CAACsC,QAAQ,CAAC;MAEnD,IAAI,CAACtC,IAAI,CAACsC,QAAQ,EAAE;QAClB,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;MACrD;MAEA,MAAMC,WAAW,GAAG,MAAMxC,IAAI,CAACsC,QAAQ,CAACG,KAAK,CAAC,aAAa,CAAC;MAC5DN,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,WAAW,CAACE,UAAU,CAAC;MAEhE,IAAI1C,IAAI,CAAC2C,IAAI,KAAK,MAAM,IAAI3C,IAAI,CAAC2C,IAAI,KAAK,KAAK,EAAE;QAC/C,IAAI;UACF,MAAMC,MAAM,GAAG,MAAMrD,OAAO,CAACsD,aAAa,CAAC;YAAEL;UAAY,CAAC,CAAC;UAC3D7B,UAAU,CAACiC,MAAM,CAACE,KAAK,CAAC;UACxBvB,mBAAmB,CAACqB,MAAM,CAACE,KAAK,CAAC;;UAEjC;UACA,IAAIF,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACG,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACjDb,OAAO,CAACc,IAAI,CAAC,8BAA8B,EAAEL,MAAM,CAACG,QAAQ,CAAC;UAC/D;QACF,CAAC,CAAC,OAAOG,QAAQ,EAAE;UACjBf,OAAO,CAACrB,KAAK,CAAC,4BAA4B,EAAEoC,QAAQ,CAAC;UACrD;UACA,IAAI;YACF,MAAMC,IAAI,GAAG,IAAIC,WAAW,CAAC,OAAO,EAAE;cAAEC,SAAS,EAAE;YAAK,CAAC,CAAC,CAACC,MAAM,CAACd,WAAW,CAAC;YAC9E,MAAMe,SAAS,GAAGJ,IAAI,CAACK,OAAO,CAAC,wCAAwC,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;YACnF,IAAIF,SAAS,CAACP,MAAM,GAAG,GAAG,EAAE;cAC1BrC,UAAU,CAAE,QAAO4C,SAAU,QAAO,CAAC;cACrChC,mBAAmB,CAACgC,SAAS,CAAC;cAC9BpB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD,CAAC,MAAM;cACL,MAAM,IAAIG,KAAK,CAAC,sCAAsC,CAAC;YACzD;UACF,CAAC,CAAC,OAAOmB,aAAa,EAAE;YACtB,MAAM,IAAInB,KAAK,CAAE,kBAAiBvC,IAAI,CAAC2C,IAAI,CAACgB,WAAW,CAAC,CAAE,qDAAoD,CAAC;UACjH;QACF;MACF,CAAC,MAAM,IAAI3D,IAAI,CAAC2C,IAAI,KAAK,KAAK,EAAE;QAC9B,MAAMQ,IAAI,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACE,MAAM,CAACd,WAAW,CAAC;QAClD7B,UAAU,CAAE,QAAOwC,IAAK,QAAO,CAAC;QAChC5B,mBAAmB,CAAC4B,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLpC,QAAQ,CAAE,0BAAyBf,IAAI,CAAC2C,IAAI,CAACgB,WAAW,CAAC,CAAE,wCAAuC,CAAC;MACrG;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZzB,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAE8C,GAAG,CAAC;MACjDzB,OAAO,CAACrB,KAAK,CAAC,gBAAgB,EAAE8C,GAAG,CAACC,OAAO,CAAC;MAC5C1B,OAAO,CAACrB,KAAK,CAAC,uBAAuB,EAAEd,IAAI,CAAC;MAE5C,IAAI8D,YAAY,GAAG,6BAA6B;MAChD,IAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;QACpCD,YAAY,GAAG,uFAAuF;MACxG,CAAC,MAAM,IAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxCD,YAAY,GAAG,qEAAqE;MACtF;MAEA/C,QAAQ,CAAC+C,YAAY,CAAC;IACxB,CAAC,SAAS;MACRjD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,SAAS,GAAGpC,MAAM,CAACqC,YAAY,CAAC,CAAC;IACvC,MAAMf,IAAI,GAAGc,SAAS,CAACE,QAAQ,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IACxCtB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;MAAEe,IAAI;MAAEH,MAAM,EAAEG,IAAI,CAACH;IAAO,CAAC,CAAC;IAC7E/B,eAAe,CAACkC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMiB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIpD,YAAY,EAAE;MAChB;MACA,MAAMqD,aAAa,GAAGxC,MAAM,CAACyC,QAAQ,CAACC,MAAM,CAACR,QAAQ,CAAC,YAAY,CAAC;MACnE,MAAMS,YAAY,GAAG3C,MAAM,CAACyC,QAAQ,CAACG,QAAQ,CAACV,QAAQ,CAAC,QAAQ,CAAC;MAChE,MAAMW,eAAe,GAAGC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,MAAM;MACzE,MAAMC,eAAe,GAAG9C,QAAQ,CAAC+C,QAAQ,CAACf,QAAQ,CAAC,QAAQ,CAAC;;MAE5D;MACA,MAAMgB,aAAa,GAAGtD,OAAO,IAAI4C,aAAa,IAAIG,YAAY,IAAIE,eAAe,IAAIG,eAAe,IAC/EhD,MAAM,CAACyC,QAAQ,CAACU,IAAI,CAACjB,QAAQ,CAAC,OAAO,CAAC;MAAI;MAC1ChC,QAAQ,CAAC+C,QAAQ,CAACf,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAM;;MAE/D,MAAMkB,YAAY,GAAGF,aAAa,GAAG,gBAAgB,GAAG,UAAU;MAElEzD,QAAQ,CAAC2D,YAAY,EAAE;QACrBC,KAAK,EAAE;UACLC,UAAU,EAAEnE,YAAY;UACxBV,SAAS,EAAEkB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAElB,SAAS;UACjC8E,gBAAgB,EAAE,IAAI;UACtBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAEP,aAAa;UAAE;UAC/B5E,KAAK,EAAEA,KAAK,CAAC;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEL,OAAA;IAAKyF,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAEzC1F,OAAA;MAAKyF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B1F,OAAA;QAAKyF,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC1F,OAAA;UAAQ2F,OAAO,EAAExF,MAAO;UAACsF,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC9C1F,OAAA,CAACJ,KAAK,CAACgG,eAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACThG,OAAA;UAAKyF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1F,OAAA;YAAIyF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC3B1F,OAAA;cAAMyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAExF,IAAI,CAAC+F;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC7C9F,IAAI,CAACqC,IAAI;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACLhG,OAAA;YAAGyF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAC5BxF,IAAI,CAAC2C,IAAI,CAACgB,WAAW,CAAC,CAAC,EAAC,0BAC3B;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhG,OAAA;QAAKyF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9B1F,OAAA,CAACF,gBAAgB;UACfoG,SAAS,EAAC,SAAS;UACnB1F,SAAS,EAAEA,SAAU;UACrBF,OAAO,EAAEA,OAAQ;UACjB6F,UAAU,EAAC,aAAa;UACxBC,UAAU,EAAC,cAAI;UACfC,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,OAAO;UACZC,WAAW,EAAGC,EAAE,IAAK;YACnBnE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkE,EAAE,CAAC;YAC1C;YACA,IAAI9F,qBAAqB,EAAE;cACzBA,qBAAqB,CAAC8F,EAAE,CAAC;YAC3B;UACF;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDzF,OAAO,IAAIA,OAAO,CAAC2C,MAAM,GAAG,CAAC,iBAC5BlD,OAAA;UACE2F,OAAO,EAAEA,CAAA,KAAMtE,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;UAC1DqE,SAAS,EAAC,uBAAuB;UACjCgB,KAAK,EAAErF,kBAAkB,GAAG,cAAc,GAAG,cAAe;UAAAsE,QAAA,eAE5D1F,OAAA,CAACJ,KAAK,CAAC8G,YAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACT,EAGAW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC7G,OAAA;UAAK8G,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAM,CAAE;UAAAvB,QAAA,GAAC,YACrD,EAACxE,YAAY,GAAI,IAAGA,YAAY,CAACgG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAE,MAAK,GAAG,MAAM;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CACN,EAEA9E,YAAY,iBACXlB,OAAA;UAAQ2F,OAAO,EAAErB,mBAAoB;UAACmB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACvE1F,OAAA,CAACJ,KAAK,CAACuH,cAAc;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAE1B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhG,OAAA;MAAKyF,SAAS,EAAG,wBAAuB,CAACrE,kBAAkB,GAAG,gBAAgB,GAAG,EAAG,EAAE;MAAAsE,QAAA,gBAEpF1F,OAAA;QAAKyF,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GACvC5E,SAAS,iBACRd,OAAA;UAAKyF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B1F,OAAA;YAAKyF,SAAS,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvChG,OAAA;YAAA0F,QAAA,EAAG;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACN,EAEAhF,KAAK,iBACJhB,OAAA;UAAKyF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1F,OAAA,CAACJ,KAAK,CAACwH,eAAe;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzBhG,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAA0F,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BhG,OAAA;cAAA0F,QAAA,EAAI1E;YAAK;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACbhF,KAAK,CAACiD,QAAQ,CAAC,MAAM,CAAC,iBACrBjE,OAAA;cAAKyF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAC,eAC7B,eAAA1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yHAC1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAAClF,SAAS,IAAI,CAACE,KAAK,IAAIJ,OAAO,iBAC9BZ,OAAA;UAAKyF,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxC1F,OAAA;YACEyF,SAAS,EAAC,iBAAiB;YAC3B4B,uBAAuB,EAAE;cAAEC,MAAM,EAAE1G;YAAQ,CAAE;YAC7C2G,SAAS,EAAErD,mBAAoB;YAC/BsD,OAAO,EAAEtD;UAAoB;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDhG,OAAA;UAAKyF,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC1F,OAAA;YAAKyF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B1F,OAAA;cAAMyF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7ChG,OAAA;cAAA0F,QAAA,EAAM;YAAwC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNhG,OAAA;YAAKyF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B1F,OAAA;cAAMyF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5ChG,OAAA;cAAA0F,QAAA,EAAM;YAAoE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL9E,YAAY,iBACXlB,OAAA;UAAKyF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1F,OAAA;YAAKyF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B1F,OAAA;cAAMyF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1ChG,OAAA;cAAA0F,QAAA,GAAM,iBAAe,EAACxE,YAAY,CAACgC,MAAM,EAAC,cAAY;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNhG,OAAA;YAAKyF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/BxE,YAAY,CAACgG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAC9BhG,YAAY,CAACgC,MAAM,GAAG,GAAG,IAAI,KAAK;UAAA;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL5E,kBAAkB,iBACjBpB,OAAA;QAAKyF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BnF,OAAO,IAAIA,OAAO,CAAC2C,MAAM,GAAG,CAAC,iBAC5BlD,OAAA,CAACH,oBAAoB;UACnBW,SAAS,EAAEA,SAAU;UACrBD,OAAO,EAAEA,OAAQ;UACjBkH,WAAW,EAAE,IAAK;UAClBC,cAAc,EAAE,IAAK;UACrBC,QAAQ,EAAE,CAAAjG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiG,QAAQ,KAAI,EAAG;UACtClH,aAAa,EAAEA,aAAc;UAC7BmH,cAAc,EAAEA,CAAA,KAAM;YACpBvF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE9B,SAAS,CAAC;UACtD;QAAE;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACrF,EAAA,CAzRIV,eAAe;EAAA,QAOFT,WAAW,EACiBE,UAAU,EACnCC,OAAO;AAAA;AAAAkI,EAAA,GATvB5H,eAAe;AA2RrB,eAAeA,eAAe;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}