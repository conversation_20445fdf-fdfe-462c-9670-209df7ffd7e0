import React, { useState } from 'react';
import { useZipQueue } from '../../context/ZipQueueContext';
import FolderUploader from './FolderUploader';
import ZipQueueDashboard from './ZipQueueDashboard';
import './FolderZipWorkflow.css';

const FolderZipWorkflow = () => {
  const { currentFolder, addZipQueue } = useZipQueue();
  const [currentView, setCurrentView] = useState(currentFolder ? 'dashboard' : 'upload');

  const handleFolderProcessed = (folderData) => {
    addZipQueue(folderData);
    setCurrentView('dashboard');
  };

  const handleBackToUpload = () => {
    setCurrentView('upload');
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'upload':
        return (
          <FolderUploader onFolderProcessed={handleFolderProcessed} />
        );
      
      case 'dashboard':
        return (
          <ZipQueueDashboard onBack={handleBackToUpload} />
        );
      
      default:
        return (
          <FolderUploader onFolderProcessed={handleFolderProcessed} />
        );
    }
  };

  return (
    <div className="folder-zip-workflow">
      {renderCurrentView()}
    </div>
  );
};

export default FolderZipWorkflow;
