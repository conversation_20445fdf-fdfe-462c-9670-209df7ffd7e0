/* Base Button Styles */
.raise-query-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-family: inherit;
}

.raise-query-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Icon */
.button-icon {
  font-size: 1.1em;
  line-height: 1;
}

.button-text {
  line-height: 1;
}

/* Size Variants */
.raise-query-button--small {
  padding: 0.4rem 0.8rem;
  font-size: 0.85rem;
}

.raise-query-button--medium {
  padding: 0.5rem 1rem;
  font-size: 0.95rem;
}

.raise-query-button--large {
  padding: 0.85rem 2rem;
  font-size: 1rem;
}

/* Color Variants */

/* Primary (Red/Pink - for validation/urgent queries) */
.raise-query-button--primary {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a6f 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.raise-query-button--primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.raise-query-button--primary:hover::before {
  left: 100%;
}

.raise-query-button--primary:hover {
  background: linear-gradient(135deg, #ee5a6f 0%, #fa5252 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
}

.raise-query-button--primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* Secondary (Blue - for general queries) */
.raise-query-button--secondary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.raise-query-button--secondary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.raise-query-button--secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Warning (Yellow/Orange - for author queries) */
.raise-query-button--warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.raise-query-button--warning:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
}

.raise-query-button--warning:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* Danger (Dark Red - for critical queries) */
.raise-query-button--danger {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.raise-query-button--danger:hover {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
}

.raise-query-button--danger:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .raise-query-button--large {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
  }
  
  .raise-query-button--medium {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .raise-query-button--small {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }
}

