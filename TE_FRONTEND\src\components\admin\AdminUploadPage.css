.admin-upload-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.upload-page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.upload-page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.upload-page-description {
  color: #6b7280;
  font-size: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.upload-mode-selector {
  margin-bottom: 2rem;
}

.mode-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.mode-option:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.mode-option.active {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
}

.mode-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.mode-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.mode-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.mode-option.active .mode-content h3 {
  color: #1d4ed8;
}

.mode-option.active .mode-content p {
  color: #3b82f6;
}

.upload-page-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-upload-page {
    padding: 1rem;
  }
  
  .upload-page-content {
    padding: 1rem;
  }
  
  .upload-page-title {
    font-size: 1.5rem;
  }
}
