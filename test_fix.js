// Test script to verify the fix for term.replace error
const { createReferenceObjects } = require('./TE_FRONTEND/src/services/helpers/arrayHelpers.js');

// Test data that would cause the original error
const testDataWithObjects = [
  { term: "1. <PERSON>, <PERSON><PERSON>. A study on cancer research. Nature. 2023;123:45-50." },
  { term: "2. <PERSON>, Wilson C. Machine learning applications. Science. 2022;456:78-90." },
  { term: "3. <PERSON>, <PERSON>. Climate change effects. Cell. 2021;789:12-25." }
];

const testDataWithStrings = [
  "1. <PERSON>, <PERSON>e A. A study on cancer research. Nature. 2023;123:45-50.",
  "2. <PERSON>, Wilson C. Machine learning applications. Science. 2022;456:78-90.",
  "3. <PERSON>, <PERSON>. Climate change effects. Cell. 2021;789:12-25."
];

console.log("Testing createReferenceObjects with object input:");
try {
  const result1 = createReferenceObjects(testDataWithObjects);
  console.log("✅ Success with object input:", result1.length, "references processed");
} catch (error) {
  console.log("❌ Error with object input:", error.message);
}

console.log("\nTesting createReferenceObjects with string input:");
try {
  const result2 = createReferenceObjects(testDataWithStrings);
  console.log("✅ Success with string input:", result2.length, "references processed");
} catch (error) {
  console.log("❌ Error with string input:", error.message);
}
