# GitHub Actions Playwright Automation Setup

## 🎯 Overview

This setup allows you to run Playwright automation tests via GitHub Actions with:
- ✅ **System date** - Automatically uses today's date (no manual date entry)
- ✅ **Sequential workflow** - Download → Wait → Upload → Email
- ✅ **Batch selection** - Upload specific batch folders
- ✅ **Email notifications** - Sends Drive <NAME_EMAIL>
- ✅ **Manual triggers** with custom status/mode parameters
- ✅ **Scheduled runs** (daily at 9 AM UTC / 2:30 PM IST)
- ✅ **No local machine needed** - Runs on GitHub's servers
- ✅ **No AWS costs** - Uses GitHub's free infrastructure
- ✅ **Centralized logs** - All runs tracked in GitHub
- ✅ **Artifact storage** - Downloaded files stored for 7 days

---

## 📋 Prerequisites

1. **GitHub Repository** with your code
2. **GitHub Secrets** configured (see below)
3. **Playwright tests** in `tests/` directory

---

## 🔐 Step 1: Configure GitHub Secrets

Go to your GitHub repository → **Settings** → **Secrets and variables** → **Actions** → **New repository secret**

Add the following secrets:

### Required Secrets:

| Secret Name | Description | Example Value |
|-------------|-------------|---------------|
| `TE_USERNAME` | TE Portal username | `<EMAIL>` |
| `TE_PASSWORD` | TE Portal password | `Editing@1234` |
| `GOOGLE_SHEETS_ID` | Google Sheets spreadsheet ID | `1sdERmtt3I6QDV3Xtw1NzJswQ0MhrbiGg6mf5mTiYlSU` |
| `GOOGLE_SHEETS_PAGE` | Sheet page reference | `TESTAUTOMATION!A2` |
| `GOOGLE_SHEETS_SERVICE_ACCOUNT` | Service account email | `115083737441987478188` |
| `GOOGLE_SHEETS_PRIVATE_KEY` | Private key for Google Sheets | `-----BEGIN PRIVATE KEY-----\n***` |
| `GOOGLE_DRIVE_SERVICE_ACCOUNT_JSON` | Full service account JSON file | `{"type": "service_account", ***}` |
| `SMTP_SERVER` | SMTP server for email | `smtpout.secureserver.net` |
| `SMTP_PORT` | SMTP port | `465` |
| `SMTP_USERNAME` | Email username | `<EMAIL>` |
| `SMTP_PASSWORD` | Email password | `your-password` |

### How to Add Secrets:

```bash
# 1. Go to GitHub repository
# 2. Click Settings → Secrets and variables → Actions
# 3. Click "New repository secret"
# 4. Enter name and value
# 5. Click "Add secret"
```

**Important Notes:**

1. **For `GOOGLE_SHEETS_PRIVATE_KEY`**: Copy the entire private key including:
   ```
   -----BEGIN PRIVATE KEY-----
   MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC***
   -----END PRIVATE KEY-----
   ```

2. **For `GOOGLE_DRIVE_SERVICE_ACCOUNT_JSON`**: Copy the entire contents of your `service_account.json` file:
   ```json
   {
     "type": "service_account",
     "project_id": "your-project",
     "private_key_id": "***",
     "private_key": "-----BEGIN PRIVATE KEY-----\n***",
     "client_email": "***",
     "client_id": "***",
     "auth_uri": "https://accounts.google.com/o/oauth2/auth",
     "token_uri": "https://oauth2.googleapis.com/token",
     "auth_provider_x509_cert_url": "***",
     "client_x509_cert_url": "***"
   }
   ```
   **Tip**: Use `cat service_account.json | jq -c` to get a compact single-line JSON (or just copy the entire file content)

---

## 🚀 Step 2: How to Run the Workflow

### **Option A: Manual Trigger (Recommended)**

1. Go to your GitHub repository
2. Click **Actions** tab
3. Select **"TE Playwright Automation"** workflow
4. Click **"Run workflow"** button
5. Fill in the parameters:

   **Workflow Type** (choose one):
   - **`download`** - Only download articles (no upload)
   - **`upload`** - Only upload a specific batch folder
   - **`download_and_upload`** - Download, then upload (full workflow) ⭐ **Recommended**

   **Article Status** (for download):
   - `Yet-to-Start` ⭐ **Default**
   - `In-Progress`
   - `Completed`

   **Download Mode** (for download):
   - `normal` ⭐ **Default** - Download by date/status
   - `download_articles_by_ids` - Download specific article IDs

   **Article IDs** (optional, only for by-IDs mode):
   - Example: `ija_123_45, ija_456_78`

   **Batch Folder** (optional, only for upload-only workflow):
   - Example: `21-12-2025/batch-1766297173961-1`
   - Leave empty for download_and_upload (uses latest batch)

6. Click **"Run workflow"**

**Note:** The workflow automatically uses **today's date** (system date), so you don't need to enter a date manually!

### **Option B: Scheduled Runs**

The workflow is configured to run automatically:
- **Daily at 9:00 AM UTC** (2:30 PM IST)
- Uses default parameters:
  - Workflow Type: `download_and_upload` (full workflow)
  - Date: Today's date (system date)
  - Status: `Yet-to-Start`
  - Mode: `normal`

To change the schedule, edit `.github/workflows/playwright-automation.yml`:
```yaml
schedule:
  - cron: '0 9 * * *'  # Daily at 9 AM UTC
  # - cron: '0 0 * * 1'  # Weekly on Monday at midnight
  # - cron: '0 0 1 * *'  # Monthly on 1st at midnight
```

---

## 🔄 Workflow Types Explained

### **1. Download Only** (`download`)
- Downloads articles from TE portal
- Creates batch folder in `incoming/`
- **Does NOT upload** to Drive
- **Does NOT send email**
- **Use case:** Testing, or when you want to review articles before uploading

**Example:**
```
Workflow Type: download
Status: Yet-to-Start
Mode: normal
```

**Result:**
- ✅ Articles downloaded to `incoming/21-12-2025/batch-XXX/`
- ❌ No Drive upload
- ❌ No email sent

---

### **2. Upload Only** (`upload`)
- Uploads a **specific batch folder** to Drive
- Sends email with Drive link
- **Does NOT download** new articles
- **Use case:** Re-uploading a batch, or uploading after manual download

**Example:**
```
Workflow Type: upload
Batch Folder: 21-12-2025/batch-1766297173961-1
```

**Result:**
- ❌ No download
- ✅ Uploads specified batch to Drive
- ✅ Email <NAME_EMAIL> with Drive link

---

### **3. Download and Upload** (`download_and_upload`) ⭐ **RECOMMENDED**
- **Full automated workflow**
- Downloads articles → Waits 10 seconds → Uploads to Drive → Sends email
- **Use case:** Daily automation, complete hands-off operation

**Example:**
```
Workflow Type: download_and_upload
Status: Yet-to-Start
Mode: normal
```

**Result:**
- ✅ Articles downloaded to `incoming/21-12-2025/batch-XXX/`
- ⏳ Waits 10 seconds
- ✅ Uploads latest batch to Drive
- ✅ Email <NAME_EMAIL> with Drive link

**Workflow Steps:**
1. Download articles from TE portal
2. Create batch folder with metadata
3. Wait 10 seconds (ensures files are ready)
4. Upload batch to Google Drive
5. Extract Drive folder link
6. Send email <NAME_EMAIL>
7. Generate summary report

---

## 📧 Email Notification

After successful upload, an email is automatically sent to **<EMAIL>** with:

**Email Contents:**
- ✅ Batch name and date
- ✅ Number of articles
- ✅ **Clickable Drive link** (button + text link)
- ✅ Next steps for processing
- ✅ Batch folder path
- ✅ Link to workflow run details

**Email Preview:**
```
Subject: ✅ TE Articles Uploaded - batch-1766297173961-1

Batch Information:
- Batch Name: batch-1766297173961-1
- Date: 21-12-2025
- Articles: 10 files
- Status: Yet-to-Start

Google Drive Link:
[📂 Open Drive Folder] (clickable button)

Next Steps:
1. Download the articles from the Drive folder
2. Process the batch in the TE Frontend
3. Assign articles to Technical Editors
```

---

## 📊 Step 3: Monitor the Workflow

### **View Running Workflow:**

1. Go to **Actions** tab
2. Click on the running workflow
3. Click on the job name (`playwright-automation`)
4. Expand steps to see real-time logs

### **Check Results:**

After the workflow completes:

1. **Summary**: View the summary at the top of the job page
   - Shows date, status, mode
   - Shows number of articles downloaded

2. **Artifacts**: Download the results
   - Click on **"automation-results-XXX"** artifact
   - Downloads a ZIP file with:
     - `incoming/` - Downloaded article ZIPs
     - `playwright-report/` - Test reports
     - `test-results/` - Test screenshots/videos

3. **Logs**: View detailed logs for each step
   - Download logs
   - Upload logs
   - Error messages (if any)

---

## 🔧 Step 4: Workflow Configuration

### **File Location:**
`.github/workflows/playwright-automation.yml`

### **Key Features:**

1. **Manual Trigger with Parameters:**
   ```yaml
   workflow_dispatch:
     inputs:
       date:
         description: 'Target Date (DD-MM-YYYY)'
         required: true
         default: '21-12-2025'
   ```

2. **Scheduled Trigger:**
   ```yaml
   schedule:
     - cron: '0 9 * * *'  # Daily at 9 AM UTC
   ```

3. **Environment Setup:**
   - Installs Node.js 20
   - Installs Playwright with Chromium
   - Creates `.env` file with secrets

4. **Artifact Upload:**
   - Stores results for 7 days
   - Can be downloaded from Actions tab

---

## 📝 Step 5: Customization

### **Change Timeout:**

Default: 90 minutes (1.5 hours)

```yaml
jobs:
  playwright-automation:
    timeout-minutes: 120  # Change to 2 hours
```

### **Add Email Notifications:**

Add this step at the end:

```yaml
- name: 📧 Send Email Notification
  if: always()
  uses: dawidd6/action-send-mail@v3
  with:
    server_address: smtp.gmail.com
    server_port: 465
    username: ${{ secrets.EMAIL_USERNAME }}
    password: ${{ secrets.EMAIL_PASSWORD }}
    subject: Playwright Automation ${{ job.status }}
    body: |
      Workflow: ${{ github.workflow }}
      Status: ${{ job.status }}
      Date: ${{ env.TARGET_DATE }}
      Articles: Check artifacts
    to: <EMAIL>
```

### **Add Slack Notifications:**

```yaml
- name: 📢 Send Slack Notification
  if: always()
  uses: slackapi/slack-github-action@v1
  with:
    webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
    payload: |
      {
        "text": "Playwright Automation ${{ job.status }}",
        "blocks": [
          {
            "type": "section",
            "text": {
              "type": "mrkdwn",
              "text": "*Status:* ${{ job.status }}\n*Date:* ${{ env.TARGET_DATE }}"
            }
          }
        ]
      }
```

---

## 🐛 Troubleshooting

### **Issue: Workflow fails with "Secrets not found"**

**Solution:**
- Check that all secrets are added in GitHub Settings
- Secret names must match exactly (case-sensitive)
- Re-add the secret if needed

### **Issue: Playwright browser installation fails**

**Solution:**
- The workflow uses `--with-deps` flag to install dependencies
- If it still fails, add this step before running tests:
  ```yaml
  - name: Install System Dependencies
    run: |
      sudo apt-get update
      sudo apt-get install -y libgbm1 libxkbcommon0 libgtk-3-0
  ```

### **Issue: Download fails with "No articles found"**

**Solution:**
- Check the date format (DD-MM-YYYY)
- Verify the status matches articles in TE portal
- Check TE portal credentials in secrets

### **Issue: Timeout after 90 minutes**

**Solution:**
- Increase `timeout-minutes` in the workflow file
- Or split into separate download/upload jobs

---

## 📈 Benefits vs Other Solutions

| Feature | GitHub Actions | Local Server | AWS EC2 |
|---------|---------------|--------------|---------|
| **Cost** | ✅ Free (2000 min/month) | ✅ Free | ❌ $10-50/month |
| **Setup** | ✅ Easy (5 minutes) | ⚠️ Medium | ❌ Complex |
| **Scheduling** | ✅ Built-in | ❌ Manual | ✅ Cron jobs |
| **Logs** | ✅ Centralized | ❌ Local only | ⚠️ CloudWatch |
| **Notifications** | ✅ Email/Slack | ❌ None | ⚠️ SNS setup |
| **Maintenance** | ✅ None | ❌ Manual updates | ❌ Server management |
| **Scalability** | ✅ Auto-scales | ❌ Limited | ⚠️ Manual scaling |

---

## 🎯 Next Steps

1. ✅ Add GitHub Secrets (Step 1)
2. ✅ Commit the workflow file to your repository
3. ✅ Test manual trigger (Step 2)
4. ✅ Check results and artifacts (Step 3)
5. ✅ Configure schedule if needed (Step 4)
6. ✅ Add notifications (Step 5)

---

## 📚 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Playwright CI Documentation](https://playwright.dev/docs/ci)
- [Cron Expression Generator](https://crontab.guru/)
- [GitHub Actions Marketplace](https://github.com/marketplace?type=actions)

