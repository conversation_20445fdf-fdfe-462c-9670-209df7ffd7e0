import logging
from flask import Flask, request, jsonify, session
from flask_cors import CORS
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
import docx
import unicodedata
import os
import time
import re
import requests
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv  # Load environment variables from .env
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.exc import IntegrityError
from sqlalchemy import or_, func, text
import secrets
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

# Load environment variables with explicit path
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '.env'))

# Verify critical environment variables are loaded
required_env_vars = ['GOOGLE_DRIVE_PARENT_FOLDER_ID', 'GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE']
missing_vars = [var for var in required_env_vars if not os.getenv(var)]
if missing_vars:
    print(f"Warning: Missing environment variables: {missing_vars}")
    print("TE assignment features may not work properly.")
else:
    print("All required environment variables loaded successfully")

# Initialize Flask app and CORS
app = Flask(__name__)

# Configure CORS with explicit settings for production
# IMPORTANT: Must specify exact origin for credentials to work
ALLOWED_ORIGINS = [
    "http://te-frontend-app.s3-website.ap-south-1.amazonaws.com",
    "http://localhost:3000",  # For local development & Word Plugin
    "http://127.0.0.1:3000",
    "http://localhost:5173",  # Vite default port
    "http://127.0.0.1:5173",  # Vite default port
    "http://localhost:5174",  # Vite alternate port
    "http://127.0.0.1:5174",  # Vite alternate port
    "https://localhost:3000",  # Word Plugin with HTTPS
    "https://127.0.0.1:3000"   # Word Plugin with HTTPS
]

CORS(app,
     supports_credentials=True,
     resources={
         r"/*": {  # Apply to ALL routes, not just /api/*
             "origins": ALLOWED_ORIGINS,
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization", "Access-Control-Allow-Origin"],
             "expose_headers": ["Content-Type"],
             "max_age": 3600,
             "supports_credentials": True
         }
     })

# Add after_request handler to ensure CORS headers on all responses
@app.after_request
def after_request(response):
    origin = request.headers.get('Origin')
    # Always add CORS headers if origin is in allowed list
    if origin in ALLOWED_ORIGINS:
        response.headers['Access-Control-Allow-Origin'] = origin
        response.headers['Access-Control-Allow-Credentials'] = 'true'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Access-Control-Allow-Origin'
        response.headers['Access-Control-Expose-Headers'] = 'Content-Type'
    # For debugging: log when origin is not in allowed list
    elif origin:
        print(f"[CORS] Origin not in allowed list: {origin}")
        print(f"[CORS] Allowed origins: {ALLOWED_ORIGINS}")
    return response

# Configure session management
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', secrets.token_hex(32))
app.config['SESSION_COOKIE_HTTPONLY'] = False  # Allow JavaScript access for debugging
app.config['SESSION_COOKIE_SECURE'] = False  # Must be False for HTTP (not HTTPS)
app.config['SESSION_COOKIE_SAMESITE'] = None  # CRITICAL: Remove SameSite for cross-origin cookies
app.config['SESSION_COOKIE_NAME'] = 'te_automation_session'
app.config['SESSION_COOKIE_DOMAIN'] = None  # Allow cookie on any domain
app.config['SESSION_COOKIE_PATH'] = '/'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500 MB

# --- Database Setup ---
# Use SQLite for simplicity
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Check for PostgreSQL configuration first, fallback to SQLite
DATABASE_URL = os.getenv('DATABASE_URL')
if DATABASE_URL:
    app.config['SQLALCHEMY_DATABASE_URI'] = DATABASE_URL
else:
    # Use SQLite for development and production
    DB_PATH = os.path.join(BASE_DIR, 'article_references.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{DB_PATH}'

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize SQLAlchemy
db = SQLAlchemy(app)

# Import and register blueprints (after db initialization)
from routes.word_plugin_routes import init_word_plugin_routes

# Define comprehensive models inline to avoid import conflicts
class User(db.Model):
    """Stores all user accounts: Coordinator, Technical Editor (TE), Copy Editor (CE), Admin"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.Enum('SuperAdmin', 'Admin', 'Coordinator', 'TE', 'CE', name='user_roles'), nullable=False)
    email = db.Column(db.String(255), unique=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'email': self.email,
            'isActive': self.is_active,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'lastLogin': self.last_login.isoformat() if self.last_login else None
        }

class ArticleFile(db.Model):
    """Master record for every article zip processed by the system"""
    __tablename__ = 'article_files'

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    journal_code = db.Column(db.String(50), index=True)
    journal_name = db.Column(db.String(255))
    status = db.Column(db.Enum('new', 'processing', 'processed', 'assigned_TE', 'assigned_CE', 'completed', name='article_status'), default='new', index=True)
    current_stage = db.Column(db.String(50))
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'), index=True)
    folder_path = db.Column(db.Text)
    priority = db.Column(db.Enum('HIGH', 'MEDIUM', 'LOW', name='priority_levels'), default='MEDIUM')
    deadline = db.Column(db.DateTime)
    original_filename = db.Column(db.String(255))
    file_size = db.Column(db.Integer)
    file_hash = db.Column(db.String(64))  # SHA-256 hash
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    article_metadata = db.Column(db.JSON)  # Extra data from PE portal JSON

    def to_dict(self):
        return {
            'id': self.id,
            'articleId': self.article_id,
            'journalCode': self.journal_code,
            'journalName': self.journal_name,
            'status': self.status,
            'currentStage': self.current_stage,
            'assignedTo': self.assigned_to,
            'priority': self.priority,
            'deadline': self.deadline.isoformat() if self.deadline else None,
            'originalFilename': self.original_filename,
            'fileSize': self.file_size,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'metadata': self.article_metadata
        }

class ArticleReference(db.Model):
    """Stores raw & processed reference list per article"""
    __tablename__ = 'article_references'

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('article_files.id'), nullable=False, index=True)
    reference_text = db.Column(db.JSON)  # Raw list from manuscript
    processed_references = db.Column(db.JSON)  # Cleaned/standardized references
    processing_metadata = db.Column(db.JSON)  # Session details, validation logs
    total_quality_score = db.Column(db.Float)  # Average quality across refs (0–1)
    source_distribution = db.Column(db.JSON)  # { "pubmed_found": 10, "crossref_found": 8, "not_found": 2 }

    # Analytics summary fields
    total_references = db.Column(db.Integer, default=0)  # Count of individual references
    high_confidence_count = db.Column(db.Integer, default=0)  # quality_score >= 0.8
    medium_confidence_count = db.Column(db.Integer, default=0)  # quality_score 0.5-0.79
    low_confidence_count = db.Column(db.Integer, default=0)  # quality_score < 0.5
    needs_review_count = db.Column(db.Integer, default=0)  # Flagged for review

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    processed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    processing_source = db.Column(db.String(50))  # e.g., "zip_workflow", "manual"

    def to_dict(self):
        return {
            'id': self.id,
            'articleId': self.article_id,
            'referenceText': self.reference_text,
            'processedReferences': self.processed_references,
            'processingMetadata': self.processing_metadata,
            'totalQualityScore': self.total_quality_score,
            'sourceDistribution': self.source_distribution,
            'totalReferences': self.total_references,
            'highConfidenceCount': self.high_confidence_count,
            'mediumConfidenceCount': self.medium_confidence_count,
            'lowConfidenceCount': self.low_confidence_count,
            'needsReviewCount': self.needs_review_count,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'processedBy': self.processed_by,
            'processingSource': self.processing_source
        }

class JournalAbbreviation(db.Model):
    """Stores all journal abbreviations (manual or generated)"""
    __tablename__ = 'journal_abbreviations'

    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(500), nullable=False, index=True)
    abbreviation = db.Column(db.String(200), nullable=False)
    alternate_names = db.Column(db.JSON)  # Other variations
    source = db.Column(db.String(50), default='manual')
    confidence_score = db.Column(db.Float, default=1.0)
    usage_count = db.Column(db.Integer, default=0)
    is_verified = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    __table_args__ = (
        db.UniqueConstraint('full_name', name='uq_journal_full_name'),
        db.Index('idx_journal_full_name_lower', func.lower(full_name)),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'fullName': self.full_name,
            'abbreviation': self.abbreviation,
            'alternateNames': self.alternate_names,
            'source': self.source,
            'confidenceScore': self.confidence_score,
            'usageCount': self.usage_count,
            'isVerified': self.is_verified,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'createdBy': self.created_by
        }

class TEAssignment(db.Model):
    """Tracks TE assignments via Google Drive uploads"""
    __tablename__ = 'te_assignments'

    id = db.Column(db.Integer, primary_key=True)
    batch_name = db.Column(db.String(100), nullable=False, unique=True, index=True)  # batch-001, batch-002, etc.
    drive_folder_id = db.Column(db.String(255))  # Google Drive folder ID
    drive_folder_link = db.Column(db.String(500))  # Shareable Drive link
    drive_file_id = db.Column(db.String(255))  # Google Drive file ID for ZIP
    drive_file_link = db.Column(db.String(500))  # Shareable Drive file link
    zip_ids = db.Column(db.JSON, nullable=False)  # Array of ZIP IDs from ZipQueue
    article_ids = db.Column(db.JSON, nullable=False)  # Array of article IDs assigned
    zip_file_paths = db.Column(db.JSON)  # Array of actual ZIP file paths on server
    assigned_te_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)  # TE user assigned to
    assigned_te_email = db.Column(db.String(255), nullable=False)  # TE email for reference
    assignment_status = db.Column(db.Enum('pending', 'uploading', 'uploaded', 'emailed', 'completed', name='assignment_status'), default='pending', index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    uploaded_at = db.Column(db.DateTime)  # When uploaded to Drive
    email_sent_at = db.Column(db.DateTime)  # When email sent to TE
    assigned_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # Admin/Coordinator who made assignment
    notes = db.Column(db.Text)  # Optional notes about the assignment

    # Relationships
    assigned_te = db.relationship('User', foreign_keys=[assigned_te_id], backref='te_assignments_received')
    assigned_by_user = db.relationship('User', foreign_keys=[assigned_by], backref='te_assignments_created')

    def to_dict(self):
        return {
            'id': self.id,
            'batchName': self.batch_name,
            'driveFolderId': self.drive_folder_id,
            'driveFolderLink': self.drive_folder_link,
            'zipIds': self.zip_ids,
            'articleIds': self.article_ids,
            'assignedTeId': self.assigned_te_id,
            'assignedTeEmail': self.assigned_te_email,
            'assignmentStatus': self.assignment_status,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'uploadedAt': self.uploaded_at.isoformat() if self.uploaded_at else None,
            'emailSentAt': self.email_sent_at.isoformat() if self.email_sent_at else None,
            'assignedBy': self.assigned_by,
            'notes': self.notes,
            'assignedTeName': self.assigned_te.username if self.assigned_te else None,
            'assignedByName': self.assigned_by_user.username if self.assigned_by_user else None
        }

class AuthorQuery(db.Model):
    """Stores author-related queries raised during article processing"""
    __tablename__ = 'author_queries'

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.String(50), nullable=False, index=True)  # Article reference
    author_name = db.Column(db.String(255), nullable=False)  # Queried author name
    query_type = db.Column(db.Enum('copyright_no', 'missing_in_system', 'missing_in_manuscript', 'validation_error', name='query_types'), nullable=False)
    query_text = db.Column(db.Text, nullable=False)  # Generated query text
    raised_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # User who raised the query
    raised_on = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)  # Timestamp
    batch_id = db.Column(db.String(100), nullable=True, index=True)  # Linked to TE batch if exists
    status = db.Column(db.Enum('open', 'closed', 'sent', name='query_status'), default='open', nullable=False)
    email_sent = db.Column(db.Boolean, default=False, nullable=False)  # Whether email was sent
    validation_data = db.Column(db.Text, nullable=True)  # JSON data for validation errors

    # Relationships
    raised_by_user = db.relationship('User', foreign_keys=[raised_by], backref='author_queries_raised')

    def to_dict(self):
        return {
            'id': self.id,
            'articleId': self.article_id,
            'authorName': self.author_name,
            'queryType': self.query_type,
            'queryText': self.query_text,
            'raisedBy': self.raised_by,
            'raisedOn': self.raised_on.isoformat() if self.raised_on else None,
            'batchId': self.batch_id,
            'status': self.status,
            'raisedByName': self.raised_by_user.username if self.raised_by_user else None
        }

# For backward compatibility, create aliases for the old model names
AdminUser = User  # Keep AdminUser alias for existing code
ArticleReferences = ArticleReference  # Keep ArticleReferences alias

UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Configure Logging
logging.basicConfig(
    filename='app.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# ===== AUTHOR QUERY TEMPLATES =====
AUTHOR_QUERY_TEMPLATES = {
    'copyright_no': "The author(s) {names} has/have not agreed to the copyright terms and conditions which were sent on the author's email address.",
    'missing_in_system': "The following author(s) {names} are missing in the system, which prevents verification of the copyright status. Kindly provide necessary details (email ID, affiliations) so we can update the system and proceed with sending the copyright email.",
    'missing_in_manuscript': "Author name {names} given in copyright information provided through mail (system). However, author name(s) is missing in author list provided in manuscript."
}

def create_queries_file(batch_id, queries_data, raised_by_name):
    """Create or append to queries.txt file for a batch"""
    import os

    # Create queries directory if it doesn't exist
    queries_dir = os.path.join(os.getcwd(), 'queries')
    os.makedirs(queries_dir, exist_ok=True)

    # Determine filename based on batch_id format
    if batch_id:
        # Handle both full batch names (batch-20251019-134204-922) and just IDs (20251019-134204-922)
        if batch_id.startswith('batch-'):
            filename = f"{batch_id}_queries.txt"
        else:
            filename = f"batch-{batch_id}_queries.txt"
    else:
        # Use article_id for temporary files
        article_id = queries_data[0]['article_id'] if queries_data else 'unknown'
        filename = f"{article_id}_queries.txt"

    filepath = os.path.join(queries_dir, filename)

    # Check if file exists to determine if we need header
    file_exists = os.path.exists(filepath)

    with open(filepath, 'a', encoding='utf-8') as f:
        if not file_exists:
            # Write header for new file
            f.write("=" * 50 + "\n")
            f.write("EDITINK AUTHOR QUERIES REPORT\n")
            if batch_id:
                display_batch_id = batch_id if batch_id.startswith('batch-') else f"batch-{batch_id}"
                f.write(f"Batch ID: {display_batch_id}\n")
            f.write(f"Generated On: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Raised By: {raised_by_name}\n")
            f.write("=" * 50 + "\n\n")

        # Write queries
        for query in queries_data:
            f.write(f"[Article ID: {query['article_id']}]\n")
            f.write(f"Query Type: {query['query_type'].replace('_', ' ').title()}\n")
            f.write(f"Author(s): {query['author_name']}\n")
            f.write("Query:\n")
            f.write(f"{query['query_text']}\n")
            f.write("-" * 50 + "\n\n")

    return filepath


# ===== OPENAI INTEGRATION =====


# ===== REGISTER BLUEPRINTS =====
# Register Word Plugin routes after all models are defined
word_plugin_blueprint = init_word_plugin_routes(db, User, ArticleFile, ArticleReference, TEAssignment)
app.register_blueprint(word_plugin_blueprint)


# ===== AUTHENTICATION ENDPOINTS =====

@app.route('/api/auth/login', methods=['POST'])
def user_login():
    try:
        print(f"[LOGIN] Starting login process at {datetime.utcnow()}")

        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        print(f"[LOGIN] Login attempt for username: {username}")

        if not username or not password:
            print("[LOGIN] Missing username or password")
            return jsonify({'error': 'Username and password required'}), 400

        print("[LOGIN] Querying database for user...")
        user = User.query.filter_by(username=username, is_active=True).first()

        if not user:
            print(f"[LOGIN] User not found: {username}")
            return jsonify({'error': 'Invalid credentials'}), 401

        print(f"[LOGIN] User found: {user.username}, role: {user.role}")

        if user and user.check_password(password):
            print("[LOGIN] Password check successful")
            print("[LOGIN] Setting session data...")

            # Store user info in session with role-based access
            session['user_id'] = user.id
            session['username'] = user.username
            session['user_role'] = user.role
            session.permanent = True

            print(f"[LOGIN] Session data set: user_id={user.id}, role={user.role}")

            # Backward compatibility for admin endpoints
            if user.role in ['SuperAdmin', 'Admin']:
                session['admin_id'] = user.id
                session['admin_username'] = user.username
                print(f"[LOGIN] Admin session set: admin_id={user.id}")

            print("[LOGIN] Updating last login...")
            # Update last login
            user.last_login = datetime.utcnow()
            db.session.commit()
            print("[LOGIN] Database commit successful")

            print("[LOGIN] Preparing response...")
            response_data = {
                'message': 'Login successful',
                'user': user.to_dict()
            }
            print(f"[LOGIN] Login successful for {username}")

            # Create response with explicit headers to ensure cookie is set
            response = jsonify(response_data)
            response.status_code = 200

            # Get the origin from request
            origin = request.headers.get('Origin', '')
            print(f"[LOGIN] Request Origin: {origin}")

            # List of allowed origins
            allowed_origins = [
                'http://te-frontend-app.s3-website.ap-south-1.amazonaws.com',
                'http://localhost:3000',
                'http://127.0.0.1:3000'
            ]

            # Set CORS headers - must match exact origin for credentials
            if origin in allowed_origins:
                response.headers['Access-Control-Allow-Origin'] = origin
                response.headers['Access-Control-Allow-Credentials'] = 'true'
                print(f"[LOGIN] CORS headers set for origin: {origin}")
            else:
                print(f"[LOGIN] WARNING: Origin not in allowed list: {origin}")
                # Still set headers but log warning
                response.headers['Access-Control-Allow-Origin'] = origin if origin else '*'
                response.headers['Access-Control-Allow-Credentials'] = 'true'

            print(f"[LOGIN] Session cookie should be set: te_automation_session")

            return response
        else:
            print("[LOGIN] Password check failed")
            return jsonify({'error': 'Invalid credentials'}), 401

    except Exception as e:
        print(f"[LOGIN] ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': 'Login failed due to server error'}), 500

@app.route('/api/auth/logout', methods=['POST'])
def user_logout():
    session.clear()
    return jsonify({'message': 'Logout successful'}), 200

@app.route('/api/auth/check', methods=['GET'])
def check_auth():
    # Check for new user session first
    if 'user_id' in session:
        user = User.query.get(session['user_id'])
        if user and user.is_active:
            return jsonify({
                'authenticated': True,
                'user': user.to_dict()
            }), 200

    # Backward compatibility check for admin session
    elif 'admin_id' in session:
        user = User.query.get(session['admin_id'])
        if user and user.is_active:
            return jsonify({
                'authenticated': True,
                'user': user.to_dict()
            }), 200

    return jsonify({'authenticated': False}), 200

@app.route('/api/debug/session', methods=['GET'])
def debug_session():
    """Debug endpoint to check session contents and cookies"""
    return jsonify({
        'session_keys': list(session.keys()),
        'session_data': {
            'user_id': session.get('user_id'),
            'admin_id': session.get('admin_id'),
            'username': session.get('username'),
            'user_role': session.get('user_role')
        },
        'cookies_received': list(request.cookies.keys()),
        'has_session_cookie': 'te_automation_session' in request.cookies,
        'remote_addr': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', 'Unknown'),
        'origin': request.headers.get('Origin', 'Not set'),
        'referer': request.headers.get('Referer', 'Not set')
    }), 200

# Helper function to check user permissions
def check_user_permission(required_roles=None):
    """Check if current user has required role permissions"""
    # Check if session has user_id or admin_id
    if 'user_id' not in session and 'admin_id' not in session:
        logging.debug(f"[AUTH] No user_id or admin_id in session. Session keys: {list(session.keys())}")
        return False, None

    user_id = session.get('user_id') or session.get('admin_id')
    user = User.query.get(user_id)

    if not user:
        logging.warning(f"[AUTH] User not found for user_id: {user_id}")
        return False, None

    if not user.is_active:
        logging.warning(f"[AUTH] User {user.username} (ID: {user_id}) is not active")
        return False, None

    if required_roles and user.role not in required_roles:
        logging.warning(f"[AUTH] User {user.username} has role '{user.role}', required: {required_roles}")
        return False, user

    logging.debug(f"[AUTH] Permission granted for user {user.username} (Role: {user.role})")
    return True, user

# ===== JOURNAL SEARCH ENDPOINTS =====

@app.route('/api/journal/search', methods=['GET'])
def search_journal():
    """Search for journal abbreviation in database"""
    try:
        journal_name = request.args.get('name', '').strip()

        if not journal_name:
            return jsonify({'success': False, 'error': 'Journal name is required'}), 400

        start_time = time.time()

        # Check if input is already an abbreviation (short form)
        if len(journal_name) <= 50 and '.' in journal_name:
            # Likely already an abbreviation, return as-is
            search_time = int((time.time() - start_time) * 1000)
            return jsonify({
                'success': True,
                'data': {
                    'abbreviation': journal_name,
                    'source': 'input_abbreviation',
                    'confidence': 1.0,
                    'searchTimeMs': search_time,
                    'message': 'Input appears to be an abbreviation'
                }
            })

        # Search in database using fuzzy matching
        # First try exact match
        journal = JournalAbbreviation.query.filter(
            func.lower(JournalAbbreviation.full_name) == func.lower(journal_name)
        ).first()

        # If no exact match, try partial match
        if not journal:
            journal = JournalAbbreviation.query.filter(
                JournalAbbreviation.full_name.ilike(f'%{journal_name}%')
            ).order_by(JournalAbbreviation.confidence_score.desc()).first()

        if journal:
            # Update usage count
            journal.usage_count += 1
            db.session.commit()

            search_time = int((time.time() - start_time) * 1000)

            return jsonify({
                'success': True,
                'data': {
                    'abbreviation': journal.abbreviation,
                    'source': 'database',
                    'confidence': journal.confidence_score,
                    'searchTimeMs': search_time
                }
            })

        # If not found in database, return indication to use GenAI
        search_time = int((time.time() - start_time) * 1000)

        return jsonify({
            'success': True,
            'data': {
                'abbreviation': None,
                'source': 'not_found',
                'searchTimeMs': search_time,
                'message': 'Not found in database, use GenAI fallback'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500



@app.route('/api/dashboard/analytics', methods=['GET'])
def get_detailed_analytics():
    """Get detailed analytics for admin dashboard"""
    # Check authentication - Admin, Coordinator, or SuperAdmin can view analytics
    has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
    if not has_permission:
        logging.warning(f"[DASHBOARD_ANALYTICS] Authentication failed")
        return jsonify({'error': 'Authentication required'}), 401

    logging.info(f"[DASHBOARD_ANALYTICS] User authenticated: {user.username} (Role: {user.role})")

    try:
        # Enhanced Processing trends using new metadata (last 30 days)
        processing_trends = db.session.execute(text("""
            SELECT
                DATE(created_at) as date,
                COUNT(*) as articles_processed,
                AVG(COALESCE(total_references, 1)) as avg_references_per_article,
                SUM(CASE WHEN processing_source = 'zip_workflow' THEN 1 ELSE 0 END) as zip_workflow_count,
                SUM(CASE WHEN processing_source = 'manual_entry' THEN 1 ELSE 0 END) as manual_entry_count
            FROM article_references
            WHERE created_at >= DATE('now', '-30 days')
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        """)).fetchall()

        # Enhanced Reference Quality Metrics using new metadata
        reference_quality_metrics = db.session.execute(text("""
            SELECT
                SUM(CASE
                    WHEN processing_metadata IS NOT NULL AND processing_metadata != '' THEN
                        CAST(JSON_EXTRACT(processing_metadata, '$.statistics.pubmedCount') AS INTEGER)
                    ELSE 0
                END) as total_pubmed_found,
                SUM(CASE
                    WHEN processing_metadata IS NOT NULL AND processing_metadata != '' THEN
                        CAST(JSON_EXTRACT(processing_metadata, '$.statistics.crossrefCount') AS INTEGER)
                    ELSE 0
                END) as total_crossref_found,
                SUM(CASE
                    WHEN processing_metadata IS NOT NULL AND processing_metadata != '' THEN
                        CAST(JSON_EXTRACT(processing_metadata, '$.statistics.notFoundCount') AS INTEGER)
                    ELSE 0
                END) as total_not_found,
                AVG(CASE
                    WHEN processing_metadata IS NOT NULL AND processing_metadata != '' THEN
                        CAST(JSON_EXTRACT(processing_metadata, '$.statistics.averageQuality') AS REAL)
                    ELSE 0
                END) as avg_quality_score,
                COUNT(*) as total_articles_with_metadata
            FROM article_references
            WHERE processing_metadata IS NOT NULL AND processing_metadata != ''
        """)).fetchone()

        # Quality Distribution from dedicated analytics fields (more reliable than JSON_EXTRACT)
        quality_distribution = db.session.execute(text("""
            SELECT
                COALESCE(SUM(high_confidence_count), 0) as high_quality,
                COALESCE(SUM(medium_confidence_count), 0) as medium_quality,
                COALESCE(SUM(low_confidence_count), 0) as low_quality
            FROM article_references
            WHERE total_references IS NOT NULL
        """)).fetchone()

        # Journal quality metrics (keeping original for journal data)
        journal_quality_metrics = db.session.execute(text("""
            SELECT
                CASE
                    WHEN confidence_score >= 0.9 THEN 'High'
                    WHEN confidence_score >= 0.7 THEN 'Medium'
                    ELSE 'Low'
                END as quality_tier,
                COUNT(*) as count,
                AVG(usage_count) as avg_usage
            FROM journal_abbreviations
            GROUP BY quality_tier
            ORDER BY
                CASE quality_tier
                    WHEN 'High' THEN 1
                    WHEN 'Medium' THEN 2
                    ELSE 3
                END
        """)).fetchall()

        # System performance metrics
        performance_metrics = {
            'avgProcessingTime': None,  # Could be added with timing data
            'errorRate': 0,  # Could be tracked with error logging
            'systemUptime': None,  # Could be tracked with deployment timestamps
            'databaseSize': {
                'journals': JournalAbbreviation.query.count(),
                'articles': ArticleReferences.query.count(),
                'admins': AdminUser.query.count()
            }
        }

        # Recent activity summary - SQLite compatible
        recent_activity = db.session.execute(text("""
            SELECT
                'article_processed' as activity_type,
                article_id as item_id,
                created_at as timestamp
            FROM article_references
            WHERE created_at >= DATE('now', '-7 days')
            ORDER BY created_at DESC
            LIMIT 10
        """)).fetchall()

        return jsonify({
            'processingTrends': [
                {
                    'date': row[0] if isinstance(row[0], str) else (row[0].isoformat() if row[0] else None),
                    'articlesProcessed': row[1],
                    'avgReferencesPerArticle': float(row[2]) if row[2] else 0,
                    'zipWorkflowCount': row[3] if len(row) > 3 else 0,
                    'manualEntryCount': row[4] if len(row) > 4 else 0
                }
                for row in processing_trends
            ],
            'referenceQualityMetrics': {
                'totalPubmedFound': reference_quality_metrics[0] if reference_quality_metrics else 0,
                'totalCrossrefFound': reference_quality_metrics[1] if reference_quality_metrics else 0,
                'totalNotFound': reference_quality_metrics[2] if reference_quality_metrics else 0,
                'avgQualityScore': float(reference_quality_metrics[3]) if reference_quality_metrics and reference_quality_metrics[3] else 0,
                'totalArticlesWithMetadata': reference_quality_metrics[4] if reference_quality_metrics else 0
            },
            'qualityDistribution': {
                'highQuality': quality_distribution[0] if quality_distribution else 0,
                'mediumQuality': quality_distribution[1] if quality_distribution else 0,
                'lowQuality': quality_distribution[2] if quality_distribution else 0
            },
            'journalQualityMetrics': [
                {
                    'qualityTier': row[0],
                    'count': row[1],
                    'avgUsage': float(row[2]) if row[2] else 0
                }
                for row in journal_quality_metrics
            ],
            'performance': performance_metrics,
            'recentActivity': [
                {
                    'type': row[0],
                    'itemId': row[1],
                    'timestamp': row[2] if isinstance(row[2], str) else (row[2].isoformat() if row[2] else None)
                }
                for row in recent_activity
            ]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/statistics', methods=['GET'])
def get_simplified_statistics():
    """Get simplified statistics for admin statistics page - only essential metrics"""
    # Check authentication - Admin, Coordinator, or SuperAdmin can view statistics
    has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
    if not has_permission:
        logging.warning(f"[ADMIN_STATISTICS] Authentication failed")
        return jsonify({'error': 'Authentication required'}), 401

    logging.info(f"[ADMIN_STATISTICS] User authenticated: {user.username} (Role: {user.role})")

    try:
        # ===== REFERENCES ANALYTICS =====

        # 1. Quality Breakdown - get counts from analytics fields
        quality_stats = db.session.execute(text("""
            SELECT
                COALESCE(SUM(high_confidence_count), 0) as high_confidence,
                COALESCE(SUM(medium_confidence_count), 0) as medium_confidence,
                COALESCE(SUM(low_confidence_count), 0) as low_confidence
            FROM article_references
        """)).fetchone()

        # Calculate needs_review efficiently using database aggregation
        needs_review_refs = 0

        # Use analytics fields instead of processing JSON for performance
        needs_review_stats = db.session.execute(text("""
            SELECT COALESCE(SUM(needs_review_count), 0) as total_needs_review
            FROM article_references
        """)).fetchone()

        needs_review_refs = needs_review_stats[0] or 0

        # Skip the complex JSON processing loop for performance
        # Complex JSON processing removed for performance - using database aggregation instead

        # 2. Source Breakdown - use analytics fields for performance
        source_breakdown_stats = db.session.execute(text("""
            SELECT
                COALESCE(SUM(pubmed_count), 0) as pubmed_found,
                COALESCE(SUM(crossref_count), 0) as crossref_found,
                COALESCE(SUM(not_found_count), 0) as not_found
            FROM article_references
        """)).fetchone()

        # Use pre-calculated values
        pubmed_found = source_breakdown_stats[0] or 0
        crossref_found = source_breakdown_stats[1] or 0
        not_found = source_breakdown_stats[2] or 0

        # JSON parsing loop removed for performance - using pre-calculated values above

        # ===== JOURNALS ANALYTICS =====

        # 3. Journal Counts - database vs manual
        journal_stats = db.session.execute(text("""
            SELECT
                COUNT(CASE WHEN source != 'manual' THEN 1 END) as database_journals,
                COUNT(CASE WHEN source = 'manual' THEN 1 END) as manual_journals
            FROM journal_abbreviations
        """)).fetchone()

        # ===== RETURN SIMPLIFIED ANALYTICS =====

        return jsonify({
            # REFERENCES ANALYTICS
            'references': {
                # 1. Quality Breakdown
                'quality_breakdown': {
                    'high_confidence': quality_stats[0] if quality_stats else 0,
                    'medium_confidence': quality_stats[1] if quality_stats else 0,
                    'low_confidence': quality_stats[2] if quality_stats else 0,
                    'needs_review': needs_review_refs
                },

                # 2. Review Status (comparison/ratio)
                'review_status': {
                    'total_needs_review': needs_review_refs,
                    'total_high_confidence': quality_stats[0] if quality_stats else 0,
                    'review_ratio': round((needs_review_refs / max(quality_stats[0] if quality_stats else 1, 1)) * 100, 2) if quality_stats and quality_stats[0] > 0 else 0
                },

                # 3. Source Breakdown
                'source_breakdown': {
                    'pubmed_found': pubmed_found,
                    'crossref_found': crossref_found,
                    'not_found': not_found,
                    'total_references': pubmed_found + crossref_found + not_found
                }
            },

            # JOURNALS ANALYTICS
            'journals': {
                'database_journals': journal_stats[0] if journal_stats else 0,
                'manual_journals': journal_stats[1] if journal_stats else 0
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/articles', methods=['GET'])
def get_articles_list():
    # Check authentication - Admin, Coordinator, or SuperAdmin can view articles
    has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
    if not has_permission:
        logging.warning(f"[DASHBOARD_ARTICLES] Authentication failed - Session: user_id={session.get('user_id')}, admin_id={session.get('admin_id')}")
        return jsonify({'error': 'Authentication required'}), 401

    logging.info(f"[DASHBOARD_ARTICLES] User authenticated: {user.username} (ID: {user.id}, Role: {user.role})")
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)  # Limit to max 100 to prevent crashes
        search = request.args.get('search', '', type=str)
        sort_by = request.args.get('sort_by', 'created_at', type=str)
        sort_order = request.args.get('sort_order', 'desc', type=str)
        date_start = request.args.get('date_start', '', type=str)
        date_end = request.args.get('date_end', '', type=str)
        min_refs = request.args.get('min_refs', '', type=str)
        max_refs = request.args.get('max_refs', '', type=str)

        # Join with ArticleFile to get the actual article identifiers and additional metadata
        query = db.session.query(
            ArticleReferences,
            ArticleFile.article_id.label('actual_article_id'),
            ArticleFile.status.label('status'),
            ArticleFile.current_stage.label('current_stage'),
            ArticleFile.priority.label('priority'),
            ArticleFile.journal_name.label('journal_name')
        ).join(
            ArticleFile, ArticleReferences.article_id == ArticleFile.id
        )

        # Apply search filter - search in the actual article ID from ArticleFile
        if search:
            query = query.filter(ArticleFile.article_id.contains(search))
        # Apply date range filter
        if date_start:
            try:
                start_date = datetime.strptime(date_start, '%Y-%m-%d').date()
                query = query.filter(ArticleReferences.created_at >= start_date)
            except ValueError:
                pass

        if date_end:
            try:
                end_date = datetime.strptime(date_end, '%Y-%m-%d').date()
                query = query.filter(ArticleReferences.created_at <= end_date)
            except ValueError:
                pass

        # Apply reference count filters
        if min_refs:
            try:
                min_refs_int = int(min_refs)
                query = query.filter(ArticleReferences.total_references >= min_refs_int)
            except ValueError:
                pass

        if max_refs:
            try:
                max_refs_int = int(max_refs)
                query = query.filter(ArticleReferences.total_references <= max_refs_int)
            except ValueError:
                pass
        # Apply sorting
        if sort_by == 'created_at':
            if sort_order == 'asc':
                query = query.order_by(ArticleReferences.created_at.asc())
            else:
                query = query.order_by(ArticleReferences.created_at.desc())
        elif sort_by == 'total_references':
            if sort_order == 'asc':
                query = query.order_by(ArticleReferences.total_references.asc())
            else:
                query = query.order_by(ArticleReferences.total_references.desc())

        # Paginate results
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        articles = []
        for item in paginated.items:
            # item is a tuple: (ArticleReferences, actual_article_id, status, current_stage, priority, journal_name)
            article = item[0]  # ArticleReferences object
            actual_article_id = item[1]  # The real article ID from ArticleFile
            status = item[2]
            current_stage = item[3]
            priority = item[4]
            journal_name = item[5]

            # Calculate quality metrics
            total_refs = article.total_references or 0
            high_conf = article.high_confidence_count or 0
            needs_review = article.needs_review_count or 0

            success_rate = round((high_conf / total_refs * 100) if total_refs > 0 else 0, 1)
            review_rate = round((needs_review / total_refs * 100) if total_refs > 0 else 0, 1)

            # Parse source distribution
            source_breakdown = {'pubmed': 0, 'crossref': 0, 'not_found': 0}
            if article.source_distribution:
                try:
                    if isinstance(article.source_distribution, str):
                        first_parse = json.loads(article.source_distribution)
                        if isinstance(first_parse, str):
                            source_data = json.loads(first_parse)
                        else:
                            source_data = first_parse
                    else:
                        source_data = article.source_distribution

                    if isinstance(source_data, dict):
                        source_breakdown['pubmed'] = source_data.get('pubmed_found', source_data.get('PubMed', 0))
                        source_breakdown['crossref'] = source_data.get('crossref_found', source_data.get('CrossRef', 0))
                        source_breakdown['not_found'] = source_data.get('not_found', source_data.get('NotFound', 0))
                except Exception:
                    pass

            articles.append({
                'id': article.id,
                'article_id': actual_article_id,  # Use the actual article ID (e.g., 'ija_995_25')
                'articleId': actual_article_id,  # Frontend expects this field name
                'total_references': total_refs,
                'referenceCount': total_refs,  # Frontend expects this field name
                'high_confidence_count': high_conf,
                'medium_confidence_count': article.medium_confidence_count or 0,
                'low_confidence_count': article.low_confidence_count or 0,
                'needs_review_count': needs_review,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'createdAt': article.created_at.isoformat() if article.created_at else None,  # Frontend expects this field name
                'updated_at': article.updated_at.isoformat() if article.updated_at else None,

                # Enhanced fields
                'status': status,
                'currentStage': current_stage,
                'priority': priority,
                'journalName': journal_name,
                'processedBy': article.processed_by,
                'processingSource': article.processing_source,
                'totalQualityScore': round(article.total_quality_score * 100, 1) if article.total_quality_score else 0,
                'successRate': success_rate,
                'reviewRate': review_rate,
                'sourceBreakdown': source_breakdown
            })

        return jsonify({
            'articles': articles,
            'pagination': {
                'page': paginated.page,
                'pages': paginated.pages,
                'per_page': paginated.per_page,
                'total': paginated.total,
                'has_next': paginated.has_next,
                'has_prev': paginated.has_prev
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
# File upload endpoint
@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No file selected for uploading"}), 400

    if file and file.filename.endswith('.docx'):
        filename = secure_filename(file.filename)
        timestamp = int(time.time())  # Avoid overwriting
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        try:
            file.save(filepath)
            references = extract_references(filepath)
            return jsonify({"references": references})

        except Exception as e:
            logging.error(f"Error processing file {filename}: {e}")
            return jsonify({"error": "Internal server error"}), 500

    return jsonify({"error": "Invalid file format. Only .docx files are allowed"}), 400
def extract_references(filepath):
    """Extract references from a DOCX file"""
    try:
        doc = Document(filepath)
        references = []

        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text and (text[0].isdigit() or text.startswith('[')):
                references.append(text)

        return references if references else ["No references found"]
    except Exception as e:
        logging.error(f"Error extracting references: {e}")
        return []

def get_reference_details(reference_text):
    """Get details for a reference using OpenAI API"""
    try:
        client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        prompt = f"""
        Analyze this reference and extract the following information:
        Reference: "{reference_text}"

        Please provide:
        1. Journal name (full name)
        2. Journal abbreviation (if different from full name)
        3. Authors
        4. Title
        5. Year
        6. Volume/Issue/Pages

        Format as JSON with keys: journal_full, journal_abbrev, authors, title, year, volume_info
        """

        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=500,
            temperature=0.3
        )

        result = response.choices[0].message.content.strip()

        try:
            return json.loads(result)
        except json.JSONDecodeError:
            return {"error": "Failed to extract details", "response": result}

    except Exception as e:
        return {"error": "Error interacting with OpenAI API", "details": str(e)}

@app.route('/api/save-references', methods=['POST'])
def save_references():
    if 'file' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No file selected for uploading"}), 400

    if file and file.filename.endswith('.docx'):
        filename = secure_filename(file.filename)
        timestamp = int(time.time())  # Avoid overwriting
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        try:
            file.save(filepath)
            references = extract_references(filepath)

            if not references:
                return jsonify({"error": "No references found"}), 400

            return jsonify({"references": references})

        except Exception as e:
            logging.error(f"Error processing file {filename}: {e}")
            return jsonify({"error": "Internal server error"}), 500

    return jsonify({"error": "Only .docx files are allowed"}), 400

def extract_references(filepath):
    try:
        doc = docx.Document(filepath)
        references = []
        is_reference_section = False

        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()

            if not is_reference_section:
            # Detect the start of the References section (handling variations)
                if "references" in text.lower() or "bibliography" in text.lower() or "references:" in text.lower():
                    print("References Section:", text)  # Debug print
                    is_reference_section = True
                    continue

            # Stop processing if another major section starts
            if is_reference_section:
                if any(text.lower().startswith(kw) for kw in ["figure", "tables", "acknowledgement"]):
                    print("Stopping at section:", text)
                    break
            # Add valid reference lines, cleaned of Unicode
                if text:
                    print("Valid reference:", text)  # Debug print
                    cleaned_text = unicodedata.normalize("NFKD", text)
                    references.append(cleaned_text)

        return references if references else ["No references found"]
    except Exception as e:
        logging.error(f"Error extracting references: {e}")
        return []


API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

def extract_details(content):
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }

    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "system", "content": "You are an assistant that extracts structured data from strings."},
            {"role": "user", "content": content},
        ],
        "max_tokens": 500,
    }

    try:
        response = requests.post(OPENAI_API_URL, headers=headers, json=payload)
        result = response.json()

        if response.status_code == 200 and "choices" in result and result["choices"]:
            content = result["choices"][0]["message"]["content"].strip()
            content = content.replace("```json", "").replace("```", "").strip()  # Clean extraneous markdown
            return json.loads(content)  # Convert string to JSON
        else:
            return {"error": "Failed to extract details", "response": result}

    except Exception as e:
        return {"error": "Error interacting with OpenAI API", "details": str(e)}

@app.route("/extract", methods=["POST"])
def extract():
    data = request.json

    extracted_data = extract_details(data["content"])  # Convert string function to callable
    return jsonify(extracted_data)

# ===== SECURE OPENAI PROXY ENDPOINTS =====
# These endpoints act as a secure proxy to OpenAI API
# API key is stored on backend only, never exposed to frontend

@app.route("/api/openai/extract-details", methods=["POST"])
def openai_extract_details():
    """
    Secure proxy for OpenAI API - Extract structured data from content
    Frontend sends content, backend handles OpenAI API call with secure API key

    Request body:
    {
        "content": "text to analyze",
        "prompt_type": "default|journal|reference|spell_check|grammar_check",
        "max_tokens": 500,
        "temperature": 0.3
    }
    """
    try:
        data = request.json
        content = data.get('content')
        prompt_type = data.get('prompt_type', 'default')

        if not content:
            return jsonify({"error": "Content is required"}), 400

        # Check if API key is configured
        if not API_KEY:
            logging.error("OPENAI_API_KEY not configured")
            return jsonify({"error": "OpenAI API not configured"}), 500

        # Build system prompt based on type
        system_prompts = {
            'default': "You are an assistant that extracts structured data from word files.",
            'journal': "You are an expert in academic journal abbreviations. Extract and standardize journal names.",
            'reference': "You are an expert reference formatter. Extract and structure bibliographic information.",
            'spell_check': "You are a spell checker. Identify spelling errors in academic text.",
            'grammar_check': "You are a grammar checker. Identify grammatical errors in academic text."
        }

        system_prompt = system_prompts.get(prompt_type, system_prompts['default'])

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}",
        }

        payload = {
            "model": "gpt-4o-mini",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": content},
            ],
            "max_tokens": data.get('max_tokens', 500),
            "temperature": data.get('temperature', 0.3)
        }

        response = requests.post(OPENAI_API_URL, headers=headers, json=payload)
        result = response.json()

        if response.status_code == 200 and "choices" in result and result["choices"]:
            content_result = result["choices"][0]["message"]["content"].strip()
            # Clean markdown formatting
            content_result = content_result.replace("```json", "").replace("```", "").strip()

            # Try to parse as JSON, return as-is if not valid JSON
            try:
                parsed_result = json.loads(content_result)
                return jsonify({"success": True, "data": parsed_result})
            except json.JSONDecodeError:
                return jsonify({"success": True, "data": content_result})
        else:
            logging.error(f"OpenAI API error: {result}")
            return jsonify({"error": "Failed to process with OpenAI", "details": result}), 500

    except Exception as e:
        logging.error(f"Error in OpenAI proxy: {str(e)}")
        return jsonify({"error": "Internal server error", "details": str(e)}), 500

@app.route("/api/openai/chat", methods=["POST"])
def openai_chat():
    """
    Secure proxy for OpenAI Chat API
    Allows custom messages while keeping API key secure

    Request body:
    {
        "messages": [{"role": "user", "content": "..."}],
        "model": "gpt-4o-mini",
        "max_tokens": 500,
        "temperature": 0.3
    }
    """
    try:
        data = request.json
        messages = data.get('messages', [])
        model = data.get('model', 'gpt-4o-mini')
        max_tokens = data.get('max_tokens', 500)
        temperature = data.get('temperature', 0.3)

        if not messages:
            return jsonify({"error": "Messages are required"}), 400

        if not API_KEY:
            logging.error("OPENAI_API_KEY not configured")
            return jsonify({"error": "OpenAI API not configured"}), 500

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}",
        }

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        response = requests.post(OPENAI_API_URL, headers=headers, json=payload)
        result = response.json()

        if response.status_code == 200:
            return jsonify({"success": True, "data": result})
        else:
            logging.error(f"OpenAI API error: {result}")
            return jsonify({"error": "Failed to process with OpenAI", "details": result}), 500

    except Exception as e:
        logging.error(f"Error in OpenAI chat proxy: {str(e)}")
        return jsonify({"error": "Internal server error", "details": str(e)}), 500

# --- Reference Processing Utilities ---

def calculate_reference_statistics(references):
    """Calculate comprehensive statistics from processed references"""
    if not references:
        return {}

    # Fix source assignment for NOT_FOUND references
    for ref in references:
        if isinstance(ref, dict):
            ref_type = ref.get('type', '')
            ref_source = ref.get('source', '')

            # Fix NOT_FOUND references to have source='not_found' instead of 'manual'
            if ref_type == 'NOT_FOUND' and ref_source == 'manual':
                ref['source'] = 'not_found'
                # Keep needs_review=True as requested
                ref['needs_review'] = True

    stats = {
        'total_count': len(references),
        'pubmed_found': 0,
        'crossref_found': 0,
        'not_found': 0,
        'quality_scores': [],
        'high_quality': 0,  # score >= 85
        'medium_quality': 0,  # score 70-84
        'low_quality': 0,  # score < 70
        'needs_review': 0,
        'processing_sources': {},
        'confidence_levels': {'high': 0, 'medium': 0, 'low': 0}
    }

    for ref in references:
        # Count by source
        if hasattr(ref, 'type') or 'type' in ref:
            ref_type = ref.get('type', 'unknown') if isinstance(ref, dict) else getattr(ref, 'type', 'unknown')
            if 'pubmed' in ref_type.lower() or ref_type == 'FOUND':
                stats['pubmed_found'] += 1
            elif 'crossref' in ref_type.lower():
                stats['crossref_found'] += 1
            else:
                stats['not_found'] += 1

        # Quality scoring - use 0-1 scale to match our schema
        quality_score = ref.get('score', 0) if isinstance(ref, dict) else getattr(ref, 'score', 0)

        # Convert percentage scores to 0-1 scale if needed
        if quality_score > 1:
            quality_score = quality_score / 100.0

        if quality_score > 0:
            stats['quality_scores'].append(quality_score)
            if quality_score >= 0.8:  # High confidence >= 0.8
                stats['high_quality'] += 1
            elif quality_score >= 0.5:  # Medium confidence 0.5-0.79
                stats['medium_quality'] += 1
            else:  # Low confidence < 0.5
                stats['low_quality'] += 1

        # Confidence levels
        confidence = ref.get('confidence', 'medium') if isinstance(ref, dict) else getattr(ref, 'confidence', 'medium')
        if confidence in stats['confidence_levels']:
            stats['confidence_levels'][confidence] += 1

        # Needs review flag
        needs_review = ref.get('needs_review', False) if isinstance(ref, dict) else getattr(ref, 'needs_review', False)
        if needs_review:
            stats['needs_review'] += 1

    # Calculate averages
    if stats['quality_scores']:
        stats['average_quality'] = sum(stats['quality_scores']) / len(stats['quality_scores'])
        stats['min_quality'] = min(stats['quality_scores'])
        stats['max_quality'] = max(stats['quality_scores'])
    else:
        stats['average_quality'] = 0
        stats['min_quality'] = 0
        stats['max_quality'] = 0

    return stats

# --- Reference API Endpoints ---

# POST /api/references
@app.route('/api/references', methods=['POST'])
def save_processed_references():
    data = request.get_json()
    article_id = data.get('articleId')
    references = data.get('references')
    processing_metadata = data.get('processingMetadata', {})
    processed_by = data.get('processedBy', 'unknown')
    processing_source = data.get('processingSource', 'manual')

    if not article_id or references is None:
        return jsonify({'error': 'Missing articleId or references'}), 400

    try:
        # Handle article_id - ensure we have a corresponding article_files record
        article_file_record = None
        if isinstance(article_id, str):
            # Look for existing article_files record
            article_file_record = ArticleFile.query.filter_by(article_id=article_id).first()
            if not article_file_record:
                # Create new article_files record
                article_file_record = ArticleFile(
                    article_id=article_id,
                    status='processing',
                    current_stage='reference_processing',
                    created_at=datetime.utcnow()
                )
                db.session.add(article_file_record)
                db.session.flush()  # Get the ID
        else:
            # Assume it's already an integer ID
            article_file_record = ArticleFile.query.get(article_id)
            if not article_file_record:
                return jsonify({'error': f'Article file with ID {article_id} not found'}), 404

        # Use the article_files record ID for the foreign key
        article_file_id = article_file_record.id
        # Calculate comprehensive statistics from references
        stats = calculate_reference_statistics(references)

        # Enhance metadata with statistics and processing info
        enhanced_metadata = {
            **processing_metadata,
            'statistics': stats,
            'processed_at': datetime.utcnow().isoformat(),
            'total_references': len(references),
            'processing_session_id': data.get('sessionId', f"session_{int(datetime.utcnow().timestamp())}")
        }

        # Calculate analytics fields from statistics
        total_references = stats.get('total_count', len(references))
        high_confidence_count = stats.get('high_quality', 0)
        medium_confidence_count = stats.get('medium_quality', 0)
        low_confidence_count = stats.get('low_quality', 0)
        needs_review_count = stats.get('needs_review', 0)

        # Create simplified source distribution - only essential metrics
        source_distribution = {
            'pubmed_found': stats.get('pubmed_found', 0),
            'crossref_found': stats.get('crossref_found', 0),
            'not_found': stats.get('not_found', 0)
        }

        # Calculate total quality score (average) - already in 0-1 scale
        total_quality_score = stats.get('average_quality', 0)

        # Try to get existing record using the article_files ID
        record = ArticleReference.query.filter_by(article_id=article_file_id).first()
        if record:
            # Update existing record with all fields
            record.reference_text = json.dumps(references)
            record.processed_references = json.dumps(references)
            record.processing_metadata = json.dumps(enhanced_metadata)
            record.total_quality_score = total_quality_score
            record.source_distribution = json.dumps(source_distribution)
            record.total_references = total_references
            record.high_confidence_count = high_confidence_count
            record.medium_confidence_count = medium_confidence_count
            record.low_confidence_count = low_confidence_count
            record.needs_review_count = needs_review_count
            record.updated_at = datetime.utcnow()
            record.processed_by = processed_by
            record.processing_source = processing_source
        else:
            # Create new record with all fields
            record = ArticleReference(
                article_id=article_file_id,
                reference_text=json.dumps(references),
                processed_references=json.dumps(references),
                processing_metadata=json.dumps(enhanced_metadata),
                total_quality_score=total_quality_score,
                source_distribution=json.dumps(source_distribution),
                total_references=total_references,
                high_confidence_count=high_confidence_count,
                medium_confidence_count=medium_confidence_count,
                low_confidence_count=low_confidence_count,
                needs_review_count=needs_review_count,
                processed_by=processed_by,
                processing_source=processing_source
            )
            db.session.add(record)

        db.session.commit()
        return jsonify({
            'message': 'References saved with comprehensive analytics',
            'articleId': article_file_record.article_id,  # Return the original string ID
            'articleFileId': article_file_id,  # Also return the database ID
            'statistics': stats,
            'analytics': {
                'totalReferences': total_references,
                'highConfidence': high_confidence_count,
                'mediumConfidence': medium_confidence_count,
                'lowConfidence': low_confidence_count,
                'needsReview': needs_review_count,
                'sourceDistribution': source_distribution,
                'qualityScore': total_quality_score
            },
            'metadata': enhanced_metadata
        }), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# GET /api/references/<article_id>
@app.route('/api/references/<string:article_id>', methods=['GET'])
def get_references(article_id):
    # Find the article_files record first
    article_file = ArticleFile.query.filter_by(article_id=article_id).first()
    if not article_file:
        return jsonify({'error': 'Article not found'}), 404

    # Then find the article_references record using the file ID
    record = ArticleReference.query.filter_by(article_id=article_file.id).first()
    if not record:
        return jsonify({'error': 'References not found'}), 404

    # Return data in the format expected by the frontend
    response_data = record.to_dict()

    # Add the 'references' field that the frontend expects
    # Use processed_references if available, otherwise use reference_text
    if record.processed_references:
        if isinstance(record.processed_references, str):
            try:
                response_data['references'] = json.loads(record.processed_references)
            except:
                response_data['references'] = []
        else:
            response_data['references'] = record.processed_references
    elif record.reference_text:
        if isinstance(record.reference_text, str):
            try:
                response_data['references'] = json.loads(record.reference_text)
            except:
                response_data['references'] = []
        else:
            response_data['references'] = record.reference_text
    else:
        response_data['references'] = []

    return jsonify(response_data), 200

# GET /api/article-ids - Get all article IDs for autocomplete
@app.route('/api/article-ids', methods=['GET'])
def get_article_ids():
    """Get all article IDs for autocomplete functionality"""
    try:
        # Get search query parameter for filtering
        search = request.args.get('search', '').strip()
        limit = min(int(request.args.get('limit', 10)), 50)  # Max 50 results

        # Join with ArticleFile to get the actual article identifiers
        query = db.session.query(ArticleFile.article_id).join(
            ArticleReferences, ArticleFile.id == ArticleReferences.article_id
        )

        if search:
            # Filter by article IDs that contain the search term (case-insensitive)
            query = query.filter(ArticleFile.article_id.ilike(f'%{search}%'))

        # Order by article_id and limit results
        results = query.order_by(ArticleFile.article_id).limit(limit).all()

        article_ids = [result[0] for result in results]

        return jsonify({
            'articleIds': article_ids,
            'total': len(article_ids),
            'hasMore': len(article_ids) == limit
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# PUT /api/references/<string:article_id>
@app.route('/api/references/<string:article_id>', methods=['PUT'])
def update_references(article_id):
    data = request.get_json()
    references = data.get('references')
    if references is None:
        return jsonify({'error': 'Missing references'}), 400
    record = ArticleReferences.query.filter_by(article_id=article_id).first()
    if not record:
        return jsonify({'error': 'Not found'}), 404
    try:
        record.reference_text = json.dumps(references)
        record.updated_at = datetime.utcnow()
        db.session.commit()
        return jsonify({'message': 'References updated', 'articleId': article_id}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# ===== HEALTH CHECK ENDPOINT =====
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Test database connection
        db.session.execute(text('SELECT 1'))

        # Get basic stats
        journal_count = JournalAbbreviation.query.count()
        article_count = ArticleReferences.query.count()
        admin_count = AdminUser.query.count()

        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': datetime.utcnow().isoformat(),
            'stats': {
                'journals': journal_count,
                'articles': article_count,
                'admins': admin_count
            },
            'version': '1.0.0'
        }), 200

    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

# ===== JOURNAL ABBREVIATION CRUD ENDPOINTS =====

@app.route('/api/journals', methods=['GET'])
def get_journals():
    """
    Get all journal abbreviations with optional filtering and pagination
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '', type=str)
        source = request.args.get('source', '', type=str)
        verified_only = request.args.get('verified_only', False, type=bool)

        # Build query
        query = JournalAbbreviation.query

        # Apply filters
        if search:
            query = query.filter(
                or_(
                    JournalAbbreviation.full_name.ilike(f'%{search}%'),
                    JournalAbbreviation.abbreviation.ilike(f'%{search}%')
                )
            )

        if source:
            query = query.filter(JournalAbbreviation.source == source)

        if verified_only:
            query = query.filter(JournalAbbreviation.is_verified == True)

        # Order by usage count (most used first), then by name
        query = query.order_by(JournalAbbreviation.usage_count.desc(), JournalAbbreviation.full_name)

        # Paginate
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        journals = pagination.items

        return jsonify({
            'success': True,
            'data': [journal.to_dict() for journal in journals],
            'pagination': {
                'page': page,
                'pages': pagination.pages,
                'per_page': per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals', methods=['POST'])
def create_journal():
    """
    Create a new journal abbreviation
    """
    try:
        data = request.get_json()

        if not data or not data.get('fullName') or not data.get('abbreviation'):
            return jsonify({
                'success': False,
                'error': 'fullName and abbreviation are required'
            }), 400

        # Check if journal already exists
        existing = JournalAbbreviation.query.filter_by(full_name=data['fullName']).first()
        if existing:
            return jsonify({
                'success': False,
                'error': 'Journal with this name already exists'
            }), 409

        journal = JournalAbbreviation(
            full_name=data['fullName'],
            abbreviation=data['abbreviation'],
            source=data.get('source', 'manual'),
            confidence_score=data.get('confidenceScore', 1.0),
            is_verified=data.get('isVerified', False),
            created_by=data.get('createdBy', 'admin')
        )

        db.session.add(journal)
        db.session.commit()

        return jsonify({
            'success': True,
            'data': journal.to_dict(),
            'message': 'Journal abbreviation created successfully'
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals/<int:journal_id>', methods=['PUT'])
def update_journal(journal_id):
    """
    Update an existing journal abbreviation
    """
    try:
        journal = JournalAbbreviation.query.get_or_404(journal_id)
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Update fields
        if 'fullName' in data:
            # Check if new name conflicts with existing journal
            existing = JournalAbbreviation.query.filter(
                JournalAbbreviation.full_name == data['fullName'],
                JournalAbbreviation.id != journal_id
            ).first()
            if existing:
                return jsonify({
                    'success': False,
                    'error': 'Journal with this name already exists'
                }), 409
            journal.full_name = data['fullName']

        if 'abbreviation' in data:
            journal.abbreviation = data['abbreviation']
        if 'source' in data:
            journal.source = data['source']
        if 'confidenceScore' in data:
            journal.confidence_score = data['confidenceScore']
        if 'isVerified' in data:
            journal.is_verified = data['isVerified']

        journal.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'data': journal.to_dict(),
            'message': 'Journal abbreviation updated successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals/<int:journal_id>', methods=['DELETE'])
def delete_journal(journal_id):
    """
    Delete a journal abbreviation
    """
    try:
        journal = JournalAbbreviation.query.get_or_404(journal_id)

        db.session.delete(journal)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Journal abbreviation deleted successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals/search', methods=['POST'])
def search_journal_abbreviation():
    """
    Search for journal abbreviation in database first, then fallback to GenAI
    """
    try:
        data = request.get_json()
        search_term = data.get('searchTerm', '').strip()

        if not search_term:
            return jsonify({'success': False, 'error': 'searchTerm is required'}), 400

        start_time = time.time()

        # Use the proven working search algorithm from the test
        import re

        journal = None

        # 1. Check if search term is already an abbreviation
        journal = JournalAbbreviation.query.filter(
            func.lower(JournalAbbreviation.abbreviation) == func.lower(search_term)
        ).first()

        # 2. Exact match on full name (case-insensitive)
        if not journal:
            journal = JournalAbbreviation.query.filter(
                func.lower(JournalAbbreviation.full_name) == func.lower(search_term)
            ).first()

        # 3. Try variations if exact match fails
        if not journal:
            variations = []

            # Replace & with 'and'
            if '&' in search_term:
                variations.append(search_term.replace('&', 'and'))
                variations.append(search_term.replace('&', ' and '))

            # Normalize spaces
            normalized_spaces = re.sub(r'\s+', ' ', search_term.strip())
            if normalized_spaces != search_term:
                variations.append(normalized_spaces)

            # Try each variation
            for variation in variations:
                journal = JournalAbbreviation.query.filter(
                    func.lower(JournalAbbreviation.full_name) == func.lower(variation)
                ).first()
                if journal:
                    break

        # 4. Fuzzy matching with normalization (if still not found)
        if not journal:
            def normalize_for_matching(text):
                if not text:
                    return ""
                text = text.lower()
                text = text.replace('&', 'and')
                text = re.sub(r'\s+', ' ', text)
                text = re.sub(r'[,.:;()\-]', '', text)
                text = re.sub(r'^the\s+', '', text)
                return text.strip()

            normalized_search = normalize_for_matching(search_term)
            all_journals = JournalAbbreviation.query.all()

            for j in all_journals:
                normalized_db = normalize_for_matching(j.full_name)
                if normalized_db == normalized_search:
                    journal = j
                    break

        # 5. Partial matching (contains) as last resort
        if not journal:
            journal = JournalAbbreviation.query.filter(
                func.lower(JournalAbbreviation.full_name).contains(func.lower(search_term))
            ).first()

        if journal:
            # Update usage count
            journal.usage_count += 1
            db.session.commit()

            search_time = int((time.time() - start_time) * 1000)

            return jsonify({
                'success': True,
                'data': {
                    'abbreviation': journal.abbreviation,
                    'source': 'database',
                    'confidence': journal.confidence_score,
                    'searchTimeMs': search_time
                }
            })

        # If not found in database, return indication to use GenAI
        search_time = int((time.time() - start_time) * 1000)

        return jsonify({
            'success': True,
            'data': {
                'abbreviation': None,
                'source': 'not_found',
                'searchTimeMs': search_time,
                'message': 'Not found in database, use GenAI fallback'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals/test-search', methods=['POST'])
def test_journal_search():
    """
    Test endpoint to verify fuzzy matching works
    """
    try:
        data = request.get_json()
        search_terms = data.get('searchTerms', [])

        def normalize_journal_name(name):
            """Normalize journal name for fuzzy matching"""
            import re
            name = name.lower()
            name = re.sub(r'^(the\s+)', '', name)
            name = re.sub(r'\s+', ' ', name)
            name = name.replace('&', 'and')
            name = name.replace(',', '')
            name = name.replace('.', '')
            name = name.replace(':', '')
            name = name.replace(';', '')
            name = name.strip()
            return name

        results = []
        for term in search_terms:
            normalized = normalize_journal_name(term)

            # Find matches
            matches = []
            all_journals = JournalAbbreviation.query.all()
            for j in all_journals:
                if normalize_journal_name(j.full_name) == normalized:
                    matches.append({
                        'id': j.id,
                        'fullName': j.full_name,
                        'abbreviation': j.abbreviation,
                        'normalized': normalize_journal_name(j.full_name)
                    })

            results.append({
                'searchTerm': term,
                'normalized': normalized,
                'matches': matches
            })

        return jsonify({
            'success': True,
            'data': results
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== TE ASSIGNMENT ENDPOINTS =====

@app.route('/api/te-assignments/available-tes', methods=['GET'])
def get_available_tes():
    """Get all available TEs for assignment"""
    try:
        # Log request details for debugging
        logging.info(f"[AVAILABLE_TES] Request from {request.remote_addr}")
        logging.info(f"[AVAILABLE_TES] Session keys: {list(session.keys())}")
        logging.info(f"[AVAILABLE_TES] Cookies: {list(request.cookies.keys())}")

        # Check authentication
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            logging.warning(f"[AVAILABLE_TES] Authentication failed - No valid session")
            return jsonify({'error': 'Authentication required'}), 401

        logging.info(f"[AVAILABLE_TES] User authenticated: {user.username} (Role: {user.role})")

        # Check if required environment variables are available
        if not os.getenv('GOOGLE_DRIVE_PARENT_FOLDER_ID'):
            logging.error("GOOGLE_DRIVE_PARENT_FOLDER_ID environment variable not set")
            return jsonify({'success': False, 'error': 'TE assignment service not configured properly'}), 500

        # Get all active TEs
        tes = User.query.filter_by(role='TE', is_active=True).all()

        # Include Pratik Jain specifically (SuperAdmin can also be assigned as TE)
        pratik_te = User.query.filter_by(username='pratik', is_active=True).first()
        if pratik_te and pratik_te not in tes:
            tes.append(pratik_te)

        te_list = []
        for te in tes:
            te_list.append({
                'id': te.id,
                'username': te.username,
                'email': te.email or f'{te.username}@editink.in',
                'role': te.role
            })

        return jsonify({
            'success': True,
            'tes': te_list,
            'count': len(te_list)
        }), 200

    except Exception as e:
        logging.error(f"Error getting available TEs: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/te-assignments/smart-batches', methods=['POST'])
def create_smart_batches():
    """Create smart batches for TE assignment"""
    try:
        # Check authentication
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        article_ids = data.get('article_ids', [])
        if not article_ids:
            return jsonify({'success': False, 'error': 'No article IDs provided'}), 400

        # Get available TEs
        tes = User.query.filter_by(role='TE', is_active=True).all()

        # Include Pratik Jain specifically
        pratik_te = User.query.filter_by(username='pratik', is_active=True).first()
        if pratik_te and pratik_te not in tes:
            tes.append(pratik_te)

        available_tes = [{'id': te.id, 'username': te.username, 'email': te.email or f'{te.username}@editink.in'} for te in tes]

        if not available_tes:
            return jsonify({'success': False, 'error': 'No available TEs found'}), 400

        # Get optional parameters
        batch_size = data.get('batch_size')
        group_by_journal = data.get('group_by_journal', True)
        articles_data = data.get('articles_data', [])

        # Simple batching logic (can be enhanced later)
        default_batch_size = batch_size or 5
        batches = []

        for i in range(0, len(article_ids), default_batch_size):
            batch_articles = article_ids[i:i + default_batch_size]
            te_index = (i // default_batch_size) % len(available_tes)
            suggested_te = available_tes[te_index]

            # Get article details for frontend
            articles = []
            for article_id in batch_articles:
                # Try to get article details from articles_data first
                article_data = None
                if articles_data:
                    article_data = next((a for a in articles_data if a.get('article_id') == article_id), None)

                if article_data:
                    articles.append({
                        'article_id': article_id,
                        'journal_name': article_data.get('journal_name', 'Unknown Journal')
                    })
                else:
                    # Fallback: try to get from database
                    article_file = ArticleFile.query.filter_by(article_id=article_id).first()
                    if article_file:
                        articles.append({
                            'article_id': article_id,
                            'journal_name': getattr(article_file, 'journal_name', 'Unknown Journal')
                        })
                    else:
                        articles.append({
                            'article_id': article_id,
                            'journal_name': 'Unknown Journal'
                        })

            batches.append({
                'batch_id': f'batch_{i//default_batch_size + 1}',
                'batch_number': i//default_batch_size + 1,
                'article_ids': batch_articles,
                'articles': articles,
                'suggested_te': suggested_te,
                'default_te_id': suggested_te['id'],
                'article_count': len(batch_articles),
                'status': 'pending'
            })

        return jsonify({
            'success': True,
            'batches': batches,
            'total_batches': len(batches),
            'total_articles': len(article_ids),
            'available_tes': available_tes
        }), 200

    except Exception as e:
        logging.error(f"Error creating smart batches: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/te-assignments/upload-zips', methods=['POST'])
def upload_zips_for_assignment():
    """Upload ZIP files for TE assignment"""
    try:
        # Check authentication
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        # Handle file uploads
        if 'files' not in request.files:
            # Handle potential direct drive usage legacy check
            return jsonify({'success': False, 'error': 'No files provided'}), 400

        files = request.files.getlist('files')
        if not files:
            return jsonify({'success': False, 'error': 'No files selected'}), 400

        uploaded_files = []
        for file in files:
            if file.filename == '':
                continue

            if file and file.filename.endswith('.zip'):
                # Save file temporarily
                filename = secure_filename(file.filename)
                # Use UPLOAD_FOLDER instead of /tmp for cross-platform compatibility
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)

                uploaded_files.append({
                    'filename': filename,
                    'filepath': filepath,
                    'size': os.path.getsize(filepath)
                })

        return jsonify({
            'success': True,
            'uploaded_files': uploaded_files,
            'count': len(uploaded_files)
        }), 200

    except Exception as e:
        logging.error(f"Error uploading ZIP files: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/drive/list-dump-files', methods=['GET'])
def list_dump_folder_files():
    """
    List all files in the dump folder for debugging
    """
    try:
        # Check authentication
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        from services.drive_service import create_drive_service
        drive_service = create_drive_service()

        # Hardcoded dump folder ID
        dump_folder_id = '1xHhAfSIgcX5k7LQStSvszlQmN7OP52di'

        # List all files in dump folder
        query = f"'{dump_folder_id}' in parents and trashed=false"
        results = drive_service.service.files().list(
            q=query,
            fields='files(id, name, mimeType, createdTime, webViewLink)',
            pageSize=100,
            orderBy='createdTime desc'
        ).execute()

        files = results.get('files', [])

        logging.info(f"Found {len(files)} files in dump folder {dump_folder_id}")

        return jsonify({
            'success': True,
            'dump_folder_id': dump_folder_id,
            'file_count': len(files),
            'files': files
        }), 200

    except Exception as e:
        logging.error(f"Error listing dump folder files: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/drive/get-file-ids-by-names', methods=['POST'])
def get_drive_file_ids_by_names():
    """
    Get Drive file IDs for given filenames from dump folder
    Used during assignment to lookup files that were uploaded by Playwright
    """
    try:
        # Check authentication
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        filenames = data.get('filenames', [])

        if not filenames:
            return jsonify({'success': False, 'error': 'filenames array is required'}), 400

        from services.drive_service import create_drive_service
        drive_service = create_drive_service()

        # Hardcoded dump folder ID
        dump_folder_id = '1xHhAfSIgcX5k7LQStSvszlQmN7OP52di'

        # Log the search details for debugging
        logging.info(f"Searching for {len(filenames)} files in dump folder {dump_folder_id}")
        logging.info(f"Filenames to search: {filenames}")

        # Search for files in dump folder
        file_mapping = drive_service.search_files_by_name(dump_folder_id, filenames)

        logging.info(f"Found {len(file_mapping)}/{len(filenames)} files in dump folder")
        if len(file_mapping) == 0:
            logging.warning(f"No files found! Searched filenames: {filenames}")

        return jsonify({
            'success': True,
            'file_mapping': file_mapping,
            'found_count': len(file_mapping),
            'requested_count': len(filenames),
            'searched_filenames': filenames,  # Return what was searched for debugging
            'dump_folder_id': dump_folder_id  # Return folder ID for debugging
        }), 200

    except Exception as e:
        logging.error(f"Error getting file IDs: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/te-assignments/create-from-dump', methods=['POST'])
def create_te_assignment_from_dump():
    """
    Create TE assignment by moving files from dump folder to batch folder
    NO FILE UPLOAD - files are already in Drive dump folder
    """
    try:
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        te_id = data.get('te_id')
        batch_name = data.get('batch_name') or f"batch-{int(datetime.utcnow().timestamp())}"
        article_ids = data.get('article_ids', [])
        drive_file_ids = data.get('drive_file_ids', [])
        notes = data.get('notes', '')
        articles_with_authors = data.get('articles_with_authors')  # Author information from frontend

        if not te_id or not article_ids:
            return jsonify({'success': False, 'error': 'te_id and article_ids are required'}), 400
        
        if not drive_file_ids:
            return jsonify({'success': False, 'error': 'drive_file_ids are required - files must be in dump folder'}), 400
        
        # Get Drive service
        from services.drive_service import create_drive_service
        drive_service = create_drive_service()
        
        # Create batch folder
        folder_id, folder_link = drive_service.create_batch_folder(batch_name)
        logging.info(f"Created batch folder: {batch_name} (ID: {folder_id})")
        
        # Move files from dump to batch folder (API calls only, instant!)
        moved_files = []
        for file_id in drive_file_ids:
            try:
                file_link = drive_service.move_file_to_folder(file_id, folder_id)
                moved_files.append({
                    'file_id': file_id,
                    'file_link': file_link
                })
            except Exception as e:
                logging.error(f"Failed to move file {file_id}: {e}")
        
        logging.info(f"Moved {len(moved_files)}/{len(drive_file_ids)} files to batch folder")
        
        # Get TE user
        te_user = User.query.get(te_id)
        if not te_user:
            return jsonify({'success': False, 'error': 'TE not found'}), 404
        
        # Create assignment record
        assignment = TEAssignment(
            batch_name=batch_name,
            zip_ids=[],  # Empty array - not using old queue mechanism
            article_ids=article_ids,
            assigned_te_id=te_id,
            assigned_te_email=te_user.email,
            assignment_status='uploaded',
            assigned_by=user.id,
            notes=notes,
            drive_folder_id=folder_id,
            drive_folder_link=folder_link,
            uploaded_at=datetime.utcnow()
        )
        
        db.session.add(assignment)
        db.session.commit()

        # Update article statuses to assigned_TE
        updated_articles = 0
        for article_id in article_ids:
            article = ArticleFile.query.filter_by(article_id=article_id).first()
            if article:
                article.status = 'assigned_TE'
                article.assigned_to = te_id
                updated_articles += 1
                logging.info(f"Updated article {article_id} status to assigned_TE")
            else:
                logging.warning(f"Article {article_id} not found in database for status update")

        db.session.commit()
        logging.info(f"Updated {updated_articles}/{len(article_ids)} articles to assigned_TE status")

        # Log author information status
        if articles_with_authors:
            logging.info(f"📧 Received author information from frontend for {len(articles_with_authors)} articles")
        else:
            logging.warning(f"⚠️ No author information provided from frontend for batch {batch_name}")

        # Send email
        from services.te_assignment_service import TEAssignmentService
        assignment_service = TEAssignmentService(db, TEAssignment, User, ArticleFile)

        email_sent = assignment_service.send_assignment_email_direct(
            assignment_id=assignment.id,
            te_name=te_user.username,
            te_email=te_user.email,
            article_ids=article_ids,
            drive_link=folder_link,
            batch_name=batch_name,
            assigned_by_username=user.username,
            articles_with_authors=articles_with_authors
        )

        logging.info(f"Assignment created: {assignment.id}, Email sent: {email_sent}")

        return jsonify({
            'success': True,
            'assignment_id': assignment.id,
            'folder_link': folder_link,
            'moved_files': len(moved_files),
            'email_sent': email_sent,
            'updated_articles': updated_articles
        }), 200
        
    except Exception as e:
        logging.error(f"Error creating assignment from dump: {e}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/te-assignments/upload-folder', methods=['POST'])
def upload_folder_for_assignment():
    """Upload folder with ZIP files and batch summary for TE assignment"""
    try:
        # Check authentication
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        folder_path = data.get('folder_path')
        if not folder_path:
            return jsonify({'success': False, 'error': 'No folder path provided'}), 400

        # Process folder (simplified implementation)
        return jsonify({
            'success': True,
            'message': 'Folder upload processed successfully',
            'folder_path': folder_path
        }), 200

    except Exception as e:
        logging.error(f"Error uploading folder: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== AUTHOR QUERY ENDPOINTS =====

@app.route('/api/admin/author-query', methods=['POST'])
def create_author_query():
    """Create a new author query"""
    try:
        # Add detailed logging for debugging authentication issues
        logging.info(f"[AUTHOR_QUERY] Request received from {request.remote_addr}")
        logging.info(f"[AUTHOR_QUERY] Session keys: {list(session.keys())}")
        logging.info(f"[AUTHOR_QUERY] Has user_id: {'user_id' in session}, Has admin_id: {'admin_id' in session}")

        # Check authentication - Admin, Coordinator, TE, or SuperAdmin can create queries
        # TEs need this permission as they process articles and raise queries
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator', 'TE'])

        if not has_permission:
            logging.warning(f"[AUTHOR_QUERY] Authentication failed - Session: user_id={session.get('user_id')}, admin_id={session.get('admin_id')}")
            return jsonify({
                'error': 'Authentication required',
                'message': 'Your session may have expired. Please logout and login again to continue.',
                'hint': 'Only Admin, Coordinator, TE, or SuperAdmin users can create author queries.'
            }), 401

        logging.info(f"[AUTHOR_QUERY] User authenticated: {user.username} (ID: {user.id}, Role: {user.role})")

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate required fields - updated to match frontend
        required_fields = ['article_id', 'query_type', 'author_names']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Clean and join author names
        if isinstance(data['author_names'], list):
            # Clean each author name: remove extra spaces and normalize Unicode
            cleaned_names = [' '.join(name.strip().split()) for name in data['author_names']]
            author_name_str = ', '.join(cleaned_names)
        else:
            # Clean single author name string
            author_name_str = ' '.join(data['author_names'].strip().split())

        # Create author query
        author_query = AuthorQuery(
            article_id=data['article_id'],
            author_name=author_name_str,
            query_type=data['query_type'],
            query_text=data.get('query_text', ''),
            raised_by=user.id,
            batch_id=data.get('batch_id'),
            status='open'
        )

        db.session.add(author_query)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Author query created successfully',
            'query_id': author_query.id
        }), 201

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error creating author query: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/author-query/<int:query_id>', methods=['PUT'])
def update_author_query(query_id):
    """Update an author query"""
    try:
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        query = AuthorQuery.query.get_or_404(query_id)
        data = request.get_json()

        if 'status' in data:
            query.status = data['status']
        if 'query_text' in data:
            query.query_text = data['query_text']

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Author query updated successfully',
            'query': query.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error updating author query: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/author-queries', methods=['GET'])
def get_author_queries():
    """Get all author queries with pagination"""
    try:
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status_filter = request.args.get('status', '', type=str)

        query = AuthorQuery.query
        if status_filter:
            query = query.filter(AuthorQuery.status == status_filter)

        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        queries = [q.to_dict() for q in pagination.items]

        return jsonify({
            'success': True,
            'queries': queries,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total
            }
        }), 200

    except Exception as e:
        logging.error(f"Error getting author queries: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== EMAIL QUERY ENDPOINT =====

@app.route('/api/send-query-email', methods=['POST'])
def send_query_email():
    """Send validation query email with optional ZIP file attachment"""
    try:
        # Check if this is a multipart request (with file attachment)
        if request.content_type and 'multipart/form-data' in request.content_type:
            # Handle multipart form data with file attachment
            data = {
                'to': request.form.get('to'),
                'subject': request.form.get('subject'),
                'description': request.form.get('description'),
                'articleId': request.form.get('articleId'),
                'validationResult': request.form.get('validationResult')
            }

            # Get ZIP file if present
            zip_file = request.files.get('zipFile')
            logging.info(f"Multipart request detected. ZIP file present: {zip_file is not None}")
            if zip_file:
                logging.info(f"ZIP file details: filename={zip_file.filename}, size={zip_file.content_length}")
        else:
            # Handle JSON request (no file attachment)
            data = request.get_json()
            zip_file = None
            logging.info("JSON request detected. No ZIP file attachment.")

        # Validate required fields
        required_fields = ['to', 'subject', 'description', 'articleId']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Email configuration (using same config as TE_AUTOMATION)
        smtp_server = 'smtpout.secureserver.net'
        smtp_port = 465
        sender_email = '<EMAIL>'
        sender_password = 'Editink@007'

        # Create message
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = data['to']
        msg['Cc'] = "<EMAIL>, <EMAIL>, <EMAIL>"
        msg['Subject'] = f"Article Submission Query - {data['articleId']} - {data['subject']}"

        # Create professional HTML email body
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Article Submission Query</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h2 style="color: #2563eb; margin: 0 0 10px 0; font-size: 24px;">Article Submission Query</h2>
                <p style="margin: 0; color: #666; font-size: 14px;">Technical Editor Review Process</p>
            </div>

            <div style="background-color: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px;">Article Information</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold; width: 120px;">Article ID:</td>
                        <td style="padding: 8px 0;">{data['articleId']}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold;">Issue Type:</td>
                        <td style="padding: 8px 0;">{data['subject']}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: bold;">Date:</td>
                        <td style="padding: 8px 0;">{datetime.now().strftime('%B %d, %Y at %I:%M %p')}</td>
                    </tr>
                </table>
            </div>

            <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h3 style="color: #92400e; margin: 0 0 15px 0; font-size: 18px;">📋 Review Required</h3>
                <div style="background-color: #fff; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b;">
                    <div style="white-space: pre-wrap; font-family: Arial, sans-serif; line-height: 1.5;">{data['description']}</div>
                </div>
            </div>

            <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h3 style="color: #0369a1; margin: 0 0 15px 0; font-size: 18px;">📎 Attached Files</h3>
                <p style="margin: 0; color: #0369a1;">
                    {'✅ Original ZIP file attached for your review' if zip_file and zip_file.filename else '📄 No files attached - please refer to your original submission'}
                </p>
            </div>

            <div style="background-color: #f9fafb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px;">Next Steps</h3>
                <ol style="margin: 0; padding-left: 20px; color: #4b5563;">
                    <li style="margin-bottom: 8px;">Please review the issue described above</li>
                    <li style="margin-bottom: 8px;">Make the necessary corrections to your submission</li>
                    <li style="margin-bottom: 8px;">Reply to this email with your updated files or clarification</li>
                    <li style="margin-bottom: 8px;">Our team will process your response promptly</li>
                </ol>
            </div>

            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">

            <div style="text-align: center; color: #6b7280; font-size: 12px;">
                <p style="margin: 0 0 5px 0;">This email was automatically generated by the Editorial Processing System</p>
                <p style="margin: 0;">For assistance, please reply to this email or contact our editorial team</p>
            </div>
        </body>
        </html>
        """

        # Attach HTML body
        msg.attach(MIMEText(html_body, 'html'))

        # Add ZIP file attachment if provided
        if zip_file and zip_file.filename:
            try:
                # Read file content
                file_content = zip_file.read()

                # Create attachment
                from email.mime.application import MIMEApplication
                attachment = MIMEApplication(file_content, _subtype='zip')
                attachment.add_header('Content-Disposition', 'attachment', filename=zip_file.filename)
                msg.attach(attachment)

                logging.info(f"Attached ZIP file: {zip_file.filename} ({len(file_content)} bytes)")
            except Exception as e:
                logging.error(f"Failed to attach ZIP file: {e}")
                # Continue without attachment rather than failing the email

        # Send email
        with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
            server.login(sender_email, sender_password)
            server.send_message(msg)

        # Log the email sending
        logging.info(f"Query email sent for article {data['articleId']} to {data['to']}")

        # Create author query record (optional - only if database schema supports it)
        try:
            # Parse validation result if provided
            validation_data = None
            if data.get('validationResult'):
                try:
                    validation_data = json.loads(data['validationResult'])
                except json.JSONDecodeError:
                    logging.warning(f"Invalid validation result JSON: {data['validationResult']}")

            # Check authentication for database operations
            has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
            if has_permission and user:
                author_query = AuthorQuery(
                    article_id=data['articleId'],
                    author_name=data['to'],  # Using email as author name for now
                    query_type='validation_error',
                    query_text=data['description'],
                    raised_by=user.id,
                    status='sent',
                    email_sent=True,
                    validation_data=json.dumps(validation_data) if validation_data else None
                )

                db.session.add(author_query)
                db.session.commit()
                logging.info(f"Author query record created with ID: {author_query.id}")
        except Exception as db_error:
            logging.warning(f"Failed to create author query record: {db_error}")
            # Don't fail the email sending if database operation fails

        return jsonify({
            'success': True,
            'message': 'Query email sent successfully',
            'articleId': data['articleId'],
            'timestamp': datetime.now().isoformat()
        })

    except smtplib.SMTPException as e:
        logging.error(f"SMTP error sending query email: {e}")
        return jsonify({'success': False, 'error': f'Email sending failed: {str(e)}'}), 500
    except Exception as e:
        logging.error(f"Error sending query email: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== TE ASSIGNMENT MULTI-ASSIGN ENDPOINT =====

@app.route('/api/te-assignments/multi-assign-direct', methods=['POST'])
def create_multi_te_assignment_direct():
    """Create multiple TE assignments from batches with Direct-to-Drive uploaded files"""
    try:
        # Check authentication
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        batch_assignments = data.get('batch_assignments')
        
        if not batch_assignments:
             return jsonify({'success': False, 'error': 'No batch assignments provided'}), 400

        from services.te_assignment_service import TEAssignmentService
        service = TEAssignmentService(db, TEAssignment, User, ArticleFile)
        
        results = {
            'successful': [],
            'failed': [],
            'total_batches': len(batch_assignments),
            'summary': {
                'successful_batches': 0,
                'failed_batches': 0
            }
        }

        for batch in batch_assignments:
            try:
                # Essential Data
                te_id = batch.get('te_id')
                batch_name = batch.get('batch_name')
                drive_folder_id = batch.get('drive_folder_id')
                drive_folder_link = batch.get('drive_folder_link')
                article_ids = batch.get('article_ids', [])
                notes = batch.get('notes', '')
                
                # Get TE User
                te_user = User.query.get(te_id)
                if not te_user:
                     raise ValueError(f"TE not found: {te_id}")

                # Create Assignment Record
                assignment = TEAssignment(
                    batch_name=batch_name,
                    zip_ids=[],
                    article_ids=article_ids,
                    assigned_te_id=te_id,
                    assigned_te_email=te_user.email,
                    assignment_status='uploaded',
                    assigned_by=user.id,
                    notes=notes,
                    drive_folder_id=drive_folder_id,
                    drive_folder_link=drive_folder_link,
                    uploaded_at=datetime.utcnow()
                )
                
                db.session.add(assignment)
                db.session.commit() # Commit to get ID

                # Update article statuses to assigned_TE
                updated_articles = 0
                for article_id in article_ids:
                    article = ArticleFile.query.filter_by(article_id=article_id).first()
                    if article:
                        article.status = 'assigned_TE'
                        article.assigned_to = te_id
                        updated_articles += 1
                    else:
                        logging.warning(f"Article {article_id} not found for status update")

                db.session.commit()
                logging.info(f"Updated {updated_articles}/{len(article_ids)} articles to assigned_TE for batch {batch_name}")

                # Extract author information from batch data if available
                articles_with_authors = batch.get('articles_with_authors')

                # If not provided in batch, try to extract from article metadata in database
                if not articles_with_authors:
                    try:
                        articles_with_authors = []
                        for article_id in article_ids:
                            article = ArticleFile.query.filter_by(article_id=article_id).first()
                            if article and article.article_metadata:
                                # Extract author information from article_metadata JSON field
                                metadata = article.article_metadata
                                authors = metadata.get('authors', [])

                                article_info = {
                                    'article_id': article_id,
                                    'journal': article.journal_name or 'Unknown',
                                    'authors': authors,
                                    'status': article.status
                                }
                                articles_with_authors.append(article_info)
                                logging.info(f"Extracted {len(authors)} authors from metadata for {article_id}")
                            else:
                                # No metadata found, add article without author info
                                article_info = {
                                    'article_id': article_id,
                                    'journal': article.journal_name if article else 'Unknown',
                                    'authors': [],
                                    'status': 'no_metadata'
                                }
                                articles_with_authors.append(article_info)
                                logging.warning(f"No metadata found for {article_id}")

                        # If we found at least some author data, use it; otherwise set to None
                        if not any(a.get('authors') for a in articles_with_authors):
                            logging.warning(f"No author information found in metadata for batch {batch_name}")
                            articles_with_authors = None
                        else:
                            logging.info(f"Extracted author info from database metadata for {len([a for a in articles_with_authors if a.get('authors')])} articles")

                    except Exception as e:
                        logging.warning(f"Failed to extract author information for batch {batch_name}: {e}")
                        articles_with_authors = None

                # Send Email (Direct) with author information
                email_sent = service.send_assignment_email_direct(
                    assignment_id=assignment.id,
                    te_name=te_user.username,
                    te_email=te_user.email,
                    article_ids=article_ids,
                    drive_link=drive_folder_link,
                    batch_name=batch_name,
                    assigned_by_username=user.username,
                    articles_with_authors=articles_with_authors
                )

                results['successful'].append({
                    'batch_name': batch_name,
                    'assignment_id': assignment.id,
                    'email_sent': email_sent,
                    'updated_articles': updated_articles
                })
                results['summary']['successful_batches'] += 1

            except Exception as e:
                logging.error(f"Error in multi-assign-direct for batch {batch.get('batch_name')}: {e}")
                results['failed'].append({
                    'batch_name': batch.get('batch_name'),
                    'error': str(e)
                })
                results['summary']['failed_batches'] += 1
                # Continue to next batch even if one fails
        
        return jsonify({
            'success': True, 
            'results': results
        }), 200

    except Exception as e:
        logging.error(f"Error in multi-assign-direct: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/te-assignments/multi-assign', methods=['POST'])
def create_multi_te_assignment():
    """Create multiple TE assignments from batches with ZIP files"""
    try:
        # Check authentication
        has_permission, user = check_user_permission(['SuperAdmin', 'Admin', 'Coordinator'])
        if not has_permission:
            return jsonify({'error': 'Authentication required'}), 401

        # Handle form data with ZIP files (like individual assignment)
        if not request.files and not request.form:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Get batch assignments from form data
        batch_assignments_json = request.form.get('batch_assignments')
        if not batch_assignments_json:
            return jsonify({'success': False, 'error': 'No batch assignments provided'}), 400

        try:
            batch_assignments = json.loads(batch_assignments_json)
        except json.JSONDecodeError:
            return jsonify({'success': False, 'error': 'Invalid batch assignments JSON'}), 400

        if not batch_assignments:
            return jsonify({'success': False, 'error': 'No batch assignments provided'}), 400

        from services.te_assignment_service import TEAssignmentService
        service = TEAssignmentService(db, TEAssignment, User, ArticleFile)

        # Process each batch assignment with its ZIP files
        results = {
            'successful': [],
            'failed': [],
            'partial': [],
            'total_batches': len(batch_assignments),
            'total_articles': sum(len(batch.get('article_ids', [])) for batch in batch_assignments),
            'summary': {
                'successful_batches': 0,
                'failed_batches': 0,
                'successful_articles': 0,
                'failed_articles': 0
            }
        }

        for batch_idx, batch_assignment in enumerate(batch_assignments):
            batch_number = batch_assignment.get('batch_number', batch_idx + 1)
            article_ids = batch_assignment.get('article_ids', [])
            te_id = batch_assignment.get('te_id')
            notes = batch_assignment.get('notes', f"Smart batch assignment #{batch_number}")

            try:
                # Collect ZIP files for this batch
                batch_zip_files = []
                for article_id in article_ids:
                    zip_file_key = f"zip_file_{article_id}"
                    if zip_file_key in request.files:
                        batch_zip_files.append(request.files[zip_file_key])

                if not batch_zip_files:
                    raise ValueError(f"No ZIP files found for batch {batch_number}")

                # Check if JSON summary file is provided (for folder uploads with author data)
                json_summary_file = request.files.get('json_summary_file') or request.files.get('json_summary')

                if json_summary_file:
                    # Use the enhanced method with JSON summary for author information
                    assignment_result = service.create_te_assignment_with_json_summary(
                        zip_files=batch_zip_files,
                        json_summary_file=json_summary_file,
                        te_id=te_id,
                        assigned_by_id=user.id,
                        notes=notes
                    )
                    logging.info(f"✅ Created batch {batch_number} assignment with JSON summary (author data available)")
                else:
                    # For smart batching from completed articles, try to extract author info from article metadata
                    # This is the enhanced ZIP-only method that looks for author data in the database
                    assignment_result = service.create_te_assignment_from_zip_files(
                        zip_files=batch_zip_files,
                        te_id=te_id,
                        assigned_by_id=user.id,
                        notes=notes
                    )
                    logging.info(f"✅ Created batch {batch_number} assignment from completed articles (checking for author data)")

                # Add batch-specific information
                assignment_result.update({
                    'batch_number': batch_number,
                    'journal_codes': batch_assignment.get('journal_codes', []),
                    'processing_timestamp': datetime.utcnow().isoformat()
                })

                results['successful'].append(assignment_result)
                results['summary']['successful_batches'] += 1
                results['summary']['successful_articles'] += len(article_ids)

            except Exception as batch_error:
                error_details = {
                    'batch_number': batch_number,
                    'article_ids': article_ids,
                    'te_id': te_id,
                    'error': str(batch_error),
                    'processing_timestamp': datetime.utcnow().isoformat()
                }

                results['failed'].append(error_details)
                results['summary']['failed_batches'] += 1
                results['summary']['failed_articles'] += len(article_ids)

                logging.error(f"❌ Failed to create batch {batch_number} assignment: {batch_error}")
                continue

        return jsonify({
            'success': True,
            'results': results
        }), 200

    except Exception as e:
        logging.error(f"Error creating multi-TE assignment: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== USER MANAGEMENT ENDPOINTS =====

@app.route('/api/super-admin/users', methods=['GET'])
def get_all_users():
    """Get all users for Super Admin management"""
    try:
        # Check Super Admin authentication
        has_permission, user = check_user_permission(['SuperAdmin'])
        if not has_permission:
            return jsonify({'error': 'Super Admin access required'}), 403

        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '', type=str)
        role_filter = request.args.get('role', '', type=str)
        status_filter = request.args.get('status', '', type=str)

        # Build query
        query = User.query

        # Apply search filter
        if search:
            query = query.filter(
                or_(
                    User.username.ilike(f'%{search}%'),
                    User.email.ilike(f'%{search}%')
                )
            )

        # Apply role filter
        if role_filter:
            query = query.filter(User.role == role_filter)

        # Apply status filter
        if status_filter == 'active':
            query = query.filter(User.is_active == True)
        elif status_filter == 'inactive':
            query = query.filter(User.is_active == False)

        # Get paginated results
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        users = [user.to_dict() for user in pagination.items]

        return jsonify({
            'success': True,
            'users': users,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200

    except Exception as e:
        logging.error(f"Error getting users: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/super-admin/users', methods=['POST'])
def create_user():
    """Create a new user"""
    try:
        # Check Super Admin authentication
        has_permission, admin_user = check_user_permission(['SuperAdmin'])
        if not has_permission:
            return jsonify({'error': 'Super Admin access required'}), 403

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['username', 'password', 'role', 'email']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Check if username already exists
        existing_user = User.query.filter_by(username=data['username']).first()
        if existing_user:
            return jsonify({'success': False, 'error': 'Username already exists'}), 400

        # Check if email already exists
        existing_email = User.query.filter_by(email=data['email']).first()
        if existing_email:
            return jsonify({'success': False, 'error': 'Email already exists'}), 400

        # Create new user
        new_user = User(
            username=data['username'],
            email=data['email'],
            role=data['role'],
            is_active=data.get('is_active', True)
        )
        new_user.set_password(data['password'])

        db.session.add(new_user)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'User created successfully',
            'user': new_user.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error creating user: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/super-admin/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    """Update user details"""
    try:
        # Check Super Admin authentication
        has_permission, admin_user = check_user_permission(['SuperAdmin'])
        if not has_permission:
            return jsonify({'error': 'Super Admin access required'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404

        # Prevent Super Admin from modifying other Super Admins
        if user.role == 'SuperAdmin' and user.id != admin_user.id:
            return jsonify({'success': False, 'error': 'Cannot modify other Super Admin accounts'}), 403

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Update user fields
        if 'username' in data and data['username'] != user.username:
            # Check if new username already exists
            existing_user = User.query.filter_by(username=data['username']).first()
            if existing_user:
                return jsonify({'success': False, 'error': 'Username already exists'}), 400
            user.username = data['username']

        if 'email' in data and data['email'] != user.email:
            # Check if new email already exists
            existing_email = User.query.filter_by(email=data['email']).first()
            if existing_email:
                return jsonify({'success': False, 'error': 'Email already exists'}), 400
            user.email = data['email']

        if 'role' in data and user.role != 'SuperAdmin':  # Don't allow changing SuperAdmin role
            user.role = data['role']

        if 'is_active' in data and user.role != 'SuperAdmin':  # Don't allow deactivating SuperAdmin
            user.is_active = data['is_active']

        if 'password' in data and data['password']:
            user.set_password(data['password'])

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'User updated successfully',
            'user': user.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error updating user: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/super-admin/users/<int:user_id>/toggle-status', methods=['POST'])
def toggle_user_status(user_id):
    """Toggle user active/inactive status"""
    try:
        # Check Super Admin authentication
        has_permission, admin_user = check_user_permission(['SuperAdmin'])
        if not has_permission:
            return jsonify({'error': 'Super Admin access required'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404

        # Prevent deactivating Super Admin accounts
        if user.role == 'SuperAdmin':
            return jsonify({'success': False, 'error': 'Cannot modify Super Admin status'}), 403

        # Toggle status
        user.is_active = not user.is_active
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'User {"activated" if user.is_active else "deactivated"} successfully',
            'user': user.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error toggling user status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/super-admin/stats', methods=['GET'])
def get_super_admin_stats():
    """Get user management statistics for Super Admin"""
    try:
        # Check Super Admin authentication
        has_permission, user = check_user_permission(['SuperAdmin'])
        if not has_permission:
            return jsonify({'error': 'Super Admin access required'}), 403

        # Get user counts by role
        role_stats = db.session.query(
            User.role,
            func.count(User.id).label('count'),
            func.sum(func.case([(User.is_active == True, 1)], else_=0)).label('active_count')
        ).group_by(User.role).all()

        # Get recent user activity
        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()

        # Get login activity (users with recent last_login)
        active_users = User.query.filter(
            User.last_login.isnot(None),
            User.is_active == True
        ).order_by(User.last_login.desc()).limit(10).all()

        return jsonify({
            'success': True,
            'stats': {
                'roleStats': [
                    {
                        'role': row[0],
                        'total': row[1],
                        'active': row[2]
                    }
                    for row in role_stats
                ],
                'recentUsers': [user.to_dict() for user in recent_users],
                'activeUsers': [user.to_dict() for user in active_users],
                'totalUsers': User.query.count(),
                'activeUsersCount': User.query.filter_by(is_active=True).count(),
                'inactiveUsers': User.query.filter_by(is_active=False).count()
            }
        }), 200

    except Exception as e:
        logging.error(f"Error getting super admin stats: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


# ===== WORD PLUGIN API ENDPOINTS =====
# Moved to routes/word_plugin_routes.py
# All Word Plugin endpoints are now registered via Blueprint above


if __name__ == "__main__":
    app.run(debug=False, host='0.0.0.0', port=5001)
