/**
 * Filters references by URL presence
 * @param {Array} references - Array of reference objects
 * @returns {Object} - Object with urlArray and noURLArray
 */
export const filterReferencesByURL = (references) => {
  if (!references || !Array.isArray(references)) {
    return { urlArray: [], noURLArray: [] };
  }

  const urlArray = [];
  const noURLArray = [];

  references.forEach((ref) => {
    // DOI URLs are handled differently - they should be processed normally
    if (ref.term.includes("//doi.org")) {
      noURLArray.push(ref);
    } else {
      // Check for other URLs
      const hasURL =
        ref.term.includes("http://") || ref.term.includes("https://");

      if (hasURL) {
        // Mark as URL type and add to URL array
        ref.type = "URL";
        urlArray.push(ref);
      } else {
        // No URL, process normally
        noURLArray.push(ref);
      }
    }
  });

  return { urlArray, noURLArray };
};
