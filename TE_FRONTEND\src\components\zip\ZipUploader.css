/* ZIP Uploader Styles */
.zip-uploader-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
}

.zip-uploader-header {
  text-align: center;
  margin-bottom: 2rem;
}

.zip-uploader-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.zip-icon {
  font-size: 1.75rem;
}

.zip-uploader-description {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

/* Drop Zone */
.zip-drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 1.5rem;
}

.zip-drop-zone:hover {
  border-color: #3b82f6;
  background: #f8faff;
}

.zip-drop-zone.drag-over {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.02);
}

.zip-drop-zone.processing {
  border-color: #f59e0b;
  background: #fffbeb;
  cursor: not-allowed;
}

/* Upload Icon */
.zip-upload-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1.5rem;
  background: #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 1.5rem;
}

.zip-drop-zone:hover .zip-upload-icon {
  background: #dbeafe;
  color: #3b82f6;
}

/* Upload Text */
.zip-upload-text {
  margin-bottom: 1rem;
}

.zip-main-text {
  font-size: 1.125rem;
  color: #374151;
  margin-bottom: 0.5rem;
}

.zip-browse-link {
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  position: relative;
}

.zip-browse-link:hover {
  color: #1d4ed8;
}

.zip-file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.zip-sub-text {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Processing State */
.zip-processing {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.zip-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.zip-processing p {
  font-size: 1rem;
  color: #374151;
  margin: 0;
}

/* Error Message */
.zip-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  color: #dc2626;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

/* Info Section */
.zip-info {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: .5rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.zip-info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  color: #475569;
}

.zip-info-item:last-child {
  margin-bottom: 0;
}

.zip-info-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .zip-uploader-container {
    padding: 1rem;
  }
  
  .zip-drop-zone {
    padding: 2rem 1rem;
  }
  
  .zip-uploader-title {
    font-size: 1.25rem;
  }
  
  .zip-upload-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
  
  .zip-main-text {
    font-size: 1rem;
  }
}
