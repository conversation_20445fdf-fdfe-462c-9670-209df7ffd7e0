#!/usr/bin/env python3
"""
Database connection fix for Flask app hanging issue
"""

# Add these configurations to fix database connection issues
DATABASE_FIX_CONFIG = """
# Add to app.py after database configuration

# Fix SQLAlchemy connection pool settings
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
    'pool_timeout': 20,
    'max_overflow': 0,
    'pool_size': 5
}

# Add connection error handling
from sqlalchemy.exc import DisconnectionError, OperationalError
from functools import wraps

def handle_db_connection_error(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except (DisconnectionError, OperationalError) as e:
            print(f"Database connection error: {e}")
            db.session.rollback()
            db.session.remove()
            # Retry once
            try:
                return f(*args, **kwargs)
            except Exception as retry_error:
                print(f"Retry failed: {retry_error}")
                return jsonify({'error': 'Database connection error'}), 500
        except Exception as e:
            print(f"Unexpected error: {e}")
            db.session.rollback()
            return jsonify({'error': 'Internal server error'}), 500
    return decorated_function
"""

# Enhanced login function with proper error handling
ENHANCED_LOGIN_FUNCTION = '''
@app.route('/api/auth/login', methods=['POST'])
@handle_db_connection_error
def user_login():
    import time
    start_time = time.time()
    print(f"[LOGIN] Request started at {start_time}")
    
    try:
        # Parse request data
        data = request.get_json()
        if not data:
            print("[LOGIN] No JSON data provided")
            return jsonify({'error': 'No data provided'}), 400
            
        username = data.get('username')
        password = data.get('password')
        
        print(f"[LOGIN] Username: {username}, Password provided: {password is not None}")
        
        if not username or not password:
            print("[LOGIN] Missing credentials")
            return jsonify({'error': 'Username and password required'}), 400

        # Database query with timeout
        print("[LOGIN] Querying database...")
        query_start = time.time()
        
        # Use explicit session management
        try:
            user = db.session.query(User).filter_by(username=username, is_active=True).first()
            query_time = time.time() - query_start
            print(f"[LOGIN] Database query completed in {query_time:.3f}s")
            
        except Exception as db_error:
            print(f"[LOGIN] Database query failed: {db_error}")
            db.session.rollback()
            return jsonify({'error': 'Database error'}), 500
        
        if not user:
            print("[LOGIN] User not found")
            return jsonify({'error': 'Invalid credentials'}), 401

        # Password verification
        print("[LOGIN] Verifying password...")
        password_start = time.time()
        
        try:
            password_valid = user.check_password(password)
            password_time = time.time() - password_start
            print(f"[LOGIN] Password verification completed in {password_time:.3f}s")
            
        except Exception as pwd_error:
            print(f"[LOGIN] Password verification failed: {pwd_error}")
            return jsonify({'error': 'Authentication error'}), 500
        
        if not password_valid:
            print("[LOGIN] Invalid password")
            return jsonify({'error': 'Invalid credentials'}), 401

        # Session setup
        print("[LOGIN] Setting up session...")
        session['user_id'] = user.id
        session['username'] = user.username
        session['user_role'] = user.role
        session.permanent = True

        # Backward compatibility
        if user.role == 'Admin':
            session['admin_id'] = user.id
            session['admin_username'] = user.username

        # Update last login with explicit transaction
        print("[LOGIN] Updating last login...")
        update_start = time.time()
        
        try:
            user.last_login = datetime.utcnow()
            db.session.commit()
            update_time = time.time() - update_start
            print(f"[LOGIN] Database update completed in {update_time:.3f}s")
            
        except Exception as update_error:
            print(f"[LOGIN] Database update failed: {update_error}")
            db.session.rollback()
            # Continue without updating last login
            print("[LOGIN] Continuing without last login update")

        total_time = time.time() - start_time
        print(f"[LOGIN] Login completed successfully in {total_time:.3f}s")
        
        return jsonify({
            'message': 'Login successful',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        error_time = time.time() - start_time
        print(f"[LOGIN] Login failed after {error_time:.3f}s: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # Ensure session is cleaned up
        try:
            db.session.rollback()
        except:
            pass
            
        return jsonify({'error': 'Internal server error'}), 500
'''

def apply_database_fix():
    """Apply the database connection fix to app.py"""
    print("Applying database connection fix...")
    
    # Read current app.py
    with open('app.py', 'r') as f:
        content = f.read()
    
    # Add database configuration after SQLAlchemy initialization
    db_init_pos = content.find('db = SQLAlchemy(app)')
    if db_init_pos > 0:
        insert_pos = content.find('\n', db_init_pos) + 1
        content = content[:insert_pos] + '\n' + DATABASE_FIX_CONFIG + '\n' + content[insert_pos:]
        print("✅ Database configuration added")
    
    # Replace login function
    import re
    login_pattern = r'@app\.route\(\'/api/auth/login\'.*?return jsonify\(\{[^}]*\'user\': user\.to_dict\(\)[^}]*\}\), 200'
    match = re.search(login_pattern, content, re.DOTALL)
    
    if match:
        content = content.replace(match.group(), ENHANCED_LOGIN_FUNCTION.strip())
        print("✅ Login function replaced")
    else:
        print("❌ Could not find login function to replace")
    
    # Write the fixed content
    with open('app.py', 'w') as f:
        f.write(content)
    
    print("✅ Database fix applied successfully")

if __name__ == "__main__":
    apply_database_fix()
