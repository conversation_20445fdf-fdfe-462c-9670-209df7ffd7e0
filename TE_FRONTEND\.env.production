# Production Environment Configuration for TE_FRONTEND
# Copy this to .env and update with your actual values

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
FLASK_HOST=0.0.0.0
FLASK_PORT=4999

# Security Settings
SECRET_KEY=your-super-secure-secret-key-here-generate-a-new-one

# CORS Settings (update with your actual domains)
CORS_ORIGINS=https://your-frontend-domain.com,https://localhost:3000

# Logging Configuration
LOG_LEVEL=INFO

# Application Settings
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=/tmp/uploads

# Backend API URL (if different from default)
# BACKEND_API_URL=http://localhost:4999
