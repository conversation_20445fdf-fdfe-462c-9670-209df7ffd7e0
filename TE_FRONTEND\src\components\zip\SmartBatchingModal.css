/* Smart Batching Modal Styles */
.smart-batching-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.smart-batching-modal {
  background: white;
  border-radius: 12px;
  width: 95%;
  max-width: 1400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
}

.modal-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

/* TE Capacity Information Header */
.te-capacity-info-header {
  background: linear-gradient(135deg, #eff6ff 0%, #e0f2fe 100%);
  border-bottom: 2px solid #bfdbfe;
  padding: 16px 24px;
  margin: 0;
}

.capacity-info-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #1e40af;
  font-weight: 600;
  font-size: 0.95rem;
}

.capacity-info-title svg {
  color: #3b82f6;
}

.capacity-info-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.capacity-info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #bfdbfe;
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.te-name {
  font-weight: 600;
  color: #1e40af;
  font-size: 0.9rem;
}

.te-capacity {
  color: #3b82f6;
  font-size: 0.9rem;
  font-weight: 500;
}

.capacity-info-note {
  margin: 0;
  font-size: 0.85rem;
  color: #1e40af;
  font-style: italic;
  opacity: 0.9;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .capacity-info-list {
    grid-template-columns: 1fr;
  }

  .te-capacity-info-header {
    padding: 12px 16px;
  }
}

/* Journal Selection */
.journal-selection {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.journal-selection h3 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.journal-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.journal-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.journal-checkbox:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.journal-checkbox input[type="checkbox"] {
  margin: 0;
}

/* Batching Controls */
.batching-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
}

.smart-batching-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
}

.batch-size-control label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.batch-size-control input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.9rem;
}

.batch-size-info {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.5rem;
  font-style: italic;
}

.article-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.article-summary h3 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 1.1rem;
}

.article-summary p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
}

.empty-batch-message {
  padding: 1rem;
  text-align: center;
  color: #94a3b8;
  font-style: italic;
  background: #f8fafc;
  border: 1px dashed #cbd5e1;
  border-radius: 4px;
  margin: 0.5rem 0;
}

.assign-batch-btn:disabled {
  background-color: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
}

.assign-all-btn:disabled {
  background-color: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
}

.refresh-batches-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.refresh-batches-btn:hover:not(:disabled) {
  background: #2563eb;
}

.refresh-batches-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Batches Container */
.batches-container {
  padding: 20px 24px;
  flex: 1;
}

.batches-container h3 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.batches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}

/* Batch Card */
.batch-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
  transition: all 0.2s;
}

.batch-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.batch-card.completed {
  border-color: #10b981;
  background: #f0fdf4;
}

.batch-card.failed {
  border-color: #ef4444;
  background: #fef2f2;
}

/* Validation Query Batch Styling (HIGHEST PRIORITY) */
.batch-card.validation-query-batch {
  border: 2px solid #e74c3c;
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
}

.batch-card.validation-query-batch:hover {
  box-shadow: 0 6px 16px rgba(231, 76, 60, 0.3);
}

.validation-query-badge {
  display: inline-block;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 8px;
  vertical-align: middle;
  box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

/* Premium Batch Styling (SECOND PRIORITY) */
.batch-card.premium-batch {
  border: 2px solid #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.batch-card.premium-batch:hover {
  box-shadow: 0 6px 16px rgba(245, 158, 11, 0.3);
}

.premium-badge {
  display: inline-block;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 8px;
  vertical-align: middle;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.batch-header h4 {
  margin: 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.article-count {
  background: #3b82f6;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.batch-journals {
  padding: 12px 16px;
  font-size: 0.9rem;
  color: #6b7280;
  border-bottom: 1px solid #f3f4f6;
}

.te-selection {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.te-selection label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.te-selection select {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.9rem;
  background: white;
}

/* Articles List */
.articles-list {
  min-height: 120px;
  max-height: 200px;
  overflow-y: auto;
  background: #fafbfc;
}

.articles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 0.9rem;
  color: #374151;
}

.article-actions {
  position: relative;
}

.move-articles-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background 0.2s;
}

.move-articles-btn:hover {
  background: #2563eb;
}

.move-options {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
}

.move-option {
  display: block;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 0.8rem;
  color: #374151;
  transition: background 0.2s;
}

.move-option:hover {
  background: #f3f4f6;
}

.article-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 4px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.85rem;
}

.article-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.article-item.selected {
  background: #eff6ff;
  border-color: #3b82f6;
}

.article-item input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.article-id {
  font-weight: 600;
  color: #374151;
}

.article-journal {
  color: #6b7280;
  font-size: 0.8rem;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Batch Actions */
.batch-actions {
  padding: 12px 16px;
  background: #f8fafc;
}

.assign-batch-btn {
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.assign-batch-btn:not(:disabled) {
  background: #10b981;
  color: white;
}

.assign-batch-btn:hover:not(:disabled) {
  background: #059669;
}

.assign-batch-btn:disabled {
  background: #9ca3af;
  color: white;
  cursor: not-allowed;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
}

.assign-all-btn {
  background: #059669;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 1rem;
}

.assign-all-btn:hover:not(:disabled) {
  background: #047857;
}

.assign-all-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.cancel-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.cancel-btn:hover {
  background: #4b5563;
}

/* Assignment Results */
.assignment-results {
  padding: 20px 24px;
  background: #f0fdf4;
  border-top: 1px solid #d1fae5;
}

.assignment-results h3 {
  margin: 0 0 12px 0;
  color: #065f46;
  font-size: 1.1rem;
}

.results-summary p {
  margin: 4px 0;
  color: #047857;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .smart-batching-modal {
    width: 98%;
    max-height: 95vh;
  }
  
  .batches-grid {
    grid-template-columns: 1fr;
  }
  
  .batching-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 12px;
  }
  
  .assign-all-btn,
  .cancel-btn {
    width: 100%;
  }
}
