# Enhanced login endpoint with comprehensive logging
import logging
import time
from datetime import datetime

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.FileHandler('flask_debug.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

@app.route('/api/auth/login', methods=['POST'])
def user_login():
    start_time = time.time()
    request_id = f"login_{int(start_time * 1000)}"
    
    logger.info(f"[{request_id}] === LOGIN REQUEST STARTED ===")
    logger.info(f"[{request_id}] Request method: {request.method}")
    logger.info(f"[{request_id}] Request headers: {dict(request.headers)}")
    logger.info(f"[{request_id}] Request remote_addr: {request.remote_addr}")
    
    try:
        # Step 1: Parse request data
        logger.info(f"[{request_id}] Step 1: Parsing request data...")
        data = request.get_json()
        logger.info(f"[{request_id}] Request data received: {data is not None}")
        
        if not data:
            logger.warning(f"[{request_id}] No JSON data in request")
            return jsonify({'error': 'No data provided'}), 400
            
        username = data.get('username')
        password = data.get('password')
        
        logger.info(f"[{request_id}] Username provided: {username is not None}")
        logger.info(f"[{request_id}] Password provided: {password is not None}")
        
        if not username or not password:
            logger.warning(f"[{request_id}] Missing username or password")
            return jsonify({'error': 'Username and password required'}), 400

        # Step 2: Database query
        logger.info(f"[{request_id}] Step 2: Querying database for user...")
        logger.info(f"[{request_id}] Looking for username: {username}")
        
        query_start = time.time()
        user = User.query.filter_by(username=username, is_active=True).first()
        query_time = time.time() - query_start
        
        logger.info(f"[{request_id}] Database query completed in {query_time:.3f}s")
        logger.info(f"[{request_id}] User found: {user is not None}")
        
        if not user:
            logger.warning(f"[{request_id}] User not found or inactive: {username}")
            return jsonify({'error': 'Invalid credentials'}), 401

        # Step 3: Password verification
        logger.info(f"[{request_id}] Step 3: Verifying password...")
        password_start = time.time()
        password_valid = user.check_password(password)
        password_time = time.time() - password_start
        
        logger.info(f"[{request_id}] Password verification completed in {password_time:.3f}s")
        logger.info(f"[{request_id}] Password valid: {password_valid}")
        
        if not password_valid:
            logger.warning(f"[{request_id}] Invalid password for user: {username}")
            return jsonify({'error': 'Invalid credentials'}), 401

        # Step 4: Session management
        logger.info(f"[{request_id}] Step 4: Setting up session...")
        session_start = time.time()
        
        session['user_id'] = user.id
        session['username'] = user.username
        session['user_role'] = user.role
        session.permanent = True
        
        # Backward compatibility for admin endpoints
        if user.role == 'Admin':
            session['admin_id'] = user.id
            session['admin_username'] = user.username
            
        session_time = time.time() - session_start
        logger.info(f"[{request_id}] Session setup completed in {session_time:.3f}s")
        logger.info(f"[{request_id}] Session data: user_id={user.id}, role={user.role}")

        # Step 5: Update last login
        logger.info(f"[{request_id}] Step 5: Updating last login...")
        update_start = time.time()
        
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        update_time = time.time() - update_start
        logger.info(f"[{request_id}] Last login update completed in {update_time:.3f}s")

        # Step 6: Prepare response
        logger.info(f"[{request_id}] Step 6: Preparing response...")
        response_start = time.time()
        
        user_dict = user.to_dict()
        response_data = {
            'message': 'Login successful',
            'user': user_dict
        }
        
        response_time = time.time() - response_start
        logger.info(f"[{request_id}] Response preparation completed in {response_time:.3f}s")
        
        total_time = time.time() - start_time
        logger.info(f"[{request_id}] === LOGIN REQUEST COMPLETED ===")
        logger.info(f"[{request_id}] Total time: {total_time:.3f}s")
        logger.info(f"[{request_id}] Breakdown - Query: {query_time:.3f}s, Password: {password_time:.3f}s, Session: {session_time:.3f}s, Update: {update_time:.3f}s, Response: {response_time:.3f}s")
        
        return jsonify(response_data), 200
        
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"[{request_id}] === LOGIN REQUEST FAILED ===")
        logger.error(f"[{request_id}] Error after {error_time:.3f}s: {str(e)}")
        logger.error(f"[{request_id}] Exception type: {type(e).__name__}")
        logger.exception(f"[{request_id}] Full traceback:")
        
        return jsonify({'error': 'Internal server error'}), 500

# Enhanced auth check endpoint
@app.route('/api/auth/check', methods=['GET'])
def check_auth():
    start_time = time.time()
    request_id = f"auth_check_{int(start_time * 1000)}"
    
    logger.info(f"[{request_id}] === AUTH CHECK STARTED ===")
    
    try:
        # Check for new user session first
        if 'user_id' in session:
            logger.info(f"[{request_id}] Found user_id in session: {session['user_id']}")
            
            query_start = time.time()
            user = User.query.get(session['user_id'])
            query_time = time.time() - query_start
            
            logger.info(f"[{request_id}] User query completed in {query_time:.3f}s")
            logger.info(f"[{request_id}] User found: {user is not None}")
            logger.info(f"[{request_id}] User active: {user.is_active if user else 'N/A'}")
            
            if user and user.is_active:
                total_time = time.time() - start_time
                logger.info(f"[{request_id}] Auth check successful in {total_time:.3f}s")
                return jsonify({
                    'authenticated': True,
                    'user': user.to_dict()
                }), 200

        # Backward compatibility check for admin session
        elif 'admin_id' in session:
            logger.info(f"[{request_id}] Found admin_id in session: {session['admin_id']}")
            
            query_start = time.time()
            user = User.query.get(session['admin_id'])
            query_time = time.time() - query_start
            
            logger.info(f"[{request_id}] Admin user query completed in {query_time:.3f}s")
            
            if user and user.is_active:
                total_time = time.time() - start_time
                logger.info(f"[{request_id}] Admin auth check successful in {total_time:.3f}s")
                return jsonify({
                    'authenticated': True,
                    'user': user.to_dict()
                }), 200
        else:
            logger.info(f"[{request_id}] No user session found")

        total_time = time.time() - start_time
        logger.info(f"[{request_id}] Auth check completed (not authenticated) in {total_time:.3f}s")
        return jsonify({'authenticated': False}), 200
        
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"[{request_id}] Auth check failed after {error_time:.3f}s: {str(e)}")
        logger.exception(f"[{request_id}] Full traceback:")
        
        return jsonify({'authenticated': False}), 200
