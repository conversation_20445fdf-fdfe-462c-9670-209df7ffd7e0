/**
 * TE Automation Service
 * Handles integration with TE_AUTOMATION data (author and copyright information)
 * Uses actual batch data from folder uploads
 */

// Store for batch data - populated during folder upload
let batchDataStore = new Map();

/**
 * Store batch data from folder upload
 * @param {Object} batchData - Batch summary data with articles
 */
export const storeBatchData = (batchData) => {
  if (batchData && batchData.articles) {
    batchData.articles.forEach(article => {
      batchDataStore.set(article.article_id, article);
    });
    console.log(`Stored batch data for ${batchData.articles.length} articles`);
  }
};

/**
 * Store individual article data
 * @param {string} articleId - Article identifier
 * @param {Object} articleData - Article data with authors and copyright info
 */
export const storeArticleData = (articleId, articleData) => {
  batchDataStore.set(articleId, articleData);
  console.log(`Stored data for article ${articleId}`);
};

/**
 * Fetch TE_AUTOMATION data for an article
 * @param {string} articleId - Article identifier
 * @returns {Promise<Object>} Author and copyright data
 */
export const fetchTeAutomationData = async (articleId) => {
  // Check if we have data in our store
  const data = batchDataStore.get(articleId);

  if (!data) {
    console.warn(`No TE_AUTOMATION data found for article ${articleId}`);
    return {
      article_id: articleId,
      journal: 'UNKNOWN',
      authors: []
    };
  }

  console.log(`Fetched TE_AUTOMATION data for ${articleId}:`, data);
  return data;
};

/**
 * Check if we have batch data for an article
 * @param {string} articleId - Article identifier
 * @returns {boolean} True if data is available
 */
export const hasBatchData = (articleId) => {
  return batchDataStore.has(articleId);
};

/**
 * Get all stored article IDs
 * @returns {Array} Array of article IDs
 */
export const getStoredArticleIds = () => {
  return Array.from(batchDataStore.keys());
};

/**
 * Clear batch data store
 */
export const clearBatchData = () => {
  batchDataStore.clear();
  console.log('Cleared batch data store');
};

/**
 * Extract author names from TE_AUTOMATION data
 * @param {Object} teData - TE_AUTOMATION data object
 * @returns {Array} Array of author names
 */
export const extractSystemAuthors = (teData) => {
  if (!teData || !teData.authors) {
    return [];
  }

  return teData.authors.map(author => author.name);
};

/**
 * Extract authors with copyright status from TE_AUTOMATION data
 * @param {Object} teData - TE_AUTOMATION data object
 * @returns {Array} Array of author objects with copyright status
 */
export const extractSystemAuthorsWithCopyright = (teData) => {
  if (!teData || !teData.authors) {
    return [];
  }

  return teData.authors.map(author => ({
    name: author.name,
    email: author.email,
    affiliation: author.affiliation,
    copyright_status: author.copyright_status
  }));
};

/**
 * Check if article has copyright issues
 * @param {Object} teData - TE_AUTOMATION data object
 * @returns {boolean} True if there are copyright issues
 */
export const hasCopyrightIssues = (teData) => {
  if (!teData || !teData.authors) {
    return false;
  }

  return teData.authors.some(author => 
    !author.copyright_status || author.copyright_status.toLowerCase() !== 'yes'
  );
};

/**
 * Get authors without copyright agreement
 * @param {Object} teData - TE_AUTOMATION data object
 * @returns {Array} Array of authors without copyright
 */
export const getAuthorsWithoutCopyright = (teData) => {
  if (!teData || !teData.authors) {
    return [];
  }

  return teData.authors.filter(author => 
    !author.copyright_status || author.copyright_status.toLowerCase() !== 'yes'
  );
};

/**
 * Get journal name from TE_AUTOMATION data
 * @param {Object} teData - TE_AUTOMATION data object
 * @returns {string} Journal name
 */
export const getJournalName = (teData) => {
  return teData?.journal || 'UNKNOWN';
};

/**
 * Validate if TE_AUTOMATION data is available for article
 * @param {string} articleId - Article identifier
 * @returns {Promise<boolean>} True if data is available
 */
export const isTeAutomationDataAvailable = async (articleId) => {
  try {
    const data = await fetchTeAutomationData(articleId);
    return data && data.authors && data.authors.length > 0;
  } catch (error) {
    console.error(`Error checking TE_AUTOMATION data for ${articleId}:`, error);
    return false;
  }
};

/**
 * Get summary of TE_AUTOMATION data for article
 * @param {string} articleId - Article identifier
 * @returns {Promise<Object>} Summary object
 */
export const getTeAutomationSummary = async (articleId) => {
  try {
    const data = await fetchTeAutomationData(articleId);
    
    return {
      article_id: articleId,
      journal: getJournalName(data),
      total_authors: data.authors?.length || 0,
      authors_with_copyright: data.authors?.filter(a => a.copyright_status?.toLowerCase() === 'yes').length || 0,
      authors_without_copyright: getAuthorsWithoutCopyright(data).length,
      has_copyright_issues: hasCopyrightIssues(data),
      data_available: data.authors?.length > 0
    };
  } catch (error) {
    console.error(`Error getting TE_AUTOMATION summary for ${articleId}:`, error);
    return {
      article_id: articleId,
      journal: 'UNKNOWN',
      total_authors: 0,
      authors_with_copyright: 0,
      authors_without_copyright: 0,
      has_copyright_issues: false,
      data_available: false,
      error: error.message
    };
  }
};
