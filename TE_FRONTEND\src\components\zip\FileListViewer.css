/* File List Viewer Styles */
.file-list-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 1.5rem;
}

/* Header */
.file-list-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.file-list-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #e5e7eb;
  color: #111827;
}

.file-list-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.25rem 0;
}

.article-icon {
  font-size: 1.25rem;
}

.file-list-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* File Categories */
.file-categories {
  margin-bottom: 2rem;
}

.file-category {
  margin-bottom: 2rem;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.category-title.clickable {
  cursor: pointer;
  user-select: none;
  padding: 0.5rem 0.75rem;
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  border-radius: 8px;
}

.category-title.clickable:hover {
  background: rgba(0, 0, 0, 0.03);
  color: #1f2937;
}

.category-title.clickable:active {
  background: rgba(0, 0, 0, 0.05);
}

.category-icon {
  font-size: 1rem;
  transition: transform 0.2s ease;
}

.toggle-icon {
  margin-left: auto;
  font-size: 0.9rem;
  color: #6b7280;
  transition: transform 0.2s ease;
}

/* File Grid */
.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
  animation: slideDown 0.3s ease-out;
  transform-origin: top;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.file-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
}

.file-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.file-item.disabled:hover {
  border-color: #e5e7eb;
  box-shadow: none;
  transform: none;
}

/* File Icon */
.file-icon-container {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  background: #f3f4f6;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-icon {
  font-size: 1.5rem;
}

/* File Details */
.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.file-type {
  font-weight: 500;
}

.file-size {
  color: #9ca3af;
}

/* File Actions */
.file-actions {
  flex-shrink: 0;
}

.preview-button {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.preview-button:hover {
  background: #1d4ed8;
}

.not-supported {
  font-size: 0.75rem;
  color: #9ca3af;
  font-style: italic;
}

/* Manual Entry Section */
.manual-entry-section {
  margin-bottom: 2rem;
}

.manual-entry-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 0.75rem;
  gap: 1rem;
}

.manual-entry-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.manual-entry-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.manual-entry-text h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #92400e;
}

.manual-entry-text p {
  margin: 0;
  font-size: 0.875rem;
  color: #b45309;
}

.manual-entry-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.manual-entry-button:hover {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.manual-entry-button svg {
  width: 1rem;
  height: 1rem;
}

/* Instructions */
.file-instructions {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  color: #475569;
}

.instruction-item:last-child {
  margin-bottom: 0;
}

.instruction-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .file-list-container {
    padding: 1rem;
  }
  
  .file-grid {
    grid-template-columns: 1fr;
  }
  
  .file-item {
    padding: 0.75rem;
  }
  
  .file-icon-container {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .file-icon {
    font-size: 1.25rem;
  }
  
  .file-list-title {
    font-size: 1.25rem;
  }
  
  .category-title {
    font-size: 1rem;
  }

  .manual-entry-card {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem;
  }

  .manual-entry-content {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .manual-entry-text h4 {
    font-size: 0.875rem;
  }

  .manual-entry-text p {
    font-size: 0.75rem;
  }

  .manual-entry-button {
    width: 100%;
    justify-content: center;
  }
}

/* Validation Warning Section */
.validation-warning-section {
  margin-bottom: 1.5rem;
}

.validation-warning-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.validation-warning-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  flex: 1;
}

.validation-warning-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.validation-warning-text {
  flex: 1;
}

.validation-warning-text h4 {
  color: #1e40af;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.validation-warning-text p {
  color: #1e3a8a;
  font-size: 0.875rem;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.validation-details {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #93c5fd;
}

.validation-details p {
  margin: 0.25rem 0;
  font-size: 0.8125rem;
}

.validation-details strong {
  color: #1e40af;
  font-weight: 600;
}

.validation-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.raise-query-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.raise-query-button:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.raise-query-button svg {
  width: 1rem;
  height: 1rem;
}

/* Animated pulse effect for raise query button */
.raise-query-button.animated-pulse {
  animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
  }
}



/* Query Modal Styles */
.query-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.query-modal {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.query-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.query-modal-header h3 {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.query-modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.query-modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.query-modal-close svg {
  width: 1.25rem;
  height: 1.25rem;
}

.query-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  color: #374151;
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
  line-height: 1.5;
}

.query-form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 0.75rem 1.5rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background: #e5e7eb;
}

.send-query-button {
  padding: 0.75rem 1.5rem;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-query-button:hover:not(:disabled) {
  background: #1d4ed8;
}

.send-query-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
