import React, { useState } from "react";
import { Icons } from "../common";
import {
  getDescriptionForSubject,
  getSuggestedSubject,
  SUBJECT_OPTIONS,
} from "../../utils/queryMessages";
import {
  sendValidationQueryEmail,
  validateEmailData,
  getDefaultRecipient,
} from "../../services/emailService";
import { getProductionEditorEmail } from "../../utils/productionEditorMapping";
import "./QueryFormModal.css";

const QueryFormModal = ({
  validationResult,
  articleId,
  onClose,
  zipFile = null,
  onQuerySent = null,
}) => {
  // Get mapped email or fallback
  const mappedEmail = getProductionEditorEmail(articleId);
  const initialTo = mappedEmail || getDefaultRecipient(validationResult);

  const [formData, setFormData] = useState({
    to: initialTo,
    cc: "<EMAIL>",
    subject: getSuggestedSubject(validationResult),
    description: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize description when component mounts or subject changes
  React.useEffect(() => {
    if (formData.subject) {
      setFormData((prev) => ({
        ...prev,
        description: getDescriptionForSubject(
          formData.subject,
          articleId,
          validationResult
        ),
      }));
    }
  }, [formData.subject, articleId, validationResult]);

  const handleSubjectChange = (e) => {
    const selectedSubject = e.target.value;
    setFormData((prev) => ({
      ...prev,
      subject: selectedSubject,
      description: getDescriptionForSubject(
        selectedSubject,
        articleId,
        validationResult
      ),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    const validation = validateEmailData({
      to: formData.to,
      subject: formData.subject,
      description: formData.description,
      articleId: articleId,
    });

    if (!validation.isValid) {
      alert(
        `Please fix the following errors:\n${validation.errors.join("\n")}`
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Send email using the email service
      const result = await sendValidationQueryEmail({
        to: formData.to,
        cc: formData.cc,
        subject: formData.subject,
        description: formData.description,
        articleId: articleId,
        validationResult: validationResult,
        zipFile: zipFile, // Attach ZIP file if provided
      });

      alert("Query email sent successfully!");
      console.log("Email sent:", result);

      // Notify parent component that query was sent
      if (onQuerySent) {
        onQuerySent();
      }

      onClose();
    } catch (error) {
      console.error("Error sending query email:", error);
      alert(`Failed to send query: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="query-modal-overlay" onClick={onClose}>
      <div className="query-form" onClick={(e) => e.stopPropagation()}>
        <div className="query-form-header">
          <h3>Send Validation Query</h3>
          <button onClick={onClose} className="close-button">
            <Icons.XMarkIcon />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Validation Information */}
          <div className="validation-info">
            <h4>Validation Issue Details</h4>
            <p>
              <strong>Article ID:</strong> {articleId}
            </p>
            {validationResult?.error_type && (
              <p>
                <strong>Error Type:</strong> {validationResult.error_type}
              </p>
            )}
            {validationResult?.error_message && (
              <p>
                <strong>Issue:</strong> {validationResult.error_message}
              </p>
            )}
            {validationResult?.files && (
              <p>
                <strong>Files in ZIP:</strong>{" "}
                {validationResult.files.join(", ")}
              </p>
            )}
          </div>

          {/* ZIP File Attachment Info */}
          {zipFile && (
            <div className="file-attachment-info">
              <span className="icon">📎</span>
              <div className="text">
                <strong>ZIP file will be attached:</strong>{" "}
                {zipFile.name || "Unknown file"}
                <br />
                <small>
                  The original ZIP file will be included with this query email
                  for review.
                </small>
              </div>
            </div>
          )}

          <div className="form-group">
            <label htmlFor="to">To:</label>
            <input
              type="email"
              id="to"
              value={formData.to}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, to: e.target.value }))
              }
              required
              readOnly={!!mappedEmail} // Read-only if mapped
              className={mappedEmail ? "input-readonly" : ""}
              title={mappedEmail ? "Auto-mapped based on Article ID" : ""}
            />
          </div>

          <div className="form-group">
            <label htmlFor="cc">CC:</label>
            <input
              type="email"
              id="cc"
              value={formData.cc}
              readOnly
              className="input-readonly"
            />
          </div>

          <div className="form-group">
            <label htmlFor="subject">Subject:</label>
            <select
              id="subject"
              value={formData.subject}
              onChange={handleSubjectChange}
              required
            >
              <option value="">Select a subject</option>
              {SUBJECT_OPTIONS.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="description">Description:</label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              rows={10}
              required
            />
          </div>

          <div className="form-actions">
            <button type="button" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </button>
            <button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Sending..." : "Send Query"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default QueryFormModal;
