/**
 * Manuscript Validation Utility
 * Validates that ZIP files contain exactly one manuscript file
 */

/**
 * Validates manuscript files in extracted ZIP contents
 * @param {Array} files - Array of file objects from ZIP extraction
 * @param {string} articleId - Article ID extracted from ZIP filename
 * @returns {Object} Validation result object
 */
export const validateManuscript = (files, articleId) => {
  try {
    // Find all manuscript files (case-insensitive search for "manuscript" in filename)
    const manuscriptFiles = files.filter(file => 
      file.name.toLowerCase().includes('manuscript') &&
      (file.name.toLowerCase().endsWith('.docx') || 
       file.name.toLowerCase().endsWith('.doc'))
    );

    // Create base result object
    const result = {
      article_id: articleId,
      status: 'ok',
      error_type: null,
      files: files.map(f => f.name),
      manuscript_files: manuscriptFiles.map(f => f.name),
      total_files: files.length,
      validation_timestamp: new Date().toISOString()
    };

    // Validation logic
    if (manuscriptFiles.length === 0) {
      result.status = 'error';
      result.error_type = 'no_manuscript';
      result.error_message = 'No manuscript files found in ZIP archive';
    } else if (manuscriptFiles.length > 1) {
      result.status = 'error';
      result.error_type = 'multiple_manuscripts';
      result.error_message = `Found ${manuscriptFiles.length} manuscript files, expected exactly 1`;
    }

    return result;
  } catch (error) {
    return {
      article_id: articleId,
      status: 'error',
      error_type: 'validation_failed',
      error_message: `Validation failed: ${error.message}`,
      files: [],
      manuscript_files: [],
      total_files: 0,
      validation_timestamp: new Date().toISOString()
    };
  }
};

/**
 * Logs validation results to console and potentially to a logging service
 * @param {Object} validationResult - Result from validateManuscript function
 */
export const logValidationResult = (validationResult) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    article_id: validationResult.article_id,
    status: validationResult.status,
    error_type: validationResult.error_type,
    error_message: validationResult.error_message,
    manuscript_count: validationResult.manuscript_files?.length || 0,
    total_files: validationResult.total_files
  };

  // Console logging for development
  if (validationResult.status === 'error') {
    console.error('❌ Manuscript Validation Failed:', logEntry);
  } else {
    console.log('✅ Manuscript Validation Passed:', logEntry);
  }

  // TODO: In production, send to logging service or backend endpoint
  // Example: await fetch('/api/validation-logs', { method: 'POST', body: JSON.stringify(logEntry) });

  return logEntry;
};

/**
 * Shows validation error popup to user
 * @param {Object} validationResult - Result from validateManuscript function
 */
export const showValidationError = (validationResult) => {
  if (validationResult.status !== 'error') return;

  let message = '';
  let title = 'Manuscript Validation Error';

  switch (validationResult.error_type) {
    case 'no_manuscript':
      title = 'No Manuscript Found';
      message = `Article ${validationResult.article_id} does not contain any manuscript files.\n\nExpected: Files containing "manuscript" in the filename\nFound: ${validationResult.files.join(', ')}`;
      break;
    case 'multiple_manuscripts':
      title = 'Multiple Manuscripts Found';
      message = `Article ${validationResult.article_id} contains ${validationResult.manuscript_files.length} manuscript files.\n\nExpected: Exactly 1 manuscript file\nFound: ${validationResult.manuscript_files.join(', ')}`;
      break;
    case 'validation_failed':
      title = 'Validation Failed';
      message = `Failed to validate article ${validationResult.article_id}.\n\nError: ${validationResult.error_message}`;
      break;
    default:
      message = `Unknown validation error for article ${validationResult.article_id}`;
  }

  // Log to console only - UI will handle display
  console.error(title, message);
};

/**
 * Extracts article ID from ZIP filename
 * @param {string} filename - ZIP filename (e.g., "12345.zip" or "article_123.zip")
 * @returns {string} Extracted article ID
 */
export const extractArticleId = (filename) => {
  if (!filename) return 'unknown';
  
  // Remove .zip extension
  const nameWithoutExt = filename.replace(/\.zip$/i, '');
  
  // Return the filename without extension as article ID
  return nameWithoutExt;
};

/**
 * Validates First Page (FP) files in extracted ZIP contents
 * @param {Array} files - Array of file objects from ZIP extraction
 * @param {string} articleId - Article ID extracted from ZIP filename
 * @param {string} manuscript_status - Status from manuscript validation ('ok' or 'error')
 * @returns {Object} FP validation result object
 */
export const validateFirstPage = (files, articleId, manuscript_status) => {
  try {
    // Find all FP files (case-insensitive search for "FP", "FirstPage", "TitlePage", or "Title Page" in filename)
    const fpFiles = files.filter(file => {
      const fileName = file.name.toLowerCase();
      return (fileName.includes('fp') ||
              fileName.includes('firstpage') ||
              fileName.includes('titlepage') ||
              fileName.includes('title page')) &&
             (fileName.endsWith('.docx') || fileName.endsWith('.doc'));
    });

    // Create base result object
    const result = {
      article_id: articleId,
      status: 'ok',
      error_type: null,
      files: files.map(f => f.name),
      fp_files: fpFiles.map(f => f.name),
      total_files: files.length,
      manuscript_status: manuscript_status,
      validation_timestamp: new Date().toISOString()
    };

    // Validation logic based on manuscript status
    if (manuscript_status === 'error') {
      // If manuscript is missing/invalid, check if FP is also missing
      if (fpFiles.length === 0) {
        result.status = 'error';
        result.error_type = 'no_manuscript_and_fp';
        result.error_message = 'Both manuscript and First Page files are missing';
      } else {
        // Manuscript has issues but FP exists - this is handled by manuscript validation
        result.status = 'ok'; // FP validation passes, manuscript issues handled separately
        result.error_message = null;
      }
    } else {
      // Manuscript is OK, check FP
      if (fpFiles.length === 0) {
        result.status = 'error';
        result.error_type = 'missing_fp';
        result.error_message = 'First Page file is missing (manuscript exists)';
      } else if (fpFiles.length > 1) {
        result.status = 'error';
        result.error_type = 'multiple_fp';
        result.error_message = `Found ${fpFiles.length} First Page files, expected exactly 1`;
      }
    }

    return result;
  } catch (error) {
    return {
      article_id: articleId,
      status: 'error',
      error_type: 'fp_validation_failed',
      error_message: `FP validation failed: ${error.message}`,
      files: [],
      fp_files: [],
      total_files: 0,
      manuscript_status: manuscript_status,
      validation_timestamp: new Date().toISOString()
    };
  }
};

/**
 * Combined validation workflow function
 * Runs both manuscript and FP validation
 * @param {Array} files - Array of file objects from ZIP extraction
 * @param {string} zipFilename - Original ZIP filename
 * @returns {Object} Combined validation result
 */
export const runCompleteValidation = (files, zipFilename) => {
  const articleId = extractArticleId(zipFilename);

  // Step 1: Run manuscript validation
  const manuscriptResult = validateManuscript(files, articleId);

  // Step 2: Run FP validation with manuscript status
  const fpResult = validateFirstPage(files, articleId, manuscriptResult.status);

  // Step 3: Combine results
  const combinedResult = {
    article_id: articleId,
    manuscript_validation: manuscriptResult,
    fp_validation: fpResult,
    overall_status: (manuscriptResult.status === 'ok' && fpResult.status === 'ok') ? 'ok' : 'error',
    can_proceed: true, // Always allow proceeding - validation is informational
    files: files.map(f => f.name),
    total_files: files.length,
    validation_timestamp: new Date().toISOString()
  };

  // Determine primary error for UI display
  if (manuscriptResult.status === 'error' && fpResult.status === 'error') {
    // Both have errors - prioritize based on error type
    if (fpResult.error_type === 'no_manuscript_and_fp') {
      combinedResult.primary_error = fpResult;
    } else {
      combinedResult.primary_error = manuscriptResult;
      combinedResult.secondary_error = fpResult;
    }
  } else if (manuscriptResult.status === 'error') {
    combinedResult.primary_error = manuscriptResult;
  } else if (fpResult.status === 'error') {
    combinedResult.primary_error = fpResult;
  }

  // Log the results
  logValidationResult(manuscriptResult);
  logValidationResult(fpResult);

  return combinedResult;
};

/**
 * Main validation workflow function (backward compatibility)
 * @param {Array} files - Array of file objects from ZIP extraction
 * @param {string} zipFilename - Original ZIP filename
 * @returns {Object} Validation result with additional workflow info
 */
export const runManuscriptValidation = (files, zipFilename) => {
  // Use the new complete validation but return in old format for compatibility
  const completeResult = runCompleteValidation(files, zipFilename);

  // Return primary error in old format for existing UI components
  if (completeResult.primary_error) {
    return {
      ...completeResult.primary_error,
      log_entry: null,
      can_proceed: true // Always allow proceeding
    };
  }

  // No errors - return success
  return {
    article_id: completeResult.article_id,
    status: 'ok',
    error_type: null,
    files: completeResult.files,
    manuscript_files: completeResult.manuscript_validation.manuscript_files,
    total_files: completeResult.total_files,
    validation_timestamp: completeResult.validation_timestamp,
    log_entry: null,
    can_proceed: true
  };
};

/**
 * Validates author information in article metadata
 * @param {Object} articleMetadata - Article metadata object
 * @param {string} articleId - Article ID
 * @returns {Object} Author validation result object
 */
export const validateAuthorDetails = (articleMetadata, articleId) => {
  try {
    const result = {
      article_id: articleId,
      status: 'ok',
      error_type: null,
      validation_timestamp: new Date().toISOString(),
      missing_details: []
    };

    const authors = articleMetadata?.authors || [];

    if (authors.length === 0) {
      result.status = 'error';
      result.error_type = 'no_authors';
      result.error_message = 'No author information found';
      return result;
    }

    // Check for missing author details
    authors.forEach((author, index) => {
      if (!author.name || author.name.trim() === '') {
        result.missing_details.push(`Author ${index + 1}: Missing name`);
      }
      if (!author.email || author.email.trim() === '') {
        result.missing_details.push(`Author ${index + 1}: Missing email`);
      }
    });

    if (result.missing_details.length > 0) {
      result.status = 'error';
      result.error_type = 'missing_author_details';
      result.error_message = `Missing author details: ${result.missing_details.join(', ')}`;
    }

    return result;
  } catch (error) {
    return {
      article_id: articleId,
      status: 'error',
      error_type: 'author_validation_failed',
      error_message: `Author validation failed: ${error.message}`,
      missing_details: [],
      validation_timestamp: new Date().toISOString()
    };
  }
};

/**
 * Validates author affiliation information
 * @param {Object} articleMetadata - Article metadata object
 * @param {string} articleId - Article ID
 * @returns {Object} Affiliation validation result object
 */
export const validateAuthorAffiliations = (articleMetadata, articleId) => {
  try {
    const result = {
      article_id: articleId,
      status: 'ok',
      error_type: null,
      validation_timestamp: new Date().toISOString(),
      missing_affiliations: []
    };

    const authors = articleMetadata?.authors || [];

    if (authors.length === 0) {
      result.status = 'error';
      result.error_type = 'no_authors';
      result.error_message = 'No author information found for affiliation validation';
      return result;
    }

    // Check for missing affiliations
    authors.forEach((author, index) => {
      if (!author.affiliation || author.affiliation.trim() === '') {
        result.missing_affiliations.push(`Author ${index + 1} (${author.name || 'Unknown'}): Missing affiliation`);
      }
    });

    if (result.missing_affiliations.length > 0) {
      result.status = 'error';
      result.error_type = 'missing_affiliations';
      result.error_message = `Missing affiliations: ${result.missing_affiliations.join(', ')}`;
    }

    return result;
  } catch (error) {
    return {
      article_id: articleId,
      status: 'error',
      error_type: 'affiliation_validation_failed',
      error_message: `Affiliation validation failed: ${error.message}`,
      missing_affiliations: [],
      validation_timestamp: new Date().toISOString()
    };
  }
};

/**
 * Validates corresponding author information
 * @param {Object} articleMetadata - Article metadata object
 * @param {string} articleId - Article ID
 * @returns {Object} Corresponding author validation result object
 */
export const validateCorrespondingAuthor = (articleMetadata, articleId) => {
  try {
    const result = {
      article_id: articleId,
      status: 'ok',
      error_type: null,
      validation_timestamp: new Date().toISOString(),
      missing_details: []
    };

    const authors = articleMetadata?.authors || [];
    const correspondingAuthors = authors.filter(author => author.isCorresponding === true);

    if (correspondingAuthors.length === 0) {
      result.status = 'error';
      result.error_type = 'no_corresponding_author';
      result.error_message = 'No corresponding author designated';
      return result;
    }

    if (correspondingAuthors.length > 1) {
      result.status = 'error';
      result.error_type = 'multiple_corresponding_authors';
      result.error_message = `Multiple corresponding authors found: ${correspondingAuthors.length}`;
      return result;
    }

    // Validate corresponding author details
    const corrAuthor = correspondingAuthors[0];
    if (!corrAuthor.email || corrAuthor.email.trim() === '') {
      result.missing_details.push('Missing email address');
    }
    if (!corrAuthor.address || corrAuthor.address.trim() === '') {
      result.missing_details.push('Missing mailing address');
    }

    if (result.missing_details.length > 0) {
      result.status = 'error';
      result.error_type = 'missing_corresponding_details';
      result.error_message = `Corresponding author missing: ${result.missing_details.join(', ')}`;
    }

    return result;
  } catch (error) {
    return {
      article_id: articleId,
      status: 'error',
      error_type: 'corresponding_validation_failed',
      error_message: `Corresponding author validation failed: ${error.message}`,
      missing_details: [],
      validation_timestamp: new Date().toISOString()
    };
  }
};
