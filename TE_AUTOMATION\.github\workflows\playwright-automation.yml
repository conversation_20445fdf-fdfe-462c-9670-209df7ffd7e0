name: TE Playwright Automation

on:
  # Manual trigger with custom parameters
  workflow_dispatch:
    inputs:
      workflow_type:
        description: 'Workflow Type'
        required: true
        default: 'download'
        type: choice
        options:
          - download
          - upload
          - download_and_upload
      status:
        description: 'Article Status (for download)'
        required: false
        default: 'Yet-to-Start'
        type: choice
        options:
          - Yet-to-Start
          - In Progress
          - Completed
      mode:
        description: 'Download Mode (for download)'
        required: false
        default: 'normal'
        type: choice
        options:
          - normal
          - download_articles_by_ids
      article_ids:
        description: 'Article IDs (comma-separated, only for by-IDs mode)'
        required: false
        default: ''
        type: string
      batch_folder:
        description: 'Batch Folder to Upload (e.g., 21-12-2025/batch-1766297173961-1)'
        required: false
        default: ''
        type: string

  # Scheduled trigger (optional - runs daily at 9 AM UTC)
  schedule:
    - cron: '0 9 * * *'  # Daily at 9 AM UTC (2:30 PM IST)

jobs:
  playwright-automation:
    runs-on: ubuntu-latest
    timeout-minutes: 90  # 1.5 hours timeout
    
    steps:
      - name: 🔍 Checkout Repository
        uses: actions/checkout@v4
      
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      
      - name: 📥 Install Dependencies
        run: npm install
      
      - name: 🎭 Install Playwright Browsers
        run: |
          npx playwright install chromium
          npx playwright install-deps chromium || true
      
      - name: ⚙️ Setup Environment Variables
        run: |
          # Use system date (DD-MM-YYYY format)
          TARGET_DATE=$(date +'%d-%m-%Y')

          # Get workflow type and other inputs
          WORKFLOW_TYPE="${{ github.event.inputs.workflow_type || 'download_and_upload' }}"
          ARTICLE_STATUS="${{ github.event.inputs.status || 'Yet-to-Start' }}"
          DOWNLOAD_MODE="${{ github.event.inputs.mode || 'normal' }}"
          ARTICLE_IDS="${{ github.event.inputs.article_ids || '' }}"
          BATCH_FOLDER="${{ github.event.inputs.batch_folder || '' }}"

          echo "TARGET_DATE=$TARGET_DATE" >> $GITHUB_ENV
          echo "WORKFLOW_TYPE=$WORKFLOW_TYPE" >> $GITHUB_ENV
          echo "ARTICLE_STATUS=$ARTICLE_STATUS" >> $GITHUB_ENV
          echo "DOWNLOAD_MODE=$DOWNLOAD_MODE" >> $GITHUB_ENV
          echo "ARTICLE_IDS=$ARTICLE_IDS" >> $GITHUB_ENV
          echo "BATCH_FOLDER=$BATCH_FOLDER" >> $GITHUB_ENV

          # Create .env file
          cat > .env << EOF
          TE_USERNAME=${{ secrets.TE_USERNAME }}
          TE_PASSWORD=${{ secrets.TE_PASSWORD }}
          TE_LOGIN_URL=https://production.jow.medknow.com/login
          TE_MYTASK_URL=https://production.jow.medknow.com/mytask
          INCOMING_DIR=./incoming
          DOWNLOADS_DIR=./downloads
          TARGET_DATE=$TARGET_DATE
          ARTICLE_STATUS=$ARTICLE_STATUS
          DOWNLOAD_MODE=$DOWNLOAD_MODE
          ARTICLE_IDS=$ARTICLE_IDS
          GOOGLE_SHEETS_ID=${{ secrets.GOOGLE_SHEETS_ID }}
          GOOGLE_SHEETS_PAGE=${{ secrets.GOOGLE_SHEETS_PAGE }}
          GOOGLE_SHEETS_SERVICE_ACCOUNT=${{ secrets.GOOGLE_SHEETS_SERVICE_ACCOUNT }}
          GOOGLE_SHEETS_PRIVATE_KEY=${{ secrets.GOOGLE_SHEETS_PRIVATE_KEY }}
          EOF

          # Create Google Drive service account JSON file
          if [ -z "${{ secrets.GOOGLE_DRIVE_SERVICE_ACCOUNT_JSON }}" ]; then
            echo "❌ ERROR: GOOGLE_DRIVE_SERVICE_ACCOUNT_JSON secret is not set!"
            echo "Please add this secret in GitHub repository settings:"
            echo "https://github.com/${{ github.repository }}/settings/secrets/actions"
            exit 1
          fi

          echo '${{ secrets.GOOGLE_DRIVE_SERVICE_ACCOUNT_JSON }}' > service_account.json

          # Validate JSON file
          FILE_SIZE=$(wc -c < service_account.json)
          echo "📊 Service account file size: $FILE_SIZE bytes"

          if [ "$FILE_SIZE" -lt 100 ]; then
            echo "❌ ERROR: service_account.json is too small ($FILE_SIZE bytes)!"
            echo "The secret appears to be empty or incomplete."
            echo "First 200 chars: $(head -c 200 service_account.json)"
            exit 1
          fi

          if ! jq empty service_account.json 2>/dev/null; then
            echo "❌ ERROR: service_account.json is not valid JSON!"
            echo "First 200 chars: $(head -c 200 service_account.json)"
            exit 1
          fi

          echo "✅ Environment configured:"
          echo "  🎯 Workflow Type: $WORKFLOW_TYPE"
          echo "  📅 Date: $TARGET_DATE (system date)"
          echo "  📊 Status: $ARTICLE_STATUS"
          echo "  🔧 Mode: $DOWNLOAD_MODE"
          echo "  📝 Article IDs: $ARTICLE_IDS"
          echo "  📁 Batch Folder: $BATCH_FOLDER"
          echo "  🔑 Service account file created and validated ($FILE_SIZE bytes)"
      
      - name: 🎭 Run Download Automation
        if: env.WORKFLOW_TYPE == 'download' || env.WORKFLOW_TYPE == 'download_and_upload'
        run: |
          echo "🚀 Starting Playwright Download Automation..."
          npx playwright test tests/download.spec.js --reporter=list
        continue-on-error: false

      - name: 📊 Check Download Results
        if: env.WORKFLOW_TYPE == 'download' || env.WORKFLOW_TYPE == 'download_and_upload'
        id: check_download
        run: |
          echo "📁 Checking downloaded files..."
          if [ -d "incoming" ]; then
            echo "✅ Incoming directory exists"

            # Find all batch folders
            BATCH_FOLDERS=$(find incoming -mindepth 2 -maxdepth 2 -type d | sort)
            echo "📦 Available batch folders:"
            echo "$BATCH_FOLDERS"

            # Get the latest batch folder
            LATEST_BATCH=$(echo "$BATCH_FOLDERS" | tail -1)
            echo "LATEST_BATCH=$LATEST_BATCH" >> $GITHUB_ENV

            # Count files in latest batch
            ZIP_COUNT=$(find "$LATEST_BATCH" -type f -name "*.zip" 2>/dev/null | wc -l)
            echo "📦 Latest batch: $LATEST_BATCH"
            echo "📦 ZIP files in latest batch: $ZIP_COUNT"
            echo "ZIP_COUNT=$ZIP_COUNT" >> $GITHUB_ENV
          else
            echo "❌ No incoming directory found"
            exit 1
          fi

      - name: ⏳ Wait Before Upload
        if: env.WORKFLOW_TYPE == 'download_and_upload'
        run: |
          echo "⏳ Waiting 10 seconds before starting upload..."
          sleep 10

      - name: ☁️ Run Drive Upload Automation
        if: env.WORKFLOW_TYPE == 'upload' || env.WORKFLOW_TYPE == 'download_and_upload'
        id: drive_upload
        run: |
          # Determine which batch to upload
          if [ "${{ env.WORKFLOW_TYPE }}" == "upload" ] && [ -n "${{ env.BATCH_FOLDER }}" ]; then
            # Upload specific batch folder
            UPLOAD_BATCH="incoming/${{ env.BATCH_FOLDER }}"
            echo "🚀 Uploading specific batch: $UPLOAD_BATCH"
          else
            # Upload latest batch from download
            UPLOAD_BATCH="${{ env.LATEST_BATCH }}"
            echo "🚀 Uploading latest batch: $UPLOAD_BATCH"
          fi

          # Set environment variable for upload script
          export BATCH_TO_UPLOAD="$UPLOAD_BATCH"
          echo "UPLOAD_BATCH=$UPLOAD_BATCH" >> $GITHUB_ENV

          echo "☁️ Starting Google Drive Upload..."
          npx playwright test tests/drive-upload.spec.js --reporter=list
        continue-on-error: false
      
      - name: 📊 Extract Upload Summary
        if: (env.WORKFLOW_TYPE == 'upload' || env.WORKFLOW_TYPE == 'download_and_upload') && success()
        id: extract_summary
        run: |
          # Find the batch_summary.json in the uploaded batch
          BATCH_SUMMARY=$(find "${{ env.UPLOAD_BATCH }}" -name "batch_summary.json" 2>/dev/null | head -1)

          if [ -f "$BATCH_SUMMARY" ]; then
            echo "📄 Found batch summary: $BATCH_SUMMARY"

            # Extract upload statistics
            UPLOADED_COUNT=$(cat "$BATCH_SUMMARY" | grep -o '"drive_upload_count"[[:space:]]*:[[:space:]]*[0-9]*' | grep -o '[0-9]*$')
            FAILED_COUNT=$(cat "$BATCH_SUMMARY" | grep -o '"drive_upload_failed"[[:space:]]*:[[:space:]]*[0-9]*' | grep -o '[0-9]*$')

            echo "UPLOADED_COUNT=${UPLOADED_COUNT:-0}" >> $GITHUB_ENV
            echo "FAILED_COUNT=${FAILED_COUNT:-0}" >> $GITHUB_ENV

            # Use the dump folder ID from environment variable (hardcoded in fileUpload.js)
            # Default: 1xHhAfSIgcX5k7LQStSvszlQmN7OP52di
            DUMP_FOLDER_ID="${{ secrets.GOOGLE_DRIVE_DUMP_FOLDER_ID }}"
            if [ -z "$DUMP_FOLDER_ID" ]; then
              DUMP_FOLDER_ID="1xHhAfSIgcX5k7LQStSvszlQmN7OP52di"
            fi

            DRIVE_LINK="https://drive.google.com/drive/folders/$DUMP_FOLDER_ID"
            echo "DRIVE_LINK=$DRIVE_LINK" >> $GITHUB_ENV
            echo "📁 Drive Folder Link: $DRIVE_LINK"
            echo "📊 Uploaded: ${UPLOADED_COUNT:-0}, Failed: ${FAILED_COUNT:-0}"

            # Extract batch name and date
            BATCH_NAME=$(basename "${{ env.UPLOAD_BATCH }}")
            BATCH_DATE=$(basename $(dirname "${{ env.UPLOAD_BATCH }}"))
            echo "BATCH_NAME=$BATCH_NAME" >> $GITHUB_ENV
            echo "BATCH_DATE=$BATCH_DATE" >> $GITHUB_ENV
          else
            echo "⚠️ No batch_summary.json found"
            echo "UPLOADED_COUNT=0" >> $GITHUB_ENV
            echo "FAILED_COUNT=0" >> $GITHUB_ENV
          fi

      - name: 📧 Send Email Notification
        if: (env.WORKFLOW_TYPE == 'upload' || env.WORKFLOW_TYPE == 'download_and_upload') && success()
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.SMTP_SERVER }}
          server_port: ${{ secrets.SMTP_PORT }}
          username: ${{ secrets.SMTP_USERNAME }}
          password: ${{ secrets.SMTP_PASSWORD }}
          subject: "✅ TE Articles Uploaded - ${{ env.BATCH_DATE }} - Batch ${{ env.BATCH_NAME }}"
          to: <EMAIL>, <EMAIL>
          from: ${{ secrets.SMTP_USERNAME }}
          html_body: |
            <!DOCTYPE html>
            <html>
            <head>
              <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
                .content { background: #f9f9f9; padding: 20px; border: 1px solid #ddd; }
                .info-box { background: white; padding: 15px; margin: 10px 0; border-left: 4px solid #4CAF50; }
                .stats { display: flex; justify-content: space-around; margin: 15px 0; }
                .stat-item { text-align: center; padding: 10px; }
                .stat-number { font-size: 32px; font-weight: bold; color: #4CAF50; }
                .stat-label { font-size: 14px; color: #666; }
                .button { display: inline-block; padding: 12px 24px; background: #4CAF50; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="header">
                  <h1>✅ TE Articles Uploaded Successfully</h1>
                </div>
                <div class="content">
                  <p>Hello,</p>
                  <p>The TE article automation has completed successfully. The articles have been uploaded to Google Drive.</p>

                  <div class="info-box">
                    <h3>📊 Upload Summary</h3>
                    <div class="stats">
                      <div class="stat-item">
                        <div class="stat-number">${{ env.UPLOADED_COUNT }}</div>
                        <div class="stat-label">✅ Uploaded</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-number">${{ env.FAILED_COUNT }}</div>
                        <div class="stat-label">❌ Failed</div>
                      </div>
                    </div>
                    <p><strong>Date:</strong> ${{ env.BATCH_DATE }}</p>
                    <p><strong>Batch:</strong> ${{ env.BATCH_NAME }}</p>
                  </div>

                  <div class="info-box">
                    <h3>📁 Google Drive Link</h3>
                    <p>Click the button below to access the uploaded files:</p>
                    <a href="${{ env.DRIVE_LINK }}" class="button">📂 Open Drive Folder</a>
                    <p style="font-size: 12px; color: #666;">Direct link: ${{ env.DRIVE_LINK }}</p>
                  </div>

                  <div class="info-box">
                    <h3>📋 Next Steps</h3>
                    <ol>
                      <li>Download the articles from the Drive folder</li>
                      <li>Process the batch in the TE Frontend</li>
                      <li>Assign articles to Technical Editors</li>
                    </ol>
                  </div>

                  <p><strong>Batch Folder Path:</strong> <code>${{ env.UPLOAD_BATCH }}</code></p>
                  <p><strong>Workflow Run:</strong> <a href="${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}">View Details</a></p>
                </div>
                <div class="footer">
                  <p>This is an automated message from TE Automation System</p>
                  <p>Powered by GitHub Actions</p>
                </div>
              </div>
            </body>
            </html>

      - name: �📤 Upload Artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: automation-results-${{ github.run_number }}
          path: |
            incoming/
            playwright-report/
            test-results/
          retention-days: 7
      
      - name: 📋 Generate Summary
        if: always()
        run: |
          echo "## 🎭 TE Automation Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Configuration:**" >> $GITHUB_STEP_SUMMARY
          echo "- � Workflow Type: ${{ env.WORKFLOW_TYPE }}" >> $GITHUB_STEP_SUMMARY
          echo "- �📅 Date: ${{ env.TARGET_DATE }} (system date)" >> $GITHUB_STEP_SUMMARY
          echo "- 📊 Status: ${{ env.ARTICLE_STATUS }}" >> $GITHUB_STEP_SUMMARY
          echo "- 🔧 Mode: ${{ env.DOWNLOAD_MODE }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ -n "${{ env.LATEST_BATCH }}" ]; then
            echo "**Download Results:**" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ Batch: \`${{ env.LATEST_BATCH }}\`" >> $GITHUB_STEP_SUMMARY
            echo "- 📦 Articles: ${{ env.ZIP_COUNT }} files" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi

          if [ -n "${{ env.DRIVE_LINK }}" ]; then
            echo "**Upload Results:**" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ Uploaded to Drive: [${{ env.BATCH_NAME }}](${{ env.DRIVE_LINK }})" >> $GITHUB_STEP_SUMMARY
            echo "- 📧 Email sent to: <EMAIL>, <EMAIL>" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**Next Steps:**" >> $GITHUB_STEP_SUMMARY
            echo "1. Check email for Drive link" >> $GITHUB_STEP_SUMMARY
            echo "2. Download articles from Drive" >> $GITHUB_STEP_SUMMARY
            echo "3. Process batch in TE Frontend" >> $GITHUB_STEP_SUMMARY
            echo "4. Assign to Technical Editors" >> $GITHUB_STEP_SUMMARY
          fi
      
      - name: 🔔 Notify on Failure
        if: failure()
        run: |
          echo "❌ Automation failed! Check the logs for details."
          echo "Run URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

