#!/usr/bin/env python3
"""
Database Migration Script
Merges data from article_references_backup.db (old server data) into article_references.db (new schema)
"""
import sqlite3
import shutil
import os
import json
from datetime import datetime

def backup_current_database():
    """Create a backup of the current database with new schema"""
    backup_path = "TE_BACK/schema_article_references.db"
    current_path = "TE_BACK/article_references.db"
    
    print(f"Creating schema backup: {backup_path}")
    shutil.copy2(current_path, backup_path)
    print("✓ Schema backup created successfully")
    return backup_path

def get_table_columns(cursor, table_name):
    """Get column information for a table"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    return {col[1]: {'type': col[2], 'notnull': col[3], 'default': col[4], 'pk': col[5]} for col in columns}

def migrate_table_data(source_conn, target_conn, table_name):
    """Migrate data from source table to target table"""
    print(f"\n--- Migrating table: {table_name} ---")
    
    source_cursor = source_conn.cursor()
    target_cursor = target_conn.cursor()
    
    # Get column information from both databases
    source_columns = get_table_columns(source_cursor, table_name)
    target_columns = get_table_columns(target_cursor, table_name)
    
    print(f"Source columns: {list(source_columns.keys())}")
    print(f"Target columns: {list(target_columns.keys())}")
    
    # Find common columns
    common_columns = set(source_columns.keys()) & set(target_columns.keys())
    print(f"Common columns: {list(common_columns)}")
    
    # Get existing data count in target
    target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    existing_count = target_cursor.fetchone()[0]
    print(f"Existing rows in target: {existing_count}")
    
    # Get source data count
    source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    source_count = source_cursor.fetchone()[0]
    print(f"Rows to migrate from source: {source_count}")
    
    if existing_count > 0:
        print(f"⚠️  Target table already has {existing_count} rows")
        response = input(f"Do you want to clear existing data in {table_name}? (y/N): ")
        if response.lower() == 'y':
            target_cursor.execute(f"DELETE FROM {table_name}")
            print(f"✓ Cleared existing data from {table_name}")
        else:
            print(f"⚠️  Keeping existing data. New data will be appended.")
    
    # Fetch all data from source
    column_list = list(common_columns)
    columns_str = ", ".join(column_list)
    
    source_cursor.execute(f"SELECT {columns_str} FROM {table_name}")
    rows = source_cursor.fetchall()
    
    if not rows:
        print(f"No data to migrate from {table_name}")
        return
    
    # Prepare insert statement
    placeholders = ", ".join(["?" for _ in column_list])
    insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
    
    # Insert data
    try:
        target_cursor.executemany(insert_sql, rows)
        print(f"✓ Migrated {len(rows)} rows to {table_name}")
    except Exception as e:
        print(f"❌ Error migrating {table_name}: {e}")
        raise

def main():
    """Main migration function"""
    print("=== Database Migration Script ===")
    print("Merging article_references_backup.db into article_references.db")
    
    # Paths
    backup_db_path = "TE_BACK/article_references_backup.db"
    current_db_path = "TE_BACK/article_references.db"
    
    # Verify files exist
    if not os.path.exists(backup_db_path):
        print(f"❌ Backup database not found: {backup_db_path}")
        return
    
    if not os.path.exists(current_db_path):
        print(f"❌ Current database not found: {current_db_path}")
        return
    
    # Create schema backup
    schema_backup_path = backup_current_database()
    
    try:
        # Connect to databases
        print("\nConnecting to databases...")
        source_conn = sqlite3.connect(backup_db_path)
        target_conn = sqlite3.connect(current_db_path)
        
        # Get list of tables to migrate (common tables)
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        source_tables = {row[0] for row in source_cursor.fetchall()}
        
        target_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        target_tables = {row[0] for row in target_cursor.fetchall()}
        
        common_tables = source_tables & target_tables
        print(f"\nTables to migrate: {list(common_tables)}")
        
        # Migrate each table
        for table_name in sorted(common_tables):
            migrate_table_data(source_conn, target_conn, table_name)
        
        # Commit changes
        target_conn.commit()
        print("\n✓ All changes committed successfully")
        
        # Verify migration
        print("\n=== Migration Verification ===")
        for table_name in sorted(common_tables):
            target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            final_count = target_cursor.fetchone()[0]
            print(f"{table_name}: {final_count} rows")
        
        # Close connections
        source_conn.close()
        target_conn.close()
        
        print(f"\n🎉 Migration completed successfully!")
        print(f"📁 Schema backup saved as: {schema_backup_path}")
        print(f"📁 Merged database: {current_db_path}")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        print(f"🔄 You can restore the original schema from: {schema_backup_path}")
        raise

if __name__ == "__main__":
    main()
