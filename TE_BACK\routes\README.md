# Routes Directory

This directory contains modular route handlers for the TE Backend application.

## Structure

### `word_plugin_routes.py`
Contains all API endpoints for the Word Office Add-in integration.

**Endpoints:**
- `POST /api/word-plugin/auth` - User authentication
- `GET /api/word-plugin/article/<article_id>` - Get article data with references
- `POST /api/word-plugin/execute-rule` - Execute specific editing rule
- `POST /api/word-plugin/save-changes` - Save changes made in Word

**Features:**
- Session-based authentication
- Fetches references from database (`ArticleReference` table)
- Rule execution logic for validation
- Integrates with existing TE assignment workflow

**Important Note:**
- Editing rules are **NOT** defined in the backend
- Rules are defined in the frontend: `src/constants/editingRules.js`
- The Word Plugin imports rules from: `src/taskpane/constants/editingRules.js`
- Backend only executes validation logic when rules are run

**Rule Categories (defined in frontend):**
1. Running Head
2. Title
3. Authors
4. Abstract
5. References
6. XML Consistency
7. Formatting
8. General (AI-powered)

## Usage

### Registering Routes in `app.py`

```python
from routes.word_plugin_routes import init_word_plugin_routes

# After database models are defined
word_plugin_blueprint = init_word_plugin_routes(db, User, ArticleFile, ArticleReference, TEAssignment)
app.register_blueprint(word_plugin_blueprint)
```

### Adding New Routes

1. Create a new route file in this directory
2. Define a Blueprint or initialization function
3. Import and register in `app.py`

Example:
```python
# routes/my_new_routes.py
from flask import Blueprint

my_bp = Blueprint('my_routes', __name__, url_prefix='/api/my-routes')

@my_bp.route('/endpoint', methods=['GET'])
def my_endpoint():
    return {'message': 'Hello'}
```

```python
# app.py
from routes.my_new_routes import my_bp
app.register_blueprint(my_bp)
```

## Benefits of Modular Routes

1. **Separation of Concerns** - Each feature has its own file
2. **Easier Maintenance** - Changes are isolated to specific modules
3. **Better Organization** - Clear structure for large applications
4. **Reusability** - Routes can be easily moved or shared
5. **Testing** - Individual route modules can be tested independently

## Dependencies

All route modules depend on:
- Flask (Blueprint, request, jsonify)
- Database models (User, ArticleFile, ArticleReference, TEAssignment)
- SQLAlchemy database instance

## Notes

- All Word Plugin routes require authentication via session token
- References are fetched from the `article_references` table
- Rule execution logic can be extended in `word_plugin_rules.py`
- Error handling and logging are built into each endpoint

