import React, { useState } from "react";
import { Routes, Route, useNavigate, Navigate } from "react-router-dom";
import PubMedComponent from "./components/PubMed/PubMedComponent";
import ArticleIdSearchBox from "./components/ArticleIdSearchBox";
import JournalAdmin from "./components/admin/JournalAdmin";
import ZipWorkflow from "./components/zip/ZipWorkflow";
import FolderZipWorkflow from "./components/zip/FolderZipWorkflow";
import ZipQueueDashboard from "./components/zip/ZipQueueDashboard";
import IndividualZipProcessor from "./components/zip/IndividualZipProcessor";
import IndividualZipUpload from "./components/zip/IndividualZipUpload";
import ProcessingPage from "./pages/ProcessingPage";
import { ArticleProvider } from "./context/ArticleContext";
import { ZipQueueProvider } from "./context/ZipQueueContext";
import { AuthProvider, useAuth, useRole } from "./context/AuthContext";
import { AdminLogin } from "./components/auth";
import {
  AdminLayout,
  AdminDashboard,
  AdminUploadPage,
  AdminJournalsPage
} from "./components/admin";
import UserManagement from "./components/admin/UserManagement";
import AdminSearchPage from "./pages/AdminSearchPage";
// import AdminStatisticsPage from "./pages/AdminStatisticsPage";
import UserHome from "./components/user/UserHome";
import { normalizeReferences } from "./utils/referenceUtils";
import {
  isDbMode,
  LOADING_STATES,
  hasTestParam,
} from "./utils/appUtils";
import { Icons } from "./components/common";
import "./App.css";

// Legacy Home Component for existing functionality
const LegacyHome = () => {
  const [terms, setTerms] = useState({
    isLoading: LOADING_STATES.IDLE,
    data: [],
  });
  const navigate = useNavigate();
  const { role: currentRole, isAdmin } = useRole();

  const dbMode = isDbMode();
  const adminMode = hasTestParam("admin");

  const handleArticleIdSearch = (refs, articleId) => {
    const normalizedRefs = normalizeReferences(refs);
    if (normalizedRefs.length > 0) {
      setTerms({
        isLoading: LOADING_STATES.PROCESSING,
        data: normalizedRefs,
        articleId,
        fromDb: true,
      });
    } else {
      setTerms({
        isLoading: LOADING_STATES.IDLE,
        data: [],
        articleId,
        fromDb: false,
      });
    }
  };

  const handleZipUpload = () => {
    navigate("/admin/zip-upload");
  };

  const handleFolderUpload = () => {
    navigate("/admin/folder-upload");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className={"max-w-9xl mx-auto px-4 sm:px-6 lg:px-8"}>
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-semibold text-gray-900">EDITINK</h1>
              {currentRole !== "default" && (
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    currentRole === "admin"
                      ? "bg-purple-100 text-purple-800"
                      : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {currentRole.toUpperCase()} Mode
                </span>
              )}
            </div>
            <ArticleIdSearchBox onFound={handleArticleIdSearch} />
            <div className="flex items-center gap-3">
              {/* Admin Role: Show ZIP Upload buttons */}
              {isAdmin && (
                <>
                  <button
                    onClick={handleZipUpload}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-2xl hover:bg-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                  >
                    <span className="mr-2">📦</span>
                    <span>Single ZIP</span>
                  </button>
                  <button
                    onClick={handleFolderUpload}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-2xl hover:bg-blue-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                  >
                    <span className="mr-2">📁</span>
                    <span>Folder Upload</span>
                  </button>
                </>
              )}
              {/* Non-admin roles: Show search-only message */}
              {!isAdmin && (
                <span className="text-sm text-gray-500 italic">
                  Search references by Article ID
                </span>
              )}
            </div>
          </div>
        </div>
      </header>
      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {adminMode ? (
          <JournalAdmin />
        ) : dbMode ? (
          <>
            {terms.data.length > 0 && (
              <div className="animate-fadeIn">
                <PubMedComponent terms={terms} dbMode={true} />
              </div>
            )}
          </>
        ) : (
          <>
            {(terms.data.length > 0 || terms.isLoading > 0) && (
              <div className="animate-fadeIn">
                <PubMedComponent terms={terms} dbMode={false} />
              </div>
            )}
            {terms.data.length === 0 && terms.isLoading === 0 && (
              <div className="text-center py-16 bg-white rounded-2xl shadow-md">
                <div className="w-16 h-16 mx-auto mb-4 text-gray-400 bg-gray-100 rounded-2xl flex items-center justify-center">
                  <Icons.DocumentIcon />
                </div>
                {isAdmin ? (
                  <>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Ready to enhance your references
                    </h3>
                    <p className="text-sm text-gray-600 max-w-md mx-auto mb-6">
                      Upload ZIP archives with documents to get started with
                      AI-powered citation enhancement.
                    </p>
                    <div className="flex gap-4 justify-center">
                      <button
                        onClick={handleZipUpload}
                        className="inline-flex items-center px-6 py-3 bg-green-600 text-white text-base font-medium rounded-2xl hover:bg-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                      >
                        <span className="mr-2">📦</span>
                        <span>Single ZIP</span>
                      </button>
                      <button
                        onClick={handleFolderUpload}
                        className="inline-flex items-center px-6 py-3 bg-blue-600 text-white text-base font-medium rounded-2xl hover:bg-blue-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                      >
                        <span className="mr-2">📁</span>
                        <span>Folder Upload</span>
                      </button>
                    </div>
                  </>
                ) : (
                  <>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Search References by Article ID
                    </h3>
                    <p className="text-sm text-gray-600 max-w-md mx-auto mb-6">
                      Use the search box above to find references for a specific
                      article ID.
                    </p>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                      <div className="flex items-center gap-2 text-blue-800">
                        <Icons.InfoIcon />
                        <span className="font-medium">
                          {currentRole === "te" ? "TE Mode" : "User Mode"}
                        </span>
                      </div>
                      <p className="text-sm text-blue-700 mt-1">
                        You can only search for existing references by Article ID.
                      </p>
                    </div>
                  </>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

// Protected Route Component for Admin Authentication
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <AdminLogin />;
  }

  return <AdminLayout>{children}</AdminLayout>;
};

// Main App Component with Authentication
const AppContent = () => {
  const { isAuthenticated } = useAuth();

  // Check if user is trying to access admin routes via URL parameters
  const isAdminRoute = window.location.pathname.startsWith('/admin');
  const hasAdminParam = hasTestParam("admin");

  // If user has admin parameter but is not authenticated, redirect to admin login
  if (hasAdminParam && !isAuthenticated && !isAdminRoute) {
    window.location.href = '/admin/dashboard';
    return null;
  }

  return (
    <Routes>
      {/* Main user interface */}
      <Route path="/" element={<UserHome />} />

      {/* Legacy routes for existing functionality */}
      <Route path="/legacy" element={<LegacyHome />} />
      <Route path="/zip-upload" element={<ZipWorkflow />} />
      <Route path="/process" element={<ProcessingPage />} />

      {/* New folder-based ZIP processing routes */}
      <Route path="/folder-upload" element={<FolderZipWorkflow />} />
      <Route path="/zip-queue" element={<ZipQueueDashboard />} />
      <Route path="/process-zip/:zipId" element={<IndividualZipProcessor />} />

      {/* Admin ZIP upload routes */}
      <Route
        path="/admin/zip-upload"
        element={
          <ProtectedRoute>
            <IndividualZipUpload />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/folder-upload"
        element={
          <ProtectedRoute>
            <FolderZipWorkflow />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/zip-queue"
        element={
          <ProtectedRoute>
            <ZipQueueDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/process"
        element={
          <ProtectedRoute>
            <ProcessingPage />
          </ProtectedRoute>
        }
      />

      {/* Admin authentication and dashboard routes */}
      <Route path="/admin/login" element={<AdminLogin />} />
      <Route
        path="/admin/dashboard"
        element={
          <ProtectedRoute>
            <AdminDashboard />
          </ProtectedRoute>
        }
      />
      {/* <Route
        path="/admin/statistics"
        element={
          <ProtectedRoute>
            <AdminStatisticsPage />
          </ProtectedRoute>
        }
      /> */}
      <Route
        path="/admin/search"
        element={
          <ProtectedRoute>
            <AdminSearchPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/upload"
        element={
          <ProtectedRoute>
            <AdminUploadPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/journals"
        element={
          <ProtectedRoute>
            <AdminJournalsPage />
          </ProtectedRoute>
        }
      />
        <Route
          path="/admin/users"
          element={
            <ProtectedRoute>
              <UserManagement />
            </ProtectedRoute>
          }
        />
      <Route
        path="/admin/process"
        element={
          <ProtectedRoute>
            <ProcessingPage />
          </ProtectedRoute>
        }
      />

      {/* Redirect /admin to dashboard */}
      <Route path="/admin" element={<Navigate to="/admin/dashboard" replace />} />
    </Routes>
  );
};

function App() {
  return (
    <AuthProvider>
      <ArticleProvider>
        <ZipQueueProvider>
          <AppContent />
        </ZipQueueProvider>
      </ArticleProvider>
    </AuthProvider>
  );
}

export default App;
