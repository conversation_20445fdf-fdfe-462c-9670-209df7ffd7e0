# EDITINK - AI-Powered Reference Enhancement & TE Automation System

EDITINK is a comprehensive web application designed to enhance and validate academic references using AI-powered search across multiple databases including PubMed, CrossRef, and Google Scholar. The system provides intelligent reference formatting, duplicate detection, quality assessment, and complete Technical Editor (TE) workflow automation.

## 🚀 Features

### Core Reference Processing
- **File Upload**: Upload .docx files to extract references automatically
- **Manual Input**: Paste references directly into the text area
- **Multi-Database Search**: Searches across PubMed, CrossRef, and Google Scholar
- **AI Enhancement**: Uses OpenAI to improve reference formatting and completeness
- **Duplicate Detection**: Intelligent duplicate reference identification
- **Quality Assessment**: Automated quality scoring for references
- **Export Options**: Download enhanced references as DOCX or Excel files

### Journal Management System
- **Journal Abbreviation Database**: PostgreSQL-backed journal abbreviation management
- **Fuzzy Matching**: Intelligent journal name matching with fallback to AI
- **CRUD Operations**: Full Create, Read, Update, Delete operations for journal entries
- **Admin Interface**: Dedicated admin panel for journal database management

### User Management & Authentication
- **Role-Based Access Control**: SuperAdmin, Admin, Coordinator, TE, CE roles
- **Secure Authentication**: JWT-based authentication with session management
- **User Administration**: Complete user management system for admins
- **Permission System**: Granular permissions based on user roles

### TE Automation & Workflow
- **ZIP Processing**: Automated processing of article ZIP files with real-time validation
- **Manuscript Validation**: Intelligent validation of manuscript files in ZIP archives
- **Smart Batching System**: AI-powered batch creation with count-based and journal-based grouping
- **TE Assignment System**: Automated assignment of articles to Technical Editors with load balancing
- **Google Drive Integration**: Direct ZIP file upload to Google Drive with secure sharing
- **Email Notifications**: Automated email notifications with correct Drive folder links
- **Batch Processing**: Efficient batch processing with partial failure recovery
- **Queue Management**: Real-time queue management with status tracking and filtering
- **Empty Batch Filtering**: Intelligent filtering of empty batches to prevent unnecessary assignments

### Advanced Analytics & Reporting
- **Real-time Progress Tracking**: Visual progress indicators during processing
- **Interactive Editing**: Inline reference editing with AI suggestions
- **Statistical Dashboard**: Comprehensive statistics about reference quality and processing
- **Filtering System**: Advanced filtering by type, quality, status, and date ranges
- **Database Mode**: Special mode for saved reference collections
- **Test Mode**: Development and testing features with QA checkboxes

## 📦 Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- Python 3.8+
- npm or yarn
- SQLite (included with Python)
- Google Drive API credentials (for TE automation)
- SMTP credentials (for email notifications)

### Environment Configuration

#### Backend Environment Variables
Create a `.env` file in `TE_BACK/` directory:
```bash
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Google Drive Integration (for TE automation)
GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE=service_account.json
GOOGLE_DRIVE_PARENT_FOLDER_ID=your_drive_folder_id

# Email Service Configuration
SMTP_SERVER=smtpout.secureserver.net
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=True

# Flask Configuration
SECRET_KEY=your-super-secure-secret-key
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration (optional - SQLite used by default)
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name
```

### Frontend Setup
```bash
cd TE_FRONTEND
npm install
npm start
```

### Backend Setup
```bash
cd TE_BACK

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Initialize database
python init_db.py

# Create super admin user
python create_super_admin.py

# Start server
python app.py
```

### Google Drive Setup (for TE Automation)
1. Create a Google Cloud Project
2. Enable Google Drive API
3. Create a Service Account
4. Download service account JSON file as `service_account.json`
5. Share your Google Drive folder with the service account email
6. Set the folder ID in environment variables

## 🏗️ Architecture

### System Overview
EDITINK follows a modern full-stack architecture with microservices approach and clear separation of concerns:

- **Frontend**: React.js with modern hooks, context API, and role-based routing
- **Backend**: Flask REST API with SQLite/PostgreSQL database
- **AI Integration**: OpenAI GPT for reference enhancement and journal abbreviations
- **External APIs**: PubMed, CrossRef, Google Scholar integration
- **Cloud Services**: Google Drive API for file sharing, SMTP for email notifications
- **Authentication**: JWT-based authentication with role-based access control

### Data Flow

#### Reference Processing Flow
1. **Input Processing**: Documents are parsed to extract references
2. **Database Search**: References are searched across multiple databases
3. **AI Enhancement**: OpenAI improves incomplete or malformed references
4. **Quality Assessment**: Each reference receives a quality score
5. **User Review**: Interactive interface for manual corrections
6. **Export**: Final references exported in desired format

#### TE Automation Flow
1. **ZIP Upload**: Articles uploaded as ZIP files with metadata validation
2. **Validation**: Manuscript validation and file structure verification with detailed logging
3. **Queue Processing**: Articles queued for TE assignment with status tracking
4. **Smart Batching**: AI-powered batch creation with configurable grouping strategies
5. **Assignment**: Intelligent assignment of articles to Technical Editors with load balancing
6. **Direct Upload**: ZIP files uploaded directly from browser to Google Drive (no server storage)
7. **Email Notification**: Single email per assignment with correct Drive folder links
8. **Status Management**: Real-time status updates and comprehensive tracking
9. **Analytics**: Detailed reporting and assignment analytics

### Key Components

#### Frontend Architecture
- **React Router**: Client-side routing with protected routes
- **Context API**: Global state management (Auth, Articles, ZIP Queue)
- **Custom Hooks**: Reusable logic for data fetching and processing
- **Component Library**: Modular, reusable UI components with role-based rendering
- **Admin Dashboard**: Comprehensive admin interface for system management

#### Backend Architecture
- **Flask**: Lightweight web framework with blueprint organization
- **SQLAlchemy**: ORM with comprehensive database models
- **CORS**: Cross-origin resource sharing for frontend integration
- **File Processing**: Document parsing, ZIP handling, and validation
- **Service Layer**: Modular services for Drive, Email, and TE assignments
- **Authentication**: JWT tokens with role-based middleware

### Database Schema

#### Core Tables
- **users**: User accounts with role-based permissions
- **article_files**: Article metadata and processing status
- **zip_queue**: ZIP file processing queue and status
- **te_assignments**: TE assignment batches with Drive links
- **journals**: Journal abbreviation database
- **article_references**: Processed reference storage
- **daily_statistics**: Analytics and reporting data

### API Endpoints

#### Authentication & User Management
- `POST /api/auth/login` - User authentication
- `POST /api/auth/logout` - User logout
- `GET /api/admin/users` - User management (Admin only)
- `POST /api/admin/users` - Create new user
- `PUT /api/admin/users/:id` - Update user details

#### Reference Processing
- `POST /api/upload` - Upload and process documents
- `GET /api/references` - Retrieve processed references
- `POST /api/enhance` - AI enhancement of references
- `POST /api/search` - Search external databases
- `GET /api/autocomplete` - Article ID autocomplete

#### Journal Management
- `GET /api/journals` - List all journals
- `POST /api/journals` - Create new journal entry
- `PUT /api/journals/:id` - Update journal entry
- `DELETE /api/journals/:id` - Delete journal entry
- `POST /api/journals/search` - Search journal abbreviations

#### TE Automation & ZIP Processing
- `POST /api/admin/zip-upload` - Upload ZIP files for processing with validation
- `GET /api/admin/zip-queue` - Get ZIP processing queue with filtering
- `POST /api/admin/zip-queue/:id/process` - Process specific ZIP with status updates
- `GET /api/te-assignments` - List TE assignments with pagination
- `POST /api/te-assignments/create` - Create individual TE assignment
- `POST /api/te-assignments/multi-assign` - Create multiple assignments with ZIP files
- `POST /api/te-assignments/smart-batches` - Create smart batches with AI grouping
- `GET /api/te-assignments/available-tes` - Get available Technical Editors
- `POST /api/te-assignments/upload-zips` - Upload ZIP files directly to assignments

#### Analytics & Reporting
- `GET /api/admin/statistics` - Comprehensive system statistics
- `GET /api/admin/daily-stats` - Daily processing statistics
- `GET /api/health` - System health check

## 🤖 Smart Batching System

### Overview
The Smart Batching System is an advanced feature that automates the assignment of multiple articles to Technical Editors (TEs) using intelligent algorithms and configurable strategies.

### Key Features
- **Count-Based Batching**: Automatically calculates optimal batch sizes using `ceil(total_articles / total_TEs)`
- **Journal-Based Grouping**: Groups articles by journal codes for specialized TE assignments
- **Empty Batch Filtering**: Intelligently filters out empty batches to prevent unnecessary assignments
- **Direct ZIP Upload**: Uploads actual ZIP files directly from browser to Google Drive without server storage
- **Single Email Per Assignment**: Ensures each TE receives exactly one email with correct folder links
- **Partial Failure Recovery**: Continues processing other batches even if individual batches fail
- **Real-Time Status Updates**: Provides live feedback during batch processing

### Batching Strategies

#### Count-Based Batching (Default)
- **Algorithm**: `batch_size = ceil(total_articles / total_TEs)`
- **Use Case**: Even distribution of workload across all available TEs
- **Example**: 10 articles ÷ 3 TEs = 4 articles per batch (3,3,4 distribution)

#### Journal-Based Grouping
- **Algorithm**: Groups articles by journal codes, then distributes to specialized TEs
- **Use Case**: When TEs have expertise in specific journals or domains
- **Example**: All "Nature" articles go to TE specialized in high-impact journals

### Workflow Process
1. **Article Selection**: Admin selects articles from "Ready for Assignment" queue
2. **Batch Creation**: System creates smart batches based on selected strategy
3. **TE Assignment**: Batches are assigned to available TEs with load balancing
4. **ZIP Upload**: Actual ZIP files are uploaded directly to Google Drive folders
5. **Email Notification**: Single email sent to each TE with correct Drive folder link
6. **Status Tracking**: Real-time updates and comprehensive assignment tracking

### Technical Implementation
- **Frontend**: React-based modal with real-time progress indicators
- **Backend**: Flask service with intelligent batch algorithms
- **File Handling**: Direct browser-to-Drive upload using FormData and MediaIoBaseUpload
- **Error Handling**: Comprehensive error handling with detailed logging
- **Database**: Assignment records with Drive links and status tracking

## 📱 Application Modes

### User Roles & Permissions
- **SuperAdmin**: Full system access, user management, all features
- **Admin**: System administration, ZIP processing, TE assignments
- **Coordinator**: Article coordination, basic admin functions
- **TE (Technical Editor)**: Assigned article access
- **CE (Copy Editor)**: Copy editing functions

### Application Modes
- **Normal Mode**: Standard reference search and processing
- **Admin Mode**: Journal administration and system management
- **Test Mode**: Development features with QA checkboxes (`?test` parameter)
- **Database Mode**: Saved reference collections (`?db=true` parameter)

## 🚀 Available Scripts

### Frontend Scripts
```bash
# Development server
npm start

# Run tests
npm test

# Production build
npm run build

# Eject (one-way operation)
npm run eject
```

### Backend Scripts
```bash
# Start development server
python app.py

# Initialize database
python init_db.py

# Create super admin
python create_super_admin.py

# Run tests
python -m pytest

# Database migrations
python migrate_te_assignments.py
```

## 📁 Project Structure

### Frontend Structure
```
TE_FRONTEND/
├── public/                 # Static assets
├── src/
│   ├── components/         # React components
│   │   ├── admin/          # Admin-specific components
│   │   │   ├── AdminLayout.jsx
│   │   │   ├── AdminDashboard.jsx
│   │   │   ├── JournalAdmin.jsx
│   │   │   └── UserManagement.jsx
│   │   ├── auth/           # Authentication components
│   │   │   └── AdminLogin.jsx
│   │   ├── common/         # Reusable components
│   │   │   ├── Icons.jsx   # Centralized icon components
│   │   │   ├── StatusBadge.jsx
│   │   │   └── ReferenceEditor.js
│   │   ├── PubMed/         # Reference processing components
│   │   │   ├── PubMedComponent.jsx
│   │   │   ├── HeaderActions.jsx
│   │   │   ├── ProgressSteps.jsx
│   │   │   └── ReferenceTable.jsx
│   │   ├── user/           # User-specific components
│   │   │   └── UserHome.jsx
│   │   ├── zip/            # ZIP processing components
│   │   │   ├── ZipWorkflow.jsx
│   │   │   ├── FolderZipWorkflow.jsx
│   │   │   ├── ZipQueueDashboard.jsx
│   │   │   ├── IndividualZipProcessor.jsx
│   │   │   ├── SmartBatchingModal.jsx
│   │   │   └── BatchAssignmentModal.jsx
│   │   └── features/       # Feature-specific components
│   ├── context/            # React Context providers
│   │   ├── AuthContext.jsx # Authentication state
│   │   ├── ArticleContext.js # Article processing state
│   │   └── ZipQueueContext.js # ZIP queue state
│   ├── constants/          # Application constants
│   │   ├── urls.js         # API endpoints
│   │   ├── env.js          # Environment variables
│   │   └── prompt.js       # AI prompts
│   ├── hooks/              # Custom React hooks
│   │   └── useFetchPubMedData.js
│   ├── pages/              # Page components
│   │   ├── AdminSearchPage.jsx
│   │   ├── AdminStatisticsPage.jsx
│   │   └── ProcessingPage.jsx
│   ├── services/           # Business logic services
│   │   ├── helpers/        # Helper functions
│   │   ├── searchInPubMed.js
│   │   ├── searchInCrossRef.js
│   │   ├── fetchFromGenAI.js
│   │   ├── journalService.js
│   │   ├── emailService.js
│   │   └── teAutomationService.js
│   ├── utils/              # General utilities
│   │   ├── appUtils.js     # App-wide utilities
│   │   ├── referenceUtils.js # Reference processing
│   │   ├── quality.js      # Quality assessment
│   │   ├── filters.js      # Data filtering
│   │   └── manuscriptValidator.js # ZIP validation
│   ├── features/           # Feature modules
│   │   └── downloadExcel.js
│   ├── App.js              # Main application component
│   ├── App.css             # Global styles
│   └── index.js            # Application entry point
└── package.json            # Dependencies and scripts
```

### Backend Structure
```
TE_BACK/
├── app.py                  # Main Flask application with all models
├── requirements.txt        # Python dependencies
├── article_references.db   # SQLite database
├── service_account.json    # Google Drive credentials
├── services/               # Service layer
│   ├── drive_service.py    # Google Drive integration with direct upload
│   ├── email_service.py    # Email notifications with HTML templates
│   └── te_assignment_service.py # TE assignment logic with smart batching
├── uploads/                # File upload directory
├── .env                    # Environment variables
├── init_db.py             # Database initialization
├── create_super_admin.py   # Admin user creation
├── migrate_te_assignments.py # Database migrations
└── .github/workflows/      # CI/CD configuration
    └── deploy.yml          # AWS deployment workflow
```

## 🛠️ Technology Stack

### Frontend Technologies
- **React 18**: Modern JavaScript library with hooks and concurrent features
- **React Router v6**: Declarative routing with protected routes
- **Axios**: Promise-based HTTP client for API requests
- **Styled Components**: CSS-in-JS styling solution
- **React Data Table**: Advanced data table with sorting/filtering
- **CKEditor 5**: Rich text editor for reference editing
- **JSZip**: Client-side ZIP file handling
- **Mammoth**: DOCX to HTML conversion
- **File Saver**: Client-side file saving functionality
- **Tailwind CSS**: Utility-first CSS framework
- **React Diff Viewer**: Reference comparison tool
- **XLSX**: Excel file generation

### Backend Technologies
- **Flask 3.0**: Python web framework with blueprints
- **SQLAlchemy 2.0**: Modern ORM with async support
- **Flask-CORS**: Cross-origin resource sharing
- **python-docx**: Document processing and parsing
- **Google APIs**: Drive API for file sharing
- **Gunicorn**: WSGI HTTP server for production
- **SQLite/PostgreSQL**: Database options for different environments

### External Integrations
- **OpenAI GPT**: AI-powered reference enhancement
- **PubMed API**: Medical literature database
- **CrossRef API**: Academic publication metadata
- **Google Scholar**: Academic search engine
- **Google Drive API**: Cloud file storage and sharing
- **SMTP Services**: Email notification system

## 🧪 Testing & Quality Assurance

### Testing Features
- **Frontend Tests**: Jest and React Testing Library
- **Duplicate Detection**: Specialized testing mode (`?test-duplicates`)
- **Manuscript Validation**: ZIP file structure validation with detailed logging
- **API Testing**: Backend endpoint testing with comprehensive test suites
- **QA Mode**: Test mode with checkboxes for quality assurance
- **Smart Batching Tests**: Automated testing for batch creation and assignment
- **Email Link Verification**: Tests to ensure correct Google Drive folder links
- **Empty Batch Filtering**: Tests for intelligent batch filtering

### Running Tests
```bash
# Frontend tests
cd TE_FRONTEND
npm test

# Backend tests
cd TE_BACK
python -m pytest

# Smart batching tests
python test_smart_batching_with_zips.py
python test_email_folder_link_fix.py
python test_empty_batch_filtering.py
python test_single_email_fix.py

# Test specific features
# Duplicate detection: http://localhost:3000?test-duplicates
# Test mode: http://localhost:3000?test
# Database mode: http://localhost:3000?db=true
```

## 🚀 Deployment & Production

### AWS EC2 Deployment
The application includes automated deployment to AWS EC2 using GitHub Actions:

```bash
# Automatic deployment on push to main branch
# Manual deployment via GitHub Actions workflow_dispatch
```

### Production Configuration
- **Gunicorn**: WSGI server with multiple workers
- **Systemd**: Service management for auto-restart
- **Environment Variables**: Secure configuration management
- **SSL/TLS**: HTTPS encryption for production
- **Database**: PostgreSQL for production, SQLite for development

### Environment Setup
```bash
# Production environment variables
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-secure-secret-key
DATABASE_URL=postgresql://...  # Optional
GOOGLE_DRIVE_PARENT_FOLDER_ID=your-folder-id
SMTP_SERVER=your-smtp-server
```

## � Recent Improvements & Bug Fixes

### Smart Batching Enhancements (v3.1.0)
- **Fixed Duplicate Email Issue**: Eliminated duplicate emails by removing redundant completion step
- **Corrected Email Folder Links**: Fixed database transaction timing issue causing empty folder links in emails
- **Empty Batch Filtering**: Added intelligent filtering to prevent assignment of empty batches
- **Direct ZIP Upload**: Implemented browser-to-Drive upload without server storage
- **Enhanced UI Feedback**: Added visual indicators for empty batches and disabled buttons
- **Improved Error Handling**: Better error messages and partial failure recovery

### ZIP Processing Improvements
- **Real-Time Validation**: Enhanced manuscript validation with detailed logging
- **Status Management**: Comprehensive status tracking across the entire workflow
- **File Handling**: Improved ZIP file processing with better error handling
- **Queue Management**: Enhanced queue dashboard with filtering and search capabilities

### Email System Fixes & Enhancements (v3.2.0)
- **Single Email Guarantee**: Ensures exactly one email per assignment
- **Correct Folder Links**: Fixed issue where email links pointed to empty folders
- **Author Queries Integration**: Emails now include author queries from database with HTML formatting
- **Queries File Attachment**: Automatic generation and attachment of detailed queries files (.txt)
- **Flask Context Fixes**: Resolved SQLAlchemy context errors in database operations
- **Variable Initialization**: Fixed crashes due to uninitialized variables in email service
- **Enhanced Email Templates**: Improved HTML formatting with author query summary tables
- **Template Consistency**: Synchronized query templates between frontend and backend
- **Delivery Tracking**: Added email delivery status tracking and logging

### Performance Optimizations
- **Database Queries**: Optimized database queries for better performance
- **File Upload**: Streamlined file upload process with progress indicators
- **Memory Management**: Improved memory usage for large ZIP file processing
- **API Response Times**: Reduced API response times through better caching

### User Experience Improvements
- **Button States**: Smart button enabling/disabling based on content availability
- **Progress Indicators**: Real-time progress feedback during batch processing
- **Error Messages**: More descriptive error messages for better troubleshooting
- **Visual Feedback**: Enhanced UI with clear status indicators and loading states

## �🔒 Security Features

### Authentication & Authorization
- **JWT Tokens**: Secure authentication with expiration
- **Role-Based Access**: Granular permissions by user role
- **Session Management**: Secure session handling
- **Password Hashing**: Bcrypt password encryption

### Data Protection
- **Input Validation**: Comprehensive input sanitization
- **File Upload Security**: Secure file handling and validation
- **CORS Configuration**: Controlled cross-origin access
- **Environment Variables**: Secure credential management

## 🤝 Contributing

### Development Guidelines
- **Code Style**: ESLint and Prettier for consistent formatting
- **React Patterns**: Modern hooks and functional components
- **API Design**: RESTful endpoints with consistent responses
- **Documentation**: JSDoc comments for functions and components
- **Git Workflow**: Feature branches with pull request reviews

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

---

**Version**: 3.1.0
**Last Updated**: January 2025
**License**: Proprietary - EDITINK System

## 🤝 Contributing

### Code Style
- Use ESLint and Prettier for code formatting
- Follow React best practices and hooks patterns
- Maintain consistent file naming conventions
- Add JSDoc comments for functions

---

**Version**: 3.2.0
**Last Updated**: October 2025
**License**: Proprietary - EDITINK System
**Maintainer**: EDITINK Development Team