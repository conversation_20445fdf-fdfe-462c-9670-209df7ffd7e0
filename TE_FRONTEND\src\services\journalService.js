/**
 * Service for managing journal abbreviations via database API
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4999';

class JournalService {
  /**
   * Search for journal abbreviation in database first, then fallback to GenAI
   * @param {string} searchTerm - Journal name to search for
   * @returns {Promise<Object>} - Search result with abbreviation and source
   */
  async searchJournalAbbreviation(searchTerm) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/journals/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          searchTerm: searchTerm.trim()
        })
      });
      
      const data = await response.json();
      if (data.success) {
        return data.data;
      }
      
      throw new Error(data.error || 'Search failed');
    } catch (error) {
      console.error('Journal search error:', error);
      throw error;
    }
  }

  /**
   * Get all journal abbreviations with optional filtering
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Paginated journal list
   */
  async getJournals(params = {}) {
    try {
      const queryParams = new URLSearchParams({
        page: params.page || 1,
        per_page: params.perPage || 50,
        search: params.search || '',
        source: params.source || '',
        verified_only: params.verifiedOnly || false
      });

      const response = await fetch(`${API_BASE_URL}/api/journals?${queryParams}`, {
        credentials: 'include'
      });
      const data = await response.json();
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.error || 'Failed to fetch journals');
    } catch (error) {
      console.error('Get journals error:', error);
      throw error;
    }
  }

  /**
   * Create a new journal abbreviation
   * @param {Object} journalData - Journal data
   * @returns {Promise<Object>} - Created journal
   */
  async createJournal(journalData) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/journals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fullName: journalData.fullName,
          abbreviation: journalData.abbreviation,
          source: journalData.source || 'manual',
          confidenceScore: journalData.confidenceScore || 1.0,
          isVerified: journalData.isVerified || false,
          createdBy: journalData.createdBy || 'admin'
        })
      });
      
      const data = await response.json();
      if (data.success) {
        return data.data;
      }
      
      throw new Error(data.error || 'Failed to create journal');
    } catch (error) {
      console.error('Create journal error:', error);
      throw error;
    }
  }

  /**
   * Update an existing journal abbreviation
   * @param {number} journalId - Journal ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} - Updated journal
   */
  async updateJournal(journalId, updateData) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/journals/${journalId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      });
      
      const data = await response.json();
      if (data.success) {
        return data.data;
      }
      
      throw new Error(data.error || 'Failed to update journal');
    } catch (error) {
      console.error('Update journal error:', error);
      throw error;
    }
  }

  /**
   * Delete a journal abbreviation
   * @param {number} journalId - Journal ID
   * @returns {Promise<boolean>} - Success status
   */
  async deleteJournal(journalId) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/journals/${journalId}`, {
        method: 'DELETE'
      });
      
      const data = await response.json();
      if (data.success) {
        return true;
      }
      
      throw new Error(data.error || 'Failed to delete journal');
    } catch (error) {
      console.error('Delete journal error:', error);
      throw error;
    }
  }

  /**
   * Add a new journal mapping from GenAI result
   * @param {string} fullName - Full journal name
   * @param {string} abbreviation - Journal abbreviation
   * @param {number} confidence - Confidence score from GenAI
   * @returns {Promise<Object>} - Created journal
   */
  async addJournalFromGenAI(fullName, abbreviation, confidence = 0.8) {
    try {
      return await this.createJournal({
        fullName,
        abbreviation,
        source: 'genai',
        confidenceScore: confidence,
        isVerified: false,
        createdBy: 'genai_system'
      });
    } catch (error) {
      // If journal already exists, that's okay
      if (error.message.includes('409') || error.message.includes('already exists')) {
        console.log(`Journal already exists: ${fullName}`);
        return null;
      }
      throw error;
    }
  }

  /**
   * Get journal statistics
   * @returns {Promise<Object>} - Statistics about journals
   */
  async getJournalStats() {
    try {
      const response = await this.getJournals({ perPage: 1 });
      
      // Get counts by source
      const allJournals = await this.getJournals({ perPage: 10 }); // Get more for stats
      const journals = allJournals.data;
      
      const stats = {
        total: response.pagination.total,
        verified: journals.filter(j => j.isVerified).length,
        bySource: {
          manual: journals.filter(j => j.source === 'manual').length,
          genai: journals.filter(j => j.source === 'genai').length,
          pubmed: journals.filter(j => j.source === 'pubmed').length,
          crossref: journals.filter(j => j.source === 'crossref').length,
          file_migration: journals.filter(j => j.source === 'file_migration').length
        },
        mostUsed: journals
          .sort((a, b) => b.usageCount - a.usageCount)
          .slice(0, 10)
      };
      
      return stats;
    } catch (error) {
      console.error('Get journal stats error:', error);
      throw error;
    }
  }
}

// Create singleton instance
const journalService = new JournalService();

export default journalService;
