/* Processing Page Styles */
.processing-page {
  min-height: 100vh;
  background: #f9fafb;
}

/* Header */
.processing-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-to-upload {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  color: #374151;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-to-upload:hover {
  background: #e5e7eb;
  color: #111827;
}

.page-title h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.25rem 0;
}

.article-id {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.zip-processor-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #3b82f6;
  margin: 0.25rem 0 0 0;
  font-weight: 500;
}

.info-icon {
  width: 0.875rem;
  height: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.complete-processing-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.complete-processing-button:hover {
  background-color: #059669;
}

.complete-processing-button svg {
  width: 1rem;
  height: 1rem;
}

/* Main Content */
.processing-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

/* Reference Input Section */
.reference-input-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: start;
}

.input-container {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  padding: 2rem;
}

.input-header {
  margin-bottom: 1.5rem;
}

.input-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.input-header p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.manual-entry-icon {
  margin-right: 0.5rem;
}

.manual-entry-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 0.5rem;
  color: #92400e;
  font-size: 0.875rem;
}

.manual-entry-notice svg {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* Textarea */
.textarea-container {
  margin-bottom: 1.5rem;
}

.references-textarea {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.references-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.references-textarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.textarea-info {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

/* Input Actions */
.input-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.clear-button,
.process-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-button {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.clear-button:hover:not(:disabled) {
  background: #e5e7eb;
}

.process-button {
  background: #3b82f6;
  color: white;
}

.process-button:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.clear-button:disabled,
.process-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Help Section */
.help-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 1rem;
  padding: 1.5rem;
}

.help-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.help-steps {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.help-step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #475569;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

/* PubMed Section */
.pubmed-section {
  background: #ffffff;
  border-radius: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .reference-input-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .help-section {
    order: -1;
  }
}

@media (max-width: 768px) {
  .processing-content {
    padding: 1rem;
  }
  
  .input-container {
    padding: 1.5rem;
  }
  
  .header-content {
    padding: 0 1rem;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .input-actions {
    flex-direction: column;
  }
  
  .clear-button,
  .process-button {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .references-textarea {
    min-height: 200px;
    font-size: 0.75rem;
  }
  
  .textarea-info {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .help-step {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    text-align: left;
  }
}

/* Article ID Section Styles */
.article-id-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.article-id-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.article-id-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.article-id-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Save Status Styles */
.save-status {
  margin-top: 12px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.save-status.success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.save-status.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.save-status.info {
  background: #dbeafe;
  color: #1d4ed8;
  border: 1px solid #bfdbfe;
}

/* Admin Save Section in Results */
.admin-save-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.save-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.article-id-input-results {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.article-id-input-results:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.save-button-results {
  padding: 10px 20px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.save-button-results:hover:not(:disabled) {
  background: #059669;
}

.save-button-results:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .processing-page {
    padding: 0;
  }

  .header-content {
    padding: 0 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-left {
    align-items: center;
  }

  .page-title h1 {
    font-size: 1.25rem;
  }

  .header-actions {
    justify-content: center;
  }

  .complete-processing-button {
    width: 100%;
    justify-content: center;
  }

  .processing-content {
    padding: 1rem;
  }

  .input-container {
    padding: 1.5rem;
  }

  .reference-textarea {
    min-height: 200px;
  }

  .input-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .process-button,
  .clear-button {
    width: 100%;
    justify-content: center;
  }
}
