import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useZipQueue } from "../../context/ZipQueueContext";
import { Icons } from "../common";
import SmartBatchingModal from "./SmartBatchingModal";
import AuthorSummary from "./AuthorSummary";
import "./ZipQueueDashboard.css";

const ZipQueueDashboard = ({ onBack }) => {
  const navigate = useNavigate();
  const {
    zipQueue,
    currentFolder,
    getQueueStats,
    getCompletedZips,
    getReadyForAssignmentZips,
    getAssignedZips,
    clearQueue,
    markZipAssigned,
    updateCurrentFolderWithBatchSummary,
  } = useZipQueue();
  const [activeTab, setActiveTab] = useState("pending");
  const [selectedZips, setSelectedZips] = useState([]);
  const [showTEAssignmentModal, setShowTEAssignmentModal] = useState(false);
  const [showSmartBatchingModal, setShowSmartBatchingModal] = useState(false);
  const [availableTEs, setAvailableTEs] = useState([]);

  const stats = getQueueStats();

  // API base URL
  const API_BASE = process.env.REACT_APP_API_URL || "http://localhost:4999";

  // Fetch completed articles and available TEs
  React.useEffect(() => {
    const fetchAvailableTEs = async () => {
      try {
        // Fetch available TEs
        const tesResponse = await fetch(
          `${API_BASE}/api/te-assignments/available-tes`,
          {
            credentials: "include",
          }
        );
        if (tesResponse.ok) {
          const tesData = await tesResponse.json();
          if (tesData.success) {
            setAvailableTEs(tesData.tes);
          }
        }
      } catch (error) {
        console.error("Error fetching available TEs:", error);
      }
    };

    fetchAvailableTEs();
  }, [API_BASE]);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return "⏳";
      case "processing":
        return "🔄";
      case "ready_for_assignment":
        return "📋";
      case "assigned":
        return "👤";
      case "completed":
        return "✅";
      default:
        return "📦";
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "processing":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "ready_for_assignment":
        return "text-purple-600 bg-purple-50 border-purple-200";
      case "assigned":
        return "text-orange-600 bg-orange-50 border-orange-200";
      case "completed":
        return "text-green-600 bg-green-50 border-green-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const handleProcessZip = (zip) => {
    // Preserve admin context when navigating to ZIP processor
    const currentUrl = window.location.href;
    const hasAdminContext =
      currentUrl.includes("/admin") || currentUrl.includes("admin=true");

    if (hasAdminContext) {
      sessionStorage.setItem("adminContext", "true");
      navigate(`/process-zip/${zip.id}?admin=true`);
    } else {
      navigate(`/process-zip/${zip.id}`);
    }
  };

  // Test function to create sample ready-for-assignment ZIPs for TE assignment testing
  const createSampleCompletedZips = () => {
    const sampleZips = [
      {
        id: `test-${Date.now()}-1`,
        name: "sample-article-1.zip",
        size: 1024 * 500, // 500KB
        status: "ready_for_assignment",
        processedAt: new Date().toISOString(),
        extractedFiles: [
          {
            name: "manuscript.docx",
            type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          },
          { name: "references.txt", type: "text/plain" },
        ],
        articleCount: 1,
        referenceCount: 25,
        file: new File(["sample content"], "sample-article-1.zip", {
          type: "application/zip",
        }),
      },
      {
        id: `test-${Date.now()}-2`,
        name: "sample-article-2.zip",
        size: 1024 * 750, // 750KB
        status: "ready_for_assignment",
        processedAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        extractedFiles: [
          {
            name: "manuscript.docx",
            type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          },
          { name: "references.txt", type: "text/plain" },
        ],
        articleCount: 1,
        referenceCount: 18,
        file: new File(["sample content"], "sample-article-2.zip", {
          type: "application/zip",
        }),
      },
      {
        id: `test-${Date.now()}-3`,
        name: "sample-article-3.zip",
        size: 1024 * 300, // 300KB
        status: "ready_for_assignment",
        processedAt: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        extractedFiles: [
          {
            name: "manuscript.docx",
            type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          },
          { name: "references.txt", type: "text/plain" },
        ],
        articleCount: 1,
        referenceCount: 32,
        file: new File(["sample content"], "sample-article-3.zip", {
          type: "application/zip",
        }),
      },
    ];

    // Add sample ZIPs directly to localStorage
    const existingProcessed = JSON.parse(
      localStorage.getItem("processedZips") || "[]"
    );
    const updatedProcessed = [...existingProcessed, ...sampleZips];
    localStorage.setItem("processedZips", JSON.stringify(updatedProcessed));

    // Force a page refresh to reload the data
    window.location.reload();
  };

  const handleAssignToTE = (zip) => {
    setSelectedZips([zip]);
    setShowTEAssignmentModal(true);
  };

  const handleSmartBatching = () => {
    const readyZips = getReadyForAssignmentZips();
    if (readyZips.length === 0) {
      alert("No articles ready for assignment.");
      return;
    }
    if (availableTEs.length === 0) {
      alert("No available TEs found.");
      return;
    }
    setShowSmartBatchingModal(true);
  };

  const handleSmartBatchingComplete = (results) => {
    console.log("Smart batching completed:", results);
    setShowSmartBatchingModal(false);

    // Update ZIP statuses to "assigned" for successfully assigned articles
    if (results.successful && results.successful.length > 0) {
      const readyZips = getReadyForAssignmentZips();

      results.successful.forEach((assignment) => {
        // Each assignment contains article_ids that were assigned
        assignment.article_ids.forEach((articleId) => {
          // Find the corresponding ZIP file
          const zip = readyZips.find(
            (z) => z.articleId === articleId || z.id === articleId
          );
          if (zip) {
            // Mark ZIP as assigned with assignment details
            markZipAssigned(zip.id, {
              teId: assignment.assigned_te_id || assignment.te_id || "unknown",
              teName:
                assignment.te_name ||
                assignment.assigned_te_name ||
                "Unknown TE",
              teEmail:
                assignment.te_email || assignment.assigned_te_email || "",
              assignmentId: assignment.assignment_id,
              batchName: assignment.batch_name,
              driveLink: assignment.drive_link || assignment.folder_link || "",
              assignedAt: new Date().toISOString(),
            });
          }
        });
      });

      // Show success message
      const totalAssigned = results.successful.reduce(
        (sum, assignment) => sum + assignment.article_ids.length,
        0
      );
      alert(
        `✅ Smart batching completed successfully!\n\n` +
          `📊 ${results.summary.successful_batches} batches assigned\n` +
          `📄 ${totalAssigned} articles assigned to TEs\n` +
          `📁 Files uploaded to Google Drive\n` +
          `📧 Email notifications sent`
      );
    } else {
      alert(
        `Smart batching completed with ${results.summary.successful_batches} successful batches.`
      );
    }
  };

  const handleClearQueue = () => {
    if (
      window.confirm(
        "Are you sure you want to clear the entire queue? This will remove all ZIP files and cannot be undone."
      )
    ) {
      clearQueue();
      if (onBack) onBack();
    }
  };

  /**
   * Download all ZIPs from the current folder (including modified ones)
   */
  const handleDownloadAllZips = async () => {
    try {
      // Get all ZIPs (pending + processed)
      const allZips = [
        ...zipQueue,
        ...getReadyForAssignmentZips(),
        ...getAssignedZips(),
        ...getCompletedZips(),
      ];

      if (allZips.length === 0) {
        alert("No ZIP files to download.");
        return;
      }

      // Filter ZIPs that have file objects (not from localStorage)
      const zipsWithFiles = allZips.filter((zip) => zip.file);

      if (zipsWithFiles.length === 0) {
        alert(
          "⚠️ No ZIP files available for download.\n\nZIP files are only available in the current session. After a page refresh, you need to re-upload the folder."
        );
        return;
      }

      if (zipsWithFiles.length < allZips.length) {
        const missing = allZips.length - zipsWithFiles.length;
        if (
          !window.confirm(
            `⚠️ ${missing} ZIP file(s) are not available (likely from a previous session).\n\nDo you want to download the ${zipsWithFiles.length} available ZIP(s)?`
          )
        ) {
          return;
        }
      }

      // Show progress
      const downloadBtn = document.querySelector(".download-all-button");
      if (downloadBtn) {
        downloadBtn.disabled = true;
        downloadBtn.textContent = "⏳ Preparing download...";
      }

      if (zipsWithFiles.length === 1) {
        // Single file - download directly
        const zip = zipsWithFiles[0];
        const url = URL.createObjectURL(zip.file);
        const a = document.createElement("a");
        a.href = url;
        a.download = zip.filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        alert(
          `✅ Downloaded: ${zip.filename}${zip.modified ? " (Modified)" : ""}`
        );
      } else {
        // Multiple files - create a master ZIP containing all ZIPs
        const JSZip = (await import("jszip")).default;
        const masterZip = new JSZip();

        // Add each ZIP to the master ZIP
        for (const zip of zipsWithFiles) {
          // Keep original filename (no renaming for modified files)
          masterZip.file(zip.filename, zip.file);
        }

        // Create batch_summary.json
        const batchSummary = {
          batch_name: currentFolder.name,
          total_articles: zipsWithFiles.length,
          processed_at: new Date().toISOString(),
          articles: zipsWithFiles.map((zip) => ({
            article_id: zip.articleId || zip.id,
            filename: zip.filename,
            status: "success",
            modified: zip.modified || false,
            modified_at: zip.modifiedAt || null,
            // Drive IDs will be added after upload
            drive_file_id: null,
            drive_file_link: null,
          })),
        };

        // Add batch_summary.json to the master ZIP
        masterZip.file(
          "batch_summary.json",
          JSON.stringify(batchSummary, null, 2)
        );

        // Generate the master ZIP
        const blob = await masterZip.generateAsync({
          type: "blob",
          compression: "DEFLATE",
          compressionOptions: { level: 6 },
        });

        // Download the master ZIP
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${currentFolder.name}_ALL_ZIPS.zip`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        const modifiedCount = zipsWithFiles.filter((z) => z.modified).length;
        alert(
          `✅ Downloaded ${zipsWithFiles.length} ZIP files + batch_summary.json!\n\n` +
            `📦 File: ${currentFolder.name}_ALL_ZIPS.zip\n` +
            `${
              modifiedCount > 0
                ? `✏️ ${modifiedCount} modified ZIP(s) included\n`
                : ""
            }` +
            `📄 batch_summary.json included\n` +
            `📁 Check your Downloads folder\n\n` +
            `ℹ️ After running upload script, use "Upload Updated Batch Summary" to update Drive IDs`
        );
      }

      // Reset button
      if (downloadBtn) {
        downloadBtn.disabled = false;
        downloadBtn.innerHTML = "<svg>...</svg> Download All ZIPs";
      }
    } catch (error) {
      console.error("Error downloading ZIPs:", error);
      alert(`❌ Failed to download ZIPs: ${error.message}`);

      // Reset button
      const downloadBtn = document.querySelector(".download-all-button");
      if (downloadBtn) {
        downloadBtn.disabled = false;
        downloadBtn.innerHTML = "<svg>...</svg> Download All ZIPs";
      }
    }
  };

  /**
   * Download a single ZIP file
   */
  const handleDownloadSingleZip = (zip) => {
    if (!zip.file) {
      alert(
        "⚠️ ZIP file not available for download.\n\nZIP files are only available in the current session."
      );
      return;
    }

    try {
      const url = URL.createObjectURL(zip.file);
      const a = document.createElement("a");
      a.href = url;
      a.download = zip.modified
        ? zip.filename.replace(".zip", "_MODIFIED.zip")
        : zip.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading ZIP:", error);
      alert(`❌ Failed to download ZIP: ${error.message}`);
    }
  };

  /**
   * Upload updated batch_summary.json after running upload script
   */
  const handleUploadBatchSummary = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        const batchSummary = JSON.parse(text);

        // Validate batch summary structure
        if (!batchSummary.articles || !Array.isArray(batchSummary.articles)) {
          throw new Error("Invalid batch summary format");
        }

        // Check if Drive IDs are present
        const articlesWithDriveIds = batchSummary.articles.filter(
          (a) => a.drive_file_id
        );
        if (articlesWithDriveIds.length === 0) {
          alert(
            "⚠️ Warning: No Drive file IDs found in batch summary.\n\nMake sure you ran the upload script first."
          );
          return;
        }

        // Update current folder with batch summary data (without reloading!)
        updateCurrentFolderWithBatchSummary(batchSummary);

        alert(
          `✅ Batch summary uploaded successfully!\n\n` +
            `📊 ${articlesWithDriveIds.length}/${batchSummary.articles.length} articles have Drive IDs\n` +
            `☁️ Ready for assignment with Drive links\n\n` +
            `✨ Context preserved - your ZIPs are still available!`
        );
      } catch (error) {
        console.error("Error uploading batch summary:", error);
        alert(`❌ Failed to upload batch summary: ${error.message}`);
      }
    };
    input.click();
  };

  const renderZipCard = (zip) => (
    <div key={zip.id} className="zip-card">
      <div className="zip-card-header">
        <div className="zip-info">
          <div className="zip-filename">
            <span className="zip-icon">📦</span>
            {zip.filename}
          </div>
          {/* <div className={`zip-status ${getStatusColor(zip.status)}`}>
            <span className="status-icon">{getStatusIcon(zip.status)}</span>
            <span className="status-text">
              {zip.status.charAt(0).toUpperCase() + zip.status.slice(1)}
            </span>
          </div> */}
          <div className="zip-filename">
            <span className="zip-icon">
              <Icons.DocumentIcon className="metadata-icon" />
            </span>
            <span>{formatFileSize(zip.size)}</span>
          </div>
        </div>
      </div>

      <div className="zip-card-body">
        <div className="zip-metadata">
          <div className="metadata-item">
            <Icons.ClockIcon className="metadata-icon" />
            <span>Uploaded: {formatDate(zip.uploadedAt)}</span>
          </div>
          {zip.processedAt && (
            <div className="metadata-item">
              <Icons.CheckCircleIcon className="metadata-icon" />
              <span>Processed: {formatDate(zip.processedAt)}</span>
            </div>
          )}
          {zip.modified && (
            <div
              className="metadata-item"
              style={{ color: "#10b981", fontWeight: "600" }}
            >
              <Icons.PencilIcon className="metadata-icon" />
              <span>Modified: {formatDate(zip.modifiedAt)}</span>
            </div>
          )}
        </div>

        {/* Author Summary */}
        {/* <div className="zip-author-summary">
          <AuthorSummary articleId={zip.articleId} compact={true} />
        </div> */}

        <div className="zip-actions">
          {/* Download button for modified ZIPs */}
          {zip.modified && zip.file && (
            <button
              onClick={() => handleDownloadSingleZip(zip)}
              className="download-button"
              title="Download modified ZIP"
              style={{
                backgroundColor: "#10b981",
                color: "white",
                padding: "0.5rem 1rem",
                borderRadius: "0.375rem",
                border: "none",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                gap: "0.5rem",
                fontSize: "0.875rem",
                fontWeight: "500",
              }}
            >
              <Icons.DownloadIcon />
            </button>
          )}

          {zip.status === "pending" && (
            <button
              onClick={() => handleProcessZip(zip)}
              className="process-button"
            >
              <Icons.PlayIcon />
              Process
            </button>
          )}
          {zip.status === "processing" && (
            <button
              onClick={() => handleProcessZip(zip)}
              className="continue-button"
            >
              <Icons.ArrowRightIcon />
              Continue
            </button>
          )}
          {zip.status === "ready_for_assignment" && (
            <>
              <button
                onClick={() => handleProcessZip(zip)}
                className="view-button"
                title="View File"
              >
                <Icons.EyeIcon />
              </button>
              <button
                onClick={() => handleAssignToTE(zip)}
                className="assign-button"
                style={{
                  backgroundColor: "#3b82f6",
                  color: "white",
                  marginLeft: "0.5rem",
                  padding: "0.5rem 1rem",
                  borderRadius: "0.375rem",
                  border: "none",
                  cursor: "pointer",
                  display: "flex",
                  alignItems: "center",
                  gap: "0.5rem",
                }}
              >
                <Icons.UserIcon />
                Assign to TE
              </button>
            </>
          )}
          {zip.status === "completed" && (
            <button
              onClick={() => handleProcessZip(zip)}
              className="view-button"
            >
              <Icons.EyeIcon />
              View Completed
            </button>
          )}
        </div>
      </div>
    </div>
  );

  if (!currentFolder) {
    return (
      <div className="no-folder-message">
        <div className="no-folder-icon">📁</div>
        <h3>No folder uploaded</h3>
        <p>Please upload a folder containing ZIP files to get started.</p>
        {onBack && (
          <button onClick={onBack} className="back-to-upload-button">
            <Icons.UploadIcon />
            Upload Folder
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="zip-queue-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="header-left">
          {onBack && (
            <button onClick={onBack} className="back-button">
              <Icons.ChevronLeftIcon />
            </button>
          )}
          <div className="folder-info">
            <h1 className="dashboard-title">
              <span className="folder-icon">📁</span>
              {currentFolder.name}
            </h1>
            <p className="folder-subtitle">
              Uploaded {formatDate(currentFolder.uploadedAt)} • {stats.total}{" "}
              ZIP files
            </p>
          </div>
        </div>
        <div className="header-actions">
          <button
            onClick={handleDownloadAllZips}
            className="download-all-button"
          >
            <Icons.DownloadIcon />
            Download All ZIPs
          </button>
          <button onClick={handleClearQueue} className="clear-queue-button">
            <Icons.TrashIcon />
            Clear Queue
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="progress-section">
        <div className="progress-info">
          <span className="progress-text">
            Progress: {stats.completed} of {stats.total} completed (
            {stats.progress}%)
          </span>
        </div>
        <div className="progress-bar">
          <div
            className="progress-fill"
            style={{ width: `${stats.progress}%` }}
          ></div>
        </div>
      </div>

      {/* Tabs */}
      <div className="tabs-container">
        <div className="tabs">
          <button
            className={`tab ${activeTab === "pending" ? "active" : ""}`}
            onClick={() => setActiveTab("pending")}
          >
            Pending ({stats.pending + stats.processing})
          </button>
          <button
            className={`tab ${
              activeTab === "ready_for_assignment" ? "active" : ""
            }`}
            onClick={() => setActiveTab("ready_for_assignment")}
          >
            Ready for Assignment ({getReadyForAssignmentZips().length})
          </button>
          <button
            className={`tab ${activeTab === "assigned" ? "active" : ""}`}
            onClick={() => setActiveTab("assigned")}
          >
            Assigned ({getAssignedZips().length})
          </button>
          <button
            className={`tab ${activeTab === "completed" ? "active" : ""}`}
            onClick={() => setActiveTab("completed")}
          >
            Completed ({getCompletedZips().length})
          </button>
        </div>
      </div>

      {/* ZIP Cards */}
      <div className="zip-cards-container">
        {activeTab === "pending" && (
          <div className="zip-cards-grid">
            {zipQueue.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">📦</div>
                <h3>No pending ZIP files</h3>
                <p>All ZIP files have been processed.</p>
              </div>
            ) : (
              zipQueue.map(renderZipCard)
            )}
          </div>
        )}

        {activeTab === "ready_for_assignment" && (
          <>
            {getReadyForAssignmentZips().length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">📋</div>
                <h3>No files ready for assignment</h3>
                <p>
                  Completed ZIP files ready to be assigned to TEs will appear
                  here.
                </p>
              </div>
            ) : (
              <>
                <div
                  className="bulk-actions"
                  style={{
                    marginBottom: "1rem",
                    display: "flex",
                    gap: "1rem",
                    flexWrap: "wrap",
                  }}
                >
                  <button
                    onClick={() => {
                      const readyZips = getReadyForAssignmentZips();
                      setSelectedZips(readyZips);
                      setShowTEAssignmentModal(true);
                    }}
                    className="assign-button"
                    disabled={getReadyForAssignmentZips().length === 0}
                  >
                    📋 Assign All to TE
                  </button>

                  <button
                    onClick={handleSmartBatching}
                    className="smart-batch-button"
                    disabled={
                      getReadyForAssignmentZips().length === 0 ||
                      availableTEs.length === 0
                    }
                    style={{
                      background:
                        "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                      color: "white",
                      border: "none",
                      padding: "0.75rem 1.5rem",
                      borderRadius: "8px",
                      fontWeight: "600",
                      cursor:
                        getReadyForAssignmentZips().length === 0 ||
                        availableTEs.length === 0
                          ? "not-allowed"
                          : "pointer",
                      opacity:
                        getReadyForAssignmentZips().length === 0 ||
                        availableTEs.length === 0
                          ? 0.5
                          : 1,
                      transition: "all 0.2s",
                      display: "flex",
                      alignItems: "center",
                      gap: "0.5rem",
                    }}
                  >
                    🧠 Smart Batching ({getReadyForAssignmentZips().length}{" "}
                    articles)
                  </button>

                  <button
                    onClick={handleUploadBatchSummary}
                    className="upload-batch-summary-button"
                    style={{
                      background:
                        "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
                      color: "white",
                      border: "none",
                      padding: "0.75rem 1.5rem",
                      borderRadius: "8px",
                      fontWeight: "600",
                      cursor: "pointer",
                      transition: "all 0.2s",
                      display: "flex",
                      alignItems: "center",
                      gap: "0.5rem",
                    }}
                    title="Upload the updated batch_summary.json after running the upload script"
                  >
                    ☁️ Upload Updated Batch Summary
                  </button>
                </div>
                <div className="zip-cards-grid">
                  {getReadyForAssignmentZips().map(renderZipCard)}
                </div>
              </>
            )}
          </>
        )}

        {activeTab === "assigned" && (
          <div className="zip-cards-grid">
            {getAssignedZips().length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">👤</div>
                <h3>No assigned ZIP files</h3>
                <p>ZIP files assigned to TEs will appear here.</p>
              </div>
            ) : (
              getAssignedZips().map(renderZipCard)
            )}
          </div>
        )}

        {activeTab === "completed" && (
          <div className="zip-cards-grid">
            {getCompletedZips().length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">✅</div>
                <h3>No completed ZIP files</h3>
                <p>Fully completed ZIP files will appear here.</p>
                <button
                  onClick={createSampleCompletedZips}
                  className="process-button"
                  style={{ marginTop: "1rem" }}
                >
                  🧪 Create Sample Ready-for-Assignment ZIPs for Testing
                </button>
              </div>
            ) : (
              getCompletedZips().map(renderZipCard)
            )}
          </div>
        )}
      </div>

      {/* TE Assignment Modal */}
      {showTEAssignmentModal && (
        <TEAssignmentModal
          selectedZips={selectedZips}
          onClose={() => {
            setShowTEAssignmentModal(false);
            setSelectedZips([]);
          }}
          markZipAssigned={markZipAssigned}
          currentFolder={currentFolder}
        />
      )}

      {/* Smart Batching Modal */}
      {showSmartBatchingModal && (
        <SmartBatchingModal
          isOpen={showSmartBatchingModal}
          onClose={() => setShowSmartBatchingModal(false)}
          completedArticles={getReadyForAssignmentZips()}
          availableTEs={availableTEs}
          onAssignmentComplete={handleSmartBatchingComplete}
          jsonSummaryFile={currentFolder?.jsonSummaryFile} // Pass the JSON summary file for author information
          batchSummary={currentFolder?.batchSummary} // Pass the batch summary with Drive IDs
        />
      )}
    </div>
  );
};

// Simple TE Assignment Modal Component
const TEAssignmentModal = ({
  selectedZips,
  onClose,
  markZipAssigned,
  currentFolder,
}) => {
  const [selectedTE, setSelectedTE] = useState("");
  const [loading, setLoading] = useState(false);
  const [availableTEs, setAvailableTEs] = useState([]);
  const [loadingTEs, setLoadingTEs] = useState(true);
  const [jsonSummaryFile, setJsonSummaryFile] = useState(null);
  const [jsonSummaryError, setJsonSummaryError] = useState("");

  // Check if JSON summary file is available from folder context
  const contextJsonFile = currentFolder?.jsonSummaryFile;

  // API base URL - same pattern as working APIs
  const API_BASE = process.env.REACT_APP_API_URL || "http://localhost:4999";

  // Handle JSON summary file upload
  const handleJsonFileUpload = (e) => {
    const file = e.target.files[0];
    setJsonSummaryError("");

    if (!file) {
      setJsonSummaryFile(null);
      return;
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith(".json")) {
      setJsonSummaryError("Please select a JSON file.");
      setJsonSummaryFile(null);
      return;
    }

    // Validate file name contains summary or batch
    const fileName = file.name.toLowerCase();
    if (!fileName.includes("summary") && !fileName.includes("batch")) {
      setJsonSummaryError(
        'Please select a summary or batch JSON file (filename should contain "summary" or "batch").'
      );
      setJsonSummaryFile(null);
      return;
    }

    setJsonSummaryFile(file);
  };

  // Fetch available TEs on component mount
  React.useEffect(() => {
    const fetchAvailableTEs = async () => {
      try {
        setLoadingTEs(true);
        const response = await fetch(
          `${API_BASE}/api/te-assignments/available-tes`,
          {
            credentials: "include",
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setAvailableTEs(data.tes);
          } else {
            console.error("Failed to fetch TEs:", data.error);
            // Fallback to sample data
            setAvailableTEs([
              { id: 1, name: "Pratik Jain", email: "<EMAIL>" },
              {
                id: 2,
                name: "Dr. Sarah Johnson",
                email: "<EMAIL>",
              },
            ]);
          }
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        console.error("Error fetching available TEs:", error);
        // Fallback to sample data
        setAvailableTEs([
          { id: 1, name: "Pratik Jain", email: "<EMAIL>" },
          { id: 4, name: "testte", email: "<EMAIL>" },
        ]);
        setAvailableTEs([
          { id: 1, name: "Pratik Jain", email: "<EMAIL>" },
          {
            id: 2,
            name: "Dr. Sarah Johnson",
            email: "<EMAIL>",
          },
        ]);
      } finally {
        setLoadingTEs(false);
      }
    };

    fetchAvailableTEs();
  }, []);

  const handleAssign = async () => {
    if (!selectedTE) {
      alert("Please select a TE to assign the files to.");
      return;
    }

    // Use context JSON file if available, otherwise require manual upload
    const jsonFileToUse = contextJsonFile || jsonSummaryFile;

    if (!jsonFileToUse) {
      alert(
        "Please upload a JSON summary file. This file is required for enhanced author information in TE assignment emails."
      );
      return;
    }

    setLoading(true);
    try {
      const teData = availableTEs.find((te) => te.id === parseInt(selectedTE));

      // Helper to get file IDs from dump folder
      const getFileIdsFromDump = async (filenames) => {
        const response = await fetch(
          `${API_BASE}/api/drive/get-file-ids-by-names`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            credentials: "include",
            body: JSON.stringify({ filenames }),
          }
        );

        if (!response.ok)
          throw new Error("Failed to get file IDs from dump folder");
        const data = await response.json();
        if (!data.success)
          throw new Error(data.error || "Failed to get file IDs");

        return data.file_mapping;
      };

      // Generate Batch Name
      const batchName = `batch-${Date.now()}-manual`;

      // Check if we have batch summary with Drive IDs
      let driveFileIds = [];

      if (currentFolder?.batchSummary?.articles) {
        // Use Drive IDs from uploaded batch summary
        driveFileIds = selectedZips
          .map((zip) => {
            const article = currentFolder.batchSummary.articles.find(
              (a) =>
                a.filename === zip.filename || a.article_id === zip.articleId
            );
            return article?.drive_file_id;
          })
          .filter(Boolean);

        console.log(
          "📊 Using Drive IDs from batch summary:",
          driveFileIds.length
        );
      }

      // Fallback: Get file IDs from dump folder if batch summary not available
      if (driveFileIds.length === 0) {
        console.log(
          "📁 Batch summary not available, fetching from dump folder..."
        );
        const filenames = selectedZips.map((zip) => zip.filename);
        const fileMapping = await getFileIdsFromDump(filenames);

        driveFileIds = selectedZips
          .map((zip) => fileMapping[zip.filename]?.file_id)
          .filter(Boolean);
      }

      if (driveFileIds.length === 0) {
        throw new Error(
          "No Drive file IDs found. Please either:\n1. Upload the updated batch_summary.json, OR\n2. Ensure files were uploaded to dump folder by Playwright automation."
        );
      }

      // Extract article IDs and remove duplicates
      const articleIds = [
        ...new Set(
          selectedZips.map((zip) => zip.articleId || zip.id).filter(Boolean)
        ),
      ];

      // Extract author information from JSON summary file if available
      let articlesWithAuthors = null;
      if (currentFolder?.jsonSummaryFile) {
        try {
          const fileReader = new FileReader();
          const fileContent = await new Promise((resolve, reject) => {
            fileReader.onload = (e) => resolve(e.target.result);
            fileReader.onerror = reject;
            fileReader.readAsText(currentFolder.jsonSummaryFile);
          });

          const summaryData = JSON.parse(fileContent);
          const articlesMap = new Map();

          // Handle different JSON structures
          let articles = [];
          if (Array.isArray(summaryData)) {
            articles = summaryData;
          } else if (
            summaryData.articles &&
            Array.isArray(summaryData.articles)
          ) {
            articles = summaryData.articles;
          }

          // Create map of article_id -> article data
          articles.forEach((article) => {
            if (article.article_id) {
              articlesMap.set(article.article_id, {
                article_id: article.article_id,
                journal: article.journal || "Unknown",
                authors: article.authors || [],
                status: article.status || "unknown",
              });
            }
          });

          // Build articles_with_authors array for selected articles
          articlesWithAuthors = articleIds
            .map((articleId) => articlesMap.get(articleId))
            .filter(Boolean); // Remove any undefined entries

          console.log(
            `📧 Sending author data for ${articlesWithAuthors.length} articles`
          );
        } catch (error) {
          console.error("Error extracting author data:", error);
          // Continue without author data - email will use simple table
        }
      }

      // Create Assignment (moves files from dump to batch folder)
      const createResponse = await fetch(
        `${API_BASE}/api/te-assignments/create-from-dump`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({
            te_id: selectedTE,
            batch_name: batchName,
            article_ids: articleIds,
            drive_file_ids: driveFileIds,
            notes: "Manual assignment from ZIP Queue Dashboard",
            articles_with_authors: articlesWithAuthors, // Include author information
          }),
        }
      );

      if (!createResponse.ok) {
        throw new Error(
          `Failed to create assignment: ${createResponse.status}`
        );
      }

      const createData = await createResponse.json();
      if (!createData.success) {
        throw new Error(createData.error || "Failed to create assignment");
      }

      const assignmentId = createData.assignment_id;
      const folderLink = createData.folder_link;

      // Success! Mark ZIPs as assigned
      selectedZips.forEach((zip) => {
        markZipAssigned(zip.id, {
          teId: parseInt(selectedTE),
          teName: teData.name,
          teEmail: teData.email,
          assignmentId: assignmentId,
          batchName: batchName,
          driveLink: folderLink,
          assignedAt: new Date().toISOString(),
        });
      });

      alert(
        `✅ Successfully assigned ${selectedZips.length} ZIP(s) to ${teData.name}!\n\n` +
          `📁 Files moved from dump folder to batch folder\n` +
          `📧 Email sent to ${teData.email}\n` +
          `🔗 Drive Link: ${folderLink}`
      );

      onClose(); // Close modal on success
    } catch (error) {
      console.error("Assignment error:", error);
      alert(
        `❌ Failed to assign files: ${error.message}\n\nPlease try again or contact support.`
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "0.5rem",
          padding: "2rem",
          maxWidth: "500px",
          width: "90%",
          maxHeight: "80vh",
          overflow: "auto",
        }}
      >
        <div style={{ marginBottom: "1.5rem" }}>
          <h2
            style={{
              margin: "0 0 0.5rem 0",
              fontSize: "1.5rem",
              fontWeight: "600",
            }}
          >
            Assign to Technical Editor
          </h2>
          <p style={{ margin: 0, color: "#6b7280" }}>
            Assign {selectedZips.length} ZIP file(s) to a Technical Editor
          </p>
        </div>

        <div style={{ marginBottom: "1.5rem" }}>
          <h3
            style={{
              margin: "0 0 1rem 0",
              fontSize: "1rem",
              fontWeight: "500",
            }}
          >
            Selected Files:
          </h3>
          <div
            style={{
              maxHeight: "150px",
              overflow: "auto",
              border: "1px solid #e5e7eb",
              borderRadius: "0.375rem",
              padding: "0.75rem",
            }}
          >
            {selectedZips.map((zip) => (
              <div
                key={zip.id}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "0.5rem",
                  marginBottom: "0.5rem",
                }}
              >
                <span>📦</span>
                <span style={{ fontSize: "0.875rem" }}>
                  {zip.name || zip.filename}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div style={{ marginBottom: "1.5rem" }}>
          <label
            style={{
              display: "block",
              marginBottom: "0.5rem",
              fontSize: "0.875rem",
              fontWeight: "500",
            }}
          >
            JSON Summary File: <span style={{ color: "#ef4444" }}>*</span>
          </label>

          {contextJsonFile ? (
            <div
              style={{
                padding: "0.75rem",
                backgroundColor: "#f0f9ff",
                border: "1px solid #0ea5e9",
                borderRadius: "0.375rem",
                marginBottom: "0.5rem",
              }}
            >
              <p
                style={{
                  color: "#0369a1",
                  fontSize: "0.875rem",
                  margin: "0 0 0.25rem 0",
                  fontWeight: "500",
                }}
              >
                ✅ Using JSON file from upload: {contextJsonFile.name}
              </p>
              <p
                style={{
                  color: "#0369a1",
                  fontSize: "0.75rem",
                  margin: 0,
                }}
              >
                This file was uploaded with your ZIP files and contains the
                required author metadata.
              </p>
            </div>
          ) : (
            <>
              <input
                type="file"
                accept=".json"
                onChange={handleJsonFileUpload}
                style={{
                  width: "100%",
                  padding: "0.75rem",
                  border: jsonSummaryError
                    ? "1px solid #ef4444"
                    : "1px solid #d1d5db",
                  borderRadius: "0.375rem",
                  fontSize: "0.875rem",
                  marginBottom: "0.5rem",
                }}
              />
              {jsonSummaryError && (
                <p
                  style={{
                    color: "#ef4444",
                    fontSize: "0.75rem",
                    margin: "0 0 0.5rem 0",
                  }}
                >
                  {jsonSummaryError}
                </p>
              )}
              {jsonSummaryFile && (
                <p
                  style={{
                    color: "#059669",
                    fontSize: "0.75rem",
                    margin: "0 0 0.5rem 0",
                  }}
                >
                  ✅ {jsonSummaryFile.name} selected
                </p>
              )}
              <p
                style={{
                  color: "#6b7280",
                  fontSize: "0.75rem",
                  margin: 0,
                }}
              >
                Upload a JSON summary file containing article metadata and
                author information. File name should contain "summary" or
                "batch".
              </p>
            </>
          )}
        </div>

        <div style={{ marginBottom: "1.5rem" }}>
          <label
            style={{
              display: "block",
              marginBottom: "0.5rem",
              fontSize: "0.875rem",
              fontWeight: "500",
            }}
          >
            Select Technical Editor:
          </label>
          <select
            value={selectedTE}
            onChange={(e) => setSelectedTE(e.target.value)}
            disabled={loadingTEs}
            style={{
              width: "100%",
              padding: "0.75rem",
              border: "1px solid #d1d5db",
              borderRadius: "0.375rem",
              fontSize: "0.875rem",
              opacity: loadingTEs ? 0.5 : 1,
            }}
          >
            <option value="">
              {loadingTEs ? "Loading TEs..." : "Choose a TE..."}
            </option>
            {availableTEs.map((te) => (
              <option key={te.id} value={te.id}>
                {te.name} ({te.email})
              </option>
            ))}
          </select>
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "0.75rem",
          }}
        >
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              padding: "0.75rem 1.5rem",
              border: "1px solid #d1d5db",
              borderRadius: "0.375rem",
              backgroundColor: "white",
              color: "#374151",
              cursor: loading ? "not-allowed" : "pointer",
              opacity: loading ? 0.5 : 1,
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleAssign}
            disabled={loading || !selectedTE}
            style={{
              padding: "0.75rem 1.5rem",
              border: "none",
              borderRadius: "0.375rem",
              backgroundColor: "#3b82f6",
              color: "white",
              cursor: loading || !selectedTE ? "not-allowed" : "pointer",
              opacity: loading || !selectedTE ? 0.5 : 1,
            }}
          >
            {loading ? "Assigning..." : "Assign to TE"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ZipQueueDashboard;
