/**
 * Mapping of Article ID prefixes (Pubcode) to Production Editor emails.
 * Keys should be lowercase for case-insensitive matching.
 */
export const productionEditorMapping = {
  'aomd': '<EMAIL>',
  'wjcs': 'Ejaz.<PERSON><EMAIL>',
  'sgmj': '<PERSON><PERSON><PERSON>.<PERSON><PERSON>@wolterskluwer.com',
  'joacp': '<PERSON><PERSON><PERSON><PERSON>@wolterskluwer.com',
  'ijc': '<PERSON><PERSON><PERSON>.<PERSON><PERSON><PERSON>@wolterskluwer.com',
  'ni': '<PERSON>ja<PERSON>.<PERSON><EMAIL>',
  'neurol-india': 'Eja<PERSON>.<PERSON><EMAIL>',
  'ijo': '<PERSON><PERSON><PERSON>.<PERSON><PERSON>@wolterskluwer.com',
  'ija': '<EMAIL>',
  'jfmpc': 'She<PERSON>.<PERSON>@wolterskluwer.com',
  'ijn': '<EMAIL>',
  'lungindia': '<PERSON><PERSON><PERSON>.<PERSON><PERSON>@wolterskluwer.com',
  'indianjpsy': '<PERSON><PERSON><PERSON>.<PERSON><EMAIL>',
  'crst': '<EMAIL>',
  'jpgm': '<EMAIL>',
  'ijpm': '<EMAIL>',
  'njcp': '<EMAIL>',
  'jiaomr': '<EMAIL>',
  'sja': '<EMAIL>',
  'aca': '<EMAIL>',
  'ijpvm': '<EMAIL>',
  'idoj': '<EMAIL>',
  'aian': '<EMAIL>',
  'ijd': '<EMAIL>',
  'ijem': '<EMAIL>',
  'jdrysruh': '<EMAIL>',
  'jehp': '<EMAIL>',
  'jcrt': '<EMAIL>',
  'jpbs': '<EMAIL>',
  'mjdrdypu': '<EMAIL>',
  'ipj': '<EMAIL>',
  'jomfp': '<EMAIL>',
  'ijcm': '<EMAIL>',
  'njms': '<EMAIL>',
  'abr': '<EMAIL>',
  'kjo': '<EMAIL>',
  'tjosr': '<EMAIL>',
  'joc': '<EMAIL>',
  'jos': '<EMAIL>',
  'ijdr': '<EMAIL>',
  'sjg': '<EMAIL>',
  'njbcs': '<EMAIL>',
  'ijnmr': '<EMAIL>',
  'ijoem': '<EMAIL>',
  'joacc': '<EMAIL>',
  'dshmj': '<EMAIL>'
};

/**
 * Get the production editor email for a given article ID.
 * Matches the longest prefix if multiple match (though unlikely with this dataset).
 * @param {string} articleId 
 * @returns {string|null} Email address or null if not found
 */
export const getProductionEditorEmail = (articleId) => {
  if (!articleId) return null;
  const lowerId = articleId.toLowerCase();
  
  // Find matching key (prefix)
  const match = Object.keys(productionEditorMapping).find(key => lowerId.startsWith(key));
  
  return match ? productionEditorMapping[match] : null;
};
