# Manuscript Validation System

## Overview

The Manuscript Validation System is a JavaScript-based validation module that ensures ZIP files in the Reference Processing Tool contain exactly one manuscript file before processing. This validation step runs automatically after ZIP extraction and before the user can select files for reference processing.

## Features

- **Automatic Validation**: Runs automatically when ZIP files are extracted
- **Case-Insensitive Detection**: Finds manuscript files regardless of case (manuscript, MANUSCRIPT, Manuscript)
- **Flexible Naming**: Detects files containing "manuscript" anywhere in the filename
- **Comprehensive Error Reporting**: Provides detailed error messages and file listings
- **Batch Processing Support**: Validates multiple ZIP files in folder uploads
- **User-Friendly UI**: Shows validation results with clear error messages and actions

## How It Works

### 1. File Detection
The system searches for files that:
- Contain the word "manuscript" (case-insensitive) in the filename
- Have `.docx` or `.doc` extensions

### 2. Validation Rules
- **✅ Valid**: Exactly 1 manuscript file found
- **❌ Invalid**: 0 manuscript files found (`no_manuscript` error)
- **❌ Invalid**: 2+ manuscript files found (`multiple_manuscripts` error)

### 3. Integration Points

#### Individual ZIP Processing
- **Location**: `IndividualZipProcessor.jsx`
- **Trigger**: After ZIP extraction, before file selection
- **Behavior**: Shows validation error screen if validation fails

#### Batch Processing
- **Location**: `FolderUploader.jsx`
- **Trigger**: During folder processing
- **Behavior**: Shows validation summary with pass/fail statistics

## API Reference

### Core Functions

#### `validateManuscript(files, articleId)`
Validates manuscript files in extracted ZIP contents.

**Parameters:**
- `files` (Array): Array of file objects from ZIP extraction
- `articleId` (string): Article ID extracted from ZIP filename

**Returns:**
```javascript
{
  article_id: "ijd_123_25",
  status: "ok" | "error",
  error_type: "no_manuscript" | "multiple_manuscripts" | "validation_failed" | null,
  error_message: "Human-readable error message",
  files: ["list", "of", "all", "files"],
  manuscript_files: ["list", "of", "manuscript", "files"],
  total_files: 5,
  validation_timestamp: "2025-09-15T10:30:00.000Z"
}
```

#### `runManuscriptValidation(files, zipFilename)`
Main validation workflow function that combines validation, logging, and error display.

**Parameters:**
- `files` (Array): Array of file objects from ZIP extraction
- `zipFilename` (string): Original ZIP filename

**Returns:**
```javascript
{
  ...validationResult,
  log_entry: {...},
  can_proceed: true | false
}
```

#### `extractArticleId(filename)`
Extracts article ID from ZIP filename.

**Parameters:**
- `filename` (string): ZIP filename (e.g., "ijd_123_25.zip")

**Returns:**
- `string`: Article ID (e.g., "ijd_123_25")

### Utility Functions

#### `logValidationResult(validationResult)`
Logs validation results to console and potentially to a logging service.

#### `showValidationError(validationResult)`
Shows validation error popup to user (currently uses alert, can be replaced with modal).

## Usage Examples

### Basic Validation
```javascript
import { runManuscriptValidation } from '../utils/manuscriptValidator';

// After ZIP extraction
const files = [
  { name: 'Manuscript.docx', type: 'docx' },
  { name: 'Figure1.jpg', type: 'jpg' }
];

const result = runManuscriptValidation(files, 'article_123.zip');

if (result.can_proceed) {
  // Proceed with normal workflow
  setCurrentStep('files');
} else {
  // Show error and prevent progression
  setCurrentStep('validation_error');
  setError(result.error_message);
}
```

### Batch Validation
```javascript
// In folder processing
const validationResults = [];
for (const zipFile of zipFiles) {
  const validation = runManuscriptValidation(extractedFiles, zipFile.name);
  validationResults.push(validation);
}

// Show summary
const hasErrors = validationResults.some(r => !r.can_proceed);
if (hasErrors) {
  setShowValidationSummary(true);
}
```

## Error Types

### `no_manuscript`
No files containing "manuscript" in the filename were found.

**Common Causes:**
- File named differently (e.g., "Document.docx", "Paper.docx")
- Manuscript file has different extension
- ZIP contains only supplementary files

### `multiple_manuscripts`
Multiple files containing "manuscript" in the filename were found.

**Common Causes:**
- Multiple versions (e.g., "Manuscript.docx", "Manuscript_v2.docx")
- Backup files (e.g., "Manuscript.docx", "Manuscript_backup.docx")
- Different formats (e.g., "Manuscript.docx", "Manuscript.doc")

### `validation_failed`
Technical error during validation process.

**Common Causes:**
- Corrupted ZIP file
- File access permissions
- JavaScript runtime errors

## UI Components

### Validation Error Screen
Shows when individual ZIP validation fails:
- Error icon and title
- Article ID and error message
- Detailed explanation with file listings
- Actions: "Back to Queue" or "Proceed Anyway"

### Validation Summary
Shows during batch processing:
- Statistics (passed/failed/total)
- Individual validation results
- Expandable error details
- Close action

## Testing

Run the test suite to verify validation logic:

```javascript
import { runValidationTests } from '../utils/manuscriptValidator.test.js';
runValidationTests();
```

## Future Enhancements

1. **Backend Logging**: Send validation results to backend for analytics
2. **Custom Rules**: Allow configuration of validation rules per journal
3. **Auto-Fix**: Suggest fixes for common naming issues
4. **Batch Actions**: Allow bulk "proceed anyway" for failed validations
5. **Integration**: Connect with existing quality assurance workflows
