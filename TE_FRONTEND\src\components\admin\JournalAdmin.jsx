import React, { useState, useEffect } from 'react';
import journalService from '../../services/journalService';
import { Icons } from '../common';

const JournalAdmin = () => {
  const [journals, setJournals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    search: '',
    source: '',
    verifiedOnly: false,
    page: 1
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingJournal, setEditingJournal] = useState(null);
  const [stats, setStats] = useState(null);

  // Load journals
  const loadJournals = async () => {
    try {
      setLoading(true);
      const response = await journalService.getJournals(filters);
      setJournals(response.data);
      setPagination(response.pagination);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const statsData = await journalService.getJournalStats();
      setStats(statsData);
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  useEffect(() => {
    loadJournals();
  }, [filters]);

  useEffect(() => {
    loadStats();
  }, []);

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const handleDelete = async (journalId) => {
    if (!window.confirm('Are you sure you want to delete this journal abbreviation?')) {
      return;
    }

    try {
      await journalService.deleteJournal(journalId);
      loadJournals();
      loadStats();
    } catch (err) {
      setError(err.message);
    }
  };

  const handleEdit = (journal) => {
    setEditingJournal(journal);
    setShowAddModal(true);
  };

  const handleModalClose = () => {
    setShowAddModal(false);
    setEditingJournal(null);
  };

  const handleSave = async () => {
    loadJournals();
    loadStats();
    handleModalClose();
  };

  if (loading && journals.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Icons.LoadingSpinner className="w-8 h-8" />
        <span className="ml-2">Loading journals...</span>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Journal Administration</h1>
              <p className="mt-2 text-gray-600">
                Manage journal abbreviations and their mappings
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <Icons.StatsIcon className="w-4 h-4 mr-1" />
                Database Mode
              </span>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Icons.StatsIcon className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Journals</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total?.toLocaleString() || 0}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Icons.CheckIcon className="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Verified</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.verified?.toLocaleString() || 0}</p>
                  <p className="text-xs text-gray-500">
                    {stats.total > 0 ? Math.round((stats.verified / stats.total) * 100) : 0}% verified
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Icons.InfoIcon className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">From GenAI</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.bySource?.genai?.toLocaleString() || 0}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <Icons.DocumentIcon className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Manual</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.bySource?.manual?.toLocaleString() || 0}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                {/* Search */}
                <div className="relative flex-1 max-w-md">
                  <Icons.SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search journals by name or abbreviation..."
                    className="pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-full text-sm transition-colors"
                    value={filters.search}
                    onChange={(e) => handleSearch(e.target.value)}
                  />
                </div>

                {/* Source Filter */}
                <div className="relative">
                  <select
                    className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white min-w-[140px] transition-colors"
                    value={filters.source}
                    onChange={(e) => handleFilterChange('source', e.target.value)}
                  >
                    <option value="">All Sources</option>
                    <option value="manual">Manual</option>
                    <option value="genai">GenAI</option>
                    <option value="pubmed">PubMed</option>
                    <option value="crossref">CrossRef</option>
                    <option value="file_migration">File Migration</option>
                  </select>
                </div>

                {/* Verified Filter */}
                <label className="flex items-center bg-gray-50 px-4 py-3 rounded-lg border border-gray-300 cursor-pointer hover:bg-gray-100 transition-colors">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
                    checked={filters.verifiedOnly}
                    onChange={(e) => handleFilterChange('verifiedOnly', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-700 font-medium">Verified only</span>
                </label>
              </div>

              {/* Add Button */}
              <button
                onClick={() => setShowAddModal(true)}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white text-sm font-bold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl border border-blue-700"
              >
                <Icons.UploadIcon className="w-5 h-5 mr-2" />
                Add Journal
              </button>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex">
              <Icons.ExclamationIcon className="w-5 h-5 text-red-400 flex-shrink-0" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Journals Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {journals.length === 0 ? (
            <div className="text-center py-12">
              <Icons.DocumentIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No journals found</h3>
              <p className="text-gray-500 mb-6">
                {filters.search || filters.source || filters.verifiedOnly
                  ? 'Try adjusting your search filters.'
                  : 'Get started by adding your first journal.'}
              </p>
              <button
                onClick={() => setShowAddModal(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Icons.UploadIcon className="w-4 h-4 mr-2" />
                Add Journal
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-gray-300">
                <thead className="bg-gray-100 border-b-2 border-gray-300">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wide border-r border-gray-300">
                      Journal Name
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wide border-r border-gray-300">
                      Abbreviation
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wide border-r border-gray-300">
                      Source
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wide border-r border-gray-300">
                      Usage
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wide border-r border-gray-300">
                      Status
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-bold text-gray-700 uppercase tracking-wide">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white">
                  {journals.map((journal) => (
                    <JournalRow
                      key={journal.id}
                      journal={journal}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="bg-white px-6 py-4 border-t-2 border-gray-300">
            <div className="flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.has_prev}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-400 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.has_next}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-400 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">
                      {(pagination.page - 1) * pagination.per_page + 1}
                    </span>{' '}
                    to{' '}
                    <span className="font-medium">
                      {Math.min(pagination.page * pagination.per_page, pagination.total)}
                    </span>{' '}
                    of{' '}
                    <span className="font-medium">{pagination.total}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded border border-gray-400 shadow-sm">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.has_prev}
                      className="relative inline-flex items-center px-4 py-2 border-r border-gray-400 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <span className="relative inline-flex items-center px-4 py-2 border-r border-gray-400 bg-gray-50 text-sm font-medium text-gray-700">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.has_next}
                      className="relative inline-flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

        {/* Add/Edit Modal */}
        {showAddModal && (
          <JournalModal
            journal={editingJournal}
            onClose={handleModalClose}
            onSave={handleSave}
          />
        )}
      </div>
    </div>
  );
};

// Journal Row Component
const JournalRow = ({ journal, onEdit, onDelete }) => {
  const getSourceBadge = (source) => {
    const colors = {
      manual: 'bg-blue-100 text-blue-800 border border-blue-300',
      genai: 'bg-purple-100 text-purple-800 border border-purple-300',
      pubmed: 'bg-green-100 text-green-800 border border-green-300',
      crossref: 'bg-yellow-100 text-yellow-800 border border-yellow-300',
      file_migration: 'bg-gray-100 text-gray-800 border border-gray-300'
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded text-xs font-medium ${colors[source] || colors.manual}`}>
        {source.replace('_', ' ')}
      </span>
    );
  };

  return (
    <tr className="border-b border-gray-300 hover:bg-gray-50 transition-colors">
      <td className="px-6 py-4 border-r border-gray-300">
        <div className="text-sm font-medium text-gray-900" title={journal.fullName}>
          {journal.fullName}
        </div>
      </td>
      <td className="px-6 py-4 border-r border-gray-300">
        <div className="text-sm text-gray-900 font-mono">
          {journal.abbreviation}
        </div>
      </td>
      <td className="px-6 py-4 border-r border-gray-300">
        {getSourceBadge(journal.source)}
      </td>
      <td className="px-6 py-4 border-r border-gray-300 text-center">
        <span className="text-sm font-medium text-gray-900">
          {journal.usageCount || 0}
        </span>
      </td>
      <td className="px-6 py-4 border-r border-gray-300">
        {journal.isVerified ? (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Icons.CheckIcon className="w-3 h-3 mr-1" />
            Verified
          </span>
        ) : (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Icons.ExclamationIcon className="w-3 h-3 mr-1" />
            Unverified
          </span>
        )}
      </td>
      <td className="px-6 py-4 text-center">
        <div className="flex items-center justify-center space-x-3">
          <button
            onClick={() => onEdit(journal)}
            className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded hover:bg-blue-200 transition-colors"
            title="Edit journal"
          >
            Edit
          </button>
          <button
            onClick={() => onDelete(journal.id)}
            className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-700 bg-red-100 border border-red-300 rounded hover:bg-red-200 transition-colors"
            title="Delete journal"
          >
            Delete
          </button>
        </div>
      </td>
    </tr>
  );
};

// Journal Modal Component (Add/Edit)
const JournalModal = ({ journal, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    fullName: journal?.fullName || '',
    abbreviation: journal?.abbreviation || '',
    source: journal?.source || 'manual',
    confidenceScore: journal?.confidenceScore || 1.0,
    isVerified: journal?.isVerified || false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (journal) {
        // Update existing journal
        await journalService.updateJournal(journal.id, formData);
      } else {
        // Create new journal
        await journalService.createJournal(formData);
      }
      onSave();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              {journal ? 'Edit Journal' : 'Add New Journal'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Icons.XIcon className="w-5 h-5" />
            </button>
          </div>
          
          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Full Journal Name
              </label>
              <input
                type="text"
                required
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={formData.fullName}
                onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Abbreviation
              </label>
              <input
                type="text"
                required
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={formData.abbreviation}
                onChange={(e) => setFormData(prev => ({ ...prev, abbreviation: e.target.value }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Source
              </label>
              <select
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={formData.source}
                onChange={(e) => setFormData(prev => ({ ...prev, source: e.target.value }))}
              >
                <option value="manual">Manual</option>
                <option value="genai">GenAI</option>
                <option value="pubmed">PubMed</option>
                <option value="crossref">CrossRef</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Confidence Score
              </label>
              <input
                type="number"
                min="0"
                max="1"
                step="0.1"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={formData.confidenceScore}
                onChange={(e) => setFormData(prev => ({ ...prev, confidenceScore: parseFloat(e.target.value) }))}
              />
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  checked={formData.isVerified}
                  onChange={(e) => setFormData(prev => ({ ...prev, isVerified: e.target.checked }))}
                />
                <span className="ml-2 text-sm text-gray-700">Verified</span>
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : (journal ? 'Update' : 'Create')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default JournalAdmin;
