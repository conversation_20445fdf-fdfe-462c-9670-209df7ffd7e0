import { Document, Packer, Paragraph } from "docx";
import { saveAs } from "file-saver";

/**
 * Normalize references to ensure 'term' is always a string
 * Centralized function to avoid duplication across components
 * @param {Array} refs - Array of reference objects or strings
 * @returns {Array} - Normalized array of reference objects
 */
export const normalizeReferences = (refs) => {
  return (refs || []).map(ref => {
    if (typeof ref === 'string') {
      return { term: ref };
    }
    return {
      ...ref,
      term: typeof ref.term === 'string' ? ref.term : (ref.term ? String(ref.term) : ''),
    };
  });
};

/**
 * Downloads references as a DOCX file
 * @param {Array} references - Array of reference objects
 */
export const downloadDocx = async (references) => {
  const doc = new Document({
    sections: [
      {
        properties: {},
        children: references.map((ref) => {
          return new Paragraph({
            text: `${ref.ind + 1}. ${ref.finalStr || ref.term}`,
          });
        }),
      },
    ],
  });

  const blob = await Packer.toBlob(doc);
  saveAs(blob, "references.docx");
};

/**
 * Copies text to clipboard
 * @param {string} text - Text to copy
 */
export const copyToClipboard = (text) => {
  if (!text) {
    return;
  }
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(text);
  } else {
    // Fallback for HTTP or unsupported browsers
    const textArea = document.createElement("textarea");
    textArea.value = text;
    // Avoid scrolling to bottom
    textArea.style.position = "fixed";
    textArea.style.top = 0;
    textArea.style.left = 0;
    textArea.style.width = "2em";
    textArea.style.height = "2em";
    textArea.style.padding = 0;
    textArea.style.border = "none";
    textArea.style.outline = "none";
    textArea.style.boxShadow = "none";
    textArea.style.background = "transparent";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      const successful = document.execCommand("copy");
      if (successful) {
        alert("Copied to clipboard!");
      } else {
        alert("Failed to copy.");
      }
    } catch (err) {
      alert("Failed to copy: " + err);
    }
    document.body.removeChild(textArea);
  }
};

/**
 * Opens PubMed search in a new tab
 * @param {string} text - Search text
 */
export const searchInPubMed = (text) => {
  const searchTerm = encodeURIComponent(text);
  window.open(`https://pubmed.ncbi.nlm.nih.gov/?term=${searchTerm}`, "_blank");
};