2025-09-28 21:50:01,770 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:4999
2025-09-28 21:50:01,770 - INFO - [33mPress CTRL+C to quit[0m
2025-09-28 21:50:01,786 - INFO -  * Restarting with stat
2025-09-28 21:50:05,589 - WARNING -  * Debugger is active!
2025-09-28 21:50:05,589 - INFO -  * Debugger PIN: 197-************-09-28 21:50:27,785 - INFO - 127.0.0.1 - - [28/Sep/2025 21:50:27] "GET /health HTTP/1.1" 200 -
2025-09-28 21:51:32,914 - INFO - 127.0.0.1 - - [28/Sep/2025 21:51:32] "GET /api/auth/check HTTP/1.1" 200 -
2025-09-28 21:51:32,918 - INFO - 127.0.0.1 - - [28/Sep/2025 21:51:32] "GET /api/auth/check HTTP/1.1" 200 -
2025-09-28 21:51:37,915 - INFO - 127.0.0.1 - - [28/Sep/2025 21:51:37] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-09-28 21:51:38,354 - INFO - 127.0.0.1 - - [28/Sep/2025 21:51:38] "POST /api/auth/login HTTP/1.1" 200 -
2025-09-28 21:51:38,557 - INFO - 127.0.0.1 - - [28/Sep/2025 21:51:38] "[31m[1mGET /api/dashboard/stats?days=30 HTTP/1.1[0m" 401 -
2025-09-28 21:51:38,567 - INFO - 127.0.0.1 - - [28/Sep/2025 21:51:38] "GET /api/dashboard/articles?page=1&per_page=1000&search=&sort_by=created_at&sort_order=desc HTTP/1.1" 200 -
2025-09-28 21:51:38,569 - INFO - 127.0.0.1 - - [28/Sep/2025 21:51:38] "[31m[1mGET /api/dashboard/stats?days=30 HTTP/1.1[0m" 401 -
2025-09-28 21:51:38,591 - INFO - 127.0.0.1 - - [28/Sep/2025 21:51:38] "GET /api/dashboard/articles?page=1&per_page=1000&search=&sort_by=created_at&sort_order=desc HTTP/1.1" 200 -
2025-09-28 21:54:46,682 - INFO - 127.0.0.1 - - [28/Sep/2025 21:54:46] "GET /api/auth/check HTTP/1.1" 200 -
2025-09-28 21:54:46,690 - INFO - 127.0.0.1 - - [28/Sep/2025 21:54:46] "GET /api/auth/check HTTP/1.1" 200 -
2025-09-28 21:54:52,670 - INFO - 127.0.0.1 - - [28/Sep/2025 21:54:52] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-09-28 21:54:53,400 - INFO - 127.0.0.1 - - [28/Sep/2025 21:54:53] "POST /api/auth/login HTTP/1.1" 200 -
2025-09-28 21:54:53,672 - INFO - 127.0.0.1 - - [28/Sep/2025 21:54:53] "[31m[1mGET /api/dashboard/stats?days=30 HTTP/1.1[0m" 401 -
2025-09-28 21:54:53,816 - INFO - 127.0.0.1 - - [28/Sep/2025 21:54:53] "GET /api/dashboard/articles?page=1&per_page=1000&search=&sort_by=created_at&sort_order=desc HTTP/1.1" 200 -
2025-09-28 21:54:53,837 - INFO - 127.0.0.1 - - [28/Sep/2025 21:54:53] "GET /api/dashboard/articles?page=1&per_page=1000&search=&sort_by=created_at&sort_order=desc HTTP/1.1" 200 -
2025-09-28 21:54:53,849 - INFO - 127.0.0.1 - - [28/Sep/2025 21:54:53] "[31m[1mGET /api/dashboard/stats?days=30 HTTP/1.1[0m" 401 -
2025-09-28 21:55:54,785 - INFO - 127.0.0.1 - - [28/Sep/2025 21:55:54] "[31m[1mGET /api/te-assignments/available-tes HTTP/1.1[0m" 401 -
2025-09-28 22:00:12,785 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:4999
2025-09-28 22:00:12,793 - INFO - [33mPress CTRL+C to quit[0m
2025-09-28 22:00:12,793 - INFO -  * Restarting with stat
2025-09-28 22:00:19,888 - WARNING -  * Debugger is active!
2025-09-28 22:00:19,898 - INFO -  * Debugger PIN: 197-147-098
