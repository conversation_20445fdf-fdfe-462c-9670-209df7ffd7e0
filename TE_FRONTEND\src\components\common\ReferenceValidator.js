import React, { useState } from 'react';
import { ActionBadge } from './StatusBadge';

/**
 * Reference Validation and Auto-Fix Component
 * Provides automatic validation and fixing of common reference issues
 */
const ReferenceValidator = ({ reference, onFix }) => {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState(null);

  const validateReference = (ref) => {
    const text = ref.finalStr || ref.term || '';
    const issues = [];
    const fixes = [];

    // Check for common issues and suggest fixes
    
    // 1. Missing or incorrect punctuation
    if (!text.endsWith('.')) {
      issues.push('Missing final period');
      fixes.push({
        type: 'punctuation',
        description: 'Add final period',
        fix: text + '.'
      });
    }

    // 2. Double spaces
    if (text.includes('  ')) {
      issues.push('Multiple consecutive spaces');
      fixes.push({
        type: 'spacing',
        description: 'Fix spacing',
        fix: text.replace(/\s+/g, ' ')
      });
    }

    // 3. Incorrect capitalization after colons
    const colonMatch = text.match(/:\s*[a-z]/);
    if (colonMatch) {
      issues.push('Lowercase letter after colon');
      fixes.push({
        type: 'capitalization',
        description: 'Capitalize after colon',
        fix: text.replace(/:\s*([a-z])/, (match, p1) => ': ' + p1.toUpperCase())
      });
    }

    // 4. Page number formatting
    const pageMatch = text.match(/(\d+)-(\d+)/);
    if (pageMatch) {
      const [, start, end] = pageMatch;
      if (start.length === end.length && start.length > 2) {
        const abbreviated = abbreviatePages(start, end);
        if (abbreviated !== `${start}-${end}`) {
          issues.push('Page numbers can be abbreviated');
          fixes.push({
            type: 'pages',
            description: 'Abbreviate page numbers',
            fix: text.replace(`${start}-${end}`, abbreviated)
          });
        }
      }
    }

    // 5. Author name formatting
    if (text.match(/[A-Z][a-z]+\s+[A-Z][a-z]+/)) {
      issues.push('Author names may need formatting');
      fixes.push({
        type: 'authors',
        description: 'Format author names (LastName FirstInitial)',
        fix: text // This would need more complex logic
      });
    }

    return { issues, fixes, score: Math.max(0, 100 - (issues.length * 15)) };
  };

  const abbreviatePages = (start, end) => {
    const startNum = parseInt(start);
    const endNum = parseInt(end);
    
    if (startNum >= endNum) return `${start}-${end}`;
    
    // Find common prefix
    let i = 0;
    while (i < start.length && start[i] === end[i]) {
      i++;
    }
    
    return `${start}-${end.slice(i)}`;
  };

  const handleValidate = async () => {
    setIsValidating(true);
    
    // Simulate validation process
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const results = validateReference(reference);
    setValidationResults(results);
    setIsValidating(false);
  };

  const handleApplyFix = (fix) => {
    if (onFix) {
      onFix(reference.ind, fix.fix);
    }
    setValidationResults(null);
  };

  const handleApplyAllFixes = () => {
    if (validationResults && validationResults.fixes.length > 0) {
      let fixedText = reference.finalStr || reference.term;
      
      // Apply all fixes in sequence
      validationResults.fixes.forEach(fix => {
        if (fix.type === 'punctuation' && !fixedText.endsWith('.')) {
          fixedText += '.';
        } else if (fix.type === 'spacing') {
          fixedText = fixedText.replace(/\s+/g, ' ');
        } else if (fix.type === 'capitalization') {
          fixedText = fixedText.replace(/:\s*([a-z])/, (match, p1) => ': ' + p1.toUpperCase());
        } else if (fix.type === 'pages') {
          const pageMatch = fixedText.match(/(\d+)-(\d+)/);
          if (pageMatch) {
            const [, start, end] = pageMatch;
            const abbreviated = abbreviatePages(start, end);
            fixedText = fixedText.replace(`${start}-${end}`, abbreviated);
          }
        }
      });
      
      if (onFix) {
        onFix(reference.ind, fixedText);
      }
    }
    setValidationResults(null);
  };

  return (
    <div className="reference-validator">
      <ActionBadge
        action="validate"
        onClick={handleValidate}
        disabled={isValidating}
        className="validate-button"
      />
      
      {isValidating && (
        <div className="validation-progress">
          <span>Validating...</span>
        </div>
      )}
      
      {validationResults && (
        <div className="validation-results">
          <div className="validation-header">
            <span className="validation-score">
              Score: {validationResults.score}%
            </span>
            {validationResults.fixes.length > 0 && (
              <ActionBadge
                action="fix-all"
                onClick={handleApplyAllFixes}
                className="fix-all-button"
              />
            )}
          </div>
          
          {validationResults.issues.length > 0 && (
            <div className="validation-issues">
              <h4>Issues Found:</h4>
              <ul>
                {validationResults.issues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          )}
          
          {validationResults.fixes.length > 0 && (
            <div className="validation-fixes">
              <h4>Suggested Fixes:</h4>
              {validationResults.fixes.map((fix, index) => (
                <div key={index} className="fix-item">
                  <span className="fix-description">{fix.description}</span>
                  <ActionBadge
                    action="apply"
                    onClick={() => handleApplyFix(fix)}
                    className="apply-fix-button"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ReferenceValidator;
