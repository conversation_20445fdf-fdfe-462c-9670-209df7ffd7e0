/**
 * Test file for manuscript validation utility
 * This demonstrates how the validation works with different scenarios
 */

import { validateManuscript, extractArticleId, runManuscriptValidation, runCompleteValidation } from './manuscriptValidator';

// Test data - simulating extracted ZIP file contents
const testCases = [
  {
    name: 'Valid ZIP with one manuscript',
    files: [
      { name: 'Manuscript.docx', type: 'docx' },
      { name: 'Figure1.jpg', type: 'jpg' },
      { name: 'Table1.xlsx', type: 'xlsx' }
    ],
    zipFilename: 'article_123.zip',
    expectedStatus: 'ok'
  },
  {
    name: 'Real case - idoj_605_25 with multiple manuscripts',
    files: [
      { name: 'copyright.doc.docx', type: 'docx' },
      { name: 'FirstPage.docx', type: 'docx' },
      { name: 'Manuscript with track changes.docx', type: 'docx' },
      { name: 'Manuscript.docx', type: 'docx' },
      { name: 'Reply to reviewers.docx', type: 'docx' },
      { name: 'idoj_605_25.png', type: 'png' },
      { name: 'idoj_605_25.xml', type: 'xml' },
      { name: 'IMG_20250408_103846.jpg', type: 'jpg' },
      { name: 'IMG_20250408_104205.jpg', type: 'jpg' },
      { name: 'Patient consent form .pdf', type: 'pdf' },
      { name: 'WhatsApp Image 2025-04-08 at 2.26.29 PM.JPG', type: 'jpg' }
    ],
    zipFilename: 'idoj_605_25.zip',
    expectedStatus: 'error',
    expectedErrorType: 'multiple_manuscripts'
  },
  {
    name: 'Invalid ZIP with no manuscript',
    files: [
      { name: 'Document.docx', type: 'docx' },
      { name: 'Figure1.jpg', type: 'jpg' },
      { name: 'Table1.xlsx', type: 'xlsx' }
    ],
    zipFilename: 'article_456.zip',
    expectedStatus: 'error',
    expectedErrorType: 'no_manuscript'
  },
  {
    name: 'Invalid ZIP with multiple manuscripts',
    files: [
      { name: 'Manuscript.docx', type: 'docx' },
      { name: 'Manuscript_v2.docx', type: 'docx' },
      { name: 'Figure1.jpg', type: 'jpg' }
    ],
    zipFilename: 'article_789.zip',
    expectedStatus: 'error',
    expectedErrorType: 'multiple_manuscripts'
  },
  {
    name: 'Valid ZIP with manuscript in different case',
    files: [
      { name: 'MANUSCRIPT.DOC', type: 'doc' },
      { name: 'Figure1.jpg', type: 'jpg' }
    ],
    zipFilename: 'article_101.zip',
    expectedStatus: 'ok'
  },
  {
    name: 'Valid ZIP with manuscript containing other text',
    files: [
      { name: 'Final_Manuscript_Version.docx', type: 'docx' },
      { name: 'Figure1.jpg', type: 'jpg' }
    ],
    zipFilename: 'article_202.zip',
    expectedStatus: 'ok'
  }
];

// Function to run all tests
export const runValidationTests = () => {
  console.log('🧪 Running Manuscript Validation Tests...\n');
  
  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log('Files:', testCase.files.map(f => f.name));
    
    const articleId = extractArticleId(testCase.zipFilename);
    const result = validateManuscript(testCase.files, articleId);
    
    console.log('Result:', {
      article_id: result.article_id,
      status: result.status,
      error_type: result.error_type,
      manuscript_files: result.manuscript_files
    });
    
    // Check if test passed
    const passed = result.status === testCase.expectedStatus && 
                  (testCase.expectedErrorType ? result.error_type === testCase.expectedErrorType : true);
    
    console.log(passed ? '✅ PASSED' : '❌ FAILED');
    console.log('---\n');
  });
  
  console.log('🏁 Tests completed!');
};

// Example usage in browser console:
// import { runValidationTests } from './utils/manuscriptValidator.test.js';
// runValidationTests();

// Example of how to use the validation in your components:
export const exampleUsage = () => {
  // Simulate files extracted from a ZIP
  const extractedFiles = [
    { name: 'Manuscript.docx', type: 'docx' },
    { name: 'Figure1.jpg', type: 'jpg' },
    { name: 'References.pdf', type: 'pdf' }
  ];
  
  const zipFilename = 'ijd_123_25.zip';
  
  // Run validation
  const validationResult = runManuscriptValidation(extractedFiles, zipFilename);
  
  console.log('Validation Result:', validationResult);
  
  // Check if we can proceed
  if (validationResult.can_proceed) {
    console.log('✅ Validation passed - can proceed with processing');
  } else {
    console.log('❌ Validation failed:', validationResult.error_message);
    // Show error to user or handle accordingly
  }
  
  return validationResult;
};

// Test the extractArticleId function
export const testArticleIdExtraction = () => {
  const testFilenames = [
    'article_123.zip',
    'ijd_456_25.zip',
    'manuscript.zip',
    'test-file.zip',
    'complex_name_with_underscores.zip'
  ];

  console.log('Testing Article ID Extraction:');
  testFilenames.forEach(filename => {
    const articleId = extractArticleId(filename);
    console.log(`${filename} → ${articleId}`);
  });
};

// Quick test for the real case
export const testRealCase = () => {
  console.log('🧪 Testing Real Case: idoj_605_25');

  const files = [
    { name: 'copyright.doc.docx', type: 'docx' },
    { name: 'FirstPage.docx', type: 'docx' },
    { name: 'Manuscript with track changes.docx', type: 'docx' },
    { name: 'Manuscript.docx', type: 'docx' },
    { name: 'Reply to reviewers.docx', type: 'docx' },
    { name: 'idoj_605_25.png', type: 'png' },
    { name: 'idoj_605_25.xml', type: 'xml' },
    { name: 'IMG_20250408_103846.jpg', type: 'jpg' },
    { name: 'IMG_20250408_104205.jpg', type: 'jpg' },
    { name: 'Patient consent form .pdf', type: 'pdf' },
    { name: 'WhatsApp Image 2025-04-08 at 2.26.29 PM.JPG', type: 'jpg' }
  ];

  const result = validateManuscript(files, 'idoj_605_25');

  console.log('Validation Result:', {
    article_id: result.article_id,
    status: result.status,
    error_type: result.error_type,
    error_message: result.error_message,
    manuscript_files: result.manuscript_files,
    total_files: result.total_files
  });

  // Check manuscript detection
  const manuscriptFiles = files.filter(file =>
    file.name.toLowerCase().includes('manuscript') &&
    (file.name.toLowerCase().endsWith('.docx') ||
     file.name.toLowerCase().endsWith('.doc'))
  );

  console.log('Manual manuscript detection:', manuscriptFiles.map(f => f.name));
  console.log('Expected: 2 manuscripts, Found:', manuscriptFiles.length);

  return result;
};

// Test complete validation (manuscript + FP)
export const testCompleteValidation = () => {
  console.log('🧪 Testing Complete Validation (Manuscript + FP)');

  const testCases = [
    {
      name: 'Real case: idoj_605_25 (multiple manuscripts + FP exists)',
      files: [
        { name: 'copyright.doc.docx', type: 'docx' },
        { name: 'FirstPage.docx', type: 'docx' },
        { name: 'Manuscript with track changes.docx', type: 'docx' },
        { name: 'Manuscript.docx', type: 'docx' },
        { name: 'Reply to reviewers.docx', type: 'docx' }
      ],
      zipName: 'idoj_605_25.zip'
    },
    {
      name: 'Missing FP case',
      files: [
        { name: 'Manuscript.docx', type: 'docx' },
        { name: 'Figure1.jpg', type: 'jpg' }
      ],
      zipName: 'test_123.zip'
    },
    {
      name: 'Missing both manuscript and FP',
      files: [
        { name: 'Figure1.jpg', type: 'jpg' },
        { name: 'Table1.xlsx', type: 'xlsx' }
      ],
      zipName: 'test_456.zip'
    },
    {
      name: 'Perfect case (1 manuscript + 1 FP)',
      files: [
        { name: 'Manuscript.docx', type: 'docx' },
        { name: 'FirstPage.docx', type: 'docx' },
        { name: 'Figure1.jpg', type: 'jpg' }
      ],
      zipName: 'perfect_789.zip'
    }
  ];

  testCases.forEach(testCase => {
    console.log(`\n--- ${testCase.name} ---`);
    const result = runCompleteValidation(testCase.files, testCase.zipName);

    console.log('Overall Status:', result.overall_status);
    console.log('Manuscript Status:', result.manuscript_validation.status, result.manuscript_validation.error_type);
    console.log('FP Status:', result.fp_validation.status, result.fp_validation.error_type);

    if (result.primary_error) {
      console.log('Primary Error:', result.primary_error.error_type, '-', result.primary_error.error_message);
    }
    if (result.secondary_error) {
      console.log('Secondary Error:', result.secondary_error.error_type, '-', result.secondary_error.error_message);
    }

    // Check FP detection
    const fpFiles = testCase.files.filter(file => {
      const fileName = file.name.toLowerCase();
      return (fileName.includes('fp') || fileName.includes('firstpage')) &&
             (fileName.endsWith('.docx') || fileName.endsWith('.doc'));
    });
    console.log('FP Files Found:', fpFiles.map(f => f.name));
  });
};
