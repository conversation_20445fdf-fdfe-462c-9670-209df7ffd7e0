import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DataTable from 'react-data-table-component';
import { Icons } from '../common';
import './AdminDashboard.css';

const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:4999';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState(null);
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [articlesLoading, setArticlesLoading] = useState(false);
  const [error, setError] = useState('');
  const [dateRange, setDateRange] = useState(30);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [filterText, setFilterText] = useState('');
  const [resetPaginationToggle, setResetPaginationToggle] = useState(false);

  // Handle article ID click to navigate to search page
  const handleArticleIdClick = (articleId) => {
    navigate(`/admin/search?articleId=${encodeURIComponent(articleId)}`);
  };
  const [dateFilter, setDateFilter] = useState({ start: '', end: '' });
  const [refCountFilter, setRefCountFilter] = useState({ min: '', max: '' });

  // const fetchDashboardData = async () => {
  //   try {
  //     const response = await fetch(
  //       `${API_BASE}/api/dashboard/stats?days=${dateRange}`,
  //       {
  //         credentials: 'include',
  //       }
  //     );

  //     if (response.ok) {
  //       const data = await response.json();
  //       setStats(data);
  //     } else {
  //       setError('Failed to fetch dashboard data');
  //     }
  //   } catch (error) {
  //     setError('Network error while fetching dashboard data');
  //     console.error('Dashboard fetch error:', error);
  //   }
  // };

  const fetchArticles = async (search = '', dateStart = '', dateEnd = '') => {
    try {
      setArticlesLoading(true);
      const params = new URLSearchParams({
        page: 1,
        per_page: 10, // Fetch a large number to get all articles
        search: search,
        sort_by: 'created_at',
        sort_order: 'desc',
      });

      // Add date filters if provided
      if (dateStart) params.append('date_start', dateStart);
      if (dateEnd) params.append('date_end', dateEnd);

      const response = await fetch(
        `${API_BASE}/api/dashboard/articles?${params}`,
        {
          credentials: 'include',
        }
      );

      if (response.ok) {
        const data = await response.json();
        setArticles(data.articles);
        setArticlesLoading(false);
        if (loading) setLoading(false);
      } else {
        setError('Failed to fetch articles');
        setArticlesLoading(false);
        if (loading) setLoading(false);
      }
    } catch (error) {
      setError('Network error while fetching articles');
      setArticlesLoading(false);
      if (loading) setLoading(false);
      console.error('Articles fetch error:', error);
    }
  };

  useEffect(() => {
    // fetchDashboardData();
    fetchArticles('', '', '');
  }, [dateRange]);

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Client-side filtering function
  const getFilteredData = () => {
    let filtered = [...articles];

    // Apply search filter
    if (filterText) {
      filtered = filtered.filter(article =>
        article.articleId.toLowerCase().includes(filterText.toLowerCase())
      );
    }

    // Apply reference count filter
    if (refCountFilter.min && refCountFilter.min.trim() !== '') {
      const minCount = parseInt(refCountFilter.min);
      if (!isNaN(minCount)) {
        filtered = filtered.filter(article => article.referenceCount >= minCount);
      }
    }

    if (refCountFilter.max && refCountFilter.max.trim() !== '') {
      const maxCount = parseInt(refCountFilter.max);
      if (!isNaN(maxCount)) {
        filtered = filtered.filter(article => article.referenceCount <= maxCount);
      }
    }

    return filtered;
  };

  // Simple handlers for client-side operations
  const handleFilter = (searchText) => {
    setFilterText(searchText);
    setResetPaginationToggle(!resetPaginationToggle);
  };

  const handleDateFilterChange = (field, value) => {
    const newDateFilter = { ...dateFilter, [field]: value };
    setDateFilter(newDateFilter);
    setResetPaginationToggle(!resetPaginationToggle);
    fetchArticles(filterText, newDateFilter.start, newDateFilter.end);
  };

  const handleRefCountFilterChange = (field, value) => {
    const newRefCountFilter = { ...refCountFilter, [field]: value };
    setRefCountFilter(newRefCountFilter);
    setResetPaginationToggle(!resetPaginationToggle);
  };

  const handleClearFilters = () => {
    setFilterText('');
    setDateFilter({ start: '', end: '' });
    setRefCountFilter({ min: '', max: '' });
    setResetPaginationToggle(!resetPaginationToggle);
    fetchArticles('', '', '');
  };

  // Helper function to format quality metrics
  const formatQualityBadge = (value, total, type) => {
    if (total === 0) return <span className="quality-badge quality-badge-empty">0</span>;
    const percentage = Math.round((value / total) * 100);
    return (
      <span className={`quality-badge quality-badge-${type}`} title={`${value}/${total} (${percentage}%)`}>
        {value}
      </span>
    );
  };

  // Helper function to format status badge
  const formatStatusBadge = (status) => {
    const statusColors = {
      'completed': 'green',
      'processing': 'blue',
      'pending': 'yellow',
      'error': 'red',
      'review': 'orange'
    };
    const color = statusColors[status] || 'gray';
    return <span className={`status-badge status-badge-${color}`}>{status}</span>;
  };

  // Helper function to format priority badge
  const formatPriorityBadge = (priority) => {
    const priorityColors = {
      'HIGH': 'red',
      'MEDIUM': 'yellow',
      'LOW': 'green'
    };
    const color = priorityColors[priority] || 'gray';
    return <span className={`priority-badge priority-badge-${color}`}>{priority}</span>;
  };

  // Define enhanced table columns for react-data-table-component
  const articleColumns = [
    {
      name: 'Article ID',
      selector: row => row.articleId,
      sortable: true,
      sortField: 'article_id',
      cell: row => (
        <div className="article-id-cell">
          <button
            className="article-id clickable-article-id"
            onClick={() => handleArticleIdClick(row.articleId)}
            title={`Click to search references for ${row.articleId}`}
          >
            {row.articleId}
          </button>
          {row.journalName && (
            <div className="journal-name">{row.journalName}</div>
          )}
        </div>
      ),
      width: '220px',
    },
    {
      name: 'References',
      selector: row => row.referenceCount,
      sortable: true,
      sortField: 'reference_count',
      cell: row => (
        <div className="reference-summary">
          <div className="reference-count-main">{row.referenceCount}</div>
          <div className="quality-breakdown">
            {formatQualityBadge(row.high_confidence_count, row.referenceCount, 'high')}
            {row.needs_review_count > 0 && formatQualityBadge(row.needs_review_count, row.referenceCount, 'review')}
          </div>
        </div>
      ),
      center: true,
      width: '140px',
    },
    {
      name: 'Quality Score',
      selector: row => row.totalQualityScore,
      sortable: true,
      cell: row => (
        <div className="quality-score-cell">
          <div className="quality-score">{row.totalQualityScore}%</div>
          <div className="quality-rates">
            <span className="success-rate" title="Success Rate">✓ {row.successRate}%</span>
            {row.reviewRate > 0 && (
              <span className="review-rate" title="Review Rate">⚠ {row.reviewRate}%</span>
            )}
          </div>
        </div>
      ),
      center: true,
      width: '120px',
    },
    {
      name: 'Sources',
      selector: row => row.sourceBreakdown,
      cell: row => (
        <div className="source-breakdown">
          {row.sourceBreakdown.pubmed > 0 && (
            <span className="source-badge source-pubmed" title="PubMed">
              PM: {row.sourceBreakdown.pubmed}
            </span>
          )}
          {row.sourceBreakdown.crossref > 0 && (
            <span className="source-badge source-crossref" title="CrossRef">
              CR: {row.sourceBreakdown.crossref}
            </span>
          )}
          {row.sourceBreakdown.not_found > 0 && (
            <span className="source-badge source-not-found" title="Not Found">
              NF: {row.sourceBreakdown.not_found}
            </span>
          )}
        </div>
      ),
      width: '120px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <div className="status-cell">
          {formatStatusBadge(row.status)}
          {formatPriorityBadge(row.priority)}
        </div>
      ),
      width: '120px',
    },
    {
      name: 'Processing',
      selector: row => row.processedBy,
      cell: row => (
        <div className="processing-info">
          <div className="processed-by">{row.processedBy || 'System'}</div>
          <div className="processing-source">{row.processingSource || 'manual'}</div>
        </div>
      ),
      width: '120px',
    },
    {
      name: 'Created',
      selector: row => row.createdAt,
      sortable: true,
      sortField: 'created_at',
      cell: row => formatDate(row.createdAt),
      width: '140px',
    }
  ];

  const StatCard = ({ title, value, icon: Icon, color = 'blue' }) => (
    <div className={`stat-card stat-card-${color}`}>
      <div className="stat-card-content">
        <div className="stat-card-header">
          <h3 className="stat-card-title">{title}</h3>
          <Icon className="stat-card-icon" />
        </div>
        <p className="stat-card-value">{value}</p>
      </div>
    </div>
  );

  if (loading && !stats) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <h1 className="dashboard-title">Admin Dashboard</h1>
        <div className="dashboard-controls">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(Number(e.target.value))}
            className="date-range-select"
          >
            <option value={7}>Last 7 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
            <option value={365}>Last year</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="error-banner">
          <Icons.ExclamationIcon className="error-icon" />
          {error}
        </div>
      )}

      {stats && (
        <>
          {/* Statistics Cards */}
          <div className="stats-grid">
            <StatCard
              title="Total Articles"
              value={stats.totalArticles}
              icon={Icons.DocumentIcon}
              color="blue"
            />
            <StatCard
              title="Processed Today"
              value={stats.articlesToday}
              icon={Icons.ChartIcon}
              color="green"
            />
            <StatCard
              title={`Last ${dateRange} Days`}
              value={stats.articlesInRange}
              icon={Icons.CalendarIcon}
              color="purple"
            />
            <StatCard
              title="Total References"
              value={stats.totalReferences}
              icon={Icons.CollectionIcon}
              color="orange"
            />
          </div>

          {/* Daily Activity Chart */}
          {stats.dailyStats && stats.dailyStats.length > 0 && (
            <div className="chart-section">
              <h2 className="section-title">Daily Processing Activity</h2>
              <div className="chart-container">
                <div className="chart-bars">
                  {stats.dailyStats.slice(0, 14).reverse().map((day, index) => (
                    <div key={index} className="chart-bar-container">
                      <div
                        className="chart-bar"
                        style={{
                          height: `${Math.max((day.count / Math.max(...stats.dailyStats.map(d => d.count))) * 100, 5)}%`,
                        }}
                        title={`${day.date}: ${day.count} articles`}
                      ></div>
                      <span className="chart-label">
                        {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Articles List */}
      <div className="articles-section">
        <div className="section-header">
          <h2 className="section-title">Recent Articles</h2>
        </div>

        <DataTable
          columns={articleColumns}
          data={getFilteredData()}
          progressPending={articlesLoading}
          pagination
          paginationPerPage={perPage}
          paginationRowsPerPageOptions={[10, 20, 50, 100]}
          paginationResetDefaultPage={resetPaginationToggle}
          onChangeRowsPerPage={(newPerPage) => setPerPage(newPerPage)}
          sortable
          subHeader
          subHeaderComponent={
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              padding: '1rem',
              backgroundColor: '#f9fafb',
              borderRadius: '8px',
              marginBottom: '1rem',
              flexWrap: 'wrap'
            }}>
              {/* Search Input */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Icons.SearchIcon style={{ width: '1rem', height: '1rem', color: '#6b7280' }} />
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={filterText}
                  onChange={(e) => handleFilter(e.target.value)}
                  style={{
                    padding: '0.5rem 0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '0.875rem',
                    minWidth: '200px'
                  }}
                />
              </div>

              {/* Date Range Filters */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>From:</span>
                <input
                  type="date"
                  value={dateFilter.start}
                  onChange={(e) => handleDateFilterChange('start', e.target.value)}
                  style={{
                    padding: '0.5rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '0.875rem'
                  }}
                />
                <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>To:</span>
                <input
                  type="date"
                  value={dateFilter.end}
                  onChange={(e) => handleDateFilterChange('end', e.target.value)}
                  style={{
                    padding: '0.5rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '0.875rem'
                  }}
                />
              </div>

              {/* Reference Count Filters */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>Refs:</span>
                <input
                  type="number"
                  placeholder="Min"
                  value={refCountFilter.min}
                  onChange={(e) => handleRefCountFilterChange('min', e.target.value)}
                  style={{
                    padding: '0.5rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '0.875rem',
                    width: '80px'
                  }}
                />
                <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>-</span>
                <input
                  type="number"
                  placeholder="Max"
                  value={refCountFilter.max}
                  onChange={(e) => handleRefCountFilterChange('max', e.target.value)}
                  style={{
                    padding: '0.5rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '0.875rem',
                    width: '80px'
                  }}
                />
              </div>

              {/* Clear Filters Button */}
              <button
                onClick={handleClearFilters}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#ef4444',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}
              >
                <Icons.XIcon style={{ width: '1rem', height: '1rem' }} />
                Clear Filters
              </button>

              {/* Filter Status */}
              {(filterText || dateFilter.start || dateFilter.end || refCountFilter.min || refCountFilter.max) && (
                <div style={{
                  fontSize: '0.875rem',
                  color: '#3b82f6',
                  fontWeight: '500'
                }}>
                  {filterText && `Search: "${filterText}"`}
                  {filterText && (dateFilter.start || dateFilter.end || refCountFilter.min || refCountFilter.max) && ' | '}
                  {(dateFilter.start || dateFilter.end) &&
                    `Date: ${dateFilter.start || 'Any'} to ${dateFilter.end || 'Any'}`
                  }
                  {(dateFilter.start || dateFilter.end) && (refCountFilter.min || refCountFilter.max) && ' | '}
                  {(refCountFilter.min || refCountFilter.max) &&
                    `Refs: ${refCountFilter.min || '0'} to ${refCountFilter.max || '∞'}`
                  }
                </div>
              )}
            </div>
          }
          noDataComponent={
            <div style={{
              padding: '3rem',
              textAlign: 'center',
              color: '#6b7280',
              fontSize: '1rem'
            }}>
              {(filterText || dateFilter.start || dateFilter.end || refCountFilter.min || refCountFilter.max)
                ? 'No articles match your current filters'
                : 'No articles found'
              }
            </div>
          }
          customStyles={{
            table: {
              style: {
                backgroundColor: 'white',
              },
            },
            headRow: {
              style: {
                backgroundColor: '#f9fafb',
                borderBottomColor: '#e5e7eb',
                borderBottomWidth: '2px',
              },
            },
            headCells: {
              style: {
                color: '#374151',
                fontWeight: '600',
                fontSize: '0.875rem',
                paddingLeft: '1rem',
                paddingRight: '1rem',
              },
            },
            cells: {
              style: {
                paddingLeft: '1rem',
                paddingRight: '1rem',
                paddingTop: '0.75rem',
                paddingBottom: '0.75rem',
              },
            },
            rows: {
              style: {
                '&:hover': {
                  backgroundColor: '#f9fafb',
                },
              },
            },
            subHeader: {
              style: {
                padding: 0,
              },
            },
          }}
        />
      </div>
    </div>
  );
};

export default AdminDashboard;
