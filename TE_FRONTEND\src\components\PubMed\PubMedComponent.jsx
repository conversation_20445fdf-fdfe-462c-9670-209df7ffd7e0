import React, { useState } from "react";
import "../../App.css";
import useFetchPubMedData from "../../hooks/useFetchPubMedData";
import {
  NotFoundReferences,
  ErrorNotification,
} from "../common";

import { downloadDocx, normalizeReferences } from "../../utils/referenceUtils";
import downloadExcel from "../../features/downloadExcel";
import { getFilteredData } from "../../utils/filters";
import { useRole } from "../../context/AuthContext";
import { useZipQueue } from "../../context/ZipQueueContext";
import { useNavigate } from "react-router-dom";
import "./MultiSelect.css";

// Import new components
import HeaderActions from "./HeaderActions";
import ProgressSteps from "./ProgressSteps";
import ReferenceStatisticsWrapper from "./ReferenceStatisticsWrapper";
import ReferenceTable from "./ReferenceTable";
import ArticleIdSaveBox from "./ArticleIdSaveBox";

/**
 * Main PubMed component that manages state and coordinates child components
 */
const PubMedComponent = ({ terms = {}, dbMode, fromZipProcessor, zipId }) => {
  const [prog, setProg] = useState(0);
  const { role: currentRole, isAdmin } = useRole();
  const { markZipAsProcessed } = useZipQueue();
  const navigate = useNavigate();
  console.log(currentRole,'currentRole');
  const isAdminMode = isAdmin;
  const [isDiffViewerOpen, setIsDiffViewerOpen] = useState(false);
  const [showStatistics, setShowStatistics] = useState(false);
  const [activeFilter, setActiveFilter] = useState("all");
  const [rowSelections, setRowSelections] = useState({});
  // Article ID state (use from terms if present, else local)
  const [articleId, setArticleId] = useState(terms?.articleId || "");

  // Check if data is from database (for fast-track rendering)
  const isFromDb = !!terms.fromDb;

  // Always call the hook at the top level (never conditionally!)
  const pubmedHook = useFetchPubMedData(terms?.data || [], true, setProg);

  // Using centralized normalizeReferences from utils

  // Use DB data if fromDb, otherwise use hook data
  const normalizedData = isFromDb ? { found: normalizeReferences(terms.data) } : pubmedHook.data ? { found: normalizeReferences(pubmedHook.data.found) } : { found: [] };
  const data = normalizedData;
  const loading = isFromDb ? false : pubmedHook.loading;
  const error = isFromDb ? null : pubmedHook.error;
  const setData = pubmedHook.setData;
  const progressStep = isFromDb ? null : pubmedHook.progressStep;

  // --- Save to backend only when user clicks Save ---
  const [saveStatus, setSaveStatus] = useState("");
  const handleSaveClick = async () => {
    if (!articleId) {
      setSaveStatus("Please enter an Article ID before saving.");
      return;
    }
    if (!data?.found || data.found.length === 0) {
      setSaveStatus("No references to save.");
      return;
    }
    setSaveStatus("Saving...");
    try {
      await saveReferencesToBackend(articleId, data.found);
      setSaveStatus("References saved successfully.");

      // Auto-mark ZIP as processed if this is from ZIP workflow
      if (fromZipProcessor && zipId) {
        setSaveStatus("References saved successfully. Processing ZIP completion...");

        // Mark ZIP as processed immediately
        try {
          markZipAsProcessed(zipId);
          setSaveStatus("References saved and ZIP marked as processed!");

          // Navigate back to queue after a short delay
          setTimeout(() => {
            const queuePath = isAdmin ? '/admin/zip-queue' : '/zip-queue';
            navigate(queuePath);
          }, 2000);
        } catch (error) {
          console.error('Error marking ZIP as processed:', error);
          setSaveStatus("References saved but failed to mark ZIP as processed.");
        }
      }
    } catch (e) {
      console.error('Error saving references:', e);
      setSaveStatus("Error saving references.");
    }
  };

  // Save references to backend with comprehensive metadata
  async function saveReferencesToBackend(articleId, references) {
    try {
      const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:4999';

      // Generate session ID for tracking
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Prepare comprehensive metadata
      const processingMetadata = {
        sessionId,
        processingDate: new Date().toISOString(),
        userAgent: navigator.userAgent,
        processingDuration: Date.now() - (window.processingStartTime || Date.now()),
        fromZipWorkflow: fromZipProcessor || false,
        zipId: zipId || null,
        isAdminUser: isAdmin || false,
        totalReferencesProcessed: references.length,
        processingSteps: ['pubmed', 'crossref', 'styling'], // Could be dynamic
        qualityMetrics: calculateClientSideQuality(references)
      };

      const payload = {
        articleId,
        references,
        processingMetadata,
        processedBy: isAdmin ? 'admin_user' : 'regular_user',
        processingSource: fromZipProcessor ? 'zip_workflow' : 'manual_entry',
        sessionId
      };

      const response = await fetch(`${API_BASE}/api/references`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      return result;
    } catch (e) {
      console.error('Error saving references:', e);
      throw e;
    }
  }

  // Calculate client-side quality metrics
  function calculateClientSideQuality(references) {
    const metrics = {
      totalCount: references.length,
      pubmedCount: 0,
      crossrefCount: 0,
      notFoundCount: 0,
      averageQuality: 0,
      qualityDistribution: { high: 0, medium: 0, low: 0 }
    };

    let totalQuality = 0;

    references.forEach(ref => {
      // Count by source
      if (ref.type === 'FOUND' || ref.type?.includes('PubMed')) {
        metrics.pubmedCount++;
      } else if (ref.type?.includes('CrossRef')) {
        metrics.crossrefCount++;
      } else {
        metrics.notFoundCount++;
      }

      // Quality scoring
      const score = ref.score || 0;
      totalQuality += score;

      if (score >= 85) metrics.qualityDistribution.high++;
      else if (score >= 70) metrics.qualityDistribution.medium++;
      else metrics.qualityDistribution.low++;
    });

    metrics.averageQuality = references.length > 0 ? totalQuality / references.length : 0;

    return metrics;
  }

  // Memoize filtered data to prevent unnecessary recalculations
  const filteredData = React.useMemo(() => {
    return getFilteredData(data?.found, activeFilter);
  }, [data?.found, activeFilter]);

  // Use callbacks for event handlers to prevent unnecessary re-renders
  const handleCheckboxChange = React.useCallback((option, elem) => {
    const rowId = elem.ind;
    setRowSelections((prev) => {
      const currentRowSelections = prev[rowId] || [];
      const newRowSelections = currentRowSelections.includes(option)
        ? currentRowSelections.filter((item) => item !== option)
        : [...currentRowSelections, option];

      // IMMUTABLY update the incorrect array in data.found
      setData((prevData) => ({
        ...prevData,
        found: prevData.found.map((item, idx) =>
          idx === rowId ? { ...item, incorrect: newRowSelections } : item
        ),
      }));

      return {
        ...prev,
        [rowId]: newRowSelections,
      };
    });
  }, [setData]);

  const handleFilterChange = React.useCallback((filterType) => {
    setActiveFilter(filterType);
  }, []);

  const handleReferenceEdit = React.useCallback((index, newText) => {
    setData((prevData) => ({
      ...prevData,
      found: prevData.found.map((ref) =>
        ref.ind === index ? { ...ref, finalStr: newText } : ref
      ),
    }));
  }, [setData]);

  const toggleDiffViewer = React.useCallback(() => {
    setIsDiffViewerOpen((prev) => !prev);
  }, []);

  const toggleStatistics = React.useCallback(() => {
    setShowStatistics((prev) => !prev);
  }, []);

  const handleDownloadDocx = React.useCallback(() => {
    downloadDocx(data.found);
  }, [data]);

  const handleDownloadExcel = React.useCallback(() => {
    downloadExcel(data.found, "references.xlsx");
  }, [data]);

  // Handle error and loading states
  if (terms.isLoading === 2 && terms.data.length === 0)
    return <NotFoundReferences />;
  if (error) return <ErrorNotification message={error} />;
  if (typeof data === "string") return <ErrorNotification message={data} />;

  return (
    <>
      {/* Article ID input and Save */}
      <ArticleIdSaveBox
        articleId={articleId}
        setArticleId={setArticleId}
        handleSaveClick={handleSaveClick}
        saveStatus={saveStatus}
        adminMode={isAdminMode}
        loading={loading}
        hasData={data?.found && data.found.length > 0}
      />

      {/* Progress and loading indicators */}
      {!isFromDb && (
        <ProgressSteps
          loading={loading}
          progress={prog}
          progressStep={progressStep}
          totalCount={terms?.data?.length}
        />
      )}

      {/* Statistics display */}
      <ReferenceStatisticsWrapper
        showStatistics={!loading && showStatistics}
        references={data?.found}
        onFilterChange={handleFilterChange}
        activeFilter={activeFilter}
      />

      {/* Header action buttons and Reference table */}
      {!loading && (
        <>
          <div className="found-citation-wrapper">
            <div className="results-header">
              <h3 className="text-lg font-semibold text-gray-900">
                Enhanced Citations
              </h3>
            </div>
            {/* Header action buttons */}
            <HeaderActions
              isDiffViewerOpen={isDiffViewerOpen}
              toggleDiffViewer={toggleDiffViewer}
              showStatistics={showStatistics}
              toggleStatistics={toggleStatistics}
              onDownloadDocx={handleDownloadDocx}
              onDownloadExcel={handleDownloadExcel}
            />
          </div>
          <ReferenceTable
            data={filteredData}
            isDiffViewerOpen={isDiffViewerOpen}
            handleCheckboxChange={handleCheckboxChange}
            rowSelections={rowSelections}
            onReferenceEdit={handleReferenceEdit}
          />
        </>
      )}
    </>
  );
};

export default PubMedComponent;
