import React, { useState, useEffect } from 'react';
import { Icons } from '../common';
import { authorQueryService } from '../../services/authorQueryService';
import './AuthorSummary.css';

const AuthorSummary = ({ articleId, compact = false }) => {
  const [authorData, setAuthorData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // useEffect(() => {
  //   if (articleId) {
  //     loadAuthorData();
  //   }
  // }, [articleId]);

  // const loadAuthorData = async () => {
  //   setLoading(true);
  //   setError(null);
  //   try {
  //     const response = await authorQueryService.getArticleMetadata(articleId);
  //     setAuthorData(response.article);
  //   } catch (err) {
  //     console.error('Error loading author data:', err);
  //     setError(err.message);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const getCopyrightCounts = () => {
    if (!authorData?.authors) return { yes: 0, no: 0, unknown: 0 };
    
    return authorData.authors.reduce((counts, author) => {
      const status = author.copyright_status?.toLowerCase();
      if (status === 'yes') counts.yes++;
      else if (status === 'no') counts.no++;
      else counts.unknown++;
      return counts;
    }, { yes: 0, no: 0, unknown: 0 });
  };

  if (loading) {
    return (
      <div className={`author-summary ${compact ? 'compact' : ''} loading`}>
        <Icons.UserIcon className="summary-icon" />
        <span>Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`author-summary ${compact ? 'compact' : ''} error`}>
        <Icons.ExclamationTriangleIcon className="summary-icon error" />
        <span>Error loading authors</span>
      </div>
    );
  }

  if (!authorData?.authors || authorData.authors.length === 0) {
    return (
      <div className={`author-summary ${compact ? 'compact' : ''} no-data`}>
        <Icons.UserIcon className="summary-icon" />
        <span>No author data</span>
      </div>
    );
  }

  const counts = getCopyrightCounts();
  const totalAuthors = authorData.authors.length;

  if (compact) {
    return (
      <div className="author-summary compact">
        <Icons.UserIcon className="summary-icon" />
        <span className="author-count">{totalAuthors}</span>
        <div className="copyright-indicators">
          {counts.yes > 0 && <span className="indicator yes">{counts.yes}✓</span>}
          {counts.no > 0 && <span className="indicator no">{counts.no}✗</span>}
          {counts.unknown > 0 && <span className="indicator unknown">{counts.unknown}?</span>}
        </div>
      </div>
    );
  }

  return (
    <div className="author-summary">
      <div className="summary-header">
        <Icons.UserIcon className="summary-icon" />
        <span className="summary-title">Authors ({totalAuthors})</span>
      </div>
      <div className="summary-details">
        <div className="copyright-breakdown">
          <span className="breakdown-item yes">
            <span className="count">{counts.yes}</span>
            <span className="label">Agreed</span>
          </span>
          <span className="breakdown-item no">
            <span className="count">{counts.no}</span>
            <span className="label">Not Agreed</span>
          </span>
          {counts.unknown > 0 && (
            <span className="breakdown-item unknown">
              <span className="count">{counts.unknown}</span>
              <span className="label">Unknown</span>
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthorSummary;
