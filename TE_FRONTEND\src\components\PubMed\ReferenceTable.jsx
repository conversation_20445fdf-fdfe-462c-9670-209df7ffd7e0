import React from "react";
import DetailedBox from "./DetailedBox";
import { getCurrentRole } from "../../utils/appUtils";

/**
 * ReferenceTable component for PubMed interface
 * Renders the table of references with DetailedBox rows
 */
const ReferenceTable = ({
  data,
  isDiffViewerOpen,
  handleCheckboxChange,
  rowSelections,
  onReferenceEdit,
}) => {
  const currentRole = getCurrentRole();
  const isAdminMode = currentRole === 'admin';

  if (!data || data.length === 0) return null;

  return (
    <table className={isAdminMode ? "table table-test" : "table"}>
      <thead>
        <tr>
          <th>Reference</th>
          {isDiffViewerOpen ? (
            <th>Comparison</th>
          ) : (
            <>
              <th>Original Citation</th>
              <th>Enhanced Citation</th>
              {isAdminMode && <th>Actions</th>}
            </>
          )}
        </tr>
      </thead>
      <tbody>
        {data.map((elem, ind) => (
          <DetailedBox
            elem={elem}
            key={"found" + ind}
            isDiffViewerOpen={isDiffViewerOpen}
            selectedOptions={rowSelections[elem.ind] || []}
            handleCheckboxChange={handleCheckboxChange}
            onReferenceEdit={onReferenceEdit}
          />
        ))}
      </tbody>
    </table>
  );
};

export default ReferenceTable;