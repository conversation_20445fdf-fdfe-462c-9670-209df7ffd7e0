/**
 * Reference quality assessment utilities
 */

/**
 * Evaluates the quality of a reference
 * @param {Object} ref - Reference object
 * @returns {Object} Quality assessment with score and suggestions
 */
export function checkQuality(ref) {
  const originalText =
    ref.term !== undefined && ref.term !== null ? ref.term : "";
  const finalText =
    ref.finalStr !== undefined && ref.finalStr !== null
      ? ref.finalStr
      : ref.term !== undefined && ref.term !== null
      ? ref.term
      : "";

  // Calculate quality based on improvements made
  const improvementScore = calculateImprovementScore(originalText, finalText);
  const structuralScore = calculateStructuralScore(finalText);
  const completenessScore = calculateCompletenessScore(finalText);

  // Weighted average of different quality aspects
  const overallScore = Math.round(
    improvementScore * 0.4 + structuralScore * 0.3 + completenessScore * 0.3
  );

  const issues = [];
  const suggestions = [];

  // Analyze issues based on scores
  if (improvementScore < 70) {
    issues.push("Significant changes needed");
    suggestions.push("Original reference required major corrections");
  }

  if (structuralScore < 70) {
    issues.push("Formatting issues");
    suggestions.push("Check punctuation and structure");
  }

  if (completenessScore < 70) {
    issues.push("Missing information");
    suggestions.push("Some required fields may be missing");
  }

  return {
    issues,
    suggestions,
    score: overallScore,
    breakdown: {
      improvement: improvementScore,
      structure: structuralScore,
      completeness: completenessScore,
    },
  };
}

/**
 * Calculates improvement score based on text changes
 * @param {string} original - Original text
 * @param {string} final - Final text
 * @returns {number} Improvement score
 */
function calculateImprovementScore(original, final) {
  if (!original || !final) return 50;

  const changes = calculateTextChanges(original, final);
  const changeRatio = changes / Math.max(original.length, final.length);

  // Less changes = higher quality (original was already good)
  // More changes = lower quality (original needed lots of work)
  if (changeRatio < 0.1) return 95; // Very few changes needed
  if (changeRatio < 0.2) return 85; // Minor changes
  if (changeRatio < 0.4) return 70; // Moderate changes
  if (changeRatio < 0.6) return 55; // Major changes
  return 40; // Extensive changes needed
}

/**
 * Calculates structural score based on formatting
 * @param {string} text - Text to analyze
 * @returns {number} Structural score
 */
function calculateStructuralScore(text) {
  if (!text) return 0;

  let score = 100;

  // Check for proper punctuation
  if (!text.includes(".")) score -= 20;
  if (text.match(/\.{2,}|,{2,}|;{2,}/)) score -= 15; // Duplicate punctuation

  // Check for proper capitalization
  if (!text.match(/^[A-Z]/)) score -= 10; // Should start with capital

  // Check for balanced parentheses
  const openParens = (text.match(/\(/g) || []).length;
  const closeParens = (text.match(/\)/g) || []).length;
  if (openParens !== closeParens) score -= 15;

  // Check for reasonable length
  if (text.length < 30) score -= 20; // Too short
  if (text.length > 500) score -= 10; // Too long

  return Math.max(0, score);
}

/**
 * Calculates completeness score based on reference components
 * @param {string} text - Text to analyze
 * @returns {number} Completeness score
 */
function calculateCompletenessScore(text) {
  if (!text) return 0;

  let score = 0;
  const maxScore = 100;

  // Check for authors (names with initials or full names)
  if (text.match(/[A-Z][a-z]+,?\s+[A-Z]/)) score += 25;

  // Check for year
  if (text.match(/\b(19|20)\d{2}\b/)) score += 20;

  // Check for journal/source
  if (text.match(/[A-Z][a-z]+.*[A-Z]/)) score += 20;

  // Check for volume/pages
  if (text.match(/\d+:\d+/) || text.match(/\d+-\d+/)) score += 15;

  // Check for title (sentence case with proper ending)
  if (text.match(/[A-Z][a-z].*\./)) score += 20;

  return Math.min(score, maxScore);
}

/**
 * Calculates text changes using Levenshtein distance
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} Edit distance
 */
function calculateTextChanges(str1, str2) {
  if (!str1 || !str2) return Math.max(str1?.length || 0, str2?.length || 0);

  const len1 = str1.length;
  const len2 = str2.length;
  const matrix = Array(len1 + 1)
    .fill()
    .map(() => Array(len2 + 1).fill(0));

  // Initialize first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;

  // Fill the matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1, // deletion
          matrix[i][j - 1] + 1, // insertion
          matrix[i - 1][j - 1] + 1 // substitution
        );
      }
    }
  }

  return matrix[len1][len2];
}