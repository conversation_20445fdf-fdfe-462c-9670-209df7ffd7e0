[{"C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\App.js": "3", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\ZipQueueContext.js": "4", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\ArticleContext.js": "5", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\referenceUtils.js": "6", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\appUtils.js": "7", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\AuthContext.jsx": "8", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\ArticleIdSearchBox.jsx": "9", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\JournalAdmin.jsx": "10", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\PubMedComponent.jsx": "11", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipWorkflow.jsx": "12", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FolderZipWorkflow.jsx": "13", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\IndividualZipProcessor.jsx": "14", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipQueueDashboard.jsx": "15", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\ProcessingPage.jsx": "16", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\AdminSearchPage.jsx": "17", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\AdminStatisticsPage.jsx": "18", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\user\\UserHome.jsx": "19", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\index.js": "20", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\auth\\index.js": "21", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\index.js": "22", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\filters.js": "23", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\hooks\\useFetchPubMedData.js": "24", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\features\\downloadExcel.js": "25", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\journalService.js": "26", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\HeaderActions.jsx": "27", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ReferenceStatisticsWrapper.jsx": "28", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ProgressSteps.jsx": "29", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ArticleIdSaveBox.jsx": "30", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ReferenceTable.jsx": "31", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipUploader.jsx": "32", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminStatistics.jsx": "33", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FileListViewer.jsx": "34", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\DocumentPreview.jsx": "35", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FolderUploader.jsx": "36", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\NotFoundReferences.js": "37", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\DiffHighlighter.js": "38", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\ErrorNotification.js": "39", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\ReferenceEditor.js": "40", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\LoaderSpinner.js": "41", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\StatusBadge.js": "42", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminDashboard.jsx": "43", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminNavigation.jsx": "44", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminLayout.jsx": "45", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminUploadPage.jsx": "46", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminJournalsPage.jsx": "47", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\auth\\AdminLogin.jsx": "48", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\DataTable.jsx": "49", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\Icons.jsx": "50", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\quality.js": "51", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\filterReferencesByURL.js": "52", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\searchInPubMed.js": "53", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\searchInCrossRef.js": "54", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\manuscriptValidator.js": "55", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\helpers\\arrayHelpers.js": "56", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\helpers\\stateHelpers.js": "57", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\fetchFromGenAI.js": "58", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\DetailedBox.jsx": "59", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ManualEntrySection.jsx": "60", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ValidationWarning.jsx": "61", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\styling.js": "62", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\env.js": "63", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\urls.js": "64", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\utils\\util.js": "65", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\getJournalName.js": "66", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\prompt.js": "67", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\MultiSelect.js": "68", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\QueryFormModal.jsx": "69", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\utils\\stopWords.js": "70", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\queryMessages.js": "71", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\emailService.js": "72", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\UserManagement.jsx": "73", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\SmartBatchingModal.jsx": "74", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\IndividualZipUpload.jsx": "75", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ArticleMetadataPanel.jsx": "76", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\AuthorSummary.jsx": "77", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\AuthorQueryModal.jsx": "78", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\authorQueryService.js": "79", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\productionEditorMapping.js": "80", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\xmlExtractor.js": "81", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\RaiseQueryButton.jsx": "82", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\zipMetadataUtils.js": "83"}, {"size": 648, "mtime": 1753549981327, "results": "84", "hashOfConfig": "85"}, {"size": 375, "mtime": 1739898273435, "results": "86", "hashOfConfig": "85"}, {"size": 12839, "mtime": 1767590803052, "results": "87", "hashOfConfig": "85"}, {"size": 8398, "mtime": 1767126684568, "results": "88", "hashOfConfig": "85"}, {"size": 1732, "mtime": 1753550000177, "results": "89", "hashOfConfig": "85"}, {"size": 2642, "mtime": 1758655447571, "results": "90", "hashOfConfig": "85"}, {"size": 2566, "mtime": 1753555635542, "results": "91", "hashOfConfig": "85"}, {"size": 3730, "mtime": 1767590803070, "results": "92", "hashOfConfig": "85"}, {"size": 9024, "mtime": 1760984905194, "results": "93", "hashOfConfig": "85"}, {"size": 25314, "mtime": 1752861254514, "results": "94", "hashOfConfig": "85"}, {"size": 10823, "mtime": 1758655447637, "results": "95", "hashOfConfig": "85"}, {"size": 4723, "mtime": 1753556015961, "results": "96", "hashOfConfig": "85"}, {"size": 1179, "mtime": 1754837571126, "results": "97", "hashOfConfig": "85"}, {"size": 17397, "mtime": 1767516640737, "results": "98", "hashOfConfig": "85"}, {"size": 48908, "mtime": 1767510348509, "results": "99", "hashOfConfig": "85"}, {"size": 9338, "mtime": 1760177616914, "results": "100", "hashOfConfig": "85"}, {"size": 9487, "mtime": 1755448422116, "results": "101", "hashOfConfig": "85"}, {"size": 201, "mtime": 1753609410630, "results": "102", "hashOfConfig": "85"}, {"size": 9175, "mtime": 1753559107703, "results": "103", "hashOfConfig": "85"}, {"size": 314, "mtime": 1753554204018, "results": "104", "hashOfConfig": "85"}, {"size": 54, "mtime": 1753553594598, "results": "105", "hashOfConfig": "85"}, {"size": 788, "mtime": 1767119684779, "results": "106", "hashOfConfig": "85"}, {"size": 1153, "mtime": 1751192363856, "results": "107", "hashOfConfig": "85"}, {"size": 9010, "mtime": 1751192664762, "results": "108", "hashOfConfig": "85"}, {"size": 1435, "mtime": 1753205861762, "results": "109", "hashOfConfig": "85"}, {"size": 6401, "mtime": 1760984950299, "results": "110", "hashOfConfig": "85"}, {"size": 2991, "mtime": 1753552297722, "results": "111", "hashOfConfig": "85"}, {"size": 557, "mtime": 1751190994974, "results": "112", "hashOfConfig": "85"}, {"size": 2325, "mtime": 1751190987851, "results": "113", "hashOfConfig": "85"}, {"size": 2366, "mtime": 1767514536704, "results": "114", "hashOfConfig": "85"}, {"size": 1343, "mtime": 1753552211546, "results": "115", "hashOfConfig": "85"}, {"size": 5662, "mtime": 1753886824759, "results": "116", "hashOfConfig": "85"}, {"size": 14551, "mtime": 1755445973052, "results": "117", "hashOfConfig": "85"}, {"size": 6911, "mtime": 1767512861437, "results": "118", "hashOfConfig": "85"}, {"size": 12176, "mtime": 1767597752669, "results": "119", "hashOfConfig": "85"}, {"size": 17286, "mtime": 1760857139082, "results": "120", "hashOfConfig": "85"}, {"size": 709, "mtime": 1747814210066, "results": "121", "hashOfConfig": "85"}, {"size": 852, "mtime": 1747814194074, "results": "122", "hashOfConfig": "85"}, {"size": 722, "mtime": 1750935412093, "results": "123", "hashOfConfig": "85"}, {"size": 9419, "mtime": 1753552359154, "results": "124", "hashOfConfig": "85"}, {"size": 642, "mtime": 1747814216620, "results": "125", "hashOfConfig": "85"}, {"size": 4420, "mtime": 1750880834594, "results": "126", "hashOfConfig": "85"}, {"size": 20779, "mtime": 1761041903993, "results": "127", "hashOfConfig": "85"}, {"size": 2063, "mtime": 1759068216292, "results": "128", "hashOfConfig": "85"}, {"size": 9863, "mtime": 1760970297558, "results": "129", "hashOfConfig": "85"}, {"size": 636, "mtime": 1754883415022, "results": "130", "hashOfConfig": "85"}, {"size": 671, "mtime": 1753554191849, "results": "131", "hashOfConfig": "85"}, {"size": 4109, "mtime": 1753556937896, "results": "132", "hashOfConfig": "85"}, {"size": 103, "mtime": 1754812451894, "results": "133", "hashOfConfig": "85"}, {"size": 19896, "mtime": 1767590803061, "results": "134", "hashOfConfig": "85"}, {"size": 5267, "mtime": 1751192350551, "results": "135", "hashOfConfig": "85"}, {"size": 980, "mtime": 1747814319521, "results": "136", "hashOfConfig": "85"}, {"size": 12355, "mtime": 1752859919167, "results": "137", "hashOfConfig": "85"}, {"size": 5398, "mtime": 1752859930882, "results": "138", "hashOfConfig": "85"}, {"size": 15966, "mtime": 1765813554048, "results": "139", "hashOfConfig": "85"}, {"size": 2606, "mtime": 1758655447647, "results": "140", "hashOfConfig": "85"}, {"size": 391, "mtime": 1744316361480, "results": "141", "hashOfConfig": "85"}, {"size": 4082, "mtime": 1758655447571, "results": "142", "hashOfConfig": "85"}, {"size": 3867, "mtime": 1753552006534, "results": "143", "hashOfConfig": "85"}, {"size": 1024, "mtime": 1757959982669, "results": "144", "hashOfConfig": "85"}, {"size": 4222, "mtime": 1767514916614, "results": "145", "hashOfConfig": "85"}, {"size": 7616, "mtime": 1752838762323, "results": "146", "hashOfConfig": "85"}, {"size": 145, "mtime": 1739898273435, "results": "147", "hashOfConfig": "85"}, {"size": 1053, "mtime": 1759077858293, "results": "148", "hashOfConfig": "85"}, {"size": 10288, "mtime": 1761040236540, "results": "149", "hashOfConfig": "85"}, {"size": 739, "mtime": 1744304362024, "results": "150", "hashOfConfig": "85"}, {"size": 1222, "mtime": 1745681981972, "results": "151", "hashOfConfig": "85"}, {"size": 1005, "mtime": 1753205861754, "results": "152", "hashOfConfig": "85"}, {"size": 6814, "mtime": 1767515837376, "results": "153", "hashOfConfig": "85"}, {"size": 4526, "mtime": 1751192020572, "results": "154", "hashOfConfig": "85"}, {"size": 10270, "mtime": 1765813581915, "results": "155", "hashOfConfig": "85"}, {"size": 5658, "mtime": 1765911011342, "results": "156", "hashOfConfig": "85"}, {"size": 28809, "mtime": 1760970624092, "results": "157", "hashOfConfig": "85"}, {"size": 34711, "mtime": 1767453166337, "results": "158", "hashOfConfig": "85"}, {"size": 9236, "mtime": 1760861196740, "results": "159", "hashOfConfig": "85"}, {"size": 6816, "mtime": 1767514399854, "results": "160", "hashOfConfig": "85"}, {"size": 3650, "mtime": 1760872518753, "results": "161", "hashOfConfig": "85"}, {"size": 16599, "mtime": 1767121519679, "results": "162", "hashOfConfig": "85"}, {"size": 3447, "mtime": 1765908025503, "results": "163", "hashOfConfig": "85"}, {"size": 2917, "mtime": 1767519359097, "results": "164", "hashOfConfig": "85"}, {"size": 10296, "mtime": 1765644616269, "results": "165", "hashOfConfig": "85"}, {"size": 2085, "mtime": 1767514771259, "results": "166", "hashOfConfig": "85"}, {"size": 3703, "mtime": 1767597711169, "results": "167", "hashOfConfig": "85"}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1evm8tk", {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\ZipQueueContext.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\ArticleContext.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\referenceUtils.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\appUtils.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\ArticleIdSearchBox.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\JournalAdmin.jsx", ["417"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\PubMedComponent.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipWorkflow.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FolderZipWorkflow.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\IndividualZipProcessor.jsx", ["418", "419"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipQueueDashboard.jsx", ["420", "421", "422", "423"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\ProcessingPage.jsx", ["424"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\AdminSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\AdminStatisticsPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\user\\UserHome.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\auth\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\filters.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\hooks\\useFetchPubMedData.js", ["425", "426", "427"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\features\\downloadExcel.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\journalService.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\HeaderActions.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ReferenceStatisticsWrapper.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ProgressSteps.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ArticleIdSaveBox.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ReferenceTable.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipUploader.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminStatistics.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FileListViewer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\DocumentPreview.jsx", ["428", "429"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FolderUploader.jsx", ["430", "431"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\NotFoundReferences.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\DiffHighlighter.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\ErrorNotification.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\ReferenceEditor.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\LoaderSpinner.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\StatusBadge.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminDashboard.jsx", ["432", "433", "434", "435"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminNavigation.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminLayout.jsx", ["436"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminUploadPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminJournalsPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\auth\\AdminLogin.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\DataTable.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\Icons.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\quality.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\filterReferencesByURL.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\searchInPubMed.js", ["437", "438", "439", "440", "441", "442"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\searchInCrossRef.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\manuscriptValidator.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\helpers\\arrayHelpers.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\helpers\\stateHelpers.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\fetchFromGenAI.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\DetailedBox.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ManualEntrySection.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ValidationWarning.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\styling.js", ["443", "444"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\env.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\urls.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\utils\\util.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\getJournalName.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\prompt.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\MultiSelect.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\QueryFormModal.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\utils\\stopWords.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\queryMessages.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\emailService.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\UserManagement.jsx", ["445"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\SmartBatchingModal.jsx", ["446", "447"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\IndividualZipUpload.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ArticleMetadataPanel.jsx", ["448"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\AuthorSummary.jsx", ["449", "450", "451", "452", "453"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\AuthorQueryModal.jsx", [], ["454"], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\authorQueryService.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\productionEditorMapping.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\xmlExtractor.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\RaiseQueryButton.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\zipMetadataUtils.js", [], [], {"ruleId": "455", "severity": 1, "message": "456", "line": 47, "column": 6, "nodeType": "457", "endLine": 47, "endColumn": 15, "suggestions": "458"}, {"ruleId": "459", "severity": 1, "message": "460", "line": 16, "column": 9, "nodeType": "461", "messageId": "462", "endLine": 16, "endColumn": 17}, {"ruleId": "455", "severity": 1, "message": "463", "line": 129, "column": 9, "nodeType": "464", "endLine": 163, "endColumn": 4}, {"ruleId": "459", "severity": 1, "message": "465", "line": 6, "column": 8, "nodeType": "461", "messageId": "462", "endLine": 6, "endColumn": 21}, {"ruleId": "459", "severity": 1, "message": "466", "line": 70, "column": 9, "nodeType": "461", "messageId": "462", "endLine": 70, "endColumn": 22}, {"ruleId": "459", "severity": 1, "message": "467", "line": 87, "column": 9, "nodeType": "461", "messageId": "462", "endLine": 87, "endColumn": 23}, {"ruleId": "455", "severity": 1, "message": "468", "line": 1014, "column": 6, "nodeType": "457", "endLine": 1014, "endColumn": 8, "suggestions": "469"}, {"ruleId": "459", "severity": 1, "message": "470", "line": 20, "column": 22, "nodeType": "461", "messageId": "462", "endLine": 20, "endColumn": 35}, {"ruleId": "459", "severity": 1, "message": "471", "line": 11, "column": 3, "nodeType": "461", "messageId": "462", "endLine": 11, "endColumn": 17}, {"ruleId": "459", "severity": 1, "message": "472", "line": 58, "column": 15, "nodeType": "461", "messageId": "462", "endLine": 58, "endColumn": 25}, {"ruleId": "459", "severity": 1, "message": "473", "line": 202, "column": 11, "nodeType": "461", "messageId": "462", "endLine": 202, "endColumn": 25}, {"ruleId": "455", "severity": 1, "message": "474", "line": 29, "column": 6, "nodeType": "457", "endLine": 29, "endColumn": 12, "suggestions": "475"}, {"ruleId": "476", "severity": 1, "message": "477", "line": 100, "column": 44, "nodeType": "478", "messageId": "479", "endLine": 100, "endColumn": 84}, {"ruleId": "455", "severity": 1, "message": "480", "line": 253, "column": 6, "nodeType": "457", "endLine": 253, "endColumn": 21, "suggestions": "481"}, {"ruleId": "459", "severity": 1, "message": "482", "line": 288, "column": 9, "nodeType": "461", "messageId": "462", "endLine": 288, "endColumn": 23}, {"ruleId": "459", "severity": 1, "message": "483", "line": 11, "column": 17, "nodeType": "461", "messageId": "462", "endLine": 11, "endColumn": 25}, {"ruleId": "459", "severity": 1, "message": "484", "line": 17, "column": 10, "nodeType": "461", "messageId": "462", "endLine": 17, "endColumn": 19}, {"ruleId": "459", "severity": 1, "message": "485", "line": 17, "column": 21, "nodeType": "461", "messageId": "462", "endLine": 17, "endColumn": 33}, {"ruleId": "455", "severity": 1, "message": "486", "line": 93, "column": 6, "nodeType": "457", "endLine": 93, "endColumn": 17, "suggestions": "487"}, {"ruleId": "459", "severity": 1, "message": "488", "line": 86, "column": 9, "nodeType": "461", "messageId": "462", "endLine": 86, "endColumn": 18}, {"ruleId": "459", "severity": 1, "message": "489", "line": 26, "column": 7, "nodeType": "461", "messageId": "462", "endLine": 26, "endColumn": 24}, {"ruleId": "459", "severity": 1, "message": "490", "line": 311, "column": 10, "nodeType": "461", "messageId": "462", "endLine": 311, "endColumn": 29}, {"ruleId": "491", "severity": 1, "message": "492", "line": 315, "column": 53, "nodeType": "478", "messageId": "493", "endLine": 315, "endColumn": 54, "suggestions": "494"}, {"ruleId": "491", "severity": 1, "message": "495", "line": 319, "column": 50, "nodeType": "478", "messageId": "493", "endLine": 319, "endColumn": 51, "suggestions": "496"}, {"ruleId": "491", "severity": 1, "message": "497", "line": 319, "column": 52, "nodeType": "478", "messageId": "493", "endLine": 319, "endColumn": 53, "suggestions": "498"}, {"ruleId": "491", "severity": 1, "message": "492", "line": 319, "column": 54, "nodeType": "478", "messageId": "493", "endLine": 319, "endColumn": 55, "suggestions": "499"}, {"ruleId": "459", "severity": 1, "message": "500", "line": 39, "column": 12, "nodeType": "461", "messageId": "462", "endLine": 39, "endColumn": 13}, {"ruleId": "459", "severity": 1, "message": "501", "line": 39, "column": 31, "nodeType": "461", "messageId": "462", "endLine": 39, "endColumn": 38}, {"ruleId": "455", "severity": 1, "message": "502", "line": 33, "column": 6, "nodeType": "457", "endLine": 33, "endColumn": 29, "suggestions": "503"}, {"ruleId": "459", "severity": 1, "message": "504", "line": 68, "column": 29, "nodeType": "461", "messageId": "462", "endLine": 68, "endColumn": 49}, {"ruleId": "455", "severity": 1, "message": "505", "line": 297, "column": 6, "nodeType": "457", "endLine": 297, "endColumn": 34, "suggestions": "506"}, {"ruleId": "459", "severity": 1, "message": "507", "line": 78, "column": 9, "nodeType": "461", "messageId": "462", "endLine": 78, "endColumn": 23}, {"ruleId": "459", "severity": 1, "message": "508", "line": 1, "column": 27, "nodeType": "461", "messageId": "462", "endLine": 1, "endColumn": 36}, {"ruleId": "459", "severity": 1, "message": "509", "line": 3, "column": 10, "nodeType": "461", "messageId": "462", "endLine": 3, "endColumn": 28}, {"ruleId": "459", "severity": 1, "message": "510", "line": 7, "column": 22, "nodeType": "461", "messageId": "462", "endLine": 7, "endColumn": 35}, {"ruleId": "459", "severity": 1, "message": "511", "line": 8, "column": 19, "nodeType": "461", "messageId": "462", "endLine": 8, "endColumn": 29}, {"ruleId": "459", "severity": 1, "message": "512", "line": 9, "column": 17, "nodeType": "461", "messageId": "462", "endLine": 9, "endColumn": 25}, {"ruleId": "455", "severity": 1, "message": "513", "line": 49, "column": 6, "nodeType": "457", "endLine": 49, "endColumn": 28, "suggestions": "514", "suppressions": "515"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadJournals'. Either include it or remove the dependency array.", "ArrayExpression", ["516"], "no-unused-vars", "'location' is assigned a value but never used.", "Identifier", "unusedVar", "The 'extractZipFile' function makes the dependencies of useEffect Hook (at line 213) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'extractZipFile' in its own useCallback() Hook.", "VariableDeclarator", "'AuthorSummary' is defined but never used.", "'getStatusIcon' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'API_BASE'. Either include it or remove the dependency array.", ["517"], "'setSaveStatus' is assigned a value but never used.", "'updateProgress' is defined but never used.", "'totalSteps' is assigned a value but never used.", "'completedSteps' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFileContent'. Either include it or remove the dependency array.", ["518"], "no-control-regex", "Unexpected control character(s) in regular expression: \\x00, \\x08, \\x0b, \\x0c, \\x0e, \\x1f.", "Literal", "unexpected", "React Hook useCallback has a missing dependency: 'traverseDirectory'. Either include it or remove the dependency array.", ["519"], "'formatFileSize' is assigned a value but never used.", "'setStats' is assigned a value but never used.", "'totalRows' is assigned a value but never used.", "'setTotalRows' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchArticles'. Either include it or remove the dependency array.", ["520"], "'menuItems' is assigned a value but never used.", "'extractingCurrent' is assigned a value but never used.", "'getCleanJournalName' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\-.", "unnecessaryEscape", ["521", "522"], "Unnecessary escape character: \\(.", ["523", "524"], "Unnecessary escape character: \\).", ["525", "526"], ["527", "528"], "'_' is assigned a value but never used.", "'prefix2' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["529"], "'setAssignmentResults' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'availableTEs.length' and 'filteredArticles.length'. Either include them or remove the dependency array.", ["530"], "'getAuthorCount' is assigned a value but never used.", "'useEffect' is defined but never used.", "'authorQueryService' is defined but never used.", "'setAuthorData' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'setError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'formData.selectedFile' and 'writableFiles'. Either include them or remove the dependency array.", ["531"], ["532"], {"desc": "533", "fix": "534"}, {"desc": "535", "fix": "536"}, {"desc": "537", "fix": "538"}, {"desc": "539", "fix": "540"}, {"desc": "541", "fix": "542"}, {"messageId": "543", "fix": "544", "desc": "545"}, {"messageId": "546", "fix": "547", "desc": "548"}, {"messageId": "543", "fix": "549", "desc": "545"}, {"messageId": "546", "fix": "550", "desc": "548"}, {"messageId": "543", "fix": "551", "desc": "545"}, {"messageId": "546", "fix": "552", "desc": "548"}, {"messageId": "543", "fix": "553", "desc": "545"}, {"messageId": "546", "fix": "554", "desc": "548"}, {"desc": "555", "fix": "556"}, {"desc": "557", "fix": "558"}, {"desc": "559", "fix": "560"}, {"kind": "561", "justification": "562"}, "Update the dependencies array to be: [filters, loadJournals]", {"range": "563", "text": "564"}, "Update the dependencies array to be: [API_BASE]", {"range": "565", "text": "566"}, "Update the dependencies array to be: [file, loadFileContent]", {"range": "567", "text": "568"}, "Update the dependencies array to be: [processFolder, traverseDirectory]", {"range": "569", "text": "570"}, "Update the dependencies array to be: [dateRange, fetchArticles]", {"range": "571", "text": "572"}, "removeEscape", {"range": "573", "text": "562"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "574", "text": "575"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "576", "text": "562"}, {"range": "577", "text": "575"}, {"range": "578", "text": "562"}, {"range": "579", "text": "575"}, {"range": "580", "text": "562"}, {"range": "581", "text": "575"}, "Update the dependencies array to be: [fetchUsers, filters, isSuperAdmin]", {"range": "582", "text": "583"}, "Update the dependencies array to be: [isOpen, createSmartBatches, filteredArticles.length, availableTEs.length]", {"range": "584", "text": "585"}, "Update the dependencies array to be: [formData.selectedFile, writableFiles, writableFiles.length]", {"range": "586", "text": "587"}, "directive", "", [1293, 1302], "[filters, loadJournals]", [34257, 34259], "[API_BASE]", [1318, 1324], "[file, loadFileContent]", [9284, 9299], "[processFolder, traverseDirectory]", [3082, 3093], "[date<PERSON><PERSON><PERSON>, <PERSON><PERSON>rt<PERSON>]", [10404, 10405], [10404, 10404], "\\", [10683, 10684], [10683, 10683], [10685, 10686], [10685, 10685], [10687, 10688], [10687, 10687], [1019, 1042], "[fetchUsers, filters, isSuperAdmin]", [10405, 10433], "[isOpen, createSmartBatches, filteredArticles.length, availableTEs.length]", [2036, 2058], "[formData.selectedFile, writableFiles, writableFiles.length]"]