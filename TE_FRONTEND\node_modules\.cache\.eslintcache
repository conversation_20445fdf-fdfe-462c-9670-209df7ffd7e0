[{"C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\App.js": "3", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\ZipQueueContext.js": "4", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\ArticleContext.js": "5", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\referenceUtils.js": "6", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\appUtils.js": "7", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\AuthContext.jsx": "8", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\ArticleIdSearchBox.jsx": "9", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\JournalAdmin.jsx": "10", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\PubMedComponent.jsx": "11", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipWorkflow.jsx": "12", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FolderZipWorkflow.jsx": "13", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\IndividualZipProcessor.jsx": "14", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipQueueDashboard.jsx": "15", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\ProcessingPage.jsx": "16", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\AdminSearchPage.jsx": "17", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\AdminStatisticsPage.jsx": "18", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\user\\UserHome.jsx": "19", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\index.js": "20", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\auth\\index.js": "21", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\index.js": "22", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\filters.js": "23", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\hooks\\useFetchPubMedData.js": "24", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\features\\downloadExcel.js": "25", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\journalService.js": "26", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\HeaderActions.jsx": "27", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ReferenceStatisticsWrapper.jsx": "28", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ProgressSteps.jsx": "29", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ArticleIdSaveBox.jsx": "30", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ReferenceTable.jsx": "31", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipUploader.jsx": "32", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminStatistics.jsx": "33", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FileListViewer.jsx": "34", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\DocumentPreview.jsx": "35", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FolderUploader.jsx": "36", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\NotFoundReferences.js": "37", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\DiffHighlighter.js": "38", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\ErrorNotification.js": "39", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\ReferenceEditor.js": "40", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\LoaderSpinner.js": "41", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\StatusBadge.js": "42", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminDashboard.jsx": "43", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminNavigation.jsx": "44", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminLayout.jsx": "45", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminUploadPage.jsx": "46", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminJournalsPage.jsx": "47", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\auth\\AdminLogin.jsx": "48", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\DataTable.jsx": "49", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\Icons.jsx": "50", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\quality.js": "51", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\filterReferencesByURL.js": "52", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\searchInPubMed.js": "53", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\searchInCrossRef.js": "54", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\manuscriptValidator.js": "55", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\helpers\\arrayHelpers.js": "56", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\helpers\\stateHelpers.js": "57", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\fetchFromGenAI.js": "58", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\DetailedBox.jsx": "59", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ManualEntrySection.jsx": "60", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ValidationWarning.jsx": "61", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\styling.js": "62", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\env.js": "63", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\urls.js": "64", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\utils\\util.js": "65", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\getJournalName.js": "66", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\prompt.js": "67", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\MultiSelect.js": "68", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\QueryFormModal.jsx": "69", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\utils\\stopWords.js": "70", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\queryMessages.js": "71", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\emailService.js": "72", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\UserManagement.jsx": "73", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\SmartBatchingModal.jsx": "74", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\IndividualZipUpload.jsx": "75", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ArticleMetadataPanel.jsx": "76", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\AuthorSummary.jsx": "77", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\AuthorQueryModal.jsx": "78", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\authorQueryService.js": "79", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\productionEditorMapping.js": "80", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\xmlExtractor.js": "81", "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\RaiseQueryButton.jsx": "82"}, {"size": 648, "mtime": 1753549981327, "results": "83", "hashOfConfig": "84"}, {"size": 375, "mtime": 1739898273435, "results": "85", "hashOfConfig": "84"}, {"size": 12839, "mtime": 1767590803052, "results": "86", "hashOfConfig": "84"}, {"size": 8398, "mtime": 1767126684568, "results": "87", "hashOfConfig": "84"}, {"size": 1732, "mtime": 1753550000177, "results": "88", "hashOfConfig": "84"}, {"size": 2642, "mtime": 1758655447571, "results": "89", "hashOfConfig": "84"}, {"size": 2566, "mtime": 1753555635542, "results": "90", "hashOfConfig": "84"}, {"size": 3730, "mtime": 1767590803070, "results": "91", "hashOfConfig": "84"}, {"size": 9024, "mtime": 1760984905194, "results": "92", "hashOfConfig": "84"}, {"size": 25314, "mtime": 1752861254514, "results": "93", "hashOfConfig": "84"}, {"size": 10823, "mtime": 1758655447637, "results": "94", "hashOfConfig": "84"}, {"size": 4723, "mtime": 1753556015961, "results": "95", "hashOfConfig": "84"}, {"size": 1179, "mtime": 1754837571126, "results": "96", "hashOfConfig": "84"}, {"size": 17397, "mtime": 1767516640737, "results": "97", "hashOfConfig": "84"}, {"size": 48908, "mtime": 1767510348509, "results": "98", "hashOfConfig": "84"}, {"size": 9338, "mtime": 1760177616914, "results": "99", "hashOfConfig": "84"}, {"size": 9487, "mtime": 1755448422116, "results": "100", "hashOfConfig": "84"}, {"size": 201, "mtime": 1753609410630, "results": "101", "hashOfConfig": "84"}, {"size": 9175, "mtime": 1753559107703, "results": "102", "hashOfConfig": "84"}, {"size": 314, "mtime": 1753554204018, "results": "103", "hashOfConfig": "84"}, {"size": 54, "mtime": 1753553594598, "results": "104", "hashOfConfig": "84"}, {"size": 788, "mtime": 1767119684779, "results": "105", "hashOfConfig": "84"}, {"size": 1153, "mtime": 1751192363856, "results": "106", "hashOfConfig": "84"}, {"size": 9010, "mtime": 1751192664762, "results": "107", "hashOfConfig": "84"}, {"size": 1435, "mtime": 1753205861762, "results": "108", "hashOfConfig": "84"}, {"size": 6401, "mtime": 1760984950299, "results": "109", "hashOfConfig": "84"}, {"size": 2991, "mtime": 1753552297722, "results": "110", "hashOfConfig": "84"}, {"size": 557, "mtime": 1751190994974, "results": "111", "hashOfConfig": "84"}, {"size": 2325, "mtime": 1751190987851, "results": "112", "hashOfConfig": "84"}, {"size": 2366, "mtime": 1767514536704, "results": "113", "hashOfConfig": "84"}, {"size": 1343, "mtime": 1753552211546, "results": "114", "hashOfConfig": "84"}, {"size": 5662, "mtime": 1753886824759, "results": "115", "hashOfConfig": "84"}, {"size": 14551, "mtime": 1755445973052, "results": "116", "hashOfConfig": "84"}, {"size": 6911, "mtime": 1767512861437, "results": "117", "hashOfConfig": "84"}, {"size": 10469, "mtime": 1767591040527, "results": "118", "hashOfConfig": "84"}, {"size": 17286, "mtime": 1760857139082, "results": "119", "hashOfConfig": "84"}, {"size": 709, "mtime": 1747814210066, "results": "120", "hashOfConfig": "84"}, {"size": 852, "mtime": 1747814194074, "results": "121", "hashOfConfig": "84"}, {"size": 722, "mtime": 1750935412093, "results": "122", "hashOfConfig": "84"}, {"size": 9419, "mtime": 1753552359154, "results": "123", "hashOfConfig": "84"}, {"size": 642, "mtime": 1747814216620, "results": "124", "hashOfConfig": "84"}, {"size": 4420, "mtime": 1750880834594, "results": "125", "hashOfConfig": "84"}, {"size": 20779, "mtime": 1761041903993, "results": "126", "hashOfConfig": "84"}, {"size": 2063, "mtime": 1759068216292, "results": "127", "hashOfConfig": "84"}, {"size": 9863, "mtime": 1760970297558, "results": "128", "hashOfConfig": "84"}, {"size": 636, "mtime": 1754883415022, "results": "129", "hashOfConfig": "84"}, {"size": 671, "mtime": 1753554191849, "results": "130", "hashOfConfig": "84"}, {"size": 4109, "mtime": 1753556937896, "results": "131", "hashOfConfig": "84"}, {"size": 103, "mtime": 1754812451894, "results": "132", "hashOfConfig": "84"}, {"size": 19896, "mtime": 1767590803061, "results": "133", "hashOfConfig": "84"}, {"size": 5267, "mtime": 1751192350551, "results": "134", "hashOfConfig": "84"}, {"size": 980, "mtime": 1747814319521, "results": "135", "hashOfConfig": "84"}, {"size": 12355, "mtime": 1752859919167, "results": "136", "hashOfConfig": "84"}, {"size": 5398, "mtime": 1752859930882, "results": "137", "hashOfConfig": "84"}, {"size": 15966, "mtime": 1765813554048, "results": "138", "hashOfConfig": "84"}, {"size": 2606, "mtime": 1758655447647, "results": "139", "hashOfConfig": "84"}, {"size": 391, "mtime": 1744316361480, "results": "140", "hashOfConfig": "84"}, {"size": 4082, "mtime": 1758655447571, "results": "141", "hashOfConfig": "84"}, {"size": 3867, "mtime": 1753552006534, "results": "142", "hashOfConfig": "84"}, {"size": 1024, "mtime": 1757959982669, "results": "143", "hashOfConfig": "84"}, {"size": 4222, "mtime": 1767514916614, "results": "144", "hashOfConfig": "84"}, {"size": 7616, "mtime": 1752838762323, "results": "145", "hashOfConfig": "84"}, {"size": 145, "mtime": 1739898273435, "results": "146", "hashOfConfig": "84"}, {"size": 1053, "mtime": 1759077858293, "results": "147", "hashOfConfig": "84"}, {"size": 10288, "mtime": 1761040236540, "results": "148", "hashOfConfig": "84"}, {"size": 739, "mtime": 1744304362024, "results": "149", "hashOfConfig": "84"}, {"size": 1222, "mtime": 1745681981972, "results": "150", "hashOfConfig": "84"}, {"size": 1005, "mtime": 1753205861754, "results": "151", "hashOfConfig": "84"}, {"size": 6814, "mtime": 1767515837376, "results": "152", "hashOfConfig": "84"}, {"size": 4526, "mtime": 1751192020572, "results": "153", "hashOfConfig": "84"}, {"size": 10270, "mtime": 1765813581915, "results": "154", "hashOfConfig": "84"}, {"size": 5658, "mtime": 1765911011342, "results": "155", "hashOfConfig": "84"}, {"size": 28809, "mtime": 1760970624092, "results": "156", "hashOfConfig": "84"}, {"size": 34711, "mtime": 1767453166337, "results": "157", "hashOfConfig": "84"}, {"size": 9236, "mtime": 1760861196740, "results": "158", "hashOfConfig": "84"}, {"size": 6816, "mtime": 1767514399854, "results": "159", "hashOfConfig": "84"}, {"size": 3650, "mtime": 1760872518753, "results": "160", "hashOfConfig": "84"}, {"size": 16599, "mtime": 1767121519679, "results": "161", "hashOfConfig": "84"}, {"size": 3447, "mtime": 1765908025503, "results": "162", "hashOfConfig": "84"}, {"size": 2917, "mtime": 1767519359097, "results": "163", "hashOfConfig": "84"}, {"size": 10296, "mtime": 1765644616269, "results": "164", "hashOfConfig": "84"}, {"size": 2085, "mtime": 1767514771259, "results": "165", "hashOfConfig": "84"}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1evm8tk", {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\ZipQueueContext.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\ArticleContext.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\referenceUtils.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\appUtils.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\ArticleIdSearchBox.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\JournalAdmin.jsx", ["412"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\PubMedComponent.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipWorkflow.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FolderZipWorkflow.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\IndividualZipProcessor.jsx", ["413", "414"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipQueueDashboard.jsx", ["415", "416", "417", "418"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\ProcessingPage.jsx", ["419"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\AdminSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\pages\\AdminStatisticsPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\user\\UserHome.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\auth\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\filters.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\hooks\\useFetchPubMedData.js", ["420", "421", "422"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\features\\downloadExcel.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\journalService.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\HeaderActions.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ReferenceStatisticsWrapper.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ProgressSteps.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ArticleIdSaveBox.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\ReferenceTable.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ZipUploader.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminStatistics.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FileListViewer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\DocumentPreview.jsx", ["423", "424"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\FolderUploader.jsx", ["425", "426"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\NotFoundReferences.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\DiffHighlighter.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\ErrorNotification.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\ReferenceEditor.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\LoaderSpinner.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\StatusBadge.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminDashboard.jsx", ["427", "428", "429", "430"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminNavigation.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminLayout.jsx", ["431"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminUploadPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\AdminJournalsPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\auth\\AdminLogin.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\DataTable.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\common\\Icons.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\quality.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\filterReferencesByURL.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\searchInPubMed.js", ["432", "433", "434", "435", "436", "437"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\searchInCrossRef.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\manuscriptValidator.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\helpers\\arrayHelpers.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\helpers\\stateHelpers.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\fetchFromGenAI.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\DetailedBox.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ManualEntrySection.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ValidationWarning.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\styling.js", ["438", "439"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\env.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\urls.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\utils\\util.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\getJournalName.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\constants\\prompt.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\PubMed\\MultiSelect.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\QueryFormModal.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\utils\\stopWords.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\queryMessages.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\emailService.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\admin\\UserManagement.jsx", ["440"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\SmartBatchingModal.jsx", ["441", "442"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\IndividualZipUpload.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\ArticleMetadataPanel.jsx", ["443"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\AuthorSummary.jsx", ["444", "445", "446", "447", "448"], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\AuthorQueryModal.jsx", [], ["449"], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\authorQueryService.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\utils\\productionEditorMapping.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\services\\xmlExtractor.js", [], [], "C:\\Users\\<USER>\\Documents\\Work\\MAIN_TE\\TE_FRONTEND\\src\\components\\zip\\RaiseQueryButton.jsx", [], [], {"ruleId": "450", "severity": 1, "message": "451", "line": 47, "column": 6, "nodeType": "452", "endLine": 47, "endColumn": 15, "suggestions": "453"}, {"ruleId": "454", "severity": 1, "message": "455", "line": 16, "column": 9, "nodeType": "456", "messageId": "457", "endLine": 16, "endColumn": 17}, {"ruleId": "450", "severity": 1, "message": "458", "line": 129, "column": 9, "nodeType": "459", "endLine": 163, "endColumn": 4}, {"ruleId": "454", "severity": 1, "message": "460", "line": 6, "column": 8, "nodeType": "456", "messageId": "457", "endLine": 6, "endColumn": 21}, {"ruleId": "454", "severity": 1, "message": "461", "line": 70, "column": 9, "nodeType": "456", "messageId": "457", "endLine": 70, "endColumn": 22}, {"ruleId": "454", "severity": 1, "message": "462", "line": 87, "column": 9, "nodeType": "456", "messageId": "457", "endLine": 87, "endColumn": 23}, {"ruleId": "450", "severity": 1, "message": "463", "line": 1014, "column": 6, "nodeType": "452", "endLine": 1014, "endColumn": 8, "suggestions": "464"}, {"ruleId": "454", "severity": 1, "message": "465", "line": 20, "column": 22, "nodeType": "456", "messageId": "457", "endLine": 20, "endColumn": 35}, {"ruleId": "454", "severity": 1, "message": "466", "line": 11, "column": 3, "nodeType": "456", "messageId": "457", "endLine": 11, "endColumn": 17}, {"ruleId": "454", "severity": 1, "message": "467", "line": 58, "column": 15, "nodeType": "456", "messageId": "457", "endLine": 58, "endColumn": 25}, {"ruleId": "454", "severity": 1, "message": "468", "line": 202, "column": 11, "nodeType": "456", "messageId": "457", "endLine": 202, "endColumn": 25}, {"ruleId": "450", "severity": 1, "message": "469", "line": 25, "column": 6, "nodeType": "452", "endLine": 25, "endColumn": 12, "suggestions": "470"}, {"ruleId": "471", "severity": 1, "message": "472", "line": 59, "column": 44, "nodeType": "473", "messageId": "474", "endLine": 59, "endColumn": 84}, {"ruleId": "450", "severity": 1, "message": "475", "line": 253, "column": 6, "nodeType": "452", "endLine": 253, "endColumn": 21, "suggestions": "476"}, {"ruleId": "454", "severity": 1, "message": "477", "line": 288, "column": 9, "nodeType": "456", "messageId": "457", "endLine": 288, "endColumn": 23}, {"ruleId": "454", "severity": 1, "message": "478", "line": 11, "column": 17, "nodeType": "456", "messageId": "457", "endLine": 11, "endColumn": 25}, {"ruleId": "454", "severity": 1, "message": "479", "line": 17, "column": 10, "nodeType": "456", "messageId": "457", "endLine": 17, "endColumn": 19}, {"ruleId": "454", "severity": 1, "message": "480", "line": 17, "column": 21, "nodeType": "456", "messageId": "457", "endLine": 17, "endColumn": 33}, {"ruleId": "450", "severity": 1, "message": "481", "line": 93, "column": 6, "nodeType": "452", "endLine": 93, "endColumn": 17, "suggestions": "482"}, {"ruleId": "454", "severity": 1, "message": "483", "line": 86, "column": 9, "nodeType": "456", "messageId": "457", "endLine": 86, "endColumn": 18}, {"ruleId": "454", "severity": 1, "message": "484", "line": 26, "column": 7, "nodeType": "456", "messageId": "457", "endLine": 26, "endColumn": 24}, {"ruleId": "454", "severity": 1, "message": "485", "line": 311, "column": 10, "nodeType": "456", "messageId": "457", "endLine": 311, "endColumn": 29}, {"ruleId": "486", "severity": 1, "message": "487", "line": 315, "column": 53, "nodeType": "473", "messageId": "488", "endLine": 315, "endColumn": 54, "suggestions": "489"}, {"ruleId": "486", "severity": 1, "message": "490", "line": 319, "column": 50, "nodeType": "473", "messageId": "488", "endLine": 319, "endColumn": 51, "suggestions": "491"}, {"ruleId": "486", "severity": 1, "message": "492", "line": 319, "column": 52, "nodeType": "473", "messageId": "488", "endLine": 319, "endColumn": 53, "suggestions": "493"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 319, "column": 54, "nodeType": "473", "messageId": "488", "endLine": 319, "endColumn": 55, "suggestions": "494"}, {"ruleId": "454", "severity": 1, "message": "495", "line": 39, "column": 12, "nodeType": "456", "messageId": "457", "endLine": 39, "endColumn": 13}, {"ruleId": "454", "severity": 1, "message": "496", "line": 39, "column": 31, "nodeType": "456", "messageId": "457", "endLine": 39, "endColumn": 38}, {"ruleId": "450", "severity": 1, "message": "497", "line": 33, "column": 6, "nodeType": "452", "endLine": 33, "endColumn": 29, "suggestions": "498"}, {"ruleId": "454", "severity": 1, "message": "499", "line": 68, "column": 29, "nodeType": "456", "messageId": "457", "endLine": 68, "endColumn": 49}, {"ruleId": "450", "severity": 1, "message": "500", "line": 297, "column": 6, "nodeType": "452", "endLine": 297, "endColumn": 34, "suggestions": "501"}, {"ruleId": "454", "severity": 1, "message": "502", "line": 78, "column": 9, "nodeType": "456", "messageId": "457", "endLine": 78, "endColumn": 23}, {"ruleId": "454", "severity": 1, "message": "503", "line": 1, "column": 27, "nodeType": "456", "messageId": "457", "endLine": 1, "endColumn": 36}, {"ruleId": "454", "severity": 1, "message": "504", "line": 3, "column": 10, "nodeType": "456", "messageId": "457", "endLine": 3, "endColumn": 28}, {"ruleId": "454", "severity": 1, "message": "505", "line": 7, "column": 22, "nodeType": "456", "messageId": "457", "endLine": 7, "endColumn": 35}, {"ruleId": "454", "severity": 1, "message": "506", "line": 8, "column": 19, "nodeType": "456", "messageId": "457", "endLine": 8, "endColumn": 29}, {"ruleId": "454", "severity": 1, "message": "507", "line": 9, "column": 17, "nodeType": "456", "messageId": "457", "endLine": 9, "endColumn": 25}, {"ruleId": "450", "severity": 1, "message": "508", "line": 49, "column": 6, "nodeType": "452", "endLine": 49, "endColumn": 28, "suggestions": "509", "suppressions": "510"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadJournals'. Either include it or remove the dependency array.", "ArrayExpression", ["511"], "no-unused-vars", "'location' is assigned a value but never used.", "Identifier", "unusedVar", "The 'extractZipFile' function makes the dependencies of useEffect Hook (at line 213) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'extractZipFile' in its own useCallback() Hook.", "VariableDeclarator", "'AuthorSummary' is defined but never used.", "'getStatusIcon' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'API_BASE'. Either include it or remove the dependency array.", ["512"], "'setSaveStatus' is assigned a value but never used.", "'updateProgress' is defined but never used.", "'totalSteps' is assigned a value but never used.", "'completedSteps' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFileContent'. Either include it or remove the dependency array.", ["513"], "no-control-regex", "Unexpected control character(s) in regular expression: \\x00, \\x08, \\x0b, \\x0c, \\x0e, \\x1f.", "Literal", "unexpected", "React Hook useCallback has a missing dependency: 'traverseDirectory'. Either include it or remove the dependency array.", ["514"], "'formatFileSize' is assigned a value but never used.", "'setStats' is assigned a value but never used.", "'totalRows' is assigned a value but never used.", "'setTotalRows' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchArticles'. Either include it or remove the dependency array.", ["515"], "'menuItems' is assigned a value but never used.", "'extractingCurrent' is assigned a value but never used.", "'getCleanJournalName' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\-.", "unnecessaryEscape", ["516", "517"], "Unnecessary escape character: \\(.", ["518", "519"], "Unnecessary escape character: \\).", ["520", "521"], ["522", "523"], "'_' is assigned a value but never used.", "'prefix2' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["524"], "'setAssignmentResults' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'availableTEs.length' and 'filteredArticles.length'. Either include them or remove the dependency array.", ["525"], "'getAuthorCount' is assigned a value but never used.", "'useEffect' is defined but never used.", "'authorQueryService' is defined but never used.", "'setAuthorData' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'setError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'formData.selectedFile' and 'writableFiles'. Either include them or remove the dependency array.", ["526"], ["527"], {"desc": "528", "fix": "529"}, {"desc": "530", "fix": "531"}, {"desc": "532", "fix": "533"}, {"desc": "534", "fix": "535"}, {"desc": "536", "fix": "537"}, {"messageId": "538", "fix": "539", "desc": "540"}, {"messageId": "541", "fix": "542", "desc": "543"}, {"messageId": "538", "fix": "544", "desc": "540"}, {"messageId": "541", "fix": "545", "desc": "543"}, {"messageId": "538", "fix": "546", "desc": "540"}, {"messageId": "541", "fix": "547", "desc": "543"}, {"messageId": "538", "fix": "548", "desc": "540"}, {"messageId": "541", "fix": "549", "desc": "543"}, {"desc": "550", "fix": "551"}, {"desc": "552", "fix": "553"}, {"desc": "554", "fix": "555"}, {"kind": "556", "justification": "557"}, "Update the dependencies array to be: [filters, loadJournals]", {"range": "558", "text": "559"}, "Update the dependencies array to be: [API_BASE]", {"range": "560", "text": "561"}, "Update the dependencies array to be: [file, loadFileContent]", {"range": "562", "text": "563"}, "Update the dependencies array to be: [processFolder, traverseDirectory]", {"range": "564", "text": "565"}, "Update the dependencies array to be: [dateRange, fetchArticles]", {"range": "566", "text": "567"}, "removeEscape", {"range": "568", "text": "557"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "569", "text": "570"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "571", "text": "557"}, {"range": "572", "text": "570"}, {"range": "573", "text": "557"}, {"range": "574", "text": "570"}, {"range": "575", "text": "557"}, {"range": "576", "text": "570"}, "Update the dependencies array to be: [fetchUsers, filters, isSuperAdmin]", {"range": "577", "text": "578"}, "Update the dependencies array to be: [isOpen, createSmartBatches, filteredArticles.length, availableTEs.length]", {"range": "579", "text": "580"}, "Update the dependencies array to be: [formData.selectedFile, writableFiles, writableFiles.length]", {"range": "581", "text": "582"}, "directive", "", [1293, 1302], "[filters, loadJournals]", [34257, 34259], "[API_BASE]", [1031, 1037], "[file, loadFileContent]", [9284, 9299], "[processFolder, traverseDirectory]", [3082, 3093], "[date<PERSON><PERSON><PERSON>, <PERSON><PERSON>rt<PERSON>]", [10404, 10405], [10404, 10404], "\\", [10683, 10684], [10683, 10683], [10685, 10686], [10685, 10685], [10687, 10688], [10687, 10687], [1019, 1042], "[fetchUsers, filters, isSuperAdmin]", [10405, 10433], "[isOpen, createSmartBatches, filteredArticles.length, availableTEs.length]", [2036, 2058], "[formData.selectedFile, writableFiles, writableFiles.length]"]