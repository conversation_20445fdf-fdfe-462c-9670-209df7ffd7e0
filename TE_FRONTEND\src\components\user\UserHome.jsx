import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Icons } from '../common';
import ArticleIdSearchBox from '../ArticleIdSearchBox';
import PubMedComponent from '../PubMed/PubMedComponent';
import { normalizeReferences } from '../../utils/referenceUtils';
import { LOADING_STATES } from '../../utils/appUtils';

const UserHome = () => {
  const [terms, setTerms] = useState({
    isLoading: LOADING_STATES.IDLE,
    data: [],
  });
  const navigate = useNavigate();

  const handleArticleIdSearch = (refs, articleId) => {
    const normalizedRefs = normalizeReferences(refs);
    if (normalizedRefs.length > 0) {
      setTerms({
        isLoading: LOADING_STATES.PROCESSING,
        data: normalizedRefs,
        articleId,
        fromDb: true,
      });
    } else {
      setTerms({
        isLoading: LOADING_STATES.IDLE,
        data: [],
        articleId,
        fromDb: false,
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-100 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                <Icons.DocumentIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900 tracking-tight">EDITINK</h1>
                <span className="text-xs text-gray-500 font-medium">Reference Search</span>
              </div>
            </div>

            {/* Admin Login Link */}
            <button
              onClick={() => navigate('/admin/login')}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 border border-gray-200"
            >
              <Icons.UserIcon className="w-4 h-4 mr-2" />
              Admin Login
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="max-w-3xl mx-auto">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-xl">
              <Icons.SearchIcon className="w-10 h-10 text-white" />
            </div>

            <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
              Search References by Article ID
            </h2>
            <p className="text-xl text-gray-600 mb-12 leading-relaxed">
              Enter an article ID to instantly find and view all associated references with detailed citation information
            </p>

            {/* Search Box */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-8">
              <ArticleIdSearchBox onFound={handleArticleIdSearch} />
            </div>
          </div>
        </div>

        {/* Results Section */}
        {terms.data.length > 0 && (
          <div className="animate-fadeIn">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="px-8 py-6 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                      <Icons.CheckIcon className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        Search Results
                      </h3>
                      {terms.articleId && (
                        <p className="text-sm text-gray-600">
                          Article ID: <span className="font-semibold">{terms.articleId}</span>
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Icons.DocumentIcon className="w-4 h-4" />
                    <span className="font-medium">{terms.data.length} references found</span>
                  </div>
                </div>
              </div>
              <div className="p-8">
                <PubMedComponent terms={terms} dbMode={true} />
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {terms.data.length === 0 && terms.isLoading === 0 && (
          <div className="text-center py-20">
            <div className="max-w-4xl mx-auto">
              <div className="w-24 h-24 mx-auto mb-8 text-gray-400 bg-white rounded-2xl shadow-xl border border-gray-100 flex items-center justify-center">
                <Icons.DocumentIcon className="w-12 h-12" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 tracking-tight">
                Ready to Search
              </h3>
              <p className="text-lg text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed">
                Use the search box above to find references for any article ID.
                Our advanced system will quickly retrieve all associated references with complete citation details.
              </p>

              {/* Feature Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 shadow-lg">
                  <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Icons.SearchIcon className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-bold text-gray-900 mb-2 text-lg">Quick Search</h4>
                  <p className="text-gray-600">Lightning-fast article ID lookup with instant results</p>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 shadow-lg">
                  <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Icons.DocumentIcon className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-bold text-gray-900 mb-2 text-lg">Complete References</h4>
                  <p className="text-gray-600">Full citation details with comprehensive metadata</p>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 shadow-lg">
                  <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Icons.ClockIcon className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-bold text-gray-900 mb-2 text-lg">Real-time Results</h4>
                  <p className="text-gray-600">Instant access to up-to-date reference data</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {terms.isLoading > 0 && terms.data.length === 0 && (
          <div className="text-center py-20">
            <div className="inline-flex items-center space-x-4 bg-white px-8 py-6 rounded-2xl shadow-xl border border-gray-100">
              <div className="w-6 h-6 border-3 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-gray-700 font-medium text-lg">Searching for references...</span>
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white/80 backdrop-blur-md border-t border-gray-100 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-2">
                <Icons.DocumentIcon className="w-4 h-4 text-white" />
              </div>
              <span className="text-lg font-bold text-gray-900">EDITINK</span>
            </div>
            <p className="text-sm text-gray-500 font-medium">
              © 2024 EDITINK. Advanced reference search and management system.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default UserHome;
