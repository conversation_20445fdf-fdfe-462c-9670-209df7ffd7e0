import {
  extractDetailsFromAPI,
  extractFormattedData,
} from "../components/utils/util";
import Prompt from "../constants/prompt";
import JournalPrompt from "../constants/getJournalName";
// Removed journalMap.json - now using database-backed system
import journalService from './journalService'; // Import the new service

const BATCH_SIZE = 3;

const createBatches = (array, size) => {
  const batches = [];
  for (let i = 0; i < array.length; i += size) {
    batches.push(array.slice(i, i + size));
  }
  return batches;
};

export const searchInGenAI = async (references, formatJournal, onProgress) => {
  const genAIArray = [];
  let processed = 0;
  const total = references.length;

  if (formatJournal) {
    for (const ref of references) {
      const formattedData = await extractFormattedData(
        ref.extractedData,
        formatJournal
      );
      genAIArray.push({ ...ref, ...formattedData });
      processed++;
      if (onProgress) onProgress(processed, total);
    }
    return { genAIArray };
  } else {
    const batches = createBatches(references, BATCH_SIZE);
    for (const batch of batches) {
      const structuredDataArray = await extractDetailsFromAPI(batch, Prompt);
      if (!structuredDataArray?.entries) {
        genAIArray.push({ ...batch });
        processed += batch.length;
        if (onProgress) onProgress(processed, total);
      }
      for (
        let index = 0;
        index < structuredDataArray?.entries?.length;
        index++
      ) {
        const element = structuredDataArray.entries[index];
        const formattedData = await extractFormattedData({
          ...element,
          original_term: batch[index].term // Pass the original term
        }, true); // Skip journal lookup for PubMed - we'll get journal from PubMed response
        genAIArray.push({ ...batch[index], ...formattedData });
        processed++;
        if (onProgress) onProgress(processed, total);
      }
    }
    return { genAIArray };
  }
};

export const searchJournalInGenAI = async (journal_title) => {
  const cleanedTitle = Array.isArray(journal_title)
    ? journal_title[0]
    : journal_title;
  const trimedTitle = cleanedTitle.replaceAll("The", "").trim();

  try {
    // First, try the new database-backed service
    const dbResult = await journalService.searchJournalAbbreviation(trimedTitle);

    if (dbResult.abbreviation && dbResult.source === 'database') {
      console.log(`📚 Found in database: ${trimedTitle} -> ${dbResult.abbreviation}`);
      return dbResult.abbreviation;
    }
  } catch (error) {
    console.warn('Database search failed, falling back to GenAI:', error);
  }

  // Direct fallback to GenAI (no more file-based system)
  console.log(`🤖 Generating abbreviation for: ${trimedTitle}`);
  const data = await extractDetailsFromAPI(cleanedTitle, JournalPrompt);

  // Add new mapping to database if we got a result
  if (data?.journal_title && data.journal_title !== cleanedTitle) {
    try {

      await journalService.addJournalFromGenAI(
        cleanedTitle,
        data.journal_title,
        0.8 // GenAI confidence score
      );
      console.log(`💾 Added to database: ${cleanedTitle} -> ${data.journal_title}`);
    } catch (error) {
      console.warn('Failed to add journal to database:', error);
    }
  }

  return data?.journal_title;
};

// OpenAI review for low-score references
export async function getOpenAIReview(original, enhanced) {
  const prompt = `
You are an expert reference checker. Given the original and enhanced citation, rate the enhanced citation's accuracy and formatting on a scale of 0-100, and suggest improvements if needed.\n\nOriginal: ${original}\nEnhanced: ${enhanced}\n\nRespond in JSON:\n{\n  "score": <number>,\n  "feedback": "<short feedback>",\n  "suggestion": "<improved citation, if needed>"\n}`;
  // Use your actual OpenAI integration
  const result = await extractDetailsFromAPI(prompt, () => prompt);
  return result;
}
