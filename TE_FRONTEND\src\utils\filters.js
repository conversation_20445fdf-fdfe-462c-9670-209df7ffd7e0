import { checkQuality } from "./quality";

/**
 * Filters reference data based on the active filter type
 * @param {Array} data - Array of reference objects
 * @param {string} activeFilter - Current active filter
 * @returns {Array} - Filtered array of references
 */
export const getFilteredData = (data, activeFilter) => {
  if (!data) return [];

  switch (activeFilter) {
    case "pubmed":
      return data.filter((ref) => ref.type === "FOUND");
    case "crossref":
      return data.filter((ref) => ref.type === "CROSSREF");
    case "not-found":
      return data.filter((ref) => ref.type === "NOT_FOUND");
    case "duplicates":
      return data.filter((ref) => ref.MarkType === "DUPLICATE");
    case "urls":
      return data.filter((ref) => ref.type === "URL");
    case "high-confidence":
      return data.filter((ref) => checkQuality(ref).score >= 90 && ref.type !== "NOT_FOUND");
    case "needs-review":
      // Needs review: score < 90 OR type is NOT_FOUND or CROSSREF
      return data.filter(
        (ref) => checkQuality(ref).score < 90 || ref.type === "NOT_FOUND"
      );
    case "all":
    default:
      return data;
  }
};