import React, { useState, useEffect, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { Icons } from "./common";

const API_BASE = process.env.REACT_APP_API_URL || "http://localhost:4999";

export default function ArticleIdSearchBox({ onFound }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [articleId, setArticleId] = useState("");
  const [status, setStatus] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // Handle pre-filled article ID from URL parameters
  useEffect(() => {
    const articleIdParam = searchParams.get('articleId');
    if (articleIdParam && articleIdParam !== articleId) {
      setArticleId(articleIdParam);
      // Auto-search when article ID is provided via URL
      setTimeout(async () => {
        if (!articleIdParam) return;
        setStatus("Searching...");
        setShowSuggestions(false);
        try {
          const res = await fetch(`${API_BASE}/api/references/${encodeURIComponent(articleIdParam)}`, {
            credentials: 'include'
          });
          if (!res.ok) {
            setStatus("No references found for this Article ID.");
            onFound(null, articleIdParam);
            return;
          }
          const data = await res.json();
          setStatus("References found and loaded.");
          onFound(data.references, articleIdParam);
        } catch (e) {
          setStatus("Error searching for Article ID.");
          onFound(null, articleIdParam);
        }
      }, 100);
      // Clear the URL parameter after using it
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.delete('articleId');
        return newParams;
      });
    }
  }, [searchParams, articleId, setSearchParams, onFound]);

  // Debounced search for suggestions
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (articleId.length >= 2) {
        fetchSuggestions(articleId);
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [articleId]);

  const fetchSuggestions = async (searchTerm) => {
    setIsLoadingSuggestions(true);
    try {
      const res = await fetch(`${API_BASE}/api/article-ids?search=${encodeURIComponent(searchTerm)}&limit=10`, {
        credentials: 'include'
      });
      if (res.ok) {
        const data = await res.json();
        setSuggestions(data.articleIds || []);
        setShowSuggestions(data.articleIds.length > 0);
        setSelectedIndex(-1);
      }
    } catch (e) {
      console.error("Error fetching suggestions:", e);
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleSearch = async (searchArticleId = articleId) => {
    if (!searchArticleId) {
      setStatus("Please enter an Article ID.");
      return;
    }
    setStatus("Searching...");
    setShowSuggestions(false);
    try {
      const res = await fetch(`${API_BASE}/api/references/${encodeURIComponent(searchArticleId)}`, {
        credentials: 'include'
      });
      if (!res.ok) {
        setStatus("No references found for this Article ID.");
        onFound(null, searchArticleId);
        return;
      }
      const data = await res.json();
      setStatus("References found and loaded.");
      onFound(data.references, searchArticleId);
    } catch (e) {
      setStatus("Error searching for Article ID.");
      onFound(null, searchArticleId);
    }
  };

  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'Enter') {
        handleSearch();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          selectSuggestion(suggestions[selectedIndex]);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
      default:
        break;
    }
  };

  const selectSuggestion = (suggestion) => {
    setArticleId(suggestion);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    handleSearch(suggestion);
  };

  const handleInputChange = (e) => {
    setArticleId(e.target.value);
    setStatus("");
  };

  const handleInputFocus = () => {
    if (suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }, 200);
  };

  return (
    <div className="relative w-full max-w-2xl mx-auto">
      <div className="flex items-center space-x-3">
        {/* Search Input with Autocomplete */}
        <div className="relative flex-1">
          <div className="relative">
            <input
              ref={inputRef}
              type="text"
              value={articleId}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              placeholder="Enter Article ID (e.g. PMC123456, PMID123456)"
              className="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-lg"
            />
            <div className="absolute inset-y-0 left-0 flex items-center pl-4">
              <Icons.SearchIcon className="w-5 h-5 text-gray-400" />
            </div>
            {isLoadingSuggestions && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-4">
                <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
              </div>
            )}
          </div>

          {/* Autocomplete Suggestions */}
          {showSuggestions && suggestions.length > 0 && (
            <div
              ref={suggestionsRef}
              className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-y-auto"
            >
              {suggestions.map((suggestion, index) => (
                <button
                  key={suggestion}
                  onClick={() => selectSuggestion(suggestion)}
                  className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                    index === selectedIndex ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
                  } ${index === 0 ? 'rounded-t-xl' : ''} ${
                    index === suggestions.length - 1 ? 'rounded-b-xl' : 'border-b border-gray-100'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Icons.DocumentIcon className={`w-4 h-4 ${
                      index === selectedIndex ? 'text-blue-500' : 'text-gray-400'
                    }`} />
                    <span className="font-medium">{suggestion}</span>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Search Button */}
        <button
          onClick={() => handleSearch()}
          disabled={!articleId}
          className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
        >
          <div className="flex items-center space-x-2">
            <Icons.SearchIcon className="w-5 h-5" />
            <span>Search</span>
          </div>
        </button>
      </div>

      {/* Status Message */}
      {status && (
        <div className={`mt-3 flex items-center space-x-2 text-sm font-medium ${
          status.includes('found') ? 'text-green-600' :
          status.includes('Searching') ? 'text-blue-600' : 'text-red-600'
        }`}>
          {status.includes('Searching') ? (
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          ) : status.includes('found') ? (
            <Icons.CheckIcon className="w-4 h-4" />
          ) : (
            <Icons.XIcon className="w-4 h-4" />
          )}
          <span>{status}</span>
        </div>
      )}
    </div>
  );
}
