{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Work\\\\MAIN_TE\\\\TE_FRONTEND\\\\src\\\\components\\\\zip\\\\DocumentPreview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport mammoth from 'mammoth';\nimport { useArticle } from '../../context/ArticleContext';\nimport { useRole } from '../../context/AuthContext';\nimport { Icons } from '../common';\nimport ArticleMetadataPanel from './ArticleMetadataPanel';\nimport RaiseQueryButton from './RaiseQueryButton';\nimport './DocumentPreview.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DocumentPreview = ({\n  file,\n  onBack,\n  onCopyReferences,\n  zipId,\n  zipFile,\n  authors,\n  articleId,\n  onZipModified,\n  onValidationQuerySent\n}) => {\n  _s();\n  const [content, setContent] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAuthorsSidebar, setShowAuthorsSidebar] = useState(true);\n  const [showScrollToTop, setShowScrollToTop] = useState(false);\n  const navigate = useNavigate();\n  const {\n    setExtractedContent,\n    articleData\n  } = useArticle();\n  const {\n    isAdmin\n  } = useRole();\n  useEffect(() => {\n    if (file) {\n      loadFileContent();\n    }\n  }, [file]);\n\n  // Handle scroll events to show/hide scroll-to-top button\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      setShowScrollToTop(scrollTop > 300); // Show button after scrolling 300px\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const loadFileContent = async () => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('Loading file content for:', file.name);\n      console.log('File object:', file);\n      console.log('ZipEntry exists:', !!file.zipEntry);\n      console.log('ZipEntry type:', typeof file.zipEntry);\n      if (!file.zipEntry) {\n        throw new Error('No zipEntry found in file object');\n      }\n      const arrayBuffer = await file.zipEntry.async('arraybuffer');\n      console.log('ArrayBuffer loaded, size:', arrayBuffer.byteLength);\n      if (file.type === 'docx' || file.type === 'doc') {\n        try {\n          const result = await mammoth.convertToHtml({\n            arrayBuffer\n          });\n          setContent(result.value);\n          setExtractedContent(result.value);\n\n          // Log any warnings from mammoth\n          if (result.messages && result.messages.length > 0) {\n            console.warn('Mammoth conversion warnings:', result.messages);\n          }\n        } catch (docError) {\n          console.error('Error converting document:', docError);\n          // Fallback: try to extract as plain text\n          try {\n            const text = new TextDecoder('utf-8', {\n              ignoreBOM: true\n            }).decode(arrayBuffer);\n            const cleanText = text.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]/g, '').trim();\n            if (cleanText.length > 100) {\n              setContent(`<pre>${cleanText}</pre>`);\n              setExtractedContent(cleanText);\n              console.log('Fallback: Extracted as plain text');\n            } else {\n              throw new Error('Unable to extract meaningful content');\n            }\n          } catch (fallbackError) {\n            throw new Error(`Cannot process ${file.type.toUpperCase()} file. Please try converting to .docx format first.`);\n          }\n        }\n      } else if (file.type === 'txt') {\n        const text = new TextDecoder().decode(arrayBuffer);\n        setContent(`<pre>${text}</pre>`);\n        setExtractedContent(text);\n      } else {\n        setError(`Unsupported file type: ${file.type.toUpperCase()}. Supported formats: .docx, .doc, .txt`);\n      }\n    } catch (err) {\n      console.error('Error loading file content:', err);\n      console.error('Error details:', err.message);\n      console.error('File object at error:', file);\n      let errorMessage = 'Failed to load file content';\n      if (err.message.includes('zipEntry')) {\n        errorMessage = 'File data is not properly loaded. Please try going back and selecting the file again.';\n      } else if (err.message.includes('async')) {\n        errorMessage = 'Unable to extract file from ZIP archive. The file may be corrupted.';\n      }\n      setError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    setSelectedText(text);\n  };\n  const processSelectedText = () => {\n    if (selectedText) {\n      // Check multiple indicators that this is an admin session\n      const hasAdminParam = window.location.search.includes('admin=true');\n      const hasAdminPath = window.location.pathname.includes('/admin');\n      const hasAdminSession = sessionStorage.getItem('adminContext') === 'true';\n      const referrerIsAdmin = document.referrer.includes('/admin');\n\n      // Use admin route if any admin indicator is present OR if we're clearly in admin workflow\n      const useAdminRoute = isAdmin || hasAdminParam || hasAdminPath || hasAdminSession || referrerIsAdmin || window.location.href.includes('admin') ||\n      // Current URL has admin\n      document.referrer.includes('admin'); // Came from admin page\n\n      const processRoute = useAdminRoute ? '/admin/process' : '/process';\n      navigate(processRoute, {\n        state: {\n          references: selectedText,\n          articleId: articleData === null || articleData === void 0 ? void 0 : articleData.articleId,\n          fromZipProcessor: true,\n          manualEntry: false,\n          isAdminContext: useAdminRoute,\n          // Pass admin context explicitly\n          zipId: zipId // Pass the zipId for auto-completion\n        }\n      });\n    }\n  };\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"document-preview-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-button\",\n          children: /*#__PURE__*/_jsxDEV(Icons.ChevronLeftIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-file-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"preview-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"file-icon\",\n              children: file.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), file.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"preview-subtitle\",\n            children: [file.type.toUpperCase(), \" \\u2022 Document Preview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-actions\",\n        children: [/*#__PURE__*/_jsxDEV(RaiseQueryButton, {\n          queryType: \"general\",\n          articleId: articleId,\n          zipFile: zipFile,\n          buttonText: \"Raise Query\",\n          buttonIcon: \"\\uD83D\\uDCE7\",\n          variant: \"primary\",\n          size: \"small\",\n          onQuerySent: id => {\n            console.log('Query sent for article:', id);\n            // Mark ZIP with validationQuerySent flag for Leena assignment\n            if (onValidationQuerySent) {\n              onValidationQuerySent(id);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), authors && authors.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAuthorsSidebar(!showAuthorsSidebar),\n          className: \"toggle-sidebar-button\",\n          title: showAuthorsSidebar ? \"Hide Authors\" : \"Show Authors\",\n          children: /*#__PURE__*/_jsxDEV(Icons.SettingsIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), selectedText && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: processSelectedText,\n          className: \"process-button selected\",\n          children: [/*#__PURE__*/_jsxDEV(Icons.ArrowRightIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), \"Process Selected\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `preview-with-sidebar ${!showAuthorsSidebar ? 'sidebar-hidden' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-content-wrapper\",\n        children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading document...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-error\",\n          children: [/*#__PURE__*/_jsxDEV(Icons.ExclamationIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Error Loading Document\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), error.includes('.DOC') && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-suggestion\",\n              children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tip:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 22\n              }, this), \" For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), !isLoading && !error && content && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-content-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"preview-content\",\n            dangerouslySetInnerHTML: {\n              __html: content\n            },\n            onMouseUp: handleTextSelection,\n            onKeyUp: handleTextSelection\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"instruction-icon\",\n              children: \"\\uD83D\\uDDB1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Select text to process specific sections\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"instruction-icon\",\n              children: \"\\uD83D\\uDD04\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Processing will take you directly to the reference processing screen\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 9\n        }, this), selectedText && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selection-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selection-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"selection-icon\",\n              children: \"\\u2702\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Selected Text (\", selectedText.length, \" characters)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selection-preview\",\n            children: [selectedText.substring(0, 200), selectedText.length > 200 && '...']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), showAuthorsSidebar && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"authors-sidebar\",\n        children: authors && authors.length > 0 && /*#__PURE__*/_jsxDEV(ArticleMetadataPanel, {\n          articleId: articleId,\n          authors: authors,\n          skipApiCall: true,\n          alwaysExpanded: true,\n          zipFiles: (articleData === null || articleData === void 0 ? void 0 : articleData.zipFiles) || [],\n          onZipModified: onZipModified,\n          onQueryCreated: () => {\n            console.log('Query created for article:', articleId);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), showScrollToTop && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: scrollToTop,\n        className: \"scroll-to-top-button\",\n        title: \"Scroll to top\",\n        \"aria-label\": \"Scroll to top\",\n        children: /*#__PURE__*/_jsxDEV(Icons.ChevronUpIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentPreview, \"VwWn+uPl7NUbNbsGUhusav/OZ3I=\", false, function () {\n  return [useNavigate, useArticle, useRole];\n});\n_c = DocumentPreview;\nexport default DocumentPreview;\nvar _c;\n$RefreshReg$(_c, \"DocumentPreview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "mammoth", "useArticle", "useRole", "Icons", "ArticleMetadataPanel", "RaiseQuery<PERSON><PERSON>on", "jsxDEV", "_jsxDEV", "DocumentPreview", "file", "onBack", "onCopyReferences", "zipId", "zipFile", "authors", "articleId", "onZipModified", "onValidationQuerySent", "_s", "content", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "setIsLoading", "error", "setError", "selectedText", "setSelectedText", "showAuthorsSidebar", "setShowAuthorsSidebar", "showScrollToTop", "setShowScrollToTop", "navigate", "setExtractedContent", "articleData", "isAdmin", "loadFileContent", "handleScroll", "scrollTop", "window", "pageYOffset", "document", "documentElement", "addEventListener", "removeEventListener", "console", "log", "name", "zipEntry", "Error", "arrayBuffer", "async", "byteLength", "type", "result", "convertToHtml", "value", "messages", "length", "warn", "doc<PERSON><PERSON><PERSON>", "text", "TextDecoder", "ignoreBOM", "decode", "cleanText", "replace", "trim", "fallback<PERSON><PERSON>r", "toUpperCase", "err", "message", "errorMessage", "includes", "handleTextSelection", "selection", "getSelection", "toString", "processSelectedText", "hasAdminParam", "location", "search", "has<PERSON>d<PERSON><PERSON><PERSON>", "pathname", "hasAdminSession", "sessionStorage", "getItem", "referrerIsAdmin", "referrer", "useAdminRoute", "href", "processRoute", "state", "references", "fromZipProcessor", "manualEntry", "isAdminContext", "scrollToTop", "scrollTo", "top", "behavior", "className", "children", "onClick", "ChevronLeftIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "queryType", "buttonText", "buttonIcon", "variant", "size", "onQuerySent", "id", "title", "SettingsIcon", "ArrowRightIcon", "ExclamationIcon", "dangerouslySetInnerHTML", "__html", "onMouseUp", "onKeyUp", "substring", "skipApiCall", "alwaysExpanded", "zipFiles", "onQ<PERSON>yCreated", "ChevronUpIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Work/MAIN_TE/TE_FRONTEND/src/components/zip/DocumentPreview.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport mammoth from 'mammoth';\r\nimport { useArticle } from '../../context/ArticleContext';\r\nimport { useRole } from '../../context/AuthContext';\r\nimport { Icons } from '../common';\r\nimport ArticleMetadataPanel from './ArticleMetadataPanel';\r\nimport RaiseQueryButton from './RaiseQueryButton';\r\nimport './DocumentPreview.css';\r\n\r\nconst DocumentPreview = ({ file, onBack, onCopyReferences, zipId, zipFile, authors, articleId, onZipModified, onValidationQuerySent }) => {\r\n  const [content, setContent] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [selectedText, setSelectedText] = useState('');\r\n  const [showAuthorsSidebar, setShowAuthorsSidebar] = useState(true);\r\n  const [showScrollToTop, setShowScrollToTop] = useState(false);\r\n  const navigate = useNavigate();\r\n  const { setExtractedContent, articleData } = useArticle();\r\n  const { isAdmin } = useRole();\r\n\r\n  useEffect(() => {\r\n    if (file) {\r\n      loadFileContent();\r\n    }\r\n  }, [file]);\r\n\r\n  // Handle scroll events to show/hide scroll-to-top button\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n      setShowScrollToTop(scrollTop > 300); // Show button after scrolling 300px\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  const loadFileContent = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log('Loading file content for:', file.name);\r\n      console.log('File object:', file);\r\n      console.log('ZipEntry exists:', !!file.zipEntry);\r\n      console.log('ZipEntry type:', typeof file.zipEntry);\r\n\r\n      if (!file.zipEntry) {\r\n        throw new Error('No zipEntry found in file object');\r\n      }\r\n\r\n      const arrayBuffer = await file.zipEntry.async('arraybuffer');\r\n      console.log('ArrayBuffer loaded, size:', arrayBuffer.byteLength);\r\n      \r\n      if (file.type === 'docx' || file.type === 'doc') {\r\n        try {\r\n          const result = await mammoth.convertToHtml({ arrayBuffer });\r\n          setContent(result.value);\r\n          setExtractedContent(result.value);\r\n\r\n          // Log any warnings from mammoth\r\n          if (result.messages && result.messages.length > 0) {\r\n            console.warn('Mammoth conversion warnings:', result.messages);\r\n          }\r\n        } catch (docError) {\r\n          console.error('Error converting document:', docError);\r\n          // Fallback: try to extract as plain text\r\n          try {\r\n            const text = new TextDecoder('utf-8', { ignoreBOM: true }).decode(arrayBuffer);\r\n            const cleanText = text.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]/g, '').trim();\r\n            if (cleanText.length > 100) {\r\n              setContent(`<pre>${cleanText}</pre>`);\r\n              setExtractedContent(cleanText);\r\n              console.log('Fallback: Extracted as plain text');\r\n            } else {\r\n              throw new Error('Unable to extract meaningful content');\r\n            }\r\n          } catch (fallbackError) {\r\n            throw new Error(`Cannot process ${file.type.toUpperCase()} file. Please try converting to .docx format first.`);\r\n          }\r\n        }\r\n      } else if (file.type === 'txt') {\r\n        const text = new TextDecoder().decode(arrayBuffer);\r\n        setContent(`<pre>${text}</pre>`);\r\n        setExtractedContent(text);\r\n      } else {\r\n        setError(`Unsupported file type: ${file.type.toUpperCase()}. Supported formats: .docx, .doc, .txt`);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error loading file content:', err);\r\n      console.error('Error details:', err.message);\r\n      console.error('File object at error:', file);\r\n\r\n      let errorMessage = 'Failed to load file content';\r\n      if (err.message.includes('zipEntry')) {\r\n        errorMessage = 'File data is not properly loaded. Please try going back and selecting the file again.';\r\n      } else if (err.message.includes('async')) {\r\n        errorMessage = 'Unable to extract file from ZIP archive. The file may be corrupted.';\r\n      }\r\n\r\n      setError(errorMessage);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTextSelection = () => {\r\n    const selection = window.getSelection();\r\n    const text = selection.toString().trim();\r\n    setSelectedText(text);\r\n  };\r\n\r\n  const processSelectedText = () => {\r\n    if (selectedText) {\r\n      // Check multiple indicators that this is an admin session\r\n      const hasAdminParam = window.location.search.includes('admin=true');\r\n      const hasAdminPath = window.location.pathname.includes('/admin');\r\n      const hasAdminSession = sessionStorage.getItem('adminContext') === 'true';\r\n      const referrerIsAdmin = document.referrer.includes('/admin');\r\n\r\n      // Use admin route if any admin indicator is present OR if we're clearly in admin workflow\r\n      const useAdminRoute = isAdmin || hasAdminParam || hasAdminPath || hasAdminSession || referrerIsAdmin ||\r\n                           window.location.href.includes('admin') || // Current URL has admin\r\n                           document.referrer.includes('admin');      // Came from admin page\r\n\r\n      const processRoute = useAdminRoute ? '/admin/process' : '/process';\r\n\r\n      navigate(processRoute, {\r\n        state: {\r\n          references: selectedText,\r\n          articleId: articleData?.articleId,\r\n          fromZipProcessor: true,\r\n          manualEntry: false,\r\n          isAdminContext: useAdminRoute, // Pass admin context explicitly\r\n          zipId: zipId // Pass the zipId for auto-completion\r\n        }\r\n      });\r\n    }\r\n  };\r\n\r\n  const scrollToTop = () => {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"document-preview-container\">\r\n      {/* Header */}\r\n      <div className=\"preview-header\">\r\n        <div className=\"preview-title-section\">\r\n          <button onClick={onBack} className=\"back-button\">\r\n            <Icons.ChevronLeftIcon />\r\n          </button>\r\n          <div className=\"preview-file-info\">\r\n            <h2 className=\"preview-title\">\r\n              <span className=\"file-icon\">{file.icon}</span>\r\n              {file.name}\r\n            </h2>\r\n            <p className=\"preview-subtitle\">\r\n              {file.type.toUpperCase()} • Document Preview\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"preview-actions\">\r\n          {/* Raise Query Button */}\r\n          <RaiseQueryButton\r\n            queryType=\"general\"\r\n            articleId={articleId}\r\n            zipFile={zipFile}\r\n            buttonText=\"Raise Query\"\r\n            buttonIcon=\"📧\"\r\n            variant=\"primary\"\r\n            size=\"small\"\r\n            onQuerySent={(id) => {\r\n              console.log('Query sent for article:', id);\r\n              // Mark ZIP with validationQuerySent flag for Leena assignment\r\n              if (onValidationQuerySent) {\r\n                onValidationQuerySent(id);\r\n              }\r\n            }}\r\n          />\r\n\r\n          {authors && authors.length > 0 && (\r\n            <button\r\n              onClick={() => setShowAuthorsSidebar(!showAuthorsSidebar)}\r\n              className=\"toggle-sidebar-button\"\r\n              title={showAuthorsSidebar ? \"Hide Authors\" : \"Show Authors\"}\r\n            >\r\n              <Icons.SettingsIcon />\r\n            </button>\r\n          )}\r\n\r\n\r\n          {selectedText && (\r\n            <button onClick={processSelectedText} className=\"process-button selected\">\r\n              <Icons.ArrowRightIcon />\r\n              Process Selected\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content with Sidebar Layout */}\r\n      <div className={`preview-with-sidebar ${!showAuthorsSidebar ? 'sidebar-hidden' : ''}`}>\r\n        {/* Left Side - Document Content */}\r\n        <div className=\"preview-content-wrapper\">\r\n        {isLoading && (\r\n          <div className=\"preview-loading\">\r\n            <div className=\"loading-spinner\"></div>\r\n            <p>Loading document...</p>\r\n          </div>\r\n        )}\r\n\r\n        {error && (\r\n          <div className=\"preview-error\">\r\n            <Icons.ExclamationIcon />\r\n            <div>\r\n              <h3>Error Loading Document</h3>\r\n              <p>{error}</p>\r\n              {error.includes('.DOC') && (\r\n                <div className=\"error-suggestion\">\r\n                  💡 <strong>Tip:</strong> For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {!isLoading && !error && content && (\r\n          <div className=\"preview-content-container\">\r\n            <div\r\n              className=\"preview-content\"\r\n              dangerouslySetInnerHTML={{ __html: content }}\r\n              onMouseUp={handleTextSelection}\r\n              onKeyUp={handleTextSelection}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Instructions */}\r\n        <div className=\"preview-instructions\">\r\n          <div className=\"instruction-item\">\r\n            <span className=\"instruction-icon\">🖱️</span>\r\n            <span>Select text to process specific sections</span>\r\n          </div>\r\n          <div className=\"instruction-item\">\r\n            <span className=\"instruction-icon\">🔄</span>\r\n            <span>Processing will take you directly to the reference processing screen</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Selection Info */}\r\n        {selectedText && (\r\n          <div className=\"selection-info\">\r\n            <div className=\"selection-header\">\r\n              <span className=\"selection-icon\">✂️</span>\r\n              <span>Selected Text ({selectedText.length} characters)</span>\r\n            </div>\r\n            <div className=\"selection-preview\">\r\n              {selectedText.substring(0, 200)}\r\n              {selectedText.length > 200 && '...'}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right Side - System Authors Sidebar */}\r\n      {showAuthorsSidebar && (\r\n        <div className=\"authors-sidebar\">\r\n          {authors && authors.length > 0 && (\r\n            <ArticleMetadataPanel\r\n              articleId={articleId}\r\n              authors={authors}\r\n              skipApiCall={true}\r\n              alwaysExpanded={true}\r\n              zipFiles={articleData?.zipFiles || []}\r\n              onZipModified={onZipModified}\r\n              onQueryCreated={() => {\r\n                console.log('Query created for article:', articleId);\r\n              }}\r\n            />\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Scroll to Top Button */}\r\n      {showScrollToTop && (\r\n        <button\r\n          onClick={scrollToTop}\r\n          className=\"scroll-to-top-button\"\r\n          title=\"Scroll to top\"\r\n          aria-label=\"Scroll to top\"\r\n        >\r\n          <Icons.ChevronUpIcon />\r\n        </button>\r\n      )}\r\n    </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DocumentPreview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,KAAK,QAAQ,WAAW;AACjC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,gBAAgB;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,SAAS;EAAEC,aAAa;EAAEC;AAAsB,CAAC,KAAK;EAAAC,EAAA;EACxI,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMkC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiC,mBAAmB;IAAEC;EAAY,CAAC,GAAGhC,UAAU,CAAC,CAAC;EACzD,MAAM;IAAEiC;EAAQ,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAE7BJ,SAAS,CAAC,MAAM;IACd,IAAIW,IAAI,EAAE;MACR0B,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC1B,IAAI,CAAC,CAAC;;EAEV;EACAX,SAAS,CAAC,MAAM;IACd,MAAMsC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACJ,SAAS;MAC1EP,kBAAkB,CAACO,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAEDC,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAC/C,OAAO,MAAME,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCb,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFoB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEpC,IAAI,CAACqC,IAAI,CAAC;MACnDF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEpC,IAAI,CAAC;MACjCmC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAACpC,IAAI,CAACsC,QAAQ,CAAC;MAChDH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOpC,IAAI,CAACsC,QAAQ,CAAC;MAEnD,IAAI,CAACtC,IAAI,CAACsC,QAAQ,EAAE;QAClB,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;MACrD;MAEA,MAAMC,WAAW,GAAG,MAAMxC,IAAI,CAACsC,QAAQ,CAACG,KAAK,CAAC,aAAa,CAAC;MAC5DN,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,WAAW,CAACE,UAAU,CAAC;MAEhE,IAAI1C,IAAI,CAAC2C,IAAI,KAAK,MAAM,IAAI3C,IAAI,CAAC2C,IAAI,KAAK,KAAK,EAAE;QAC/C,IAAI;UACF,MAAMC,MAAM,GAAG,MAAMrD,OAAO,CAACsD,aAAa,CAAC;YAAEL;UAAY,CAAC,CAAC;UAC3D7B,UAAU,CAACiC,MAAM,CAACE,KAAK,CAAC;UACxBvB,mBAAmB,CAACqB,MAAM,CAACE,KAAK,CAAC;;UAEjC;UACA,IAAIF,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACG,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACjDb,OAAO,CAACc,IAAI,CAAC,8BAA8B,EAAEL,MAAM,CAACG,QAAQ,CAAC;UAC/D;QACF,CAAC,CAAC,OAAOG,QAAQ,EAAE;UACjBf,OAAO,CAACrB,KAAK,CAAC,4BAA4B,EAAEoC,QAAQ,CAAC;UACrD;UACA,IAAI;YACF,MAAMC,IAAI,GAAG,IAAIC,WAAW,CAAC,OAAO,EAAE;cAAEC,SAAS,EAAE;YAAK,CAAC,CAAC,CAACC,MAAM,CAACd,WAAW,CAAC;YAC9E,MAAMe,SAAS,GAAGJ,IAAI,CAACK,OAAO,CAAC,wCAAwC,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;YACnF,IAAIF,SAAS,CAACP,MAAM,GAAG,GAAG,EAAE;cAC1BrC,UAAU,CAAE,QAAO4C,SAAU,QAAO,CAAC;cACrChC,mBAAmB,CAACgC,SAAS,CAAC;cAC9BpB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD,CAAC,MAAM;cACL,MAAM,IAAIG,KAAK,CAAC,sCAAsC,CAAC;YACzD;UACF,CAAC,CAAC,OAAOmB,aAAa,EAAE;YACtB,MAAM,IAAInB,KAAK,CAAE,kBAAiBvC,IAAI,CAAC2C,IAAI,CAACgB,WAAW,CAAC,CAAE,qDAAoD,CAAC;UACjH;QACF;MACF,CAAC,MAAM,IAAI3D,IAAI,CAAC2C,IAAI,KAAK,KAAK,EAAE;QAC9B,MAAMQ,IAAI,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACE,MAAM,CAACd,WAAW,CAAC;QAClD7B,UAAU,CAAE,QAAOwC,IAAK,QAAO,CAAC;QAChC5B,mBAAmB,CAAC4B,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLpC,QAAQ,CAAE,0BAAyBf,IAAI,CAAC2C,IAAI,CAACgB,WAAW,CAAC,CAAE,wCAAuC,CAAC;MACrG;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZzB,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAE8C,GAAG,CAAC;MACjDzB,OAAO,CAACrB,KAAK,CAAC,gBAAgB,EAAE8C,GAAG,CAACC,OAAO,CAAC;MAC5C1B,OAAO,CAACrB,KAAK,CAAC,uBAAuB,EAAEd,IAAI,CAAC;MAE5C,IAAI8D,YAAY,GAAG,6BAA6B;MAChD,IAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;QACpCD,YAAY,GAAG,uFAAuF;MACxG,CAAC,MAAM,IAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxCD,YAAY,GAAG,qEAAqE;MACtF;MAEA/C,QAAQ,CAAC+C,YAAY,CAAC;IACxB,CAAC,SAAS;MACRjD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,SAAS,GAAGpC,MAAM,CAACqC,YAAY,CAAC,CAAC;IACvC,MAAMf,IAAI,GAAGc,SAAS,CAACE,QAAQ,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IACxCxC,eAAe,CAACkC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMiB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIpD,YAAY,EAAE;MAChB;MACA,MAAMqD,aAAa,GAAGxC,MAAM,CAACyC,QAAQ,CAACC,MAAM,CAACR,QAAQ,CAAC,YAAY,CAAC;MACnE,MAAMS,YAAY,GAAG3C,MAAM,CAACyC,QAAQ,CAACG,QAAQ,CAACV,QAAQ,CAAC,QAAQ,CAAC;MAChE,MAAMW,eAAe,GAAGC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,MAAM;MACzE,MAAMC,eAAe,GAAG9C,QAAQ,CAAC+C,QAAQ,CAACf,QAAQ,CAAC,QAAQ,CAAC;;MAE5D;MACA,MAAMgB,aAAa,GAAGtD,OAAO,IAAI4C,aAAa,IAAIG,YAAY,IAAIE,eAAe,IAAIG,eAAe,IAC/EhD,MAAM,CAACyC,QAAQ,CAACU,IAAI,CAACjB,QAAQ,CAAC,OAAO,CAAC;MAAI;MAC1ChC,QAAQ,CAAC+C,QAAQ,CAACf,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAM;;MAE/D,MAAMkB,YAAY,GAAGF,aAAa,GAAG,gBAAgB,GAAG,UAAU;MAElEzD,QAAQ,CAAC2D,YAAY,EAAE;QACrBC,KAAK,EAAE;UACLC,UAAU,EAAEnE,YAAY;UACxBV,SAAS,EAAEkB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAElB,SAAS;UACjC8E,gBAAgB,EAAE,IAAI;UACtBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAEP,aAAa;UAAE;UAC/B5E,KAAK,EAAEA,KAAK,CAAC;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMoF,WAAW,GAAGA,CAAA,KAAM;IACxB1D,MAAM,CAAC2D,QAAQ,CAAC;MACdC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACE5F,OAAA;IAAK6F,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAEzC9F,OAAA;MAAK6F,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B9F,OAAA;QAAK6F,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC9F,OAAA;UAAQ+F,OAAO,EAAE5F,MAAO;UAAC0F,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC9C9F,OAAA,CAACJ,KAAK,CAACoG,eAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACTpG,OAAA;UAAK6F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9F,OAAA;YAAI6F,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC3B9F,OAAA;cAAM6F,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAE5F,IAAI,CAACmG;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC7ClG,IAAI,CAACqC,IAAI;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACLpG,OAAA;YAAG6F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAC5B5F,IAAI,CAAC2C,IAAI,CAACgB,WAAW,CAAC,CAAC,EAAC,0BAC3B;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpG,OAAA;QAAK6F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9B9F,OAAA,CAACF,gBAAgB;UACfwG,SAAS,EAAC,SAAS;UACnB9F,SAAS,EAAEA,SAAU;UACrBF,OAAO,EAAEA,OAAQ;UACjBiG,UAAU,EAAC,aAAa;UACxBC,UAAU,EAAC,cAAI;UACfC,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,OAAO;UACZC,WAAW,EAAGC,EAAE,IAAK;YACnBvE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsE,EAAE,CAAC;YAC1C;YACA,IAAIlG,qBAAqB,EAAE;cACzBA,qBAAqB,CAACkG,EAAE,CAAC;YAC3B;UACF;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAED7F,OAAO,IAAIA,OAAO,CAAC2C,MAAM,GAAG,CAAC,iBAC5BlD,OAAA;UACE+F,OAAO,EAAEA,CAAA,KAAM1E,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;UAC1DyE,SAAS,EAAC,uBAAuB;UACjCgB,KAAK,EAAEzF,kBAAkB,GAAG,cAAc,GAAG,cAAe;UAAA0E,QAAA,eAE5D9F,OAAA,CAACJ,KAAK,CAACkH,YAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACT,EAGAlF,YAAY,iBACXlB,OAAA;UAAQ+F,OAAO,EAAEzB,mBAAoB;UAACuB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACvE9F,OAAA,CAACJ,KAAK,CAACmH,cAAc;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAE1B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpG,OAAA;MAAK6F,SAAS,EAAG,wBAAuB,CAACzE,kBAAkB,GAAG,gBAAgB,GAAG,EAAG,EAAE;MAAA0E,QAAA,gBAEpF9F,OAAA;QAAK6F,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GACvChF,SAAS,iBACRd,OAAA;UAAK6F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9F,OAAA;YAAK6F,SAAS,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvCpG,OAAA;YAAA8F,QAAA,EAAG;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACN,EAEApF,KAAK,iBACJhB,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9F,OAAA,CAACJ,KAAK,CAACoH,eAAe;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzBpG,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAA8F,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BpG,OAAA;cAAA8F,QAAA,EAAI9E;YAAK;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACbpF,KAAK,CAACiD,QAAQ,CAAC,MAAM,CAAC,iBACrBjE,OAAA;cAAK6F,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAC,eAC7B,eAAA9F,OAAA;gBAAA8F,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yHAC1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAACtF,SAAS,IAAI,CAACE,KAAK,IAAIJ,OAAO,iBAC9BZ,OAAA;UAAK6F,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxC9F,OAAA;YACE6F,SAAS,EAAC,iBAAiB;YAC3BoB,uBAAuB,EAAE;cAAEC,MAAM,EAAEtG;YAAQ,CAAE;YAC7CuG,SAAS,EAAEjD,mBAAoB;YAC/BkD,OAAO,EAAElD;UAAoB;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDpG,OAAA;UAAK6F,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC9F,OAAA;YAAK6F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B9F,OAAA;cAAM6F,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CpG,OAAA;cAAA8F,QAAA,EAAM;YAAwC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNpG,OAAA;YAAK6F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B9F,OAAA;cAAM6F,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CpG,OAAA;cAAA8F,QAAA,EAAM;YAAoE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLlF,YAAY,iBACXlB,OAAA;UAAK6F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B9F,OAAA;YAAK6F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B9F,OAAA;cAAM6F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CpG,OAAA;cAAA8F,QAAA,GAAM,iBAAe,EAAC5E,YAAY,CAACgC,MAAM,EAAC,cAAY;YAAA;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNpG,OAAA;YAAK6F,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/B5E,YAAY,CAACmG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAC9BnG,YAAY,CAACgC,MAAM,GAAG,GAAG,IAAI,KAAK;UAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLhF,kBAAkB,iBACjBpB,OAAA;QAAK6F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BvF,OAAO,IAAIA,OAAO,CAAC2C,MAAM,GAAG,CAAC,iBAC5BlD,OAAA,CAACH,oBAAoB;UACnBW,SAAS,EAAEA,SAAU;UACrBD,OAAO,EAAEA,OAAQ;UACjB+G,WAAW,EAAE,IAAK;UAClBC,cAAc,EAAE,IAAK;UACrBC,QAAQ,EAAE,CAAA9F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8F,QAAQ,KAAI,EAAG;UACtC/G,aAAa,EAAEA,aAAc;UAC7BgH,cAAc,EAAEA,CAAA,KAAM;YACpBpF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE9B,SAAS,CAAC;UACtD;QAAE;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA9E,eAAe,iBACdtB,OAAA;QACE+F,OAAO,EAAEN,WAAY;QACrBI,SAAS,EAAC,sBAAsB;QAChCgB,KAAK,EAAC,eAAe;QACrB,cAAW,eAAe;QAAAf,QAAA,eAE1B9F,OAAA,CAACJ,KAAK,CAAC8H,aAAa;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACzF,EAAA,CArSIV,eAAe;EAAA,QAOFT,WAAW,EACiBE,UAAU,EACnCC,OAAO;AAAA;AAAAgI,EAAA,GATvB1H,eAAe;AAuSrB,eAAeA,eAAe;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}