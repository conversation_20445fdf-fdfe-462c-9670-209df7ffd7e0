import React, { useState, useEffect } from 'react';
import { Icons } from '../common';
import AuthorQueryModal from './AuthorQueryModal';
import { authorQueryService } from '../../services/authorQueryService';
import './ArticleMetadataPanel.css';

const ArticleMetadataPanel = ({
  articleId,
  authors,
  onQueryCreated,
  skipApiCall = false,
  alwaysExpanded = false,
  zipFiles = [],
  onZipModified
}) => {
  const [isExpanded, setIsExpanded] = useState(alwaysExpanded);
  const [articleMetadata, setArticleMetadata] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showQueryModal, setShowQueryModal] = useState(false);

  // const loadArticleMetadata = useCallback(async () => {
  //   setLoading(true);
  //   setError(null);
  //   try {
  //     const response = await authorQueryService.getArticleMetadata(articleId);
  //     setArticleMetadata(response.article);
  //   } catch (err) {
  //     console.error('Error loading article metadata:', err);
  //     setError(err.message);
  //   } finally {
  //     setLoading(false);
  //   }
  // }, [articleId]);

  // Set article metadata from props or load from API as fallback
  useEffect(() => {
    if (authors && authors.length > 0) {
      // Use provided authors data directly
      setArticleMetadata({ authors });
      setLoading(false);
      setError(null);
    } else if (articleId && !skipApiCall) {
      // Fallback to API call if no authors provided and API calls are allowed
      // loadArticleMetadata();
    } else if (skipApiCall) {
      // If we're skipping API calls, just wait for authors data
      setLoading(false);
      setError(null);
    }
  }, [articleId, authors, skipApiCall]);

  const handleRaiseQuery = () => {
    setShowQueryModal(true);
  };

  const handleQuerySubmit = async (queryData) => {
    try {
      const response = await authorQueryService.createAuthorQuery(queryData);
      console.log('Query created successfully:', response);

      // Notify parent component
      if (onQueryCreated) {
        onQueryCreated(response);
      }

      // Show success message
      alert(`✅ Author query created successfully!\n\nQuery for ${queryData.authors.length} author(s) has been saved and will be included in the batch queries file.`);

      // No need to reload metadata since we're using props-based data
      // The author data doesn't change after creating a query
    } catch (error) {
      console.error('Error creating query:', error);
      throw error; // Let the modal handle the error display
    }
  };

  const getAuthorCount = () => {
    return articleMetadata?.authors?.length || 0;
  };

  const getCopyrightStatusCounts = () => {
    if (!articleMetadata?.authors) return { yes: 0, no: 0, unknown: 0 };
    
    return articleMetadata.authors.reduce((counts, author) => {
      const status = author.copyright_status?.toLowerCase();
      if (status === 'yes') counts.yes++;
      else if (status === 'no') counts.no++;
      else counts.unknown++;
      return counts;
    }, { yes: 0, no: 0, unknown: 0 });
  };

  const renderAuthorsList = () => {
    if (!articleMetadata?.authors || articleMetadata.authors.length === 0) {
      return (
        <div className="no-authors">
          <Icons.ExclamationTriangleIcon className="warning-icon" />
          <span>No author information available</span>
        </div>
      );
    }

    return (
      <div className="authors-table">
        <div className="table-header">
          <span>Name</span>
          <span>Email</span>
          <span>Copyright Status</span>
        </div>
        {articleMetadata.authors.map((author, index) => {
          // Support both old format (author.name) and new format (author.fullName)
          // fullName already includes title (e.g., "Dr Vangipuram Shankar")
          const authorName = author.fullName || author.name || 'Unknown';
          const authorEmail = author.email && author.email.trim() ? author.email : '-';
          const copyrightStatus = author.copyright_status || 'Unknown';

          return (
            <div key={index} className="table-row">
              <span className="author-name">{authorName}</span>
              <span className="author-email">{authorEmail}</span>
              <span className={`copyright-status ${copyrightStatus.toLowerCase()}`}>
                {copyrightStatus}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="metadata-panel loading">
        <div className="panel-header">
          <Icons.DocumentIcon className="panel-icon" />
          <span>Loading article metadata...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="metadata-panel error">
        <div className="panel-header">
          <Icons.ExclamationTriangleIcon className="panel-icon error" />
          <span>Error loading metadata: {error}</span>
        </div>
      </div>
    );
  }

  const copyrightCounts = getCopyrightStatusCounts();

  return (
    <>
      <div className={`metadata-panel ${alwaysExpanded ? 'always-expanded' : ''}`}>
        <div className="panel-header" onClick={() => !alwaysExpanded && setIsExpanded(!isExpanded)}>
          <div className="header-left">
            <span className="panel-title">System Authors</span>
          </div>
          <div className="header-right">
            <div className="copyright-summary">
              <span className="status-count yes">{copyrightCounts.yes} ✓</span>
              <span className="status-count no">{copyrightCounts.no} ✗</span>
              {copyrightCounts.unknown > 0 && (
                <span className="status-count unknown">{copyrightCounts.unknown} ?</span>
              )}
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleRaiseQuery();
              }}
              className="raise-query-button"
            >
              Raise Query
            </button>
            {!alwaysExpanded && (
              <Icons.ChevronDownIcon className={`expand-icon ${isExpanded ? 'expanded' : ''}`} />
            )}
          </div>
        </div>

        {(isExpanded || alwaysExpanded) && (
          <div className="panel-content">
            {renderAuthorsList()}
          </div>
        )}
      </div>

      {showQueryModal && (
        <AuthorQueryModal
          articleId={articleId}
          authors={articleMetadata?.authors || []}
          onClose={() => setShowQueryModal(false)}
          onSubmit={handleQuerySubmit}
          zipFiles={zipFiles}
          onZipModified={onZipModified}
        />
      )}
    </>
  );
};

export default ArticleMetadataPanel;
