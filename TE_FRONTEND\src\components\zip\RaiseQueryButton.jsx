import React, { useState } from 'react';
import QueryFormModal from './QueryFormModal';
import './RaiseQueryButton.css';

/**
 * Reusable button component for raising queries
 * Supports different query types: validation, author, general, etc.
 */
const RaiseQueryButton = ({
  // Query data
  queryType = 'validation', // 'validation', 'author', 'general', 'custom'
  validationResult = null,
  articleId,
  zipFile = null,
  
  // Customization
  buttonText = '📧 Raise Query',
  buttonIcon = '📧',
  buttonClass = '',
  variant = 'primary', // 'primary', 'secondary', 'danger', 'warning'
  size = 'medium', // 'small', 'medium', 'large'
  
  // Callbacks
  onQuerySent = null,
  onModalOpen = null,
  onModalClose = null,
  
  // Additional props for custom queries
  customData = null,
}) => {
  const [showQueryForm, setShowQueryForm] = useState(false);

  const handleOpenModal = () => {
    setShowQueryForm(true);
    if (onModalOpen) {
      onModalOpen();
    }
  };

  const handleCloseModal = () => {
    setShowQueryForm(false);
    if (onModalClose) {
      onModalClose();
    }
  };

  const handleQuerySent = () => {
    setShowQueryForm(false);
    if (onQuerySent) {
      onQuerySent(articleId);
    }
  };

  // Build button class names
  const buttonClasses = [
    'raise-query-button',
    `raise-query-button--${variant}`,
    `raise-query-button--${size}`,
    buttonClass,
  ].filter(Boolean).join(' ');

  return (
    <>
      <button
        onClick={handleOpenModal}
        className={buttonClasses}
        type="button"
      >
        {buttonIcon && <span className="button-icon">{buttonIcon}</span>}
        <span className="button-text">{buttonText}</span>
      </button>

      {showQueryForm && (
        <QueryFormModal
          queryType={queryType}
          validationResult={validationResult}
          articleId={articleId}
          zipFile={zipFile}
          customData={customData}
          onClose={handleCloseModal}
          onQuerySent={handleQuerySent}
        />
      )}
    </>
  );
};

export default RaiseQueryButton;

