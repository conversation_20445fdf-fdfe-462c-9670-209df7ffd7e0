const { test, expect } = require("@playwright/test");
const fs = require("fs");
const path = require("path");

// Support custom batch directory path or fallback to default
let BATCH_DIR;
if (process.env.BATCH_DIR_PATH) {
  // Use custom path from environment variable (can be absolute or relative)
  BATCH_DIR = path.isAbsolute(process.env.BATCH_DIR_PATH)
    ? process.env.BATCH_DIR_PATH
    : path.join(__dirname, "..", process.env.BATCH_DIR_PATH);
} else {
  // Fallback to default incoming directory structure
  const TARGET_DATE = process.env.TARGET_DATE || new Date().toLocaleDateString('en-GB').replace(/\//g, '-');
  const INCOMING_DIR = path.join(__dirname, "..", "incoming");
  BATCH_DIR = path.join(INCOMING_DIR, TARGET_DATE, "1");
}

console.log(`📂 Using batch directory: ${BATCH_DIR}`);

// Read batch summary at module load time (not in beforeAll)
let articlesToUpload = [];
const summaryPath = path.join(BATCH_DIR, "batch_summary.json");

if (fs.existsSync(summaryPath)) {
  const summaryObj = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
  articlesToUpload = summaryObj.articles.filter(article => article.status === 'success');
  console.log(`\n☁️  Found ${articlesToUpload.length} articles to upload to Drive`);
} else {
  console.error(`❌ Batch summary not found: ${summaryPath}`);
}

test.describe("Upload Articles to Google Drive", () => {
  
  // Generate tests dynamically based on articles found
  articlesToUpload.forEach((article, index) => {
    test(`Upload article: ${article.article_id}`, async () => {
      const { uploadFileToDrive } = require('../util/fileUpload');
      const zipPath = path.join(BATCH_DIR, `${article.article_id}.zip`);
      const jsonPath = path.join(BATCH_DIR, `${article.article_id}.json`);

      console.log(`📤 Worker ${index + 1} uploading: ${article.article_id}`);

      try {
        const driveResult = await uploadFileToDrive(zipPath);
        console.log(`✅ Uploaded: ${article.article_id} (ID: ${driveResult.id})`);

        // Update article metadata
        article.drive_file_id = driveResult.id;
        article.drive_file_link = driveResult.url;
        article.drive_upload_status = 'staged';
        article.drive_uploaded_at = new Date().toISOString();

        // Update individual JSON file
        fs.writeFileSync(jsonPath, JSON.stringify(article, null, 2));

        expect(driveResult.id).toBeTruthy();
      } catch (error) {
        console.error(`❌ Upload failed for ${article.article_id}:`, error.message);
        
        // Update article with error
        article.drive_upload_status = 'failed';
        article.drive_upload_error = error.message;
        
        // Update individual JSON file with error
        fs.writeFileSync(jsonPath, JSON.stringify(article, null, 2));
        
        throw error; // Fail the test
      }
    });
  });

  test.afterAll(async () => {
    // Update batch summary with all Drive file IDs
    const summaryPath = path.join(BATCH_DIR, "batch_summary.json");
    const summaryObj = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
    
    // Re-read all article JSONs to get updated Drive info
    summaryObj.articles = summaryObj.articles.map(article => {
      const jsonPath = path.join(BATCH_DIR, `${article.article_id}.json`);
      if (fs.existsSync(jsonPath)) {
        return JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
      }
      return article;
    });
    
    // Count uploads
    const uploadedCount = summaryObj.articles.filter(a => a.drive_upload_status === 'staged').length;
    const failedCount = summaryObj.articles.filter(a => a.drive_upload_status === 'failed').length;
    
    summaryObj.drive_upload_count = uploadedCount;
    summaryObj.drive_upload_failed = failedCount;
    summaryObj.drive_uploaded_at = new Date().toISOString();
    
    fs.writeFileSync(summaryPath, JSON.stringify(summaryObj, null, 2));
    
    console.log(`\n📊 Drive Upload Summary:`);
    console.log(`   ✅ Uploaded: ${uploadedCount}`);
    console.log(`   ❌ Failed: ${failedCount}`);
    console.log(`   📁 Updated: ${summaryPath}`);
  });
});
