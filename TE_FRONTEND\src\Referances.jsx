import React, { useState } from "react";
import axios from "axios";
import { upload } from "../src/constants/urls";
import { ErrorNotification, Icons } from "./components/common";
import { normalizeReferences } from "./utils/referenceUtils";
import { LOADING_STATES } from "./utils/appUtils";

const Referances = ({ setReferences }) => {
  const [file, setFile] = useState(null);
  const [isError, setIsError] = useState("");
  const [localReferences, setLocalReferences] = useState([]);
  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
  };

  const handleUpload = async () => {
    if (!file) {
      setIsError("Please select a file first!");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    setReferences({ isLoading: LOADING_STATES.UPLOADING, data: [] });
    setIsError("");
    try {
      const response = await axios.post(upload, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          "Access-Control-Allow-Origin": "*",
        },
      });
      setReferences({ isLoading: LOADING_STATES.PROCESSING, data: normalizeReferences(response.data.references) });
    } catch (error) {
      console.error("Error uploading the file:", error);
    }
  };

  const handleAreaUpload = async () => {
    if (localReferences.length === 0) {
      setIsError("Please enter references or seclect file!");
      return;
    }
    setReferences({ isLoading: LOADING_STATES.PROCESSING, data: normalizeReferences(localReferences) });
  };

  function splitReferences(text) {
    return text
      .split(/\n+/)
      .filter(Boolean)
      .map((ref, index) => ({ term: `${index + 1}. ${ref.trim()}` }));
  }

  const handleAreaChange = (e) => {
    setLocalReferences(splitReferences(e.target.value.trim()));
  };
  return (
    <div className="space-y-4">
      {/* File Upload Section */}
      <div className="bg-gray-50 rounded-2xl p-4">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Upload .docx File
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-2xl p-4 text-center hover:border-gray-400 transition-colors duration-200">
          <div className="w-8 h-8 mx-auto mb-2 text-gray-400">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <p className="text-sm font-medium text-gray-700 mb-2">
            {file ? file.name : "Select .docx file"}
          </p>
          <label className="inline-block px-3 py-1 bg-blue-600 text-white text-xs rounded-2xl hover:bg-blue-700 cursor-pointer transition-colors duration-200">
            Browse Files
            <input
              type="file"
              onChange={handleFileChange}
              accept=".docx"
              className="hidden"
            />
          </label>
        </div>
        <button
          onClick={handleUpload}
          className="w-full mt-3 inline-flex items-center justify-center px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-2xl hover:bg-blue-700 transition-colors duration-200"
        >
          <svg
            className="w-3 h-3 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
          Upload File
        </button>
      </div>

      {/* Text Input Section */}
      <div className="bg-gray-50 rounded-2xl p-4">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Paste References
        </label>
        <textarea
          onChange={handleAreaChange}
          className="w-full h-32 px-3 py-2 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
          placeholder="Paste your references here, one per line..."
        />
        <button
          onClick={handleAreaUpload}
          className="w-full mt-3 inline-flex items-center justify-center px-3 py-1 bg-green-600 text-white text-xs font-medium rounded-2xl hover:bg-green-700 transition-colors duration-200"
        >
          <svg
            className="w-3 h-3 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          Process References
        </button>
      </div>

      {isError && <ErrorNotification message={isError} />}
    </div>
  );
};

export default Referances;
