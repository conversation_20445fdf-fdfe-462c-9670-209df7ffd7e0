# Integration Clarification - References & OpenAI

## 📚 Reference Database Integration

### ✅ You Already Have This!

Your system **already has** a comprehensive reference database system:

#### Database Schema (`ArticleReference` model)
```python
class ArticleReference(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.<PERSON>ey('article_files.id'))
    reference_text = db.Column(db.JSON)  # Raw references
    processed_references = db.Column(db.JSON)  # Cleaned/standardized
    processing_metadata = db.Column(db.JSON)
    total_quality_score = db.Column(db.Float)
    source_distribution = db.Column(db.JSON)
    total_references = db.Column(db.Integer)
    high_confidence_count = db.Column(db.Integer)
    medium_confidence_count = db.Column(db.Integer)
    low_confidence_count = db.Column(db.Integer)
    needs_review_count = db.Column(db.Integer)
```

#### Existing API Endpoints
```python
# Save references
POST /api/references
Body: { articleId, references, processingMetadata }

# Get references
GET /api/references/<article_id>
Returns: { references, quality_score, source_distribution, etc. }

# Update references
PUT /api/references/<article_id>
Body: { references }
```

### ✅ Word Plugin Integration - UPDATED

I've updated the Word Plugin article endpoint to **fetch references from the database**:

```python
@app.route('/api/word-plugin/article/<article_id>', methods=['GET'])
def get_article_for_word_plugin(article_id):
    # ... authentication ...
    
    # Get references from database
    reference_record = ArticleReference.query.filter_by(article_id=article_file.id).first()
    
    if reference_record:
        references_data = {
            'total_references': reference_record.total_references,
            'processed_references': json.loads(reference_record.processed_references),
            'quality_score': reference_record.total_quality_score,
            'source_distribution': json.loads(reference_record.source_distribution),
            'high_confidence_count': reference_record.high_confidence_count,
            'medium_confidence_count': reference_record.medium_confidence_count,
            'low_confidence_count': reference_record.low_confidence_count,
            'needs_review_count': reference_record.needs_review_count,
        }
    
    return jsonify({
        'article': {
            'id': article_id,
            'references': references_data  # ✅ References from database
        }
    })
```

### How It Works

```
┌─────────────────────────────────────────────────────────────┐
│                    Reference Processing Flow                 │
└─────────────────────────────────────────────────────────────┘

1. TE Frontend Tool
   ↓
   Processes references (PubMed, CrossRef, GenAI)
   ↓
2. POST /api/references
   ↓
   Saves to ArticleReference table in database
   ↓
3. Word Plugin
   ↓
   GET /api/word-plugin/article/<article_id>
   ↓
   Fetches references from database
   ↓
4. Display in Word Plugin
```

---

## 🤖 OpenAI Integration

### ✅ You Already Have Backend Integration!

Looking at your code, you **already have** OpenAI integrated in the backend:

#### Existing Backend Integration
```python
# app.py - Line 1218+
API_KEY = os.getenv("OPENAI_API_KEY")  # ✅ From .env file
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

# Existing endpoint
@app.route("/extract", methods=["POST"])
def extract():
    data = request.json
    extracted_data = extract_details(data["content"])
    return jsonify(extracted_data)

def extract_details(content):
    headers = {
        "Authorization": f"Bearer {API_KEY}",  # ✅ Secure
    }
    # ... makes OpenAI call
```

#### Existing Frontend Integration
```javascript
// src/components/utils/util.js
export async function extractDetailsFromAPI(content, Prompt) {
  const response = await fetch(extract, {  // ✅ Calls backend /extract
    method: "POST",
    body: JSON.stringify({ content: `${Prompt(content)}` }),
  });
  // ... returns result
}
```

### ⚠️ The Problem

**Two different implementations exist**:

1. **TE Frontend** (`src/components/utils/util.js`):
   - ✅ Uses backend `/extract` endpoint
   - ✅ API key secure on backend
   - ✅ **CORRECT IMPLEMENTATION**

2. **Word Plugin** (`src/taskpane/components/utils.js`):
   - ❌ Hardcoded API key in frontend
   - ❌ Direct calls to OpenAI
   - ❌ **INSECURE IMPLEMENTATION**

### ✅ Solution

The Word Plugin should use the **same backend endpoint** as the TE Frontend:

**Current (Insecure)**:
```javascript
// src/taskpane/components/utils.js
const apiKey = "sk-proj-...";  // ❌ HARDCODED
const response = await fetch("https://api.openai.com/v1/chat/completions", {
  headers: { "Authorization": `Bearer ${apiKey}` }
});
```

**Should Be (Secure)**:
```javascript
// src/taskpane/components/utils.js
import { API_BASE_URL } from '../config';

const response = await fetch(`${API_BASE_URL}/extract`, {  // ✅ Use backend
  method: "POST",
  body: JSON.stringify({ content: prompt })
});
```

---

## 📊 Summary

### References
- ✅ **Database exists** - `ArticleReference` table
- ✅ **API endpoints exist** - `/api/references/*`
- ✅ **Word Plugin updated** - Now fetches references from database
- ✅ **No changes needed** - Just use existing endpoints

### OpenAI
- ✅ **Backend integration exists** - `/extract` endpoint
- ✅ **API key secure** - Stored in backend `.env`
- ✅ **TE Frontend correct** - Uses backend endpoint
- ❌ **Word Plugin incorrect** - Hardcoded API key
- ⏳ **Action needed** - Update Word Plugin to use `/extract` endpoint

---

## 🔧 What Needs to Be Done

### 1. References - ✅ DONE
- [x] Backend endpoint updated to include references
- [x] Word Plugin will receive references from database
- [x] No frontend changes needed

### 2. OpenAI - ⏳ TODO
- [ ] Update `src/taskpane/components/utils.js` to use `/extract` endpoint
- [ ] Remove hardcoded API key from Word Plugin
- [ ] Test OpenAI calls through backend proxy

---

## 🎯 Recommended Approach

### For References
**Use existing database** - No changes needed, just fetch from:
```javascript
// Word Plugin
const response = await fetch(`${API_BASE_URL}/api/word-plugin/article/${articleId}`);
const data = await response.json();
const references = data.article.references.processed_references;
```

### For OpenAI
**Use existing backend endpoint** - Update Word Plugin to use:
```javascript
// Word Plugin - use same endpoint as TE Frontend
const response = await fetch(`${API_BASE_URL}/extract`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ content: prompt })
});
```

---

## 📝 Files to Update

### Word Plugin Only
1. **`src/taskpane/components/utils.js`**
   - Change OpenAI calls to use `/extract` endpoint
   - Remove hardcoded API key

2. **`src/taskpane/components/TextInsertion.jsx`**
   - Change OpenAI calls to use `/extract` endpoint
   - Remove hardcoded API key

### Backend - ✅ Already Done
- [x] `/api/word-plugin/article/<article_id>` - Now returns references
- [x] `/extract` - Already exists and works
- [x] OpenAI API key in `.env` - Already configured

---

## 🚀 Next Steps

1. **Test reference fetching**:
   ```bash
   curl -H "Authorization: Bearer <token>" \
     http://localhost:5001/api/word-plugin/article/ART001
   ```
   Should return article with references from database

2. **Update Word Plugin OpenAI calls**:
   - Replace direct OpenAI calls with `/extract` endpoint
   - Remove hardcoded API keys

3. **Test end-to-end**:
   - Load article in Word Plugin
   - Verify references appear
   - Test OpenAI features (if any)

---

## 💡 Key Insights

1. **You already have everything you need** - Just need to connect the pieces
2. **TE Frontend is correct** - Word Plugin should follow same pattern
3. **Database has references** - Just fetch them via API
4. **Backend has OpenAI** - Just use existing `/extract` endpoint
5. **Security is simple** - Use backend for everything, no frontend API keys

---

**Bottom Line**: Your existing infrastructure is solid. The Word Plugin just needs to use the same backend endpoints that the TE Frontend already uses successfully.

