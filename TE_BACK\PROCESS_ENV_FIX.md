# Fix: process.env is not defined in Office Add-ins

## Problem

The Word Plugin was throwing the error:
```
ReferenceError: process is not defined
```

This occurred because the code was trying to access `process.env.REACT_APP_API_URL`, but `process` is a Node.js global that doesn't exist in the browser environment.

## Root Cause

Office Add-ins run in a browser environment (not Node.js), and the webpack configuration didn't include the `DefinePlugin` to inject environment variables at build time.

## Solution

Instead of relying on `process.env`, we updated the code to use a centralized configuration file that works in the browser.

### Changes Made

#### 1. Updated `src/taskpane/config.js`

**Before:**
```javascript
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001';
```

**After:**
```javascript
const getApiBaseUrl = () => {
  // Check if we're in development (localhost) or production
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return 'http://localhost:5001';
  }
  // For production, update this URL
  return 'http://localhost:5001'; // TODO: Update for production
};

export const API_BASE_URL = getApiBaseUrl();
```

#### 2. Updated Components to Use Config

**Files Updated:**
- `src/taskpane/components/AuthLogin.jsx`
- `src/taskpane/components/RulesPanel.jsx`
- `src/taskpane/components/ArticleSelector.jsx`

**Before:**
```javascript
const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:5001';
const response = await fetch(`${API_BASE}/api/word-plugin/auth`, { ... });
```

**After:**
```javascript
import { API_ENDPOINTS } from '../config';

const response = await fetch(API_ENDPOINTS.AUTH, { ... });
```

## Benefits

✅ **No more errors** - Works in browser environment  
✅ **Centralized config** - All API endpoints in one place  
✅ **Type-safe** - Can add TypeScript types later  
✅ **Easier to maintain** - Change URL in one place  
✅ **Production-ready** - Easy to update for production deployment  

## Configuration

### Development
The plugin automatically uses `http://localhost:5001` when running on localhost.

### Production
Update the `getApiBaseUrl()` function in `src/taskpane/config.js`:

```javascript
const getApiBaseUrl = () => {
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return 'http://localhost:5001';
  }
  // Update this for production
  return 'https://your-production-api.com';
};
```

## Alternative: Use Webpack DefinePlugin

If you prefer to use environment variables, add this to `webpack.config.js`:

```javascript
const webpack = require('webpack');

module.exports = async (env, options) => {
  const config = {
    // ... existing config
    plugins: [
      // ... existing plugins
      new webpack.DefinePlugin({
        'process.env.REACT_APP_API_URL': JSON.stringify(
          process.env.REACT_APP_API_URL || 'http://localhost:5001'
        ),
      }),
    ],
  };
  return config;
};
```

Then create a `.env` file:
```
REACT_APP_API_URL=http://localhost:5001
```

## Testing

1. **Start the backend**:
   ```bash
   cd TE_BACK
   python app.py
   ```

2. **Start the Word Plugin**:
   ```bash
   npm run dev-server
   ```

3. **Test login**:
   - Open Word
   - Load the plugin
   - Try to login
   - Should work without errors!

## Files Changed

- ✅ `src/taskpane/config.js` - Updated to not use process.env
- ✅ `src/taskpane/components/AuthLogin.jsx` - Import from config
- ✅ `src/taskpane/components/RulesPanel.jsx` - Import from config
- ✅ `src/taskpane/components/ArticleSelector.jsx` - Import from config

## Next Steps

- [ ] Test all API endpoints
- [ ] Update production URL when deploying
- [ ] Consider adding environment detection logic
- [ ] Add error handling for failed API calls

---

**Status**: ✅ Fixed  
**Date**: 2026-01-04  
**Impact**: All Word Plugin API calls now work correctly

