import { useState, useEffect, useCallback, useRef } from "react";
import { filterReferencesByURL } from "../services/filterReferencesByURL";
import { searchInPubMed } from "../services/searchInPubMed";
import { searchInCrossRef } from "../services/searchInCrossRef";
import {
  createReferenceObjects,
  sortMultipleArrays,
} from "../services/helpers/arrayHelpers";
import {
  createFinalState,
  updateProgress,
} from "../services/helpers/stateHelpers";

const useFetchPubMedData = (searchTerms, usePubMed = true, setProg) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);
  const [progressStep, setProgressStep] = useState({ step: '', description: '', current: 0, total: 0 });
  const requestIdRef = useRef(0);

  const fetchPubMedData = useCallback(
    async (terms) => {
      const myRequestId = ++requestIdRef.current;
      let fakeProgressTimer = null;
      try {
        if (requestIdRef.current !== myRequestId) return;
        setLoading(true);
        setError(null);

        // Step 1: Fetching references
        if (requestIdRef.current !== myRequestId) return;
        setProgressStep({
          step: 'fetching',
          description: 'Fetching references from document...',
          current: 0,
          total: terms.length
        });
        setProgress(5);

        // Start fake progress timer up to 35%
        let fakeProgress = 5;
        fakeProgressTimer = setInterval(() => {
          if (requestIdRef.current !== myRequestId) return;
          fakeProgress += 1;
          if (fakeProgress > 50) fakeProgress = 50;
          setProgress(fakeProgress);
        }, 1500); // ~1.2s to reach 50%

        await new Promise(resolve => setTimeout(resolve, 200));
        const extractedRefs = createReferenceObjects(terms);
        if (requestIdRef.current !== myRequestId) {
          clearInterval(fakeProgressTimer);
          return;
        }

        // Calculate total steps properly: URL refs are instant, others need processing
        const totalSteps = extractedRefs.length;
        let completedSteps = 0;

        const duplicateRefArrays = [];

        // Step 2: Filtering URL references
        if (requestIdRef.current !== myRequestId) {
          clearInterval(fakeProgressTimer);
          return;
        }
        setProgressStep({
          step: 'filtering',
          description: 'Identifying URL references...',
          current: 0,
          total: extractedRefs.length
        });
        await new Promise(resolve => setTimeout(resolve, 200));

        const { urlArray, noURLArray } = filterReferencesByURL(extractedRefs);
        if (requestIdRef.current !== myRequestId) {
          clearInterval(fakeProgressTimer);
          return;
        }
        // URL references are processed instantly
        completedSteps += urlArray.length;
        // No need to update progress here, as it's a fixed step

        // Step 3: Extracting references (OpenAI extraction for all)
        if (requestIdRef.current !== myRequestId) {
          clearInterval(fakeProgressTimer);
          return;
        }
        setProgressStep({
          step: 'extracting',
          description: `Extracting 0/${extractedRefs.length}`,
          current: 0,
          total: extractedRefs.length
        });
        await new Promise(resolve => setTimeout(resolve, 1000));
        if (requestIdRef.current !== myRequestId) {
          clearInterval(fakeProgressTimer);
          return;
        }
        // Use extractedAIRefs for further steps if you have real extraction
        // For now, continue with extractedRefs

        if (usePubMed) {
          // Step 4: Search in PubMed (pass setProgressStep for extraction step)
          const { pubMedArray, notFoundArray: initialNotFoundArray } = await searchInPubMed(
            noURLArray,
            (stepObj) => {
              if (requestIdRef.current !== myRequestId) return;
              setProgressStep(stepObj);
              if (stepObj.step === 'pubmed') {
                clearInterval(fakeProgressTimer);
                // Set progress as a percentage of pubmed step
                setProgress(Math.round(33 + (stepObj.current / (stepObj.total || 1)) * 33));
              } else if (stepObj.step === 'extracting') {
                setProgress(Math.round((stepObj.current / (stepObj.total || 1)) * 33));
              }
            }
          );
          if (requestIdRef.current !== myRequestId) return;
          // Scoring mechanism: filter pubMedArray for quality, send low-quality to CrossRef
          const isPubMedResultGoodEnough = (ref) => {
            // Accept only results with score >= 90
            return ref.score === undefined || ref.score >= 90;
          };
          const goodPubMed = pubMedArray.filter(isPubMedResultGoodEnough);
          const badPubMed = pubMedArray.filter(ref => !isPubMedResultGoodEnough(ref));
          // Combine initial not found and low-quality PubMed results for CrossRef
          const notFoundArray = [...initialNotFoundArray, ...badPubMed];
          // Update progress after PubMed search
          completedSteps += goodPubMed.length;
          // No need to update progress here, as it's a fixed step

          // If all references found in PubMed and are good, we're done
          if (notFoundArray.length === 0) {
            setProgressStep({
              step: 'styling',
              description: 'Formatting citations...',
              current: goodPubMed.length,
              total: goodPubMed.length
            });
            setProgress(95);
            // Give the UI a moment to show styling step
            setTimeout(() => {
              setProgress(100);
              setData(() =>
                createFinalState(sortMultipleArrays(goodPubMed, [], urlArray, []))
              );
            }, 100);
            return;
          }

          // Step 4: Search remaining references in CrossRef
          if (requestIdRef.current !== myRequestId) return;
          setProgressStep({
            step: 'crossref',
            description: `Searching CrossRef 0/${notFoundArray.length}`,
            current: 0,
            total: notFoundArray.length
          });
          setProgress(66);

          const { crossRef, notFoundSCArray } = await searchInCrossRef(
            notFoundArray,
            (stepObj) => {
              if (requestIdRef.current !== myRequestId) return;
              setProgressStep(stepObj);
              if (stepObj.step === 'crossref') {
                setProgress(Math.round(66 + (stepObj.current / (stepObj.total || 1)) * 34));
              }
            }
          );
          if (requestIdRef.current !== myRequestId) return;
          // Update progress after CrossRef search
          completedSteps += crossRef.length + notFoundSCArray.length;

          // Step 5: Final styling
          setProgressStep({
            step: 'styling',
            description: 'Formatting and styling citations...',
            current: 0,
            total: goodPubMed.length + crossRef.length
          });
          setProgress(100);
          setTimeout(() => {
            if (requestIdRef.current !== myRequestId) return;
            const sortedArray = sortMultipleArrays(
              goodPubMed,
              crossRef,
              urlArray,
              notFoundSCArray
            );
            setData(() => createFinalState(sortedArray));
          }, 100);

          // Process complete
        } else {
          // Direct CrossRef search (no PubMed)
          const { crossRef, notFoundSCArray } = await searchInCrossRef(
            noURLArray
          );
          completedSteps += crossRef.length + notFoundSCArray.length;
          setProgress(100); // All processing complete

          const sortedArray = sortMultipleArrays(
            crossRef,
            notFoundSCArray,
            urlArray,
            duplicateRefArrays
          );
          setData(() => createFinalState(sortedArray));
        }
      } catch (err) {
        if (requestIdRef.current !== myRequestId) return;
        setError(err.message);
      } finally {
        if (fakeProgressTimer) clearInterval(fakeProgressTimer);
        if (requestIdRef.current !== myRequestId) return;
        setLoading(false);
      }
    },
    [usePubMed]
  );

  useEffect(() => {
    if (searchTerms?.length > 0) {
      fetchPubMedData(searchTerms);
    }
  }, [searchTerms, fetchPubMedData]);

  useEffect(() => {
    if (setProg) {
      setProg(progress);
    }
  }, [progress, setProg]);

  return { data, loading, error, setData, setLoading, progressStep };
};

export default useFetchPubMedData;
