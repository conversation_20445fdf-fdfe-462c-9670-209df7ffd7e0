import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StatusBadge, ActionBadge, ReferenceEditor, ReferenceQualityChecker } from "../common";
import MultiSelectCheckbox from "./MultiSelect";
import { copyToClipboard, searchInPubMed } from "../../utils/referenceUtils";
import { getCurrentRole } from "../../utils/appUtils";

/**
 * DetailedBox component displays a single reference row in the table
 */
const DetailedBox = ({
  elem,
  isDiffViewerOpen,
  handleCheckboxChange,
  selectedOptions,
  onReferenceEdit,
}) => {
  const currentRole = getCurrentRole();
  const isAdminMode = currentRole === 'admin';
  if (typeof elem === "string") {
    return null;
  }

  // Defensive: always use a string for term
  const safeTerm = typeof elem.term === 'string' ? elem.term : '';

  // Determine reference number class and badges to show
  const getRefNumberClass = () => {
    if (elem.type === "FOUND") return "pubmed";
    if (elem.type === "CROSSREF") return "crossref";
    return "default";
  };

  const getBadgesToShow = () => {
    const badges = [];

    // Only show badges for non-PubMed and non-CrossRef references
    if (elem.type !== "FOUND" && elem.type !== "CROSSREF") {
      if (elem.MarkType === "DUPLICATE" || elem.type === "DUPLICATE") {
        badges.push({ type: "DUPLICATE", duplicateOf: elem.duplicateOf });
      }
      if (elem.type === "NOT_FOUND") {
        badges.push({ type: "NOT_FOUND" });
      }
      if (elem.type === "URL") {
        badges.push({ type: "URL" });
      }
      if (elem.type === "MULTIPLE_PubMed") {
        badges.push({ type: "MULTIPLE_PubMed" });
      }
    }

    return badges;
  };

  const badgesToShow = getBadgesToShow();

  return (
    <tr>
      <td>
        <div className="badge-container">
          <span className={`reference-number ${getRefNumberClass()}`}>
            {safeTerm.match(/^[0-9]+/)
              ? safeTerm.match(/^[0-9]+/)[0]
              : elem.ind + 1}
          </span>
          {badgesToShow.map((badge, index) => (
            <StatusBadge
              key={index}
              type={badge.type}
              duplicateOf={badge.duplicateOf}
            />
          ))}
        </div>
      </td>
      {isDiffViewerOpen ? (
        <td>
          <DiffHighlighter
            text1={safeTerm}
            text2={elem.finalStr ?? safeTerm}
          />{" "}
        </td>
      ) : (
        <>
          <td>
            <div className="reference-content">
              <div className="reference-text">
                {safeTerm.replace(/^[0-9]+\.\s*/, "")}
              </div>
              <div className="badge-container">
                <ActionBadge
                  action="search"
                  onClick={() =>
                    searchInPubMed(safeTerm.replace(/^[0-9]+\.\s*/, ""))
                  }
                />
              </div>
            </div>
          </td>
          <td>
            <div className="reference-content">
              <div className="reference-main">
                <ReferenceEditor reference={elem} onSave={onReferenceEdit} />
              </div>
              <div className="badge-container">
                <ReferenceQualityChecker
                  reference={elem}
                  onSave={onReferenceEdit}
                />
                <ActionBadge
                  action="copy"
                  onClick={() => {
                    copyToClipboard(elem.finalStr);
                  }}
                />
              </div>
            </div>
          </td>
          {isAdminMode && (
            <td>
              <MultiSelectCheckbox
                elem={elem}
                handleCheckboxChange={handleCheckboxChange}
                selectedOptions={selectedOptions}
              />
            </td>
          )}
        </>
      )}
    </tr>
  );
};

export default DetailedBox;
