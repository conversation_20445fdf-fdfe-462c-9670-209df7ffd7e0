.metadata-panel {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position:sticky;
  top: 50px;
}

.metadata-panel:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.metadata-panel.always-expanded {
  position: static;
  margin-bottom: 0;
}

.metadata-panel.always-expanded .panel-header {
  cursor: default;
}

.metadata-panel.always-expanded .panel-header:hover {
  background-color: white;
}

.metadata-panel.loading,
.metadata-panel.error {
  padding: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.panel-header:hover {
  background-color: #f9fafb;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.panel-icon.error {
  color: #dc2626;
}

.panel-title {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.author-count {
  color: #6b7280;
  font-size: 0.875rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.copyright-summary {
  display: flex;
  gap: 8px;
  align-items: center;
}

.status-count {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.status-count.yes {
  background-color: #dcfce7;
  color: #166534;
}

.status-count.no {
  background-color: #fef2f2;
  color: #dc2626;
}

.status-count.unknown {
  background-color: #f3f4f6;
  color: #6b7280;
}

.raise-query-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: #f59e0b;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.raise-query-button:hover:not(:disabled) {
  background-color: #d97706;
  transform: translateY(-1px);
}

.raise-query-button:disabled {
  background-color: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.raise-query-button svg {
  width: 14px;
  height: 14px;
}

.expand-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.panel-content {
  border-top: 1px solid #e5e7eb;
  padding: 16px 20px;
  background-color: #fafafa;
}

.no-authors {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  color: #6b7280;
  font-style: italic;
  justify-content: center;
}

.warning-icon {
  width: 16px;
  height: 16px;
  color: #f59e0b;
}

.authors-table {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background-color: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr;
  gap: 16px;
  padding: 12px 16px;
  background-color: #f3f4f6;
  font-weight: 500;
  font-size: 0.75rem;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr;
  gap: 16px;
  padding: 12px 16px;
  background-color: white;
  font-size: 0.875rem;
  align-items: center;
}

.author-name {
  font-weight: 500;
  color: #111827;
}

.author-email {
  color: #6b7280;
  word-break: break-all;
}

.copyright-status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  text-align: center;
  width: fit-content;
}

.copyright-status.yes {
  background-color: #dcfce7;
  color: #166534;
}

.copyright-status.no {
  background-color: #fef2f2;
  color: #dc2626;
}

.copyright-status.unknown {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 768px) {
  .panel-header {
    padding: 12px 16px;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .copyright-summary {
    gap: 4px;
  }
  
  .raise-query-button {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1.5fr 1.5fr 1fr;
    gap: 8px;
    padding: 8px 12px;
  }
  
  .panel-content {
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 4px;
  }
  
  .table-header span:nth-child(2),
  .table-header span:nth-child(3) {
    display: none;
  }
  
  .table-row {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .author-email {
    font-size: 0.75rem;
  }
}
