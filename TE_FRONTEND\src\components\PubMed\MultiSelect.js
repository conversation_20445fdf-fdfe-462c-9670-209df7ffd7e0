import './MultiSelect.css';

const options = [
  "Authors",
  "Title",
  "Journal Name",
  "Year",
  "Page",
  "Mismatch",
  "URL",
  "NotFound",
  "Duplicate"
];

/**
 * MultiSelectCheckbox component for selecting incorrect fields in references
 */
const MultiSelectCheckbox = ({
  handleCheckboxChange,
  selectedOptions,
  elem,
}) => {
  return (
    <div className="multiselect-checkbox-container">
      <div className="multiselect-checkbox-header">Mark incorrect fields:</div>
      <div className="multiselect-checkbox-options">
        {options.map((option) => (
          <label key={option} className="multiselect-checkbox-option">
            <input
              type="checkbox"
              checked={selectedOptions?.includes(option)}
              onChange={() => handleCheckboxChange(option, elem)}
            />
            <span>{option}</span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default MultiSelectCheckbox;