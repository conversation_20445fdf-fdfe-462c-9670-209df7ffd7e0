{"ast": null, "code": "/**\n * Utility functions for extracting metadata from ZIP files\n */\n\n/**\n * Extract article metadata from a ZIP file\n * @param {File} zipFile - The ZIP file object\n * @returns {Promise<Object|null>} - The metadata object or null if not found\n */\nexport const extractMetadataFromZip = async zipFile => {\n  try {\n    const JSZip = (await import('jszip')).default;\n    const zip = new JSZip();\n\n    // Load the ZIP file\n    const zipContent = await zip.loadAsync(zipFile);\n\n    // Look for article_metadata.json file\n    const metadataFile = zipContent.file('article_metadata.json');\n    if (metadataFile) {\n      const metadataText = await metadataFile.async('text');\n      const metadata = JSON.parse(metadataText);\n      console.log('✅ Found embedded metadata in ZIP:', metadata.article_id);\n      return metadata;\n    } else {\n      console.log('⚠️ No embedded metadata found in ZIP');\n      return null;\n    }\n  } catch (error) {\n    console.error('❌ Error extracting metadata from ZIP:', error);\n    return null;\n  }\n};\n\n/**\n * Extract author information from ZIP metadata\n * @param {File} zipFile - The ZIP file object\n * @returns {Promise<Array>} - Array of author objects\n */\nexport const extractAuthorsFromZip = async zipFile => {\n  try {\n    const metadata = await extractMetadataFromZip(zipFile);\n    if (metadata && metadata.authors && Array.isArray(metadata.authors)) {\n      return metadata.authors;\n    }\n    return [];\n  } catch (error) {\n    console.error('❌ Error extracting authors from ZIP:', error);\n    return [];\n  }\n};\n\n/**\n * Check if a ZIP file contains embedded metadata\n * @param {File} zipFile - The ZIP file object\n * @returns {Promise<boolean>} - True if metadata is found\n */\nexport const hasEmbeddedMetadata = async zipFile => {\n  try {\n    const JSZip = (await import('jszip')).default;\n    const zip = new JSZip();\n    const zipContent = await zip.loadAsync(zipFile);\n    const metadataFile = zipContent.file('article_metadata.json');\n    return metadataFile !== null;\n  } catch (error) {\n    console.error('❌ Error checking for embedded metadata:', error);\n    return false;\n  }\n};\n\n/**\n * Get article summary from embedded metadata\n * @param {File} zipFile - The ZIP file object\n * @returns {Promise<Object|null>} - Article summary object\n */\nexport const getArticleSummaryFromZip = async zipFile => {\n  try {\n    const metadata = await extractMetadataFromZip(zipFile);\n    if (metadata) {\n      return {\n        articleId: metadata.article_id,\n        journal: metadata.journal,\n        downloadDate: metadata.download_date,\n        status: metadata.status,\n        authors: metadata.authors || [],\n        files: metadata.files || {},\n        source: metadata.source || 'unknown'\n      };\n    }\n    return null;\n  } catch (error) {\n    console.error('❌ Error getting article summary from ZIP:', error);\n    return null;\n  }\n};\n\n/**\n * Batch extract metadata from multiple ZIP files\n * @param {Array<File>} zipFiles - Array of ZIP file objects\n * @returns {Promise<Array>} - Array of metadata objects\n */\nexport const batchExtractMetadata = async zipFiles => {\n  const results = [];\n  for (const zipFile of zipFiles) {\n    try {\n      const metadata = await extractMetadataFromZip(zipFile);\n      results.push({\n        filename: zipFile.name,\n        metadata: metadata,\n        hasMetadata: metadata !== null\n      });\n    } catch (error) {\n      console.error(`❌ Error processing ${zipFile.name}:`, error);\n      results.push({\n        filename: zipFile.name,\n        metadata: null,\n        hasMetadata: false,\n        error: error.message\n      });\n    }\n  }\n  return results;\n};", "map": {"version": 3, "names": ["extractMetadataFromZip", "zipFile", "JSZip", "default", "zip", "zipContent", "loadAsync", "metadataFile", "file", "metadataText", "async", "metadata", "JSON", "parse", "console", "log", "article_id", "error", "extractAuthorsFromZip", "authors", "Array", "isArray", "hasEmbeddedMetadata", "getArticleSummaryFromZip", "articleId", "journal", "downloadDate", "download_date", "status", "files", "source", "batchExtractMetadata", "zipFiles", "results", "push", "filename", "name", "hasMetadata", "message"], "sources": ["C:/Users/<USER>/Documents/Work/MAIN_TE/TE_FRONTEND/src/utils/zipMetadataUtils.js"], "sourcesContent": ["/**\n * Utility functions for extracting metadata from ZIP files\n */\n\n/**\n * Extract article metadata from a ZIP file\n * @param {File} zipFile - The ZIP file object\n * @returns {Promise<Object|null>} - The metadata object or null if not found\n */\nexport const extractMetadataFromZip = async (zipFile) => {\n  try {\n    const JSZip = (await import('jszip')).default;\n    const zip = new JSZip();\n    \n    // Load the ZIP file\n    const zipContent = await zip.loadAsync(zipFile);\n    \n    // Look for article_metadata.json file\n    const metadataFile = zipContent.file('article_metadata.json');\n    \n    if (metadataFile) {\n      const metadataText = await metadataFile.async('text');\n      const metadata = JSON.parse(metadataText);\n      \n      console.log('✅ Found embedded metadata in ZIP:', metadata.article_id);\n      return metadata;\n    } else {\n      console.log('⚠️ No embedded metadata found in ZIP');\n      return null;\n    }\n  } catch (error) {\n    console.error('❌ Error extracting metadata from ZIP:', error);\n    return null;\n  }\n};\n\n/**\n * Extract author information from ZIP metadata\n * @param {File} zipFile - The ZIP file object\n * @returns {Promise<Array>} - Array of author objects\n */\nexport const extractAuthorsFromZip = async (zipFile) => {\n  try {\n    const metadata = await extractMetadataFromZip(zipFile);\n    \n    if (metadata && metadata.authors && Array.isArray(metadata.authors)) {\n      return metadata.authors;\n    }\n    \n    return [];\n  } catch (error) {\n    console.error('❌ Error extracting authors from ZIP:', error);\n    return [];\n  }\n};\n\n/**\n * Check if a ZIP file contains embedded metadata\n * @param {File} zipFile - The ZIP file object\n * @returns {Promise<boolean>} - True if metadata is found\n */\nexport const hasEmbeddedMetadata = async (zipFile) => {\n  try {\n    const JSZip = (await import('jszip')).default;\n    const zip = new JSZip();\n    \n    const zipContent = await zip.loadAsync(zipFile);\n    const metadataFile = zipContent.file('article_metadata.json');\n    \n    return metadataFile !== null;\n  } catch (error) {\n    console.error('❌ Error checking for embedded metadata:', error);\n    return false;\n  }\n};\n\n/**\n * Get article summary from embedded metadata\n * @param {File} zipFile - The ZIP file object\n * @returns {Promise<Object|null>} - Article summary object\n */\nexport const getArticleSummaryFromZip = async (zipFile) => {\n  try {\n    const metadata = await extractMetadataFromZip(zipFile);\n    \n    if (metadata) {\n      return {\n        articleId: metadata.article_id,\n        journal: metadata.journal,\n        downloadDate: metadata.download_date,\n        status: metadata.status,\n        authors: metadata.authors || [],\n        files: metadata.files || {},\n        source: metadata.source || 'unknown'\n      };\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('❌ Error getting article summary from ZIP:', error);\n    return null;\n  }\n};\n\n/**\n * Batch extract metadata from multiple ZIP files\n * @param {Array<File>} zipFiles - Array of ZIP file objects\n * @returns {Promise<Array>} - Array of metadata objects\n */\nexport const batchExtractMetadata = async (zipFiles) => {\n  const results = [];\n  \n  for (const zipFile of zipFiles) {\n    try {\n      const metadata = await extractMetadataFromZip(zipFile);\n      results.push({\n        filename: zipFile.name,\n        metadata: metadata,\n        hasMetadata: metadata !== null\n      });\n    } catch (error) {\n      console.error(`❌ Error processing ${zipFile.name}:`, error);\n      results.push({\n        filename: zipFile.name,\n        metadata: null,\n        hasMetadata: false,\n        error: error.message\n      });\n    }\n  }\n  \n  return results;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,sBAAsB,GAAG,MAAOC,OAAO,IAAK;EACvD,IAAI;IACF,MAAMC,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,EAAEC,OAAO;IAC7C,MAAMC,GAAG,GAAG,IAAIF,KAAK,CAAC,CAAC;;IAEvB;IACA,MAAMG,UAAU,GAAG,MAAMD,GAAG,CAACE,SAAS,CAACL,OAAO,CAAC;;IAE/C;IACA,MAAMM,YAAY,GAAGF,UAAU,CAACG,IAAI,CAAC,uBAAuB,CAAC;IAE7D,IAAID,YAAY,EAAE;MAChB,MAAME,YAAY,GAAG,MAAMF,YAAY,CAACG,KAAK,CAAC,MAAM,CAAC;MACrD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC;MAEzCK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEJ,QAAQ,CAACK,UAAU,CAAC;MACrE,OAAOL,QAAQ;IACjB,CAAC,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC7D,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAG,MAAOjB,OAAO,IAAK;EACtD,IAAI;IACF,MAAMU,QAAQ,GAAG,MAAMX,sBAAsB,CAACC,OAAO,CAAC;IAEtD,IAAIU,QAAQ,IAAIA,QAAQ,CAACQ,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACV,QAAQ,CAACQ,OAAO,CAAC,EAAE;MACnE,OAAOR,QAAQ,CAACQ,OAAO;IACzB;IAEA,OAAO,EAAE;EACX,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC5D,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,mBAAmB,GAAG,MAAOrB,OAAO,IAAK;EACpD,IAAI;IACF,MAAMC,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,EAAEC,OAAO;IAC7C,MAAMC,GAAG,GAAG,IAAIF,KAAK,CAAC,CAAC;IAEvB,MAAMG,UAAU,GAAG,MAAMD,GAAG,CAACE,SAAS,CAACL,OAAO,CAAC;IAC/C,MAAMM,YAAY,GAAGF,UAAU,CAACG,IAAI,CAAC,uBAAuB,CAAC;IAE7D,OAAOD,YAAY,KAAK,IAAI;EAC9B,CAAC,CAAC,OAAOU,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,wBAAwB,GAAG,MAAOtB,OAAO,IAAK;EACzD,IAAI;IACF,MAAMU,QAAQ,GAAG,MAAMX,sBAAsB,CAACC,OAAO,CAAC;IAEtD,IAAIU,QAAQ,EAAE;MACZ,OAAO;QACLa,SAAS,EAAEb,QAAQ,CAACK,UAAU;QAC9BS,OAAO,EAAEd,QAAQ,CAACc,OAAO;QACzBC,YAAY,EAAEf,QAAQ,CAACgB,aAAa;QACpCC,MAAM,EAAEjB,QAAQ,CAACiB,MAAM;QACvBT,OAAO,EAAER,QAAQ,CAACQ,OAAO,IAAI,EAAE;QAC/BU,KAAK,EAAElB,QAAQ,CAACkB,KAAK,IAAI,CAAC,CAAC;QAC3BC,MAAM,EAAEnB,QAAQ,CAACmB,MAAM,IAAI;MAC7B,CAAC;IACH;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOb,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACjE,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,oBAAoB,GAAG,MAAOC,QAAQ,IAAK;EACtD,MAAMC,OAAO,GAAG,EAAE;EAElB,KAAK,MAAMhC,OAAO,IAAI+B,QAAQ,EAAE;IAC9B,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMX,sBAAsB,CAACC,OAAO,CAAC;MACtDgC,OAAO,CAACC,IAAI,CAAC;QACXC,QAAQ,EAAElC,OAAO,CAACmC,IAAI;QACtBzB,QAAQ,EAAEA,QAAQ;QAClB0B,WAAW,EAAE1B,QAAQ,KAAK;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAE,sBAAqBhB,OAAO,CAACmC,IAAK,GAAE,EAAEnB,KAAK,CAAC;MAC3DgB,OAAO,CAACC,IAAI,CAAC;QACXC,QAAQ,EAAElC,OAAO,CAACmC,IAAI;QACtBzB,QAAQ,EAAE,IAAI;QACd0B,WAAW,EAAE,KAAK;QAClBpB,KAAK,EAAEA,KAAK,CAACqB;MACf,CAAC,CAAC;IACJ;EACF;EAEA,OAAOL,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}