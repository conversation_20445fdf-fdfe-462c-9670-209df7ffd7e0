{"ast": null, "code": "import React,{createContext,useContext,useState,useEffect}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext();const API_BASE=process.env.REACT_APP_API_URL||'http://localhost:4999';export const useAuth=()=>{const context=useContext(AuthContext);if(!context){throw new Error('useAuth must be used within an AuthProvider');}return context;};// Helper function to check URL parameters (for backward compatibility)\nconst hasTestParam=param=>{const urlParams=new URLSearchParams(window.location.search);return urlParams.has(param);};// Hook to get current user role\nexport const useRole=()=>{const{isAuthenticated}=useAuth();const getCurrentRole=()=>{// If user is authenticated and on admin routes, they are admin\nif(isAuthenticated&&window.location.pathname.startsWith('/admin')){return'admin';}// Check for TE parameter for backward compatibility\nif(hasTestParam('te')){return'te';}// Check for admin parameter for backward compatibility (non-authenticated)\nif(hasTestParam('admin')){return'admin';}return'default';};const role=getCurrentRole();return{role,isAdmin:role==='admin',isTe:role==='te',isDefault:role==='default',isAuthenticated:isAuthenticated&&role==='admin'};};export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[isLoading,setIsLoading]=useState(true);const[isAuthenticated,setIsAuthenticated]=useState(false);// Check authentication status on app load\nuseEffect(()=>{checkAuthStatus();},[]);const checkAuthStatus=async()=>{try{const response=await fetch(\"\".concat(API_BASE,\"/api/auth/check\"),{method:'GET',credentials:'include'});const data=await response.json();if(response.ok&&data.authenticated){setUser(data.user);setIsAuthenticated(true);}else{setUser(null);setIsAuthenticated(false);}}catch(error){console.error('Auth check failed:',error);setUser(null);setIsAuthenticated(false);}finally{setIsLoading(false);}};const login=async credentials=>{try{const response=await fetch(\"\".concat(API_BASE,\"/api/auth/login\"),{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify(credentials)});const data=await response.json();if(response.ok){setUser(data.user);setIsAuthenticated(true);return{success:true,user:data.user};}else{return{success:false,error:data.error||'Login failed'};}}catch(error){console.error('Login error:',error);return{success:false,error:'Network error. Please try again.'};}};const logout=async()=>{try{await fetch(\"\".concat(API_BASE,\"/api/auth/logout\"),{method:'POST',credentials:'include'});}catch(error){console.error('Logout error:',error);}finally{setUser(null);setIsAuthenticated(false);}};const value={user,isAuthenticated,isLoading,login,logout,checkAuthStatus};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};export default AuthContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsx", "_jsx", "AuthContext", "API_BASE", "process", "env", "REACT_APP_API_URL", "useAuth", "context", "Error", "hasTestParam", "param", "urlParams", "URLSearchParams", "window", "location", "search", "has", "useRole", "isAuthenticated", "getCurrentRole", "pathname", "startsWith", "role", "isAdmin", "isTe", "isDefault", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "isLoading", "setIsLoading", "setIsAuthenticated", "checkAuthStatus", "response", "fetch", "concat", "method", "credentials", "data", "json", "ok", "authenticated", "error", "console", "login", "headers", "body", "JSON", "stringify", "success", "logout", "value", "Provider"], "sources": ["C:/Users/<USER>/Documents/Work/MAIN_TE/TE_FRONTEND/src/context/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\n\r\nconst AuthContext = createContext();\r\nconst API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:4999';\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (!context) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\n// Helper function to check URL parameters (for backward compatibility)\r\nconst hasTestParam = (param) => {\r\n  const urlParams = new URLSearchParams(window.location.search);\r\n  return urlParams.has(param);\r\n};\r\n\r\n// Hook to get current user role\r\nexport const useRole = () => {\r\n  const { isAuthenticated } = useAuth();\r\n\r\n  const getCurrentRole = () => {\r\n    // If user is authenticated and on admin routes, they are admin\r\n    if (isAuthenticated && window.location.pathname.startsWith('/admin')) {\r\n      return 'admin';\r\n    }\r\n\r\n    // Check for TE parameter for backward compatibility\r\n    if (hasTestParam('te')) {\r\n      return 'te';\r\n    }\r\n\r\n    // Check for admin parameter for backward compatibility (non-authenticated)\r\n    if (hasTestParam('admin')) {\r\n      return 'admin';\r\n    }\r\n\r\n    return 'default';\r\n  };\r\n\r\n  const role = getCurrentRole();\r\n\r\n  return {\r\n    role,\r\n    isAdmin: role === 'admin',\r\n    isTe: role === 'te',\r\n    isDefault: role === 'default',\r\n    isAuthenticated: isAuthenticated && role === 'admin'\r\n  };\r\n};\r\n\r\nexport const AuthProvider = ({ children }) => {\r\n  const [user, setUser] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n\r\n  // Check authentication status on app load\r\n  useEffect(() => {\r\n    checkAuthStatus();\r\n  }, []);\r\n\r\n  const checkAuthStatus = async () => {\r\n    try {\r\n      const response = await fetch(`${API_BASE}/api/auth/check`, {\r\n        method: 'GET',\r\n        credentials: 'include',\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.authenticated) {\r\n        setUser(data.user);\r\n        setIsAuthenticated(true);\r\n      } else {\r\n        setUser(null);\r\n        setIsAuthenticated(false);\r\n      }\r\n    } catch (error) {\r\n      console.error('Auth check failed:', error);\r\n      setUser(null);\r\n      setIsAuthenticated(false);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const login = async (credentials) => {\r\n    try {\r\n      const response = await fetch(`${API_BASE}/api/auth/login`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        credentials: 'include',\r\n        body: JSON.stringify(credentials),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setUser(data.user);\r\n        setIsAuthenticated(true);\r\n        return { success: true, user: data.user };\r\n      } else {\r\n        return { success: false, error: data.error || 'Login failed' };\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      return { success: false, error: 'Network error. Please try again.' };\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      await fetch(`${API_BASE}/api/auth/logout`, {\r\n        method: 'POST',\r\n        credentials: 'include',\r\n      });\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setUser(null);\r\n      setIsAuthenticated(false);\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    isAuthenticated,\r\n    isLoading,\r\n    login,\r\n    logout,\r\n    checkAuthStatus,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport default AuthContext;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE9E,KAAM,CAAAC,WAAW,cAAGN,aAAa,CAAC,CAAC,CACnC,KAAM,CAAAO,QAAQ,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAEzE,MAAO,MAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGX,UAAU,CAACK,WAAW,CAAC,CACvC,GAAI,CAACM,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED;AACA,KAAM,CAAAE,YAAY,CAAIC,KAAK,EAAK,CAC9B,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAC7D,MAAO,CAAAJ,SAAS,CAACK,GAAG,CAACN,KAAK,CAAC,CAC7B,CAAC,CAED;AACA,MAAO,MAAM,CAAAO,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,eAAgB,CAAC,CAAGZ,OAAO,CAAC,CAAC,CAErC,KAAM,CAAAa,cAAc,CAAGA,CAAA,GAAM,CAC3B;AACA,GAAID,eAAe,EAAIL,MAAM,CAACC,QAAQ,CAACM,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,CACpE,MAAO,OAAO,CAChB,CAEA;AACA,GAAIZ,YAAY,CAAC,IAAI,CAAC,CAAE,CACtB,MAAO,IAAI,CACb,CAEA;AACA,GAAIA,YAAY,CAAC,OAAO,CAAC,CAAE,CACzB,MAAO,OAAO,CAChB,CAEA,MAAO,SAAS,CAClB,CAAC,CAED,KAAM,CAAAa,IAAI,CAAGH,cAAc,CAAC,CAAC,CAE7B,MAAO,CACLG,IAAI,CACJC,OAAO,CAAED,IAAI,GAAK,OAAO,CACzBE,IAAI,CAAEF,IAAI,GAAK,IAAI,CACnBG,SAAS,CAAEH,IAAI,GAAK,SAAS,CAC7BJ,eAAe,CAAEA,eAAe,EAAII,IAAI,GAAK,OAC/C,CAAC,CACH,CAAC,CAED,MAAO,MAAM,CAAAI,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACkC,SAAS,CAAEC,YAAY,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACqB,eAAe,CAAEe,kBAAkB,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACAC,SAAS,CAAC,IAAM,CACdoC,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAInC,QAAQ,oBAAmB,CACzDoC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CAElC,GAAIN,QAAQ,CAACO,EAAE,EAAIF,IAAI,CAACG,aAAa,CAAE,CACrCb,OAAO,CAACU,IAAI,CAACX,IAAI,CAAC,CAClBI,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,CACLH,OAAO,CAAC,IAAI,CAAC,CACbG,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1Cd,OAAO,CAAC,IAAI,CAAC,CACbG,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,OAAS,CACRD,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAc,KAAK,CAAG,KAAO,CAAAP,WAAW,EAAK,CACnC,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAInC,QAAQ,oBAAmB,CACzDoC,MAAM,CAAE,MAAM,CACdS,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDR,WAAW,CAAE,SAAS,CACtBS,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACX,WAAW,CAClC,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CAElC,GAAIN,QAAQ,CAACO,EAAE,CAAE,CACfZ,OAAO,CAACU,IAAI,CAACX,IAAI,CAAC,CAClBI,kBAAkB,CAAC,IAAI,CAAC,CACxB,MAAO,CAAEkB,OAAO,CAAE,IAAI,CAAEtB,IAAI,CAAEW,IAAI,CAACX,IAAK,CAAC,CAC3C,CAAC,IAAM,CACL,MAAO,CAAEsB,OAAO,CAAE,KAAK,CAAEP,KAAK,CAAEJ,IAAI,CAACI,KAAK,EAAI,cAAe,CAAC,CAChE,CACF,CAAE,MAAOA,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC,MAAO,CAAEO,OAAO,CAAE,KAAK,CAAEP,KAAK,CAAE,kCAAmC,CAAC,CACtE,CACF,CAAC,CAED,KAAM,CAAAQ,MAAM,CAAG,KAAAA,CAAA,GAAY,CACzB,GAAI,CACF,KAAM,CAAAhB,KAAK,IAAAC,MAAA,CAAInC,QAAQ,qBAAoB,CACzCoC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,SACf,CAAC,CAAC,CACJ,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CAAC,OAAS,CACRd,OAAO,CAAC,IAAI,CAAC,CACbG,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED,KAAM,CAAAoB,KAAK,CAAG,CACZxB,IAAI,CACJX,eAAe,CACfa,SAAS,CACTe,KAAK,CACLM,MAAM,CACNlB,eACF,CAAC,CAED,mBACElC,IAAA,CAACC,WAAW,CAACqD,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAzB,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED,cAAe,CAAA3B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}