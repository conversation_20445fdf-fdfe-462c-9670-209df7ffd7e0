import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useArticle } from '../../context/ArticleContext';
import { useRole } from '../../context/AuthContext';
import ZipUploader from './ZipUploader';
import FileListViewer from './FileListViewer';
import DocumentPreview from './DocumentPreview';
import './ZipWorkflow.css';

const ZipWorkflow = () => {
  const [currentStep, setCurrentStep] = useState('upload'); // 'upload', 'files', 'preview'
  const [selectedFile, setSelectedFile] = useState(null);
  const [copiedReferences, setCopiedReferences] = useState('');
  const navigate = useNavigate();
  const { articleData, resetArticleData } = useArticle();
  const { isAdmin } = useRole();

  const handleUploadComplete = (articleId, files) => {
    setCurrentStep('files');
  };

  const handleFileSelect = (file) => {
    setSelectedFile(file);
    setCurrentStep('preview');
  };

  const handleBackToFiles = () => {
    setSelectedFile(null);
    setCurrentStep('files');
  };

  const handleBackToUpload = () => {
    resetArticleData();
    setSelectedFile(null);
    setCopiedReferences('');
    setCurrentStep('upload');
  };

  const handleCopyReferences = (text) => {
    setCopiedReferences(text);
    // Show success message or notification
    console.log('References copied:', text.substring(0, 100) + '...');
  };

  const handleProcessReferences = () => {
    if (copiedReferences && articleData.articleId) {
      // Navigate to processing page with the copied references
      // Use admin route if user is authenticated admin, otherwise use legacy route
      const processRoute = isAdmin ? '/admin/process' : '/process';
      navigate(processRoute, {
        state: {
          references: copiedReferences,
          articleId: articleData.articleId
        }
      });
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'upload':
        return (
          <ZipUploader onUploadComplete={handleUploadComplete} />
        );
      
      case 'files':
        return (
          <FileListViewer 
            onFileSelect={handleFileSelect}
            onBack={handleBackToUpload}
          />
        );
      
      case 'preview':
        return (
          <DocumentPreview 
            file={selectedFile}
            onBack={handleBackToFiles}
            onCopyReferences={handleCopyReferences}
          />
        );
      
      default:
        return <ZipUploader onUploadComplete={handleUploadComplete} />;
    }
  };

  return (
    <div className="zip-workflow-container">
      {/* Progress Indicator */}
      <div className="workflow-progress">
        <div className="progress-steps">
          <div className={`progress-step ${currentStep === 'upload' ? 'active' : currentStep !== 'upload' ? 'completed' : ''}`}>
            <div className="step-circle">
              <span className="step-number">1</span>
            </div>
            <span className="step-label">Upload ZIP</span>
          </div>
          
          <div className="progress-line"></div>
          
          <div className={`progress-step ${currentStep === 'files' ? 'active' : currentStep === 'preview' ? 'completed' : ''}`}>
            <div className="step-circle">
              <span className="step-number">2</span>
            </div>
            <span className="step-label">Select File</span>
          </div>
          
          <div className="progress-line"></div>
          
          <div className={`progress-step ${currentStep === 'preview' ? 'active' : ''}`}>
            <div className="step-circle">
              <span className="step-number">3</span>
            </div>
            <span className="step-label">Preview & Copy</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="workflow-content">
        {renderCurrentStep()}
      </div>

      {/* Floating Action Panel */}
      {copiedReferences && (
        <div className="floating-action-panel">
          <div className="action-panel-content">
            <div className="action-info">
              <span className="action-icon">📋</span>
              <div className="action-text">
                <div className="action-title">References Ready</div>
                <div className="action-subtitle">
                  {copiedReferences.length} characters copied
                </div>
              </div>
            </div>
            <button 
              onClick={handleProcessReferences}
              className="process-button"
            >
              Process References
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ZipWorkflow;
