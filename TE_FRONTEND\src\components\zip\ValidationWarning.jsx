import React from "react";
import Rai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "./RaiseQueryButton";
import "./ValidationWarning.css";

const ValidationWarning = ({
  validationResult,
  articleId,
  zipFile = null,
  onQuerySent = null,
}) => {
  if (!validationResult || !validationResult.error_message) {
    return null;
  }

  return (
    <>
      <div className="validation-warning-section">
        {validationResult.error_type === "no_manuscript" && (
          <div className="validation-details">
            <p>
              <strong>Expected:</strong>
              <span>
                Exactly 1 manuscript file (.docx or .doc with "manuscript" in
                filename)
              </span>
            </p>
            <p>
              <strong>Found:</strong>
              <span className="status-badge error">❌ No manuscript files</span>
            </p>
          </div>
        )}

        {validationResult.error_type === "multiple_manuscripts" && (
          <div className="validation-details">
            <p>
              <strong>Expected:</strong>
              <span>Exactly 1 manuscript file</span>
            </p>
            <p>
              <strong>Found:</strong>
              <span className="status-badge error">
                ❌ {validationResult.manuscript_files?.length || 0} manuscript
                files
              </span>
            </p>
            {validationResult.manuscript_files && (
              <ul className="file-list">
                {validationResult.manuscript_files.map((file, index) => (
                  <li key={index}>{file}</li>
                ))}
              </ul>
            )}
          </div>
        )}

        {validationResult.error_type === "missing_fp" && (
          <div className="validation-details">
            <p>
              <strong>Manuscript Status:</strong>
              <span className="status-badge success">✅ Found</span>
            </p>
            <p>
              <strong>First Page Status:</strong>
              <span className="status-badge error">❌ Missing</span>
            </p>
            <p>
              <strong>Expected:</strong>
              <span>
                File containing "FP", "FirstPage", or "TitlePage" in filename
              </span>
            </p>
          </div>
        )}

        {validationResult.error_type === "no_manuscript_and_fp" && (
          <div className="validation-details">
            <p>
              <strong>Manuscript Status:</strong>
              <span className="status-badge error">❌ Missing</span>
            </p>
            <p>
              <strong>First Page Status:</strong>
              <span className="status-badge error">❌ Missing</span>
            </p>
            <p>
              <strong>Expected:</strong>
              <span>Both manuscript and first page files</span>
            </p>
          </div>
        )}

        {validationResult.error_type === "multiple_fp" && (
          <div className="validation-details">
            <p>
              <strong>Manuscript Status:</strong>
              <span className="status-badge success">✅ Found</span>
            </p>
            <p>
              <strong>First Page Status:</strong>
              <span className="status-badge warning">
                ⚠️ Multiple files found
              </span>
            </p>
            {validationResult.fp_files && (
              <ul className="file-list">
                {validationResult.fp_files.map((file, index) => (
                  <li key={index}>{file}</li>
                ))}
              </ul>
            )}
          </div>
        )}
      </div>
      <div className="validation-actions">
        <RaiseQueryButton
          queryType="validation"
          validationResult={validationResult}
          articleId={articleId}
          zipFile={zipFile}
          buttonText="Raise Query"
          buttonIcon="📧"
          variant="primary"
          size="medium"
          onQuerySent={onQuerySent}
        />
      </div>
    </>
  );
};

export default ValidationWarning;
