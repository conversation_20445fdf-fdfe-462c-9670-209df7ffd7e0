"""
Word Plugin API Routes
Handles all API endpoints for the Word Office Add-in integration
"""

import json
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, session
from werkzeug.security import check_password_hash

logger = logging.getLogger(__name__)

# Create Blueprint
word_plugin_bp = Blueprint('word_plugin', __name__, url_prefix='/api/word-plugin')

# In-memory token store (in production, use Redis or database)
# Format: {token: {'user_id': id, 'username': username, 'role': role, 'expires': timestamp}}
WORD_PLUGIN_TOKENS = {}

def init_word_plugin_routes(db, User, ArticleFile, ArticleReference, TEAssignment):
    """
    Initialize Word Plugin routes with database models
    
    Args:
        db: SQLAlchemy database instance
        User: User model class
        ArticleFile: ArticleFile model class
        ArticleReference: ArticleReference model class
        TEAssignment: TEAssignment model class
    """
    
    @word_plugin_bp.route('/auth', methods=['POST'])
    def word_plugin_auth():
        """
        Authenticate user for Word plugin access
        Uses token-based auth (not cookies) because Office Add-ins block third-party cookies
        """
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')

            if not username or not password:
                return jsonify({'error': 'Username and password required'}), 400

            user = User.query.filter_by(username=username, is_active=True).first()

            if not user or not check_password_hash(user.password_hash, password):
                return jsonify({'error': 'Invalid credentials'}), 401

            # Generate a session token for Word Plugin
            # Note: We can't use Flask sessions because Office Add-ins block third-party cookies
            import secrets
            import time
            session_token = secrets.token_urlsafe(32)

            # Store token in memory (expires in 24 hours)
            WORD_PLUGIN_TOKENS[session_token] = {
                'user_id': user.id,
                'username': user.username,
                'role': user.role,
                'expires': time.time() + (24 * 60 * 60)  # 24 hours
            }

            user.last_login = datetime.utcnow()
            db.session.commit()

            return jsonify({
                'success': True,
                'user': user.to_dict(),
                'session_token': session_token
            }), 200

        except Exception as e:
            logger.error(f"Error in Word plugin auth: {e}")
            return jsonify({'error': str(e)}), 500


    # NOTE: Rules endpoint removed - rules are defined in frontend
    # See: src/constants/editingRules.js
    # The Word Plugin should import rules from the frontend constants, not fetch from backend

    @word_plugin_bp.route('/check-session', methods=['GET'])
    def check_session():
        """Debug endpoint to check if session is working"""
        return jsonify({
            'session_data': dict(session),
            'has_user_id': 'user_id' in session,
            'cookies': dict(request.cookies)
        }), 200

    @word_plugin_bp.route('/article/<article_id>', methods=['GET'])
    def get_article_for_word_plugin(article_id):
        """
        Get article data for Word plugin
        Returns article metadata, file information, and references from database
        """
        try:
            # Get token from Authorization header
            auth_header = request.headers.get('Authorization', '')
            if not auth_header.startswith('Bearer '):
                return jsonify({'error': 'Authentication required'}), 401

            token = auth_header.replace('Bearer ', '')

            # Validate token
            import time
            if token not in WORD_PLUGIN_TOKENS:
                return jsonify({'error': 'Invalid or expired token'}), 401

            token_data = WORD_PLUGIN_TOKENS[token]
            if token_data['expires'] < time.time():
                del WORD_PLUGIN_TOKENS[token]
                return jsonify({'error': 'Token expired'}), 401

            # Get user
            user = User.query.get(token_data['user_id'])
            if not user or not user.is_active:
                return jsonify({'error': 'User not found or inactive'}), 401

            # Get article file record first
            article_file = ArticleFile.query.filter_by(article_id=article_id).first()

            if not article_file:
                return jsonify({'error': 'Article not found'}), 404

            # Get article assignment (if exists)
            assignment = TEAssignment.query.filter_by(article_id=article_id).first()

            # Check if user has access (assigned TE or admin)
            if assignment and user.role not in ['SuperAdmin', 'Admin', 'Coordinator'] and assignment.assigned_te_id != user.id:
                return jsonify({'error': 'Access denied'}), 403

            # Get references from database if they exist
            references_data = None
            reference_record = ArticleReference.query.filter_by(article_id=article_file.id).first()

            if reference_record:
                references_data = {
                    'total_references': reference_record.total_references,
                    'processed_references': json.loads(reference_record.processed_references) if reference_record.processed_references else [],
                    'quality_score': reference_record.total_quality_score,
                    'source_distribution': json.loads(reference_record.source_distribution) if reference_record.source_distribution else {},
                    'high_confidence_count': reference_record.high_confidence_count,
                    'medium_confidence_count': reference_record.medium_confidence_count,
                    'low_confidence_count': reference_record.low_confidence_count,
                    'needs_review_count': reference_record.needs_review_count,
                }

            return jsonify({
                'success': True,
                'article': {
                    'id': article_file.article_id,
                    'batch_id': assignment.batch_id if assignment else None,
                    'status': article_file.status,
                    'assigned_te': assignment.assigned_te.username if assignment and assignment.assigned_te else None,
                    'assigned_date': assignment.assigned_date.isoformat() if assignment and assignment.assigned_date else None,
                    'drive_file_id': assignment.drive_file_id if assignment else None,
                    'drive_file_name': assignment.drive_file_name if assignment else None,
                    'journal_name': article_file.journal_name,
                    'journal_code': article_file.journal_code,
                    'priority': article_file.priority,
                    'deadline': article_file.deadline.isoformat() if article_file.deadline else None,
                    'references': references_data  # Include references from database
                }
            }), 200

        except Exception as e:
            logger.error(f"Error getting article for Word plugin: {e}")
            return jsonify({'error': str(e)}), 500


    @word_plugin_bp.route('/execute-rule', methods=['POST'])
    def execute_word_plugin_rule():
        """
        Execute a specific editing rule on document content
        Returns validation results, comments, or auto-edit suggestions

        Note: Rule definitions are in frontend (src/constants/editingRules.js)
        This endpoint executes the validation logic for each rule
        """
        try:
            # Get token from Authorization header
            auth_header = request.headers.get('Authorization', '')
            if not auth_header.startswith('Bearer '):
                return jsonify({'error': 'Authentication required'}), 401

            token = auth_header.replace('Bearer ', '')

            # Validate token
            import time
            if token not in WORD_PLUGIN_TOKENS:
                return jsonify({'error': 'Invalid or expired token'}), 401

            token_data = WORD_PLUGIN_TOKENS[token]
            if token_data['expires'] < time.time():
                del WORD_PLUGIN_TOKENS[token]
                return jsonify({'error': 'Token expired'}), 401

            # Get user
            user = User.query.get(token_data['user_id'])
            if not user or not user.is_active:
                return jsonify({'error': 'User not found or inactive'}), 401

            data = request.get_json()
            rule_id = data.get('rule_id')
            content = data.get('content')  # Document content or specific section
            xml_data = data.get('xml_data')  # Optional XML data for consistency checks

            if not rule_id or not content:
                return jsonify({'error': 'rule_id and content are required'}), 400

            # Execute rule based on rule_id
            result = _execute_editing_rule(rule_id, content, xml_data)

            return jsonify({
                'success': True,
                'rule_id': rule_id,
                'result': result
            }), 200

        except Exception as e:
            logger.error(f"Error executing Word plugin rule: {e}")
            return jsonify({'error': str(e)}), 500


    @word_plugin_bp.route('/save-changes', methods=['POST'])
    def save_word_plugin_changes():
        """
        Save changes made in Word plugin back to the system
        Updates article status and logs changes
        """
        try:
            # Get token from Authorization header
            auth_header = request.headers.get('Authorization', '')
            if not auth_header.startswith('Bearer '):
                return jsonify({'error': 'Authentication required'}), 401

            token = auth_header.replace('Bearer ', '')

            # Validate token
            import time
            if token not in WORD_PLUGIN_TOKENS:
                return jsonify({'error': 'Invalid or expired token'}), 401

            token_data = WORD_PLUGIN_TOKENS[token]
            if token_data['expires'] < time.time():
                del WORD_PLUGIN_TOKENS[token]
                return jsonify({'error': 'Token expired'}), 401

            # Get user
            user = User.query.get(token_data['user_id'])
            if not user or not user.is_active:
                return jsonify({'error': 'User not found or inactive'}), 401

            data = request.get_json()
            article_id = data.get('article_id')
            changes = data.get('changes', [])
            status = data.get('status', 'in_progress')

            if not article_id:
                return jsonify({'error': 'article_id is required'}), 400

            # Get assignment
            assignment = TEAssignment.query.filter_by(article_id=article_id).first()

            if not assignment:
                return jsonify({'error': 'Article not found'}), 404

            # Update assignment status
            assignment.status = status
            assignment.completed_date = datetime.utcnow() if status == 'completed' else None

            # Log changes (you can create a separate table for this)
            logger.info(f"Word plugin changes saved for article {article_id} by user {user.username}: {len(changes)} changes")

            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Changes saved successfully',
                'article_id': article_id,
                'status': status
            }), 200

        except Exception as e:
            logger.error(f"Error saving Word plugin changes: {e}")
            db.session.rollback()
            return jsonify({'error': str(e)}), 500


    return word_plugin_bp


def _execute_editing_rule(rule_id, content, xml_data=None):
    """
    Helper function to execute specific editing rules
    Returns validation results, comments, or suggestions

    Note: This is backend validation logic only.
    Rule definitions are in frontend: src/constants/editingRules.js
    """
    result = {
        'status': 'success',
        'type': 'validation',
        'issues': [],
        'suggestions': [],
        'comments': []
    }

    # Running Head Check
    if rule_id == 'running_head_check':
        if len(content) > 50:
            result['issues'].append('Running head exceeds 50 characters')
        if ':' not in content:
            result['issues'].append('Running head should contain author surname and short title separated by colon')
        result['type'] = 'validation'

    # Title Length Check
    elif rule_id == 'title_length':
        word_count = len(content.split())
        if word_count < 10:
            result['issues'].append(f'Title is too short ({word_count} words). Recommended: 10-20 words')
        elif word_count > 20:
            result['issues'].append(f'Title is too long ({word_count} words). Recommended: 10-20 words')
        result['type'] = 'validation'

    # Abstract Length Check
    elif rule_id == 'abstract_length':
        word_count = len(content.split())
        if word_count < 150:
            result['issues'].append(f'Abstract is too short ({word_count} words). Recommended: 150-250 words')
        elif word_count > 250:
            result['issues'].append(f'Abstract is too long ({word_count} words). Recommended: 150-250 words')
        result['type'] = 'validation'

    # Add more rule implementations as needed
    # Each rule should match the rule_id from src/constants/editingRules.js
    else:
        result['status'] = 'not_implemented'
        result['issues'].append(f'Rule {rule_id} execution not yet implemented')

    return result

