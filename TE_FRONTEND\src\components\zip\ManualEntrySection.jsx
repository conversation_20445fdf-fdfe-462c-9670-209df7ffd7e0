import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Icons } from '../common';
import './ManualEntrySection.css';

const ManualEntrySection = ({ articleId }) => {
  const navigate = useNavigate();

  const handleManualEntry = () => {
    navigate(`/article/${articleId}/manual-entry`);
  };

  return (
    <div className="manual-entry-section">
      <div className="manual-entry-card">
        <div className="manual-entry-content">
          <div className="manual-entry-icon">
            <Icons.EditIcon />
          </div>
          <div className="manual-entry-text">
            <h4>Can't find your document or having trouble with file conversion?</h4>
            <p>Skip file preview and paste your references directly</p>
          </div>
          <button 
            onClick={handleManualEntry}
            className="manual-entry-button"
          >
            Manual Entry
          </button>
        </div>
      </div>
    </div>
  );
};

export default ManualEntrySection;
