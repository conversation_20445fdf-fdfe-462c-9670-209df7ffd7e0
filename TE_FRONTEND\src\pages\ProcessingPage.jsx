import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import PubMedComponent from '../components/PubMed/PubMedComponent';
import { normalizeReferences } from '../utils/referenceUtils';
import { LOADING_STATES } from '../utils/appUtils';
import { useRole } from '../context/AuthContext';
import { useZipQueue } from '../context/ZipQueueContext';
import { Icons } from '../components/common';
import './ProcessingPage.css';

const ProcessingPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [terms, setTerms] = useState({
    isLoading: LOADING_STATES.IDLE,
    data: [],
  });
  const [referencesText, setReferencesText] = useState('');
  const [articleId, setArticleId] = useState('');
  const [saveStatus, setSaveStatus] = useState('');
  const [fromZipProcessor, setFromZipProcessor] = useState(false);
  const [zipId, setZipId] = useState(null);
  const { isAdmin } = useRole();
  const { markZipAsProcessed } = useZipQueue();

  useEffect(() => {
    // Get data from navigation state (from ZIP workflow or manual entry)
    if (location.state) {
      const { references, articleId: id, manualEntry, message, fromZipProcessor: fromZip, zipId: zipIdParam } = location.state;

      if (references) {
        setReferencesText(references);
      }
      if (id) {
        setArticleId(id);
      }
      if (fromZip) {
        setFromZipProcessor(true);
      }
      if (zipIdParam) {
        setZipId(zipIdParam);
      }
      // Show message for manual entry
      if (manualEntry && message) {
        console.log('Manual entry mode:', message);
      }
    }
  }, [location.state]);

  const handleProcessReferences = () => {
    if (!referencesText.trim()) {
      alert('Please paste some references to process.');
      return;
    }

    // Split references by lines and filter out empty lines
    const referenceLines = referencesText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);

    if (referenceLines.length === 0) {
      alert('No valid references found. Please check your input.');
      return;
    }

    // Normalize references
    const normalizedRefs = normalizeReferences(referenceLines);
    
    setTerms({
      isLoading: LOADING_STATES.PROCESSING,
      data: normalizedRefs,
      articleId: articleId || 'unknown',
      fromDb: false,
    });
  };

  const handleBackToUpload = () => {
    // Navigate back to appropriate page based on context
    if (fromZipProcessor && zipId) {
      navigate(`/process-zip/${zipId}`);
    } else if (isAdmin) {
      navigate('/admin/upload');
    } else {
      navigate('/');
    }
  };

  const handleCompleteProcessing = () => {
    if (fromZipProcessor && zipId) {
      if (window.confirm('Mark this ZIP as processed? It will be moved to the "Ready for Assignment" section.')) {
        markZipAsProcessed(zipId);
        // Navigate to appropriate queue based on admin context
        const queuePath = isAdmin ? '/admin/zip-queue' : '/zip-queue';
        navigate(queuePath);
      }
    }
  };

  const handleClearReferences = () => {
    setReferencesText('');
    setTerms({
      isLoading: LOADING_STATES.IDLE,
      data: [],
    });
  };

  return (
    <div className="processing-page">
      {/* Header */}
      <header className="processing-header">
        <div className="header-content">
          <div className="header-left">
            <button onClick={handleBackToUpload} className="back-to-upload">
              <Icons.ChevronLeftIcon />
              {fromZipProcessor ? 'Back to ZIP' : 'Back to Upload'}
            </button>
            <div className="page-title">
              <h1>Process References</h1>
              {articleId && (
                <p className="article-id">Article ID: {articleId}</p>
              )}
              {fromZipProcessor && (
                <p className="zip-processor-notice">
                  <Icons.InfoIcon className="info-icon" />
                  Processing from ZIP workflow
                </p>
              )}
            </div>
          </div>
          {fromZipProcessor && (
            <div className="header-actions">
              <button onClick={handleCompleteProcessing} className="complete-processing-button">
                <Icons.CheckCircleIcon />
                Mark ZIP as Processed
              </button>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <div className="processing-content">
        {terms.data.length === 0 && terms.isLoading === LOADING_STATES.IDLE ? (
          <div className="reference-input-section">
            <div className="input-container">
              <div className="input-header">
                <h2>
                  {location.state?.manualEntry ? (
                    <>
                      <span className="manual-entry-icon">✏️</span>
                      Manual Reference Entry
                    </>
                  ) : (
                    'Paste References'
                  )}
                </h2>
                <p>
                  {location.state?.manualEntry
                    ? 'Enter your references manually, one per line'
                    : 'Paste the references you copied from the document preview'
                  }
                </p>
                {location.state?.manualEntry && (
                  <div className="manual-entry-notice">
                    <Icons.InfoIcon />
                    <span>Manual entry mode for Article ID: <strong>{articleId}</strong></span>
                  </div>
                )}
              </div>
              
              <div className="textarea-container">
                <textarea
                  value={referencesText}
                  onChange={(e) => setReferencesText(e.target.value)}
                  placeholder="Paste your references here, one per line..."
                  className="references-textarea"
                  rows={15}
                />
                <div className="textarea-info">
                  <span className="char-count">
                    {referencesText.length} characters
                  </span>
                  <span className="line-count">
                    {referencesText.split('\n').filter(line => line.trim()).length} references
                  </span>
                </div>
              </div>

              <div className="input-actions">
                <button
                  onClick={handleClearReferences}
                  className="clear-button"
                  disabled={!referencesText.trim()}
                >
                  <Icons.TrashIcon />
                  Clear
                </button>
                <button
                  onClick={handleProcessReferences}
                  className="process-button"
                  disabled={!referencesText.trim()}
                >
                  <Icons.PlayIcon />
                  Process References
                </button>
              </div>

              {/* Article ID Input for Admin Users */}
              {isAdmin && (
                <div className="article-id-section">
                  <label htmlFor="articleId" className="article-id-label">
                    Article ID (required for saving):
                  </label>
                  <input
                    id="articleId"
                    type="text"
                    value={articleId}
                    disabled
                    onChange={(e) => setArticleId(e.target.value)}
                    placeholder="Enter Article ID (e.g., PMC123456)"
                    className="article-id-input"
                  />
                </div>
              )}

              {/* Save Status Message */}
              {saveStatus && (
                <div className={`save-status ${
                  saveStatus.includes('Error') || saveStatus.includes('Please')
                    ? 'error'
                    : saveStatus.includes('successfully')
                    ? 'success'
                    : 'info'
                }`}>
                  {saveStatus}
                </div>
              )}
            </div>

            <div className="help-section">
              <h3>How to use:</h3>
              <div className="help-steps">
                <div className="help-step">
                  <span className="step-number">1</span>
                  <span>Copy references from the document preview</span>
                </div>
                <div className="help-step">
                  <span className="step-number">2</span>
                  <span>Paste them in the text area above</span>
                </div>
                <div className="help-step">
                  <span className="step-number">3</span>
                  <span>Click "Process References" to enhance them</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="pubmed-section">
            <PubMedComponent
              terms={terms}
              dbMode={false}
              fromZipProcessor={fromZipProcessor}
              zipId={zipId}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ProcessingPage;
