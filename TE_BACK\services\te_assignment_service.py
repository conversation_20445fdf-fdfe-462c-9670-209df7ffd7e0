"""
TE Assignment Service - Orchestrates the complete TE assignment workflow
"""

import os
import json
import logging
import tempfile
import zipfile
import math
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from flask import current_app

from .drive_service import create_drive_service
from .email_service import create_email_service
from .google_sheets_service import create_google_sheets_service

logger = logging.getLogger(__name__)

class TEAssignmentService:
    """Service to handle complete TE assignment workflow"""
    
    def __init__(self, db, te_assignment_model, user_model, article_file_model):
        """
        Initialize TE assignment service
        
        Args:
            db: SQLAlchemy database instance
            te_assignment_model: TEAssignment model class
            user_model: User model class
            article_file_model: ArticleFile model class
        """
        self.db = db
        self.TEAssignment = te_assignment_model
        self.User = user_model
        self.ArticleFile = article_file_model
        self.drive_service = None
        self.email_service = None
        self.sheets_service = None

    def _get_drive_service(self):
        """Get or create Drive service instance"""
        if not self.drive_service:
            self.drive_service = create_drive_service()
        return self.drive_service

    def _get_email_service(self):
        """Get or create Email service instance"""
        if not self.email_service:
            self.email_service = create_email_service()
        return self.email_service

    def _get_sheets_service(self):
        """Get or create Google Sheets service instance"""
        if not self.sheets_service:
            self.sheets_service = create_google_sheets_service()
        return self.sheets_service
    
    def get_completed_articles(self) -> List[Dict]:
        """
        Get all articles in 'completed' status ready for TE assignment
        
        Returns:
            List of article dictionaries
        """
        try:
            articles = self.ArticleFile.query.filter_by(status='completed').all()
            return [article.to_dict() for article in articles]
            
        except Exception as e:
            logger.error(f"Failed to get completed articles: {e}")
            raise

    def create_te_assignment_from_zip_files(self, zip_files: List, te_id: int,
                                          assigned_by_id: int, notes: str = None) -> Dict:
        """
        DEPRECATED: Create TE assignment directly from ZIP files (for smart batching)

        This method is deprecated. Use create_te_assignment_with_json_summary() instead
        which provides better author information extraction and consistent workflow.

        Args:
            zip_files: List of uploaded ZIP file objects
            te_id: ID of TE user to assign to
            assigned_by_id: ID of user making the assignment
            notes: Optional notes about the assignment

        Returns:
            Assignment result dictionary with upload details
        """
        try:
            # Get TE user
            te_user = self.User.query.get(te_id)
            if not te_user or te_user.role != 'TE':
                raise ValueError(f"Invalid TE user ID: {te_id}")

            # Get assigning user
            assigning_user = self.User.query.get(assigned_by_id)
            if not assigning_user:
                raise ValueError(f"Invalid assigning user ID: {assigned_by_id}")

            # Extract article IDs from ZIP filenames
            article_ids = []
            for zip_file in zip_files:
                article_id = os.path.splitext(zip_file.filename)[0]
                article_ids.append(article_id)

            # Generate batch name
            drive_service = self._get_drive_service()
            batch_name = drive_service.generate_batch_name()

            # Create TE assignment record
            assignment = self.TEAssignment(
                batch_name=batch_name,
                zip_ids=[],  # Not using ZIP queue IDs
                article_ids=article_ids,
                assigned_te_id=te_id,
                assigned_te_email=te_user.email,
                assignment_status='pending',
                assigned_by=assigned_by_id,
                notes=notes,
                zip_file_paths=[]  # Will be populated after upload
            )

            self.db.session.add(assignment)
            self.db.session.flush()  # Get the ID

            # Upload ZIP files directly to Google Drive
            drive_service = self._get_drive_service()
            folder_id, folder_link = drive_service.create_batch_folder(batch_name)

            uploaded_files = []
            for zip_file in zip_files:
                # Upload each ZIP file
                file_id, file_link = drive_service.upload_zip_file_from_memory(
                    zip_file, folder_id, zip_file.filename
                )
                uploaded_files.append({
                    'filename': zip_file.filename,
                    'file_id': file_id,
                    'file_link': file_link
                })
                logger.info(f"Uploaded ZIP file to Drive: {zip_file.filename}")

            # Update assignment with Drive info
            assignment.drive_folder_id = folder_id
            assignment.drive_folder_link = folder_link
            assignment.assignment_status = 'uploaded'
            assignment.uploaded_at = datetime.utcnow()

            self.db.session.commit()

            # Try to extract author information from ZIP files or batch summary
            articles_with_authors = self._extract_articles_with_authors_from_zips(zip_files)

            # If no author information found from ZIPs, try automation directory as fallback
            if not articles_with_authors or all(not article.get('authors') for article in articles_with_authors):
                articles_with_authors = self._try_extract_from_automation_directory(article_ids)

            # Fetch author queries for the assigned articles
            author_queries = self._get_author_queries_for_articles(article_ids)

            # Send email notification with the correct folder link
            email_sent = self.send_assignment_email_direct(
                assignment_id=assignment.id,
                te_name=te_user.username,
                te_email=te_user.email,
                article_ids=article_ids,
                drive_link=folder_link,  # Use the folder_link we just created
                batch_name=batch_name,
                assigned_by_username=assigning_user.username,
                articles_with_authors=articles_with_authors,
                author_queries=author_queries
            )

            # Update Google Sheets with TE assignment information
            self._update_google_sheets_for_articles(article_ids, te_user.username)

            logger.info(f"Created and uploaded TE assignment: {batch_name} for TE {te_user.username}")
            return {
                'assignment_id': assignment.id,
                'batch_name': batch_name,
                'te_name': te_user.username,
                'te_email': te_user.email,
                'article_ids': article_ids,
                'article_count': len(article_ids),
                'drive_folder_id': folder_id,
                'drive_folder_link': folder_link,
                'uploaded_files': uploaded_files,
                'zip_files_uploaded': len(uploaded_files),
                'email_sent': email_sent,
                'status': 'uploaded'
            }

        except Exception as e:
            self.db.session.rollback()
            logger.error(f"Failed to create TE assignment from ZIP files: {e}")
            raise

    def get_available_tes(self) -> List[Dict]:
        """
        Get all active users with TE role
        
        Returns:
            List of TE user dictionaries
        """
        try:
            tes = self.User.query.filter_by(role='TE', is_active=True).all()
            return [{'id': te.id, 'username': te.username, 'email': te.email} for te in tes]
            
        except Exception as e:
            logger.error(f"Failed to get available TEs: {e}")
            raise
    
    def create_zip_from_articles(self, article_ids: List[str], batch_name: str) -> str:
        """
        Create a ZIP file containing all files for the specified articles
        
        Args:
            article_ids: List of article IDs to include
            batch_name: Name for the batch (used in ZIP filename)
            
        Returns:
            Path to created ZIP file
        """
        try:
            # Create temporary ZIP file
            temp_dir = tempfile.gettempdir()
            zip_path = os.path.join(temp_dir, f"{batch_name}.zip")
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for article_id in article_ids:
                    # Get article record
                    article = self.ArticleFile.query.filter_by(article_id=article_id).first()
                    if not article:
                        logger.warning(f"Article not found: {article_id}")
                        continue
                    
                    # Add article files to ZIP
                    if article.folder_path and os.path.exists(article.folder_path):
                        # Add all files from article folder
                        for root, dirs, files in os.walk(article.folder_path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                # Create archive path with article ID prefix
                                archive_path = os.path.join(article_id, os.path.relpath(file_path, article.folder_path))
                                zipf.write(file_path, archive_path)
                    else:
                        # Create a placeholder file if no folder exists
                        placeholder_content = f"Article ID: {article_id}\nStatus: {article.status}\nCreated: {article.created_at}"
                        zipf.writestr(f"{article_id}/article_info.txt", placeholder_content)
            
            logger.info(f"Created ZIP file: {zip_path} with {len(article_ids)} articles")
            return zip_path
            
        except Exception as e:
            logger.error(f"Failed to create ZIP file: {e}")
            raise
    
    def create_te_assignment(self, article_ids: List[str], te_id: int, assigned_by_id: int, 
                           notes: str = None) -> Dict:
        """
        Create a new TE assignment
        
        Args:
            article_ids: List of article IDs to assign
            te_id: ID of TE user to assign to
            assigned_by_id: ID of user making the assignment
            notes: Optional notes about the assignment
            
        Returns:
            Assignment result dictionary
        """
        try:
            # Get TE user
            te_user = self.User.query.get(te_id)
            if not te_user or te_user.role != 'TE':
                raise ValueError(f"Invalid TE user ID: {te_id}")
            
            # Get assigning user
            assigning_user = self.User.query.get(assigned_by_id)
            if not assigning_user:
                raise ValueError(f"Invalid assigning user ID: {assigned_by_id}")
            
            # Generate batch name
            drive_service = self._get_drive_service()
            batch_name = drive_service.generate_batch_name()
            
            # Create TE assignment record
            assignment = self.TEAssignment(
                batch_name=batch_name,
                zip_ids=[],  # Will be populated if using ZIP queue
                article_ids=article_ids,
                assigned_te_id=te_id,
                assigned_te_email=te_user.email,
                assignment_status='pending',
                assigned_by=assigned_by_id,
                notes=notes
            )
            
            self.db.session.add(assignment)
            self.db.session.flush()  # Get the ID
            self.db.session.commit()  # Save to database

            logger.info(f"Created TE assignment: {batch_name} for TE {te_user.username}")
            return {
                'assignment_id': assignment.id,
                'batch_name': batch_name,
                'te_name': te_user.username,
                'te_email': te_user.email,
                'article_ids': article_ids,  # Include article IDs for frontend processing
                'article_count': len(article_ids),
                'status': 'pending'
            }
            
        except Exception as e:
            self.db.session.rollback()
            logger.error(f"Failed to create TE assignment: {e}")
            raise

    def create_te_assignment_with_zips(self, zip_file_paths: List[str], article_ids: List[str],
                                     te_id: int, assigned_by_id: int, notes: str = None) -> Dict:
        """
        Create a new TE assignment with actual ZIP files

        Args:
            zip_file_paths: List of paths to uploaded ZIP files
            article_ids: List of article IDs
            te_id: ID of TE user to assign to
            assigned_by_id: ID of user making the assignment
            notes: Optional notes about the assignment

        Returns:
            Assignment result dictionary
        """
        try:
            # Get TE user
            te_user = self.User.query.get(te_id)
            if not te_user or te_user.role != 'TE':
                raise ValueError(f"Invalid TE user ID: {te_id}")

            # Get assigning user
            assigning_user = self.User.query.get(assigned_by_id)
            if not assigning_user:
                raise ValueError(f"Invalid assigning user ID: {assigned_by_id}")

            # Generate batch name
            drive_service = self._get_drive_service()
            batch_name = drive_service.generate_batch_name()

            # Create TE assignment record
            assignment = self.TEAssignment(
                batch_name=batch_name,
                zip_ids=[],  # Will be populated if using ZIP queue
                article_ids=article_ids,
                assigned_te_id=te_id,
                assigned_te_email=te_user.email,
                assignment_status='pending',
                assigned_by=assigned_by_id,
                notes=notes,
                zip_file_paths=zip_file_paths  # Store the actual ZIP file paths
            )

            self.db.session.add(assignment)
            self.db.session.flush()  # Get the ID
            self.db.session.commit()  # Save to database

            logger.info(f"Created TE assignment with ZIP files: {batch_name} for TE {te_user.username}")
            return {
                'assignment_id': assignment.id,
                'batch_name': batch_name,
                'te_name': te_user.username,
                'te_email': te_user.email,
                'article_count': len(article_ids),
                'zip_count': len(zip_file_paths),
                'status': 'pending'
            }

        except Exception as e:
            self.db.session.rollback()
            logger.error(f"Failed to create TE assignment with ZIP files: {e}")
            raise

    def upload_assignment_to_drive(self, assignment_id: int) -> Dict:
        """
        Upload assignment files to Google Drive

        Args:
            assignment_id: TE assignment ID

        Returns:
            Upload result dictionary
        """
        try:
            # Get assignment
            assignment = self.TEAssignment.query.get(assignment_id)
            if not assignment:
                raise ValueError(f"Assignment not found: {assignment_id}")

            # Update status to uploading
            assignment.assignment_status = 'uploading'
            self.db.session.commit()

            # Upload to Google Drive
            drive_service = self._get_drive_service()

            # Create batch folder
            folder_id, folder_link = drive_service.create_batch_folder(assignment.batch_name)

            # Check if we have actual ZIP files to upload
            if assignment.zip_file_paths:
                # Upload existing ZIP files
                uploaded_files = []
                for zip_path in assignment.zip_file_paths:
                    if os.path.exists(zip_path):
                        filename = os.path.basename(zip_path)
                        # Remove timestamp prefix for cleaner filename
                        if filename.startswith('te_assignment_'):
                            parts = filename.split('_', 3)
                            if len(parts) >= 4:
                                filename = parts[3]  # Get original filename

                        file_id, file_link = drive_service.upload_zip_file(
                            zip_path, folder_id, filename
                        )
                        uploaded_files.append({
                            'filename': filename,
                            'file_id': file_id,
                            'file_link': file_link
                        })
                        logger.info(f"Uploaded existing ZIP file: {filename}")
                    else:
                        logger.warning(f"ZIP file not found: {zip_path}")

                if not uploaded_files:
                    raise ValueError("No ZIP files could be uploaded")

                # Use the first file for the main result (for backward compatibility)
                main_file = uploaded_files[0]
                file_id, file_link = main_file['file_id'], main_file['file_link']

            else:
                # Fallback: Create ZIP file with articles (legacy behavior)
                zip_path = self.create_zip_from_articles(assignment.article_ids, assignment.batch_name)

                try:
                    # Upload ZIP file
                    file_id, file_link = drive_service.upload_zip_file(
                        zip_path, folder_id, f"{assignment.batch_name}.zip"
                    )
                finally:
                    # Clean up temporary ZIP file
                    if os.path.exists(zip_path):
                        os.remove(zip_path)

            # Update assignment with Drive info
            assignment.drive_folder_id = folder_id
            assignment.drive_folder_link = folder_link
            assignment.drive_file_id = file_id
            assignment.drive_file_link = file_link
            assignment.assignment_status = 'uploaded'
            assignment.uploaded_at = datetime.utcnow()

            self.db.session.commit()

            logger.info(f"Uploaded assignment {assignment.batch_name} to Drive")
            return {
                'assignment_id': assignment_id,
                'batch_name': assignment.batch_name,
                'folder_id': folder_id,
                'folder_link': folder_link,
                'file_id': file_id,
                'file_link': file_link,
                'status': 'uploaded'
            }
            
        except Exception as e:
            # Update assignment status to failed
            if 'assignment' in locals():
                assignment.assignment_status = 'failed'
                self.db.session.commit()
            
            logger.error(f"Failed to upload assignment to Drive: {e}")
            raise
    
    def send_assignment_email(self, assignment_id: int) -> bool:
        """
        Send email notification to TE about assignment
        
        Args:
            assignment_id: TE assignment ID
            
        Returns:
            True if email sent successfully
        """
        try:
            # Get assignment with relationships
            assignment = self.TEAssignment.query.get(assignment_id)
            if not assignment:
                raise ValueError(f"Assignment not found: {assignment_id}")
            
            if not assignment.drive_folder_link:
                raise ValueError("Assignment must be uploaded to Drive before sending email")
            
            # Check for author queries for this batch
            queries_file_path = None
            author_queries = []

            try:
                # Import AuthorQuery model
                from app import AuthorQuery

                # Get queries for articles in this assignment (exclude validation errors)
                queries = AuthorQuery.query.filter(
                    AuthorQuery.article_id.in_(assignment.article_ids),
                    AuthorQuery.query_type != 'validation_error'
                ).all()

                if queries:
                    # Convert to list of dictionaries for email service
                    author_queries = [{
                        'article_id': query.article_id,
                        'author_name': query.author_name,
                        'query_type': query.query_type,
                        'query_text': query.query_text
                    } for query in queries]

                    # Check for queries file
                    import os
                    queries_dir = os.path.join(os.getcwd(), 'queries')
                    batch_id = assignment.batch_name.replace('batch-', '') if assignment.batch_name.startswith('batch-') else assignment.batch_name
                    queries_file_path = os.path.join(queries_dir, f"batch-{batch_id}_queries.txt")

                    if not os.path.exists(queries_file_path):
                        # Try alternative naming
                        queries_file_path = os.path.join(queries_dir, f"{assignment.batch_name}_queries.txt")
                        if not os.path.exists(queries_file_path):
                            queries_file_path = None

                    logger.info(f"Found {len(author_queries)} author queries for assignment {assignment_id}")
                    if queries_file_path:
                        logger.info(f"Queries file found: {queries_file_path}")
                    else:
                        logger.warning(f"Queries file not found for batch {assignment.batch_name}")

            except Exception as e:
                logger.warning(f"Error checking for author queries: {e}")
                # Continue without queries if there's an error

            # Send email
            email_service = self._get_email_service()
            success = email_service.send_te_assignment_notification(
                te_name=assignment.assigned_te.username,
                te_email=assignment.assigned_te_email,
                article_ids=assignment.article_ids,
                drive_link=assignment.drive_folder_link,
                batch_name=assignment.batch_name,
                assigned_by=assignment.assigned_by_user.username,
                queries_file_path=queries_file_path,
                author_queries=author_queries
            )
            
            if success:
                # Update assignment status
                assignment.assignment_status = 'emailed'
                assignment.email_sent_at = datetime.utcnow()
                self.db.session.commit()
                
                logger.info(f"Sent assignment email for {assignment.batch_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to send assignment email: {e}")
            return False

    def send_assignment_email_direct(self, assignment_id: int, te_name: str, te_email: str,
                                   article_ids: List[str], drive_link: str, batch_name: str,
                                   assigned_by_username: str, articles_with_authors: Optional[List[Dict]] = None,
                                   author_queries: Optional[List[Dict]] = None) -> bool:
        """
        Send email notification for TE assignment using direct parameters
        This avoids database query issues and ensures the correct folder link is used

        Args:
            assignment_id: Assignment ID
            te_name: TE's name
            te_email: TE's email
            article_ids: List of article IDs
            drive_link: Google Drive folder link
            batch_name: Batch name
            assigned_by_username: Username of person who made assignment
            articles_with_authors: Optional list of articles with author information

        Returns:
            True if email sent successfully
        """
        try:
            # Initialize variables to ensure they're always defined
            queries_file_path = None

            # Use provided author queries or fetch from database
            if author_queries is None:
                author_queries = []
                try:
                    # Import AuthorQuery model and app
                    from app import app, AuthorQuery

                    # Ensure we're in Flask app context
                    with app.app_context():
                        # Get queries for articles in this assignment (exclude validation errors)
                        queries = AuthorQuery.query.filter(
                            AuthorQuery.article_id.in_(article_ids),
                            AuthorQuery.query_type != 'validation_error'
                        ).all()

                        if queries:
                            # Convert to list of dictionaries for email service
                            author_queries = [{
                                'article_id': query.article_id,
                                'author_name': query.author_name,
                                'query_type': query.query_type,
                                'query_text': query.query_text
                            } for query in queries]

                        logger.info(f"Found {len(author_queries)} author queries for direct email assignment {assignment_id}")

                except Exception as e:
                    logger.error(f"Failed to fetch author queries for articles {article_ids}: {e}")
                    # Continue without queries if there's an error - variables already initialized

            # Generate queries file if we have queries (regardless of source)
            if author_queries and len(author_queries) > 0:
                try:
                    queries_file_path = self._generate_queries_file(author_queries, batch_name)
                    if queries_file_path:
                        logger.info(f"Generated queries file: {queries_file_path}")
                except Exception as e:
                    logger.error(f"Failed to generate queries file: {e}")
                    # Continue without file if generation fails

            # Send email using provided parameters
            email_service = self._get_email_service()
            success = email_service.send_te_assignment_notification(
                te_name=te_name,
                te_email=te_email,
                article_ids=article_ids,
                drive_link=drive_link,
                batch_name=batch_name,
                assigned_by=assigned_by_username,
                articles_with_authors=articles_with_authors,
                queries_file_path=queries_file_path,
                author_queries=author_queries
            )

            if success:
                # Update assignment status in database
                assignment = self.TEAssignment.query.get(assignment_id)
                if assignment:
                    assignment.assignment_status = 'emailed'
                    assignment.email_sent_at = datetime.utcnow()
                    self.db.session.commit()
                    logger.info(f"Email sent successfully for assignment {assignment_id} with drive link: {drive_link}")
                else:
                    logger.warning(f"Assignment {assignment_id} not found for status update")
                
                # Update Google Sheets tracker after successful email
                self._update_google_sheets_for_articles(article_ids, te_name)
            else:
                logger.error(f"Failed to send email for assignment {assignment_id}")

            return success

        except Exception as e:
            logger.error(f"Error sending assignment email directly: {e}")
            return False

    def _extract_articles_with_authors_from_zips(self, zip_files: List) -> Optional[List[Dict]]:
        """
        Extract article information with authors from ZIP files

        Args:
            zip_files: List of ZIP file objects

        Returns:
            List of articles with author information, or None if extraction fails
        """
        try:
            articles_with_authors = []

            for zip_file in zip_files:
                # Extract article ID from filename
                article_id = os.path.splitext(zip_file.filename)[0]

                # Create basic entry - author info will be filled by automation directory method
                article_info = {
                    'article_id': article_id,
                    'journal': 'Unknown',
                    'authors': [],
                    'status': 'pending'
                }

                articles_with_authors.append(article_info)
                logger.info(f"Added article {article_id} for processing")

            return articles_with_authors if articles_with_authors else None

        except Exception as e:
            logger.error(f"Failed to extract articles from ZIP files: {e}")
            return None

    def _find_batch_summary_for_articles(self, article_ids: List[str], batch_name: str = None) -> Optional[str]:
        """
        Find the batch_summary.json file for the given articles

        Args:
            article_ids: List of article IDs to search for
            batch_name: Optional batch name to help narrow search

        Returns:
            Path to batch_summary.json file, or None if not found
        """
        try:
            # Look in TE_AUTOMATION incoming directory
            automation_base = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'TE_AUTOMATION', 'incoming')

            if not os.path.exists(automation_base):
                logger.warning(f"TE_AUTOMATION incoming directory not found: {automation_base}")
                return None

            # Search through date directories (most recent first)
            date_dirs = sorted([d for d in os.listdir(automation_base) if os.path.isdir(os.path.join(automation_base, d))], reverse=True)

            for date_dir in date_dirs:
                date_path = os.path.join(automation_base, date_dir)

                # Search through batch directories
                batch_dirs = [d for d in os.listdir(date_path) if os.path.isdir(os.path.join(date_path, d))]

                for batch_dir in batch_dirs:
                    batch_path = os.path.join(date_path, batch_dir)
                    batch_summary_path = os.path.join(batch_path, 'batch_summary.json')

                    if os.path.exists(batch_summary_path):
                        # Check if this batch contains any of our articles
                        try:
                            with open(batch_summary_path, 'r', encoding='utf-8') as f:
                                batch_data = json.load(f)

                            batch_article_ids = [a.get('article_id') for a in batch_data.get('articles', [])]

                            # If any of our article IDs are in this batch, return this path
                            if any(aid in batch_article_ids for aid in article_ids):
                                logger.info(f"Found batch summary at: {batch_summary_path}")
                                return batch_summary_path
                        except Exception as e:
                            logger.warning(f"Error reading batch summary {batch_summary_path}: {e}")
                            continue

            logger.warning(f"No batch summary found for articles: {article_ids}")
            return None

        except Exception as e:
            logger.error(f"Error finding batch summary: {e}")
            return None

    def _extract_articles_with_authors_from_batch_summary(self, batch_summary_path: str,
                                                        article_ids: List[str]) -> Optional[List[Dict]]:
        """
        Extract article information with authors from batch summary JSON file

        Args:
            batch_summary_path: Path to batch_summary.json file
            article_ids: List of article IDs to filter for

        Returns:
            List of articles with author information, or None if extraction fails
        """
        try:
            if not os.path.exists(batch_summary_path):
                logger.warning(f"Batch summary file not found: {batch_summary_path}")
                return None

            with open(batch_summary_path, 'r', encoding='utf-8') as f:
                batch_data = json.load(f)

            # Extract articles from batch summary
            all_articles = batch_data.get('articles', [])

            # Filter articles to match the assigned article IDs
            articles_with_authors = []
            for article_id in article_ids:
                # Find matching article in batch summary
                matching_article = next(
                    (article for article in all_articles if article.get('article_id') == article_id),
                    None
                )

                if matching_article:
                    article_info = {
                        'article_id': matching_article.get('article_id', article_id),
                        'journal': matching_article.get('journal', 'Unknown'),
                        'authors': matching_article.get('authors', []),
                        'status': matching_article.get('status', 'unknown')
                    }
                    articles_with_authors.append(article_info)
                    logger.info(f"Found batch summary data for {article_id}: {len(article_info['authors'])} authors")
                else:
                    # Article not found in batch summary
                    article_info = {
                        'article_id': article_id,
                        'journal': 'Unknown',
                        'authors': [],
                        'status': 'not_in_batch'
                    }
                    articles_with_authors.append(article_info)
                    logger.warning(f"Article {article_id} not found in batch summary")

            return articles_with_authors if articles_with_authors else None

        except Exception as e:
            logger.error(f"Failed to extract articles with authors from batch summary: {e}")
            return None

    def _try_extract_from_automation_directory(self, article_ids: List[str]) -> Optional[List[Dict]]:
        """
        Try to extract author information from TE_AUTOMATION incoming directory

        Args:
            article_ids: List of article IDs to look for

        Returns:
            List of articles with author information, or None if not found
        """
        try:
            # Look for JSON files in TE_AUTOMATION incoming directory
            # Go up from TE_BACK/services to the root directory, then to TE_AUTOMATION/incoming
            automation_base = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'TE_AUTOMATION', 'incoming')

            if not os.path.exists(automation_base):
                logger.warning(f"TE_AUTOMATION incoming directory not found: {automation_base}")
                return None

            articles_with_authors = []

            # Search through date directories for JSON files
            for date_dir in os.listdir(automation_base):
                date_path = os.path.join(automation_base, date_dir)
                if not os.path.isdir(date_path):
                    continue

                # Search through batch directories
                for batch_dir in os.listdir(date_path):
                    batch_path = os.path.join(date_path, batch_dir)
                    if not os.path.isdir(batch_path):
                        continue

                    # Look for individual JSON files
                    for article_id in article_ids:
                        json_file = os.path.join(batch_path, f"{article_id}.json")
                        if os.path.exists(json_file):
                            try:
                                with open(json_file, 'r', encoding='utf-8') as f:
                                    article_data = json.load(f)

                                article_info = {
                                    'article_id': article_data.get('article_id', article_id),
                                    'journal': article_data.get('journal', 'Unknown'),
                                    'authors': article_data.get('authors', []),
                                    'status': article_data.get('status', 'unknown')
                                }

                                articles_with_authors.append(article_info)
                                logger.info(f"Found author info for {article_id} in automation directory: {len(article_info['authors'])} authors")

                            except Exception as json_error:
                                logger.error(f"Error reading JSON file {json_file}: {json_error}")

            return articles_with_authors if articles_with_authors else None

        except Exception as e:
            logger.error(f"Failed to extract from automation directory: {e}")
            return None



    def _generate_queries_file(self, author_queries: List[Dict], batch_name: str) -> Optional[str]:
        """
        Generate a temporary queries file for email attachment

        Args:
            author_queries: List of author query dictionaries
            batch_name: Batch name for the file

        Returns:
            Path to generated file or None if failed
        """
        try:
            import tempfile
            import os
            from datetime import datetime

            # Create temporary file
            temp_dir = tempfile.gettempdir()
            filename = f"{batch_name}_queries.txt"
            file_path = os.path.join(temp_dir, filename)

            # Generate content with ASCII characters only
            content = f"""AUTHOR QUERIES REPORT
Batch: {batch_name}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Total Queries: {len(author_queries)}

============================================================

"""

            # Group queries by article
            queries_by_article = {}
            for query in author_queries:
                article_id = query.get('article_id', 'Unknown')
                if article_id not in queries_by_article:
                    queries_by_article[article_id] = []
                queries_by_article[article_id].append(query)

            # Add queries for each article
            for article_id, queries in queries_by_article.items():
                content += f"ARTICLE: {article_id}\n"
                content += f"{'-'*40}\n"

                # Group queries by type and text for this article
                grouped_queries = {}
                for query in queries:
                    query_type = query.get('query_type', 'unknown').replace('_', ' ').title()
                    query_text = query.get('query_text', '').strip()
                    author_name = query.get('author_name', 'Unknown')

                    # Provide default text based on query type if empty
                    if not query_text:
                        if query_type.lower() == 'copyright no':
                            query_text = 'Author has not provided copyright permission'
                        elif query_type.lower() == 'missing in system':
                            query_text = 'Author information missing in system records'
                        elif query_type.lower() == 'missing in firstpage':
                            query_text = 'Author information missing in firstpage'
                        elif query_type.lower() == 'validation error':
                            query_text = 'Author information validation error'
                        else:
                            query_text = 'Query details not specified'

                    # Clean author name to remove extra spaces and special characters
                    author_name = ' '.join(author_name.split())  # Remove extra spaces
                    author_name = author_name.replace('\u00a0', ' ')  # Replace non-breaking spaces

                    # Create a key based on type and text
                    key = f"{query_type}|{query_text}"
                    if key not in grouped_queries:
                        grouped_queries[key] = {
                            'type': query_type,
                            'text': query_text,
                            'authors': []
                        }
                    grouped_queries[key]['authors'].append(author_name)

                # Write grouped queries
                for i, (key, group) in enumerate(grouped_queries.items(), 1):
                    content += f"\nQuery {i}:\n"
                    content += f"  Type: {group['type']}\n"
                    if len(group['authors']) == 1:
                        content += f"  Author: {group['authors'][0]}\n"
                    else:
                        # Clean and deduplicate authors
                        clean_authors = list(set(group['authors']))  # Remove duplicates
                        content += f"  Authors: <AUTHORS>
                    content += f"  Details: {group['text']}\n"

                content += f"\n============================================================\n\n"

            # Write to file with UTF-8 encoding to properly handle all characters
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            logger.info(f"Generated queries file: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"Failed to generate queries file: {e}")
            return None


    def _count_queries_per_article(self, author_queries: List[Dict]) -> Dict[str, int]:
        """
        Count queries per article from author queries list

        Args:
            author_queries: List of author query dictionaries

        Returns:
            Dictionary mapping article_id -> query count
        """
        query_counts = {}
        for query in author_queries:
            article_id = query.get('article_id')
            if article_id:
                query_counts[article_id] = query_counts.get(article_id, 0) + 1
        return query_counts

    def _update_google_sheets_for_articles(self, article_ids: List[str], te_username: str) -> int:
        """
        Update Google Sheets tracker with TE assignment information for articles

        Args:
            article_ids: List of article IDs to update
            te_username: Username of the TE assigned

        Returns:
            Number of successfully updated rows
        """
        try:
            sheets_service = self._get_sheets_service()

            # Fetch article details from DB if available
            db_articles = self.ArticleFile.query.filter(
                self.ArticleFile.article_id.in_(article_ids)
            ).all()
            db_article_map = {a.article_id: a for a in db_articles}

            # Fetch system query counts (validation_error type only) for all articles
            from app import app, AuthorQuery
            system_query_counts = {}

            try:
                with app.app_context():
                    # Count validation_error queries for each article
                    validation_queries = AuthorQuery.query.filter(
                        AuthorQuery.article_id.in_(article_ids),
                        AuthorQuery.query_type == 'validation_error'
                    ).all()

                    # Group by article_id and count
                    for query in validation_queries:
                        article_id = query.article_id
                        system_query_counts[article_id] = system_query_counts.get(article_id, 0) + 1

                    logger.info(f"Found system queries for {len(system_query_counts)} articles")
            except Exception as e:
                logger.warning(f"Failed to fetch system query counts: {e}")
                # Continue without query counts

            sheet_success_count = 0
            for article_id in article_ids:
                # Use DB data if available, otherwise minimal defaults
                article = db_article_map.get(article_id)
                article_data = {
                    'article_id': article_id,
                    'journal_code': article.journal_code if article else 'Unknown',
                    'journal_name': article.journal_name if article else 'Unknown',
                    'created_at': article.created_at if article else datetime.utcnow()
                }

                # Get system query count for this article
                system_query_count = system_query_counts.get(article_id, 0)

                if sheets_service.add_te_assignment_row(article_data, te_username, system_query_count):
                    sheet_success_count += 1

            logger.info(f"Appended {sheet_success_count}/{len(article_ids)} rows to Google Sheet")
            return sheet_success_count

        except Exception as sheets_error:
            # Don't fail the assignment if Google Sheets update fails
            logger.warning(f"Failed to update Google Sheets (assignment still successful): {sheets_error}")
            return 0

    def _get_author_queries_for_articles(self, article_ids: List[str]) -> List[Dict]:
        """
        Fetch author queries for the given article IDs from database

        Args:
            article_ids: List of article IDs to fetch queries for

        Returns:
            List of author query dictionaries
        """
        try:
            # Import AuthorQuery model and app
            from app import app, AuthorQuery

            # Ensure we're in Flask app context
            with app.app_context():
                # Get queries for articles (exclude validation errors - they're not author-related)
                queries = AuthorQuery.query.filter(
                    AuthorQuery.article_id.in_(article_ids),
                    AuthorQuery.query_type != 'validation_error'
                ).all()

                if not queries:
                    return []

                # Convert to list of dictionaries for email service
                author_queries = [{
                    'article_id': query.article_id,
                    'author_name': query.author_name,
                    'query_type': query.query_type,
                    'query_text': query.query_text,
                    'raised_by': query.raised_by,
                    'raised_on': query.raised_on.isoformat() if query.raised_on else None,
                    'status': query.status
                } for query in queries]

                logger.info(f"Found {len(author_queries)} author queries for {len(article_ids)} articles")
                return author_queries

        except Exception as e:
            logger.error(f"Failed to fetch author queries for articles {article_ids}: {e}")
            return []

    def create_te_assignment_with_json_summary(self, zip_files: List, json_summary_file,
                                              te_id: int, assigned_by_id: int, notes: str = None) -> Dict:
        """
        Create TE assignment with JSON summary file (unified method for both ZIP and folder uploads)
        This method works with any JSON summary file containing article metadata

        Args:
            zip_files: List of ZIP file objects
            json_summary_file: JSON summary file object (batch_summary.json or individual summary)
            te_id: TE user ID
            assigned_by_id: ID of user making the assignment
            notes: Optional assignment notes

        Returns:
            Assignment result dictionary
        """
        try:
            # Get TE user
            te_user = self.User.query.get(te_id)
            if not te_user or te_user.role != 'TE':
                raise ValueError(f"Invalid TE user ID: {te_id}")

            # Get assigning user
            assigning_user = self.User.query.get(assigned_by_id)
            if not assigning_user:
                raise ValueError(f"Invalid assigning user ID: {assigned_by_id}")

            # Extract article IDs from ZIP files
            article_ids = [os.path.splitext(zip_file.filename)[0] for zip_file in zip_files]

            # Read JSON summary file directly
            json_summary_file.seek(0)  # Reset file pointer
            summary_data = json.load(json_summary_file)

            # Extract author information from JSON summary
            articles_with_authors = []
            all_articles = summary_data.get('articles', [])

            for article_id in article_ids:
                # Find matching article in JSON summary
                matching_article = next(
                    (article for article in all_articles if article.get('article_id') == article_id),
                    None
                )

                if matching_article:
                    article_info = {
                        'article_id': matching_article.get('article_id', article_id),
                        'journal': matching_article.get('journal', 'Unknown'),
                        'authors': matching_article.get('authors', []),
                        'status': matching_article.get('status', 'unknown')
                    }
                    articles_with_authors.append(article_info)
                    logger.info(f"Found JSON summary data for {article_id}: {len(article_info['authors'])} authors")
                else:
                    # Article not found in batch summary
                    article_info = {
                        'article_id': article_id,
                        'journal': 'Unknown',
                        'authors': [],
                        'status': 'not_in_batch'
                    }
                    articles_with_authors.append(article_info)
                    logger.warning(f"Article {article_id} not found in batch summary")

            # Generate batch name
            batch_name = f"batch-{datetime.now().strftime('%Y%m%d-%H%M%S')}-{len(article_ids)}"

            # Create assignment record
            assignment = self.TEAssignment(
                batch_name=batch_name,
                zip_ids=[],  # Required field - empty array for smart batching
                article_ids=article_ids,
                assigned_te_id=te_id,
                assigned_te_email=te_user.email,
                assigned_by=assigned_by_id,  # Correct field name is 'assigned_by'
                assignment_status='pending',
                notes=notes or ""
            )

            self.db.session.add(assignment)
            self.db.session.flush()  # Get the ID

            # Upload ZIP files directly to Google Drive
            drive_service = self._get_drive_service()
            folder_id, folder_link = drive_service.create_batch_folder(batch_name)

            uploaded_files = []
            for zip_file in zip_files:
                # Upload each ZIP file
                file_id, file_link = drive_service.upload_zip_file_from_memory(
                    zip_file, folder_id, zip_file.filename
                )
                uploaded_files.append({
                    'filename': zip_file.filename,
                    'file_id': file_id,
                    'file_link': file_link
                })
                logger.info(f"Uploaded ZIP file to Drive: {zip_file.filename}")

            # Update assignment with Drive info
            assignment.drive_folder_id = folder_id
            assignment.drive_folder_link = folder_link
            assignment.assignment_status = 'uploaded'
            assignment.uploaded_at = datetime.now()

            self.db.session.commit()

            # Fetch author queries for the assigned articles
            author_queries = self._get_author_queries_for_articles(article_ids)

            # Debug logging for author information
            logger.info(f"📊 Author data summary for batch {batch_name}:")
            logger.info(f"   - Total articles: {len(article_ids)}")
            logger.info(f"   - Articles with author data: {len([a for a in articles_with_authors if a.get('authors')])}")
            logger.info(f"   - Total authors: {sum(len(a.get('authors', [])) for a in articles_with_authors)}")
            for article in articles_with_authors:
                authors = article.get('authors', [])
                logger.info(f"   - {article.get('article_id')}: {len(authors)} authors")
                for author in authors[:2]:  # Log first 2 authors
                    logger.info(f"     * {author.get('name', 'Unknown')} - Copyright: {author.get('copyright_status', 'UNKNOWN')}")

            # Send email notification with author information from batch summary
            email_sent = self.send_assignment_email_direct(
                assignment_id=assignment.id,
                te_name=te_user.username,
                te_email=te_user.email,
                article_ids=article_ids,
                drive_link=folder_link,
                batch_name=batch_name,
                assigned_by_username=assigning_user.username,
                articles_with_authors=articles_with_authors,  # Rich author data from batch summary
                author_queries=author_queries
            )

            # Update article statuses to assigned_TE
            for article_id in article_ids:
                article = self.ArticleFile.query.filter_by(article_id=article_id).first()
                if article:
                    article.status = 'assigned_TE'
                    article.assigned_to = assignment.assigned_te_id
                    logger.info(f"Updated article {article_id} status to assigned_TE")
                else:
                    logger.warning(f"Article {article_id} not found in database for status update")

            # Mark assignment as completed
            assignment.assignment_status = 'completed'
            self.db.session.commit()

            # Update Google Sheets with TE assignment information
            self._update_google_sheets_for_articles(article_ids, te_user.username)

            logger.info(f"Created TE assignment from folder upload: {batch_name} for TE {te_user.username}")
            logger.info(f"Author information extracted for {len([a for a in articles_with_authors if a.get('authors')])} articles")
            logger.info(f"Updated {len(article_ids)} articles to assigned_TE status")

            return {
                'assignment_id': assignment.id,
                'batch_name': batch_name,
                'article_ids': article_ids,  # Frontend needs this for ZIP status updates
                'te_name': te_user.username,
                'te_email': te_user.email,
                'assigned_te_id': te_id,  # Frontend expects this field name
                'assigned_te_name': te_user.username,  # Frontend expects this field name
                'assigned_te_email': te_user.email,  # Frontend expects this field name
                'article_count': len(article_ids),
                'zip_count': len(zip_files),
                'author_data_available': len([a for a in articles_with_authors if a.get('authors')]),
                'total_authors': sum(len(a.get('authors', [])) for a in articles_with_authors),
                'email_sent': email_sent,
                'articles_updated': len(article_ids),
                'status': 'completed',  # Assignment is now completed
                'drive_folder_link': folder_link,
                'folder_link': folder_link  # Frontend also checks this field name
            }

        except Exception as e:
            self.db.session.rollback()
            logger.error(f"Failed to create TE assignment from folder upload: {e}")
            raise

    def complete_te_assignment(self, assignment_id: int) -> Dict:
        """
        Complete the full TE assignment workflow: upload to Drive + send email + update article status
        
        Args:
            assignment_id: TE assignment ID
            
        Returns:
            Complete assignment result
        """
        try:
            # Upload to Drive
            upload_result = self.upload_assignment_to_drive(assignment_id)
            
            # Send email notification
            email_success = self.send_assignment_email(assignment_id)
            
            # Update article statuses to assigned_TE
            assignment = self.TEAssignment.query.get(assignment_id)
            if not assignment:
                raise ValueError(f"Assignment with ID {assignment_id} not found")

            for article_id in assignment.article_ids:
                article = self.ArticleFile.query.filter_by(article_id=article_id).first()
                if article:
                    article.status = 'assigned_TE'
                    article.assigned_to = assignment.assigned_te_id

            # Mark assignment as completed
            assignment.assignment_status = 'completed'
            self.db.session.commit()


            # Update Google Sheets with TE assignment information (Append Rows)
            self._update_google_sheets_for_articles(assignment.article_ids, assignment.assigned_te.username)

            result = {
                **upload_result,
                'email_sent': email_success,
                'articles_updated': len(assignment.article_ids),
                'status': 'completed'
            }

            logger.info(f"Completed TE assignment: {assignment.batch_name}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to complete TE assignment: {e}")
            raise
    
    def get_assignment_history(self, limit: int = 50) -> List[Dict]:
        """
        Get recent TE assignment history
        
        Args:
            limit: Maximum number of assignments to return
            
        Returns:
            List of assignment dictionaries
        """
        try:
            assignments = self.TEAssignment.query.order_by(
                self.TEAssignment.created_at.desc()
            ).limit(limit).all()
            
            return [assignment.to_dict() for assignment in assignments]
            
        except Exception as e:
            logger.error(f"Failed to get assignment history: {e}")
            raise

    def create_smart_batches(self, article_ids: List[str], available_tes: List[Dict],
                           batch_size: Optional[int] = None, group_by_journal: bool = True,
                           articles_data: Optional[List[Dict]] = None) -> List[Dict]:
        """
        Create smart batches for TE assignment

        Args:
            article_ids: List of article IDs to batch
            available_tes: List of available TE dictionaries
            batch_size: Optional override for batch size
            group_by_journal: Whether to group articles by journal
            articles_data: Optional pre-provided article data (for ZIP files)

        Returns:
            List of batch dictionaries with suggested TE assignments
        """
        try:
            if not article_ids:
                return []

            if not available_tes:
                raise ValueError("No available TEs for assignment")

            # Get article details for grouping
            if articles_data:
                # Use provided article data (for ZIP files)
                articles = []
                for article_data in articles_data:
                    articles.append({
                        'article_id': article_data.get('article_id'),
                        'journal_code': article_data.get('journal_code', 'unknown'),
                        'journal_name': article_data.get('journal_name', 'Unknown Journal'),
                        'priority': article_data.get('priority', 'MEDIUM'),
                        'deadline': article_data.get('deadline'),
                        'zip_id': article_data.get('zip_id'),
                        'filename': article_data.get('filename')
                    })
            else:
                # Look up articles in database (for regular articles)
                articles = []
                for article_id in article_ids:
                    article = self.ArticleFile.query.filter_by(article_id=article_id).first()
                    if article:
                        articles.append({
                            'article_id': article_id,
                            'journal_code': article.journal_code or 'unknown',
                            'journal_name': article.journal_name or 'Unknown Journal',
                            'priority': article.priority or 'MEDIUM',
                            'deadline': article.deadline.isoformat() if article.deadline else None
                        })
                    else:
                        # Article not found in database, add with minimal info
                        articles.append({
                            'article_id': article_id,
                            'journal_code': 'unknown',
                            'journal_name': 'Unknown Journal',
                            'priority': 'MEDIUM',
                            'deadline': None
                        })

            # Group articles by journal if requested
            if group_by_journal:
                journal_groups = {}
                for article in articles:
                    journal_key = article['journal_code']
                    if journal_key not in journal_groups:
                        journal_groups[journal_key] = []
                    journal_groups[journal_key].append(article)

                # Flatten groups into ordered list (prioritize by group size)
                sorted_groups = sorted(journal_groups.items(), key=lambda x: len(x[1]), reverse=True)
                grouped_articles = []
                for _, group_articles in sorted_groups:
                    grouped_articles.extend(group_articles)
                articles = grouped_articles

            # Calculate batch size
            if batch_size is None:
                # Default: ceil(total_articles / total_TEs)
                batch_size = math.ceil(len(articles) / len(available_tes))
                batch_size = max(1, min(batch_size, 20))  # Ensure reasonable batch size (1-20)

            # Create batches
            batches = []
            for i in range(0, len(articles), batch_size):
                batch_articles = articles[i:i + batch_size]
                batch_number = len(batches) + 1

                # Assign default TE (cyclical assignment)
                default_te_index = (batch_number - 1) % len(available_tes)
                default_te = available_tes[default_te_index]

                # Get unique journal codes for this batch
                journal_codes = list(set(article['journal_code'] for article in batch_articles))

                batch = {
                    'batch_number': batch_number,
                    'articles': batch_articles,
                    'article_ids': [article['article_id'] for article in batch_articles],
                    'article_count': len(batch_articles),
                    'default_te_id': default_te['id'],
                    'default_te_name': default_te['username'],
                    'default_te_email': default_te['email'],
                    'journal_codes': journal_codes,
                    'status': 'pending'
                }

                batches.append(batch)

            logger.info(f"Created {len(batches)} smart batches for {len(articles)} articles")
            return batches

        except Exception as e:
            logger.error(f"Failed to create smart batches: {e}")
            raise

    def create_multi_te_assignment(self, batch_assignments: List[Dict], assigned_by_id: int,
                                 zip_file_mapping: Dict[str, str] = None) -> Dict:
        """
        Create multiple TE assignments with partial failure recovery

        Args:
            batch_assignments: List of batch assignment dictionaries
            assigned_by_id: ID of user making the assignments
            zip_file_mapping: Optional mapping of article_id -> zip_file_path for ZIP uploads

        Returns:
            Assignment results with success/failure details
        """
        try:
            results = {
                'successful': [],
                'failed': [],
                'partial': [],
                'total_batches': len(batch_assignments),
                'total_articles': sum(len(batch.get('article_ids', [])) for batch in batch_assignments),
                'summary': {
                    'successful_batches': 0,
                    'failed_batches': 0,
                    'successful_articles': 0,
                    'failed_articles': 0
                }
            }

            # Get assigning user
            assigning_user = self.User.query.get(assigned_by_id)
            if not assigning_user:
                raise ValueError(f"Invalid assigning user ID: {assigned_by_id}")

            # Process each batch assignment
            for batch_idx, batch_assignment in enumerate(batch_assignments):
                batch_number = batch_assignment.get('batch_number', batch_idx + 1)
                article_ids = batch_assignment.get('article_ids', [])
                te_id = batch_assignment.get('te_id')
                notes = batch_assignment.get('notes', f"Smart batch assignment #{batch_number}")

                logger.info(f"Processing batch {batch_number} with {len(article_ids)} articles for TE {te_id}")

                try:
                    # Validate TE
                    te_user = self.User.query.get(te_id)
                    if not te_user or te_user.role != 'TE':
                        raise ValueError(f"Invalid TE user ID: {te_id}")

                    # Find existing ZIP files on server for these articles
                    zip_file_paths = []
                    for article_id in article_ids:
                        # Look for ZIP file with this article ID
                        zip_file_path = self._find_local_zip_file(article_id)
                        if zip_file_path:
                            zip_file_paths.append(zip_file_path)
                        else:
                            logger.warning(f"No local ZIP file found for article ID: {article_id}")

                    # Create assignment for this batch
                    if zip_file_paths:
                        # Create assignment with actual ZIP files
                        assignment_result = self.create_te_assignment_with_zips(
                            zip_file_paths=zip_file_paths,
                            article_ids=article_ids,
                            te_id=te_id,
                            assigned_by_id=assigned_by_id,
                            notes=notes
                        )

                        # Upload ZIP files to Google Drive
                        upload_result = self.upload_assignment_to_drive(assignment_result['assignment_id'])

                        # Merge upload results
                        assignment_result.update({
                            'drive_folder_id': upload_result.get('folder_id'),
                            'drive_folder_link': upload_result.get('folder_link'),
                            'drive_file_id': upload_result.get('file_id'),
                            'drive_file_link': upload_result.get('file_link'),
                            'zip_files_uploaded': len(zip_file_paths),
                            'status': upload_result.get('status', 'uploaded')
                        })

                        logger.info(f"✅ Uploaded {len(zip_file_paths)} ZIP files for batch {batch_number}")
                    else:
                        # Regular assignment (creates sample ZIP from article data)
                        assignment_result = self.create_te_assignment(
                            article_ids=article_ids,
                            te_id=te_id,
                            assigned_by_id=assigned_by_id,
                            notes=notes
                        )

                    # Add batch-specific information
                    assignment_result.update({
                        'batch_number': batch_number,
                        'journal_codes': batch_assignment.get('journal_codes', []),
                        'processing_timestamp': datetime.utcnow().isoformat()
                    })

                    results['successful'].append(assignment_result)
                    results['summary']['successful_batches'] += 1
                    results['summary']['successful_articles'] += len(article_ids)

                    logger.info(f"✅ Successfully created batch {batch_number} assignment: {assignment_result['batch_name']}")

                except Exception as batch_error:
                    error_details = {
                        'batch_number': batch_number,
                        'article_ids': article_ids,
                        'te_id': te_id,
                        'error': str(batch_error),
                        'processing_timestamp': datetime.utcnow().isoformat()
                    }

                    results['failed'].append(error_details)
                    results['summary']['failed_batches'] += 1
                    results['summary']['failed_articles'] += len(article_ids)

                    logger.error(f"❌ Failed to create batch {batch_number} assignment: {batch_error}")

                    # Continue processing other batches (partial failure recovery)
                    continue

            # Log overall results
            logger.info(f"Multi-TE assignment completed: {results['summary']['successful_batches']}/{results['total_batches']} batches successful")

            return results

        except Exception as e:
            logger.error(f"Failed to create multi-TE assignment: {e}")
            raise

    def complete_multi_te_assignment(self, assignment_ids: List[int]) -> Dict:
        """
        Complete multiple TE assignments (upload to Drive + send emails)

        Args:
            assignment_ids: List of assignment IDs to complete

        Returns:
            Completion results with success/failure details
        """
        try:
            results = {
                'successful': [],
                'failed': [],
                'total_assignments': len(assignment_ids),
                'summary': {
                    'successful_assignments': 0,
                    'failed_assignments': 0,
                    'emails_sent': 0,
                    'drive_uploads': 0
                }
            }

            for assignment_id in assignment_ids:
                try:
                    # Complete individual assignment
                    completion_result = self.complete_te_assignment(assignment_id)

                    results['successful'].append({
                        'assignment_id': assignment_id,
                        'result': completion_result,
                        'processing_timestamp': datetime.utcnow().isoformat()
                    })

                    results['summary']['successful_assignments'] += 1
                    if completion_result.get('email_sent'):
                        results['summary']['emails_sent'] += 1
                    if completion_result.get('folder_link'):
                        results['summary']['drive_uploads'] += 1

                    logger.info(f"✅ Successfully completed assignment {assignment_id}")

                except Exception as completion_error:
                    error_details = {
                        'assignment_id': assignment_id,
                        'error': str(completion_error),
                        'processing_timestamp': datetime.utcnow().isoformat()
                    }

                    results['failed'].append(error_details)
                    results['summary']['failed_assignments'] += 1

                    logger.error(f"❌ Failed to complete assignment {assignment_id}: {completion_error}")

                    # Continue processing other assignments
                    continue

            logger.info(f"Multi-TE assignment completion: {results['summary']['successful_assignments']}/{results['total_assignments']} assignments completed")

            return results

        except Exception as e:
            logger.error(f"Failed to complete multi-TE assignments: {e}")
            raise

    def retry_failed_assignments(self, failed_batch_assignments: List[Dict], assigned_by_id: int) -> Dict:
        """
        Retry failed batch assignments

        Args:
            failed_batch_assignments: List of failed batch assignments to retry
            assigned_by_id: ID of user making the retry

        Returns:
            Retry results
        """
        try:
            logger.info(f"Retrying {len(failed_batch_assignments)} failed batch assignments")

            # Use the same multi-TE assignment logic for retries
            retry_results = self.create_multi_te_assignment(failed_batch_assignments, assigned_by_id)

            # Add retry-specific metadata
            retry_results['is_retry'] = True
            retry_results['retry_timestamp'] = datetime.utcnow().isoformat()

            return retry_results

        except Exception as e:
            logger.error(f"Failed to retry batch assignments: {e}")
            raise

    def get_batch_assignment_status(self, batch_assignment_ids: List[int]) -> Dict:
        """
        Get status of multiple batch assignments

        Args:
            batch_assignment_ids: List of assignment IDs to check

        Returns:
            Status information for each assignment
        """
        try:
            assignments = []

            for assignment_id in batch_assignment_ids:
                assignment = self.TEAssignment.query.get(assignment_id)
                if assignment:
                    assignments.append({
                        'assignment_id': assignment.id,
                        'batch_name': assignment.batch_name,
                        'status': assignment.assignment_status,
                        'te_name': assignment.assigned_te.username if assignment.assigned_te else 'Unknown',
                        'article_count': len(assignment.article_ids),
                        'created_at': assignment.created_at.isoformat(),
                        'uploaded_at': assignment.uploaded_at.isoformat() if assignment.uploaded_at else None,
                        'email_sent_at': assignment.email_sent_at.isoformat() if assignment.email_sent_at else None,
                        'drive_folder_link': assignment.drive_folder_link
                    })
                else:
                    assignments.append({
                        'assignment_id': assignment_id,
                        'error': 'Assignment not found'
                    })

            return {
                'assignments': assignments,
                'total_count': len(assignments),
                'status_summary': self._get_status_summary(assignments)
            }

        except Exception as e:
            logger.error(f"Failed to get batch assignment status: {e}")
            raise

    def _get_status_summary(self, assignments: List[Dict]) -> Dict:
        """Helper method to generate status summary"""
        summary = {
            'pending': 0,
            'uploading': 0,
            'uploaded': 0,
            'emailed': 0,
            'completed': 0,
            'failed': 0
        }

        for assignment in assignments:
            status = assignment.get('status', 'unknown')
            if status in summary:
                summary[status] += 1
            elif 'error' in assignment:
                summary['failed'] += 1

        return summary
