.multiselect-checkbox-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.5rem;
  max-width: 9xl;
}

.multiselect-checkbox-header {
  font-weight: 600;
  font-size: 0.875rem;
  color: #4b5563;
}

.multiselect-checkbox-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.multiselect-checkbox-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
  font-size: 0.875rem;
}

.multiselect-checkbox-option input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  margin: 0;
  cursor: pointer;
  opacity: 1 !important;
  position: static !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* Column width adjustments */
.column-layout-4 {
  display: grid;
  grid-template-columns: 10% 40% 40% 10%;
}

.column-layout-3 {
  display: grid;
  grid-template-columns: 20% 40% 40%;
}