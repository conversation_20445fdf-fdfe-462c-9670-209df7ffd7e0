# Architecture Decision: Frontend-First Rules Management

## Decision

**Editing rules are defined in the frontend, not the backend.**

## Context

Initially, the Word Plugin API had a `/api/word-plugin/rules` endpoint that returned rule definitions from the backend. However, this created duplication since the same rules were already defined in the frontend for the Edit Article page.

## Rationale

### 1. Single Source of Truth
- Rules are defined once in `src/constants/editingRules.js`
- No duplication between frontend and backend
- Changes to rules only need to be made in one place

### 2. Frontend-First Architecture
- Rules are UI/UX concerns, not business logic
- Rule definitions include display properties (name, description, category)
- Backend only needs to execute validation logic, not define rules

### 3. Consistency
- Edit Article page and Word Plugin use the same rules
- Same rule IDs, names, descriptions, and categories
- Guaranteed consistency across all interfaces

### 4. Easier Maintenance
- Adding/removing rules only requires frontend changes
- No need to keep backend and frontend in sync
- Reduces deployment complexity

### 5. Performance
- No API call needed to fetch rules
- Rules are bundled with the application
- Faster initial load time

## Implementation

### Frontend (Source of Truth)
```
src/constants/editingRules.js
├── RULE_TYPES
├── RULE_CATEGORIES
├── EDITING_RULES (18 rules)
└── getRulesByCategory()
```

### Word Plugin (Mirrors Frontend)
```
src/taskpane/constants/editingRules.js
├── RULE_TYPES
├── RULE_CATEGORIES
├── EDITING_RULES (18 rules)
└── getRulesByCategory()
```

### Backend (Execution Only)
```
routes/word_plugin_routes.py
├── POST /api/word-plugin/execute-rule
└── _execute_editing_rule(rule_id, content, xml_data)
```

## Rule Flow

1. **Word Plugin loads** → Rules are imported from local constants
2. **User selects rule** → Rule definition is already in memory
3. **User executes rule** → Content is sent to backend for validation
4. **Backend validates** → Returns issues/suggestions based on rule_id
5. **Results displayed** → Word Plugin shows results to user

## Benefits

✅ **No duplication** - Rules defined once  
✅ **Faster loading** - No API call for rules  
✅ **Easier updates** - Change rules in one place  
✅ **Type safety** - Frontend can use TypeScript for rules  
✅ **Offline capable** - Rules work without backend  
✅ **Consistent UX** - Same rules across all interfaces  

## Trade-offs

⚠️ **Rule updates require frontend deployment** - Can't change rules without redeploying frontend  
⚠️ **Duplication between web app and Word Plugin** - Rules are copied to taskpane/constants  
⚠️ **Backend must trust frontend** - Backend assumes rule_id is valid  

## Alternatives Considered

### Alternative 1: Backend-First (Rejected)
- Rules defined in backend
- Frontend fetches rules via API
- ❌ Requires API call on every load
- ❌ Rules are not UI concerns
- ❌ Harder to maintain consistency

### Alternative 2: Shared Package (Rejected)
- Rules in separate npm package
- Both frontend and backend import package
- ❌ Overkill for simple rule definitions
- ❌ Adds deployment complexity
- ❌ Backend doesn't need full rule definitions

### Alternative 3: Database-Driven (Rejected)
- Rules stored in database
- Admin UI to manage rules
- ❌ Too complex for current needs
- ❌ Rules rarely change
- ❌ Adds unnecessary abstraction

## Migration Path

If rules need to be dynamic in the future:

1. Keep frontend rules as default/fallback
2. Add optional API endpoint for custom rules
3. Merge custom rules with default rules in frontend
4. Backend validates against both default and custom rules

## Conclusion

Frontend-first rules management is the right choice for this application because:
- Rules are primarily UI/UX definitions
- Rules change infrequently
- Consistency across interfaces is critical
- Performance and simplicity are priorities

The backend's role is to execute validation logic, not to define what rules exist.

---

**Date**: 2026-01-04  
**Status**: Implemented  
**Reviewed by**: Development Team

