import React, { useState, useCallback } from 'react';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { useArticle } from '../../context/ArticleContext';
import { Icons } from '../common';
import './ZipUploader.css';

const ZipUploader = ({ onUploadComplete }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const { setArticleId, setZipFiles, setProcessing } = useArticle();

  const extractArticleId = (filename) => {
    // Remove .zip extension and use as article ID
    return filename.replace(/\.zip$/i, '');
  };

  const getFileIcon = (filename) => {
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
      case 'docx':
      case 'doc':
        return '📄';
      case 'pdf':
        return '📕';
      case 'txt':
        return '📝';
      case 'xlsx':
      case 'xls':
        return '📊';
      case 'pptx':
      case 'ppt':
        return '📊';
      default:
        return '📁';
    }
  };

  const processZipFile = useCallback(async (file) => {
    setIsProcessing(true);
    setError(null);
    setProcessing(true);

    try {
      const zip = new JSZip();
      const zipContent = await zip.loadAsync(file);
      
      // Extract article ID from filename
      const articleId = extractArticleId(file.name);
      setArticleId(articleId);

      // Process files in the ZIP
      const files = [];
      for (const [relativePath, zipEntry] of Object.entries(zipContent.files)) {
        if (!zipEntry.dir) {
          const fileData = {
            name: relativePath,
            path: relativePath,
            size: zipEntry._data ? zipEntry._data.uncompressedSize : 0,
            icon: getFileIcon(relativePath),
            zipEntry: zipEntry,
            type: relativePath.toLowerCase().split('.').pop()
          };
          files.push(fileData);
        }
      }

      // Sort files by type (docx first, then others)
      files.sort((a, b) => {
        if (a.type === 'docx' && b.type !== 'docx') return -1;
        if (a.type !== 'docx' && b.type === 'docx') return 1;
        return a.name.localeCompare(b.name);
      });

      setZipFiles(files);
      
      if (onUploadComplete) {
        onUploadComplete(articleId, files);
      }

    } catch (err) {
      console.error('Error processing ZIP file:', err);
      setError('Failed to process ZIP file. Please ensure it\'s a valid ZIP archive.');
    } finally {
      setIsProcessing(false);
      setProcessing(false);
    }
  }, [setArticleId, setZipFiles, setProcessing, onUploadComplete]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const zipFile = files.find(file => file.name.toLowerCase().endsWith('.zip'));
    
    if (zipFile) {
      processZipFile(zipFile);
    } else {
      setError('Please upload a ZIP file.');
    }
  }, [processZipFile]);

  const handleFileInput = useCallback((e) => {
    const file = e.target.files[0];
    if (file && file.name.toLowerCase().endsWith('.zip')) {
      processZipFile(file);
    } else {
      setError('Please select a ZIP file.');
    }
  }, [processZipFile]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  return (
    <div className="zip-uploader-container">
      <div className="zip-uploader-header">
        <h2 className="zip-uploader-title">
          <span className="zip-icon">📦</span>
          Upload Article Archive
        </h2>
        <p className="zip-uploader-description">
          Upload a ZIP file containing your manuscript and related documents
        </p>
      </div>

      <div
        className={`zip-drop-zone ${isDragOver ? 'drag-over' : ''} ${isProcessing ? 'processing' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {isProcessing ? (
          <div className="zip-processing">
            <div className="zip-spinner"></div>
            <p>Processing ZIP file...</p>
          </div>
        ) : (
          <>
            <div className="zip-upload-icon">
              <Icons.UploadIcon />
            </div>
            <div className="zip-upload-text">
              <p className="zip-main-text">
                Drop your ZIP file here, or{' '}
                <label className="zip-browse-link">
                  browse
                  <input
                    type="file"
                    accept=".zip"
                    onChange={handleFileInput}
                    className="zip-file-input"
                  />
                </label>
              </p>
              <p className="zip-sub-text">
                Supports ZIP archives containing .docx, .pdf, .txt files
              </p>
            </div>
          </>
        )}
      </div>

      {error && (
        <div className="zip-error">
          <Icons.ExclamationIcon />
          <span>{error}</span>
        </div>
      )}

      <div className="zip-info">
        <div className="zip-info-item">
          <span className="zip-info-icon">💡</span>
          <span>Article ID will be extracted from the ZIP filename</span>
        </div>
        <div className="zip-info-item">
          <span className="zip-info-icon">📋</span>
          <span>Supported formats: .docx, .pdf, .txt files</span>
        </div>
      </div>
    </div>
  );
};

export default ZipUploader;
