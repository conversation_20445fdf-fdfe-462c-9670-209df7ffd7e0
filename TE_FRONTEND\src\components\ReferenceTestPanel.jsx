import React, { useState } from "react";
import { getCurrentRole } from "../utils/appUtils";

const API_BASE = process.env.REACT_APP_API_URL ? `${process.env.REACT_APP_API_URL}/api/references` : "http://localhost:4999/api/references";

function isAdminMode() {
  return getCurrentRole() === 'admin';
}

export default function ReferenceTestPanel() {
  const [articleId, setArticleId] = useState("");
  const [referencesText, setReferencesText] = useState([
    {
      term: "Smith J. 2020...",
      finalStr: "<PERSON>, et al. 2020...",
      type: "FOUND",
      ind: 0,
      score: 92,
      MarkType: null,
      incorrect: [],
    },
  ]);
  const [fetchedRefs, setFetchedRefs] = useState(null);
  const [status, setStatus] = useState("");

  const handleFetch = async () => {
    setStatus("Fetching...");
    setFetchedRefs(null);
    try {
      const res = await fetch(`${API_BASE}/${encodeURIComponent(articleId)}`);
      if (!res.ok) throw new Error("Not found");
      const data = await res.json();
      setFetchedRefs(data.references);
      setStatus("Fetched successfully");
    } catch (e) {
      setStatus("Fetch failed: " + e.message);
    }
  };

  const handleSave = async (method = "POST") => {
    setStatus("Saving...");
    setFetchedRefs(null);
    let refs;
    try {
      refs = JSON.parse(referencesText);
      if (!Array.isArray(refs)) throw new Error("References must be an array");
    } catch (e) {
      setStatus("Invalid JSON: " + e.message);
      return;
    }
    try {
      const res = await fetch(
        method === "POST"
          ? API_BASE
          : `${API_BASE}/${encodeURIComponent(articleId)}`,
        {
          method,
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ articleId, references: refs }),
        }
      );
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || "Save failed");
      setStatus(
        method === "POST" ? "Saved successfully" : "Updated successfully"
      );
    } catch (e) {
      setStatus("Save failed: " + e.message);
    }
  };

  if (!isAdminMode()) return null;

  return (
    <div
      style={{
        border: "2px solid #0074D9",
        padding: 24,
        margin: 24,
        borderRadius: 8,
      }}
    >
      <h2>Admin Mode: Reference API Panel</h2>
      <div style={{ marginBottom: 12 }}>
        <label>
          Article ID:
          <input
            type="text"
            value={articleId}
            onChange={(e) => setArticleId(e.target.value)}
            style={{ marginLeft: 8, width: 200 }}
            placeholder="PMC123456"
          />
        </label>
      </div>
      <div style={{ marginBottom: 12 }}>
        <label>
          References (JSON array):
          <textarea
            rows={8}
            cols={60}
            value={referencesText}
            onChange={(e) => setReferencesText(e.target.value)}
            style={{ display: "block", marginTop: 4 }}
          />
        </label>
      </div>
      <div style={{ marginBottom: 12 }}>
        <button onClick={() => handleSave("POST")} style={{ marginRight: 8 }}>
          Save (POST)
        </button>
        <button onClick={handleFetch} style={{ marginRight: 8 }}>
          Fetch (GET)
        </button>
        <button onClick={() => handleSave("PUT")} style={{ marginRight: 8 }}>
          Update (PUT)
        </button>
      </div>
      {status && (
        <div
          style={{
            marginBottom: 12,
            color: status.includes("fail") ? "red" : "green",
          }}
        >
          {status}
        </div>
      )}
      {fetchedRefs && (
        <div>
          <h4>Fetched References:</h4>
          <pre style={{ background: "#f4f4f4", padding: 12, borderRadius: 4 }}>
            {JSON.stringify(fetchedRefs, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
