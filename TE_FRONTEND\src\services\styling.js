// utils/referenceFormatter.js
import { uploadToDriveUrl } from "../constants/urls";

export const capitalizeAfterColon = (text) => {
  // Handle both string and object inputs
  const textString = typeof text === 'string' ? text : (text?.term || '');
  if (!textString) return '';

  return textString.replace(/:\s*(.*?)(?=\.|$)/g, (match, textAfterColon) => {
    const words = textAfterColon.split(' ');
    // Capitalize only the first word, keep others unchanged
    words[0] = words[0].charAt(0).toUpperCase() + words[0].slice(1).toLowerCase();
    return `: ${words.join(' ')}`;
  });
}

function compareStringsAndStop(s1, s2) {
  // If second number is shorter, return full second number
  if (s2.length < s1.length) {
    return s2;
  }

  // Find where numbers start to differ
  let i = 0;
  while (i < s1.length && s1[i] === s2[i]) {
    i++;
  }

  // Return the differing part
  return s2.slice(i);
}

export const abbreviatePageNumbers = (pages) => {
  if (!pages) return pages;

  // Handle prefixed page numbers (e.g., S613-S618)
  const prefixMatch = pages.match(/^([A-Za-z]*)(\d+)-([A-Za-z]*)(\d+)/);
  if (prefixMatch) {
    const [_, prefix1, start, prefix2, end] = prefixMatch;
    // Convert to numbers for comparison
    const startNum = parseInt(start);
    const endNum = parseInt(end);

    // If start and end are the same number, return just one number
    if (startNum === endNum) {
      return prefix1 + start;
    }

    const commonPrefix = compareStringsAndStop(start, end);
    return `${prefix1}${start}-${commonPrefix}`;
  }

  // Handle regular page numbers (e.g., 123-128)
  const regularMatch = pages.match(/^(\d+)-(\d+)/);
  if (regularMatch) {
    const [start, end] = pages.split("-").map(p => p.trim());
    const startNum = parseInt(start);
    const endNum = parseInt(end);

    if (startNum === endNum) {
      return start;
    }

    const commonPrefix = compareStringsAndStop(start, end);
    return `${start}-${commonPrefix}`;
  }

  return pages;
};

export const removeIssueNumbers = (volumeIssuePages) => {
  return volumeIssuePages?.replace(/\((\d+)\)/, "");
};

export const validateAuthorNames = (authors) => {
  const authorList = authors?.split(",").map((name) => name?.trim());
  if (authorList.length > 6) {
    return `${authorList?.slice(0, 6).join(", ")}, et al.`;
  }
  return authors;
};

// export const formatJournalReference = (ref) => {
//   const formattedPages = abbreviatePageNumbers(ref.volumeIssuePages || "");
//   const noIssue = removeIssueNumbers(formattedPages);
//   const formattedAuthors = validateAuthorNames(ref.authors);

//   return `${formattedAuthors}. ${ref.title}. ${ref.source} ${
//     ref.sortPubDate.split("/")[0]
//   };${noIssue}`;
// };

export const formatBookReference = (ref) => {
  const formattedAuthors = validateAuthorNames(ref.authors);
  return `${formattedAuthors}. ${ref.title}. ${ref.source}; ${
    ref.sortPubDate.split("/")[0]
  }. p. ${abbreviatePageNumbers(ref.volumeIssuePages || "")}`;
};

export const handleMissingCitation = (ref) => {
  if (!ref.cited) {
    return `Reference no. ${ref.ind} has not been cited. Kindly cite.`;
  }
  return "";
};

export const handleDuplicateReferences = (references) => {
  const uniqueRefs = [];
  const refMap = new Map();

  references.forEach((ref) => {
    const key = `${ref.authors}-${ref.title}-${ref.source}`;
    if (!refMap.has(key)) {
      refMap.set(key, ref);
      uniqueRefs.push(ref);
    }
  });

  return uniqueRefs;
};

// main styling function
// export const styleReference = (ref, references) => {
//   const formattedRefs = [];

//   // Handle duplicates
//   const uniqueReferences = handleDuplicateReferences(references);

//   uniqueReferences.forEach((uniqueRef) => {
//     let formattedRef = "";
//     switch (uniqueRef.type) {
//       case "Journal":
//         formattedRef = formatJournalReference(uniqueRef);
//         break;
//       case "Book":
//         formattedRef = formatBookReference(uniqueRef);
//         break;
//       // Add more types as needed
//       default:
//         formattedRef = `${uniqueRef.title}. Reference type not recognized.`;
//     }

//     // Handle missing citation
//     const missingCitationComment = handleMissingCitation(uniqueRef);
//     if (missingCitationComment) {
//       formattedRef += ` // ${missingCitationComment}`;
//     }

//     formattedRefs.push(formattedRef);
//   });

//   return formattedRefs;
// };

export function delayedFetch(url, options = {}, delay = 1) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      fetch(url, options).then(resolve).catch(reject);
    }, delay);
  });
}

export function customEndsWith(str, searchString) {
  if (str?.length < searchString.length) return false;
  return str?.slice(-searchString.length) === searchString;
}

export const formatText = (text) => {
  if (!text) return '';

  // Don't add period if text already ends with question mark, exclamation mark, or period
  if (customEndsWith(text, '.') || customEndsWith(text, '?') || customEndsWith(text, '!')) {
    return text;
  }

  return `${text}.`;
};

// Clean up text for search (fix spacing issues)
export const cleanTextForSearch = (text) => {
  if (!text) return '';

  return text
    .trim()
    // Fix hyphen spacing issues: "Type- A" -> "Type-A"
    .replace(/(\w+)-\s+(\w+)/g, '$1-$2')
    // Fix multiple spaces
    .replace(/\s+/g, ' ')
    // Remove extra punctuation that might interfere with search
    .replace(/\s*\.\s*$/, ''); // Remove trailing period for search
};

// Convert text to proper sentence case with lowercase prepositions and articles
export const toSentenceCase = (text) => {
  if (!text) return '';

  // List of words that should remain lowercase (prepositions, articles, conjunctions)
  const lowercaseWords = new Set([
    'a', 'an', 'and', 'as', 'at', 'but', 'by', 'for', 'if', 'in', 'into', 'is',
    'it', 'near', 'nor', 'of', 'off', 'on', 'once', 'onto', 'or', 'over', 'past',
    'so', 'than', 'that', 'the', 'to', 'up', 'upon', 'with', 'yet'
  ]);

  return text
    .toLowerCase()
    .split(' ')
    .map((word, index) => {
      // Always capitalize the first word
      if (index === 0) {
        return word.charAt(0).toUpperCase() + word.slice(1);
      }

      // Keep prepositions and articles lowercase unless they're at the beginning
      if (lowercaseWords.has(word.toLowerCase())) {
        return word.toLowerCase();
      }

      // Capitalize other words
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(' ');
};

export function findLastPage(str) {
  const subStr = str?.slice(-15);
  if (subStr.includes("-")) {
    return str?.slice(-15).split("-")[1];
  }
  return "";
}

export function uploadToDrive(obj) {
  return fetch(uploadToDriveUrl, {
    method: "POST", // HTTP method
    headers: {
      "Content-Type": "application/json", // Specify JSON content type
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({ obj }), // Convert JavaScript object to JSON string
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.json(); // Parse the JSON response
    })
    .catch((error) => {
      // Handle error during fetch
      return { error: error.message };
    });
}
