{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import mammoth from'mammoth';import{useArticle}from'../../context/ArticleContext';import{useRole}from'../../context/AuthContext';import{Icons}from'../common';import ArticleMetadataPanel from'./ArticleMetadataPanel';import RaiseQueryButton from'./RaiseQueryButton';import'./DocumentPreview.css';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const DocumentPreview=_ref=>{let{file,onBack,onCopyReferences,zipId,zipFile,authors,articleId,onZipModified,onValidationQuerySent}=_ref;const[content,setContent]=useState('');const[isLoading,setIsLoading]=useState(false);const[error,setError]=useState(null);const[selectedText,setSelectedText]=useState('');const[showAuthorsSidebar,setShowAuthorsSidebar]=useState(true);const[showScrollToTop,setShowScrollToTop]=useState(false);const navigate=useNavigate();const{setExtractedContent,articleData}=useArticle();const{isAdmin}=useRole();useEffect(()=>{if(file){loadFileContent();}},[file]);// Handle scroll events to show/hide scroll-to-top button\nuseEffect(()=>{const handleScroll=()=>{const scrollTop=window.pageYOffset||document.documentElement.scrollTop;setShowScrollToTop(scrollTop>300);// Show button after scrolling 300px\n};window.addEventListener('scroll',handleScroll);return()=>window.removeEventListener('scroll',handleScroll);},[]);const loadFileContent=async()=>{setIsLoading(true);setError(null);try{console.log('Loading file content for:',file.name);console.log('File object:',file);console.log('ZipEntry exists:',!!file.zipEntry);console.log('ZipEntry type:',typeof file.zipEntry);if(!file.zipEntry){throw new Error('No zipEntry found in file object');}const arrayBuffer=await file.zipEntry.async('arraybuffer');console.log('ArrayBuffer loaded, size:',arrayBuffer.byteLength);if(file.type==='docx'||file.type==='doc'){try{const result=await mammoth.convertToHtml({arrayBuffer});setContent(result.value);setExtractedContent(result.value);// Log any warnings from mammoth\nif(result.messages&&result.messages.length>0){console.warn('Mammoth conversion warnings:',result.messages);}}catch(docError){console.error('Error converting document:',docError);// Fallback: try to extract as plain text\ntry{const text=new TextDecoder('utf-8',{ignoreBOM:true}).decode(arrayBuffer);const cleanText=text.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]/g,'').trim();if(cleanText.length>100){setContent(\"<pre>\".concat(cleanText,\"</pre>\"));setExtractedContent(cleanText);console.log('Fallback: Extracted as plain text');}else{throw new Error('Unable to extract meaningful content');}}catch(fallbackError){throw new Error(\"Cannot process \".concat(file.type.toUpperCase(),\" file. Please try converting to .docx format first.\"));}}}else if(file.type==='txt'){const text=new TextDecoder().decode(arrayBuffer);setContent(\"<pre>\".concat(text,\"</pre>\"));setExtractedContent(text);}else{setError(\"Unsupported file type: \".concat(file.type.toUpperCase(),\". Supported formats: .docx, .doc, .txt\"));}}catch(err){console.error('Error loading file content:',err);console.error('Error details:',err.message);console.error('File object at error:',file);let errorMessage='Failed to load file content';if(err.message.includes('zipEntry')){errorMessage='File data is not properly loaded. Please try going back and selecting the file again.';}else if(err.message.includes('async')){errorMessage='Unable to extract file from ZIP archive. The file may be corrupted.';}setError(errorMessage);}finally{setIsLoading(false);}};const handleTextSelection=()=>{const selection=window.getSelection();const text=selection.toString().trim();setSelectedText(text);};const processSelectedText=()=>{if(selectedText){// Check multiple indicators that this is an admin session\nconst hasAdminParam=window.location.search.includes('admin=true');const hasAdminPath=window.location.pathname.includes('/admin');const hasAdminSession=sessionStorage.getItem('adminContext')==='true';const referrerIsAdmin=document.referrer.includes('/admin');// Use admin route if any admin indicator is present OR if we're clearly in admin workflow\nconst useAdminRoute=isAdmin||hasAdminParam||hasAdminPath||hasAdminSession||referrerIsAdmin||window.location.href.includes('admin')||// Current URL has admin\ndocument.referrer.includes('admin');// Came from admin page\nconst processRoute=useAdminRoute?'/admin/process':'/process';navigate(processRoute,{state:{references:selectedText,articleId:articleData===null||articleData===void 0?void 0:articleData.articleId,fromZipProcessor:true,manualEntry:false,isAdminContext:useAdminRoute,// Pass admin context explicitly\nzipId:zipId// Pass the zipId for auto-completion\n}});}};const scrollToTop=()=>{window.scrollTo({top:0,behavior:'smooth'});};return/*#__PURE__*/_jsxs(\"div\",{className:\"document-preview-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"preview-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"preview-title-section\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"back-button\",children:/*#__PURE__*/_jsx(Icons.ChevronLeftIcon,{})}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-file-info\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"preview-title\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"file-icon\",children:file.icon}),file.name]}),/*#__PURE__*/_jsxs(\"p\",{className:\"preview-subtitle\",children:[file.type.toUpperCase(),\" \\u2022 Document Preview\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-actions\",children:[/*#__PURE__*/_jsx(RaiseQueryButton,{queryType:\"general\",articleId:articleId,zipFile:zipFile,buttonText:\"Raise Query\",buttonIcon:\"\\uD83D\\uDCE7\",variant:\"primary\",size:\"small\",onQuerySent:id=>{console.log('Query sent for article:',id);// Mark ZIP with validationQuerySent flag for Leena assignment\nif(onValidationQuerySent){onValidationQuerySent(id);}}}),authors&&authors.length>0&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowAuthorsSidebar(!showAuthorsSidebar),className:\"toggle-sidebar-button\",title:showAuthorsSidebar?\"Hide Authors\":\"Show Authors\",children:/*#__PURE__*/_jsx(Icons.SettingsIcon,{})}),selectedText&&/*#__PURE__*/_jsxs(\"button\",{onClick:processSelectedText,className:\"process-button selected\",children:[/*#__PURE__*/_jsx(Icons.ArrowRightIcon,{}),\"Process Selected\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-with-sidebar \".concat(!showAuthorsSidebar?'sidebar-hidden':''),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"preview-content-wrapper\",children:[isLoading&&/*#__PURE__*/_jsxs(\"div\",{className:\"preview-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading document...\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"preview-error\",children:[/*#__PURE__*/_jsx(Icons.ExclamationIcon,{}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Error Loading Document\"}),/*#__PURE__*/_jsx(\"p\",{children:error}),error.includes('.DOC')&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-suggestion\",children:[\"\\uD83D\\uDCA1 \",/*#__PURE__*/_jsx(\"strong\",{children:\"Tip:\"}),\" For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.\"]})]})]}),!isLoading&&!error&&content&&/*#__PURE__*/_jsx(\"div\",{className:\"preview-content-container\",children:/*#__PURE__*/_jsx(\"div\",{className:\"preview-content\",dangerouslySetInnerHTML:{__html:content},onMouseUp:handleTextSelection,onKeyUp:handleTextSelection})}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-instructions\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"instruction-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"instruction-icon\",children:\"\\uD83D\\uDDB1\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Select text to process specific sections\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"instruction-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"instruction-icon\",children:\"\\uD83D\\uDD04\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Processing will take you directly to the reference processing screen\"})]})]}),selectedText&&/*#__PURE__*/_jsxs(\"div\",{className:\"selection-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"selection-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"selection-icon\",children:\"\\u2702\\uFE0F\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Selected Text (\",selectedText.length,\" characters)\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"selection-preview\",children:[selectedText.substring(0,200),selectedText.length>200&&'...']})]})]}),showAuthorsSidebar&&/*#__PURE__*/_jsx(\"div\",{className:\"authors-sidebar\",children:authors&&authors.length>0&&/*#__PURE__*/_jsx(ArticleMetadataPanel,{articleId:articleId,authors:authors,skipApiCall:true,alwaysExpanded:true,zipFiles:(articleData===null||articleData===void 0?void 0:articleData.zipFiles)||[],onZipModified:onZipModified,onQueryCreated:()=>{console.log('Query created for article:',articleId);}})}),showScrollToTop&&/*#__PURE__*/_jsx(\"button\",{onClick:scrollToTop,className:\"scroll-to-top-button\",title:\"Scroll to top\",\"aria-label\":\"Scroll to top\",children:/*#__PURE__*/_jsx(Icons.ChevronUpIcon,{})})]})]});};export default DocumentPreview;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "mammoth", "useArticle", "useRole", "Icons", "ArticleMetadataPanel", "RaiseQuery<PERSON><PERSON>on", "jsx", "_jsx", "jsxs", "_jsxs", "DocumentPreview", "_ref", "file", "onBack", "onCopyReferences", "zipId", "zipFile", "authors", "articleId", "onZipModified", "onValidationQuerySent", "content", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "setIsLoading", "error", "setError", "selectedText", "setSelectedText", "showAuthorsSidebar", "setShowAuthorsSidebar", "showScrollToTop", "setShowScrollToTop", "navigate", "setExtractedContent", "articleData", "isAdmin", "loadFileContent", "handleScroll", "scrollTop", "window", "pageYOffset", "document", "documentElement", "addEventListener", "removeEventListener", "console", "log", "name", "zipEntry", "Error", "arrayBuffer", "async", "byteLength", "type", "result", "convertToHtml", "value", "messages", "length", "warn", "doc<PERSON><PERSON><PERSON>", "text", "TextDecoder", "ignoreBOM", "decode", "cleanText", "replace", "trim", "concat", "fallback<PERSON><PERSON>r", "toUpperCase", "err", "message", "errorMessage", "includes", "handleTextSelection", "selection", "getSelection", "toString", "processSelectedText", "hasAdminParam", "location", "search", "has<PERSON>d<PERSON><PERSON><PERSON>", "pathname", "hasAdminSession", "sessionStorage", "getItem", "referrerIsAdmin", "referrer", "useAdminRoute", "href", "processRoute", "state", "references", "fromZipProcessor", "manualEntry", "isAdminContext", "scrollToTop", "scrollTo", "top", "behavior", "className", "children", "onClick", "ChevronLeftIcon", "icon", "queryType", "buttonText", "buttonIcon", "variant", "size", "onQuerySent", "id", "title", "SettingsIcon", "ArrowRightIcon", "ExclamationIcon", "dangerouslySetInnerHTML", "__html", "onMouseUp", "onKeyUp", "substring", "skipApiCall", "alwaysExpanded", "zipFiles", "onQ<PERSON>yCreated", "ChevronUpIcon"], "sources": ["C:/Users/<USER>/Documents/Work/MAIN_TE/TE_FRONTEND/src/components/zip/DocumentPreview.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport mammoth from 'mammoth';\nimport { useArticle } from '../../context/ArticleContext';\nimport { useRole } from '../../context/AuthContext';\nimport { Icons } from '../common';\nimport ArticleMetadataPanel from './ArticleMetadataPanel';\nimport RaiseQueryButton from './RaiseQueryButton';\nimport './DocumentPreview.css';\n\nconst DocumentPreview = ({ file, onBack, onCopyReferences, zipId, zipFile, authors, articleId, onZipModified, onValidationQuerySent }) => {\n  const [content, setContent] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAuthorsSidebar, setShowAuthorsSidebar] = useState(true);\n  const [showScrollToTop, setShowScrollToTop] = useState(false);\n  const navigate = useNavigate();\n  const { setExtractedContent, articleData } = useArticle();\n  const { isAdmin } = useRole();\n\n  useEffect(() => {\n    if (file) {\n      loadFileContent();\n    }\n  }, [file]);\n\n  // Handle scroll events to show/hide scroll-to-top button\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      setShowScrollToTop(scrollTop > 300); // Show button after scrolling 300px\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const loadFileContent = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('Loading file content for:', file.name);\n      console.log('File object:', file);\n      console.log('ZipEntry exists:', !!file.zipEntry);\n      console.log('ZipEntry type:', typeof file.zipEntry);\n\n      if (!file.zipEntry) {\n        throw new Error('No zipEntry found in file object');\n      }\n\n      const arrayBuffer = await file.zipEntry.async('arraybuffer');\n      console.log('ArrayBuffer loaded, size:', arrayBuffer.byteLength);\n      \n      if (file.type === 'docx' || file.type === 'doc') {\n        try {\n          const result = await mammoth.convertToHtml({ arrayBuffer });\n          setContent(result.value);\n          setExtractedContent(result.value);\n\n          // Log any warnings from mammoth\n          if (result.messages && result.messages.length > 0) {\n            console.warn('Mammoth conversion warnings:', result.messages);\n          }\n        } catch (docError) {\n          console.error('Error converting document:', docError);\n          // Fallback: try to extract as plain text\n          try {\n            const text = new TextDecoder('utf-8', { ignoreBOM: true }).decode(arrayBuffer);\n            const cleanText = text.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]/g, '').trim();\n            if (cleanText.length > 100) {\n              setContent(`<pre>${cleanText}</pre>`);\n              setExtractedContent(cleanText);\n              console.log('Fallback: Extracted as plain text');\n            } else {\n              throw new Error('Unable to extract meaningful content');\n            }\n          } catch (fallbackError) {\n            throw new Error(`Cannot process ${file.type.toUpperCase()} file. Please try converting to .docx format first.`);\n          }\n        }\n      } else if (file.type === 'txt') {\n        const text = new TextDecoder().decode(arrayBuffer);\n        setContent(`<pre>${text}</pre>`);\n        setExtractedContent(text);\n      } else {\n        setError(`Unsupported file type: ${file.type.toUpperCase()}. Supported formats: .docx, .doc, .txt`);\n      }\n    } catch (err) {\n      console.error('Error loading file content:', err);\n      console.error('Error details:', err.message);\n      console.error('File object at error:', file);\n\n      let errorMessage = 'Failed to load file content';\n      if (err.message.includes('zipEntry')) {\n        errorMessage = 'File data is not properly loaded. Please try going back and selecting the file again.';\n      } else if (err.message.includes('async')) {\n        errorMessage = 'Unable to extract file from ZIP archive. The file may be corrupted.';\n      }\n\n      setError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    setSelectedText(text);\n  };\n\n  const processSelectedText = () => {\n    if (selectedText) {\n      // Check multiple indicators that this is an admin session\n      const hasAdminParam = window.location.search.includes('admin=true');\n      const hasAdminPath = window.location.pathname.includes('/admin');\n      const hasAdminSession = sessionStorage.getItem('adminContext') === 'true';\n      const referrerIsAdmin = document.referrer.includes('/admin');\n\n      // Use admin route if any admin indicator is present OR if we're clearly in admin workflow\n      const useAdminRoute = isAdmin || hasAdminParam || hasAdminPath || hasAdminSession || referrerIsAdmin ||\n                           window.location.href.includes('admin') || // Current URL has admin\n                           document.referrer.includes('admin');      // Came from admin page\n\n      const processRoute = useAdminRoute ? '/admin/process' : '/process';\n\n      navigate(processRoute, {\n        state: {\n          references: selectedText,\n          articleId: articleData?.articleId,\n          fromZipProcessor: true,\n          manualEntry: false,\n          isAdminContext: useAdminRoute, // Pass admin context explicitly\n          zipId: zipId // Pass the zipId for auto-completion\n        }\n      });\n    }\n  };\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <div className=\"document-preview-container\">\n      {/* Header */}\n      <div className=\"preview-header\">\n        <div className=\"preview-title-section\">\n          <button onClick={onBack} className=\"back-button\">\n            <Icons.ChevronLeftIcon />\n          </button>\n          <div className=\"preview-file-info\">\n            <h2 className=\"preview-title\">\n              <span className=\"file-icon\">{file.icon}</span>\n              {file.name}\n            </h2>\n            <p className=\"preview-subtitle\">\n              {file.type.toUpperCase()} • Document Preview\n            </p>\n          </div>\n        </div>\n\n        <div className=\"preview-actions\">\n          {/* Raise Query Button */}\n          <RaiseQueryButton\n            queryType=\"general\"\n            articleId={articleId}\n            zipFile={zipFile}\n            buttonText=\"Raise Query\"\n            buttonIcon=\"📧\"\n            variant=\"primary\"\n            size=\"small\"\n            onQuerySent={(id) => {\n              console.log('Query sent for article:', id);\n              // Mark ZIP with validationQuerySent flag for Leena assignment\n              if (onValidationQuerySent) {\n                onValidationQuerySent(id);\n              }\n            }}\n          />\n\n          {authors && authors.length > 0 && (\n            <button\n              onClick={() => setShowAuthorsSidebar(!showAuthorsSidebar)}\n              className=\"toggle-sidebar-button\"\n              title={showAuthorsSidebar ? \"Hide Authors\" : \"Show Authors\"}\n            >\n              <Icons.SettingsIcon />\n            </button>\n          )}\n\n\n          {selectedText && (\n            <button onClick={processSelectedText} className=\"process-button selected\">\n              <Icons.ArrowRightIcon />\n              Process Selected\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Main Content with Sidebar Layout */}\n      <div className={`preview-with-sidebar ${!showAuthorsSidebar ? 'sidebar-hidden' : ''}`}>\n        {/* Left Side - Document Content */}\n        <div className=\"preview-content-wrapper\">\n        {isLoading && (\n          <div className=\"preview-loading\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading document...</p>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"preview-error\">\n            <Icons.ExclamationIcon />\n            <div>\n              <h3>Error Loading Document</h3>\n              <p>{error}</p>\n              {error.includes('.DOC') && (\n                <div className=\"error-suggestion\">\n                  💡 <strong>Tip:</strong> For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {!isLoading && !error && content && (\n          <div className=\"preview-content-container\">\n            <div\n              className=\"preview-content\"\n              dangerouslySetInnerHTML={{ __html: content }}\n              onMouseUp={handleTextSelection}\n              onKeyUp={handleTextSelection}\n            />\n          </div>\n        )}\n\n        {/* Instructions */}\n        <div className=\"preview-instructions\">\n          <div className=\"instruction-item\">\n            <span className=\"instruction-icon\">🖱️</span>\n            <span>Select text to process specific sections</span>\n          </div>\n          <div className=\"instruction-item\">\n            <span className=\"instruction-icon\">🔄</span>\n            <span>Processing will take you directly to the reference processing screen</span>\n          </div>\n        </div>\n\n        {/* Selection Info */}\n        {selectedText && (\n          <div className=\"selection-info\">\n            <div className=\"selection-header\">\n              <span className=\"selection-icon\">✂️</span>\n              <span>Selected Text ({selectedText.length} characters)</span>\n            </div>\n            <div className=\"selection-preview\">\n              {selectedText.substring(0, 200)}\n              {selectedText.length > 200 && '...'}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Right Side - System Authors Sidebar */}\n      {showAuthorsSidebar && (\n        <div className=\"authors-sidebar\">\n          {authors && authors.length > 0 && (\n            <ArticleMetadataPanel\n              articleId={articleId}\n              authors={authors}\n              skipApiCall={true}\n              alwaysExpanded={true}\n              zipFiles={articleData?.zipFiles || []}\n              onZipModified={onZipModified}\n              onQueryCreated={() => {\n                console.log('Query created for article:', articleId);\n              }}\n            />\n          )}\n        </div>\n      )}\n\n      {/* Scroll to Top Button */}\n      {showScrollToTop && (\n        <button\n          onClick={scrollToTop}\n          className=\"scroll-to-top-button\"\n          title=\"Scroll to top\"\n          aria-label=\"Scroll to top\"\n        >\n          <Icons.ChevronUpIcon />\n        </button>\n      )}\n    </div>\n    </div>\n  );\n};\n\nexport default DocumentPreview;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,OAAO,KAAM,SAAS,CAC7B,OAASC,UAAU,KAAQ,8BAA8B,CACzD,OAASC,OAAO,KAAQ,2BAA2B,CACnD,OAASC,KAAK,KAAQ,WAAW,CACjC,MAAO,CAAAC,oBAAoB,KAAM,wBAAwB,CACzD,MAAO,CAAAC,gBAAgB,KAAM,oBAAoB,CACjD,MAAO,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAE/B,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAkH,IAAjH,CAAEC,IAAI,CAAEC,MAAM,CAAEC,gBAAgB,CAAEC,KAAK,CAAEC,OAAO,CAAEC,OAAO,CAAEC,SAAS,CAAEC,aAAa,CAAEC,qBAAsB,CAAC,CAAAT,IAAA,CACnI,KAAM,CAACU,OAAO,CAAEC,UAAU,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC0B,SAAS,CAAEC,YAAY,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CAClE,KAAM,CAACkC,eAAe,CAAEC,kBAAkB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAAoC,QAAQ,CAAGlC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEmC,mBAAmB,CAAEC,WAAY,CAAC,CAAGlC,UAAU,CAAC,CAAC,CACzD,KAAM,CAAEmC,OAAQ,CAAC,CAAGlC,OAAO,CAAC,CAAC,CAE7BJ,SAAS,CAAC,IAAM,CACd,GAAIc,IAAI,CAAE,CACRyB,eAAe,CAAC,CAAC,CACnB,CACF,CAAC,CAAE,CAACzB,IAAI,CAAC,CAAC,CAEV;AACAd,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,SAAS,CAAGC,MAAM,CAACC,WAAW,EAAIC,QAAQ,CAACC,eAAe,CAACJ,SAAS,CAC1EP,kBAAkB,CAACO,SAAS,CAAG,GAAG,CAAC,CAAE;AACvC,CAAC,CAEDC,MAAM,CAACI,gBAAgB,CAAC,QAAQ,CAAEN,YAAY,CAAC,CAC/C,MAAO,IAAME,MAAM,CAACK,mBAAmB,CAAC,QAAQ,CAAEP,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClCb,YAAY,CAAC,IAAI,CAAC,CAClBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACFoB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEnC,IAAI,CAACoC,IAAI,CAAC,CACnDF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEnC,IAAI,CAAC,CACjCkC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAE,CAAC,CAACnC,IAAI,CAACqC,QAAQ,CAAC,CAChDH,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE,MAAO,CAAAnC,IAAI,CAACqC,QAAQ,CAAC,CAEnD,GAAI,CAACrC,IAAI,CAACqC,QAAQ,CAAE,CAClB,KAAM,IAAI,CAAAC,KAAK,CAAC,kCAAkC,CAAC,CACrD,CAEA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAvC,IAAI,CAACqC,QAAQ,CAACG,KAAK,CAAC,aAAa,CAAC,CAC5DN,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEI,WAAW,CAACE,UAAU,CAAC,CAEhE,GAAIzC,IAAI,CAAC0C,IAAI,GAAK,MAAM,EAAI1C,IAAI,CAAC0C,IAAI,GAAK,KAAK,CAAE,CAC/C,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAvD,OAAO,CAACwD,aAAa,CAAC,CAAEL,WAAY,CAAC,CAAC,CAC3D7B,UAAU,CAACiC,MAAM,CAACE,KAAK,CAAC,CACxBvB,mBAAmB,CAACqB,MAAM,CAACE,KAAK,CAAC,CAEjC;AACA,GAAIF,MAAM,CAACG,QAAQ,EAAIH,MAAM,CAACG,QAAQ,CAACC,MAAM,CAAG,CAAC,CAAE,CACjDb,OAAO,CAACc,IAAI,CAAC,8BAA8B,CAAEL,MAAM,CAACG,QAAQ,CAAC,CAC/D,CACF,CAAE,MAAOG,QAAQ,CAAE,CACjBf,OAAO,CAACrB,KAAK,CAAC,4BAA4B,CAAEoC,QAAQ,CAAC,CACrD;AACA,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,WAAW,CAAC,OAAO,CAAE,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,CAACC,MAAM,CAACd,WAAW,CAAC,CAC9E,KAAM,CAAAe,SAAS,CAAGJ,IAAI,CAACK,OAAO,CAAC,wCAAwC,CAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CACnF,GAAIF,SAAS,CAACP,MAAM,CAAG,GAAG,CAAE,CAC1BrC,UAAU,SAAA+C,MAAA,CAASH,SAAS,UAAQ,CAAC,CACrChC,mBAAmB,CAACgC,SAAS,CAAC,CAC9BpB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAClD,CAAC,IAAM,CACL,KAAM,IAAI,CAAAG,KAAK,CAAC,sCAAsC,CAAC,CACzD,CACF,CAAE,MAAOoB,aAAa,CAAE,CACtB,KAAM,IAAI,CAAApB,KAAK,mBAAAmB,MAAA,CAAmBzD,IAAI,CAAC0C,IAAI,CAACiB,WAAW,CAAC,CAAC,uDAAqD,CAAC,CACjH,CACF,CACF,CAAC,IAAM,IAAI3D,IAAI,CAAC0C,IAAI,GAAK,KAAK,CAAE,CAC9B,KAAM,CAAAQ,IAAI,CAAG,GAAI,CAAAC,WAAW,CAAC,CAAC,CAACE,MAAM,CAACd,WAAW,CAAC,CAClD7B,UAAU,SAAA+C,MAAA,CAASP,IAAI,UAAQ,CAAC,CAChC5B,mBAAmB,CAAC4B,IAAI,CAAC,CAC3B,CAAC,IAAM,CACLpC,QAAQ,2BAAA2C,MAAA,CAA2BzD,IAAI,CAAC0C,IAAI,CAACiB,WAAW,CAAC,CAAC,0CAAwC,CAAC,CACrG,CACF,CAAE,MAAOC,GAAG,CAAE,CACZ1B,OAAO,CAACrB,KAAK,CAAC,6BAA6B,CAAE+C,GAAG,CAAC,CACjD1B,OAAO,CAACrB,KAAK,CAAC,gBAAgB,CAAE+C,GAAG,CAACC,OAAO,CAAC,CAC5C3B,OAAO,CAACrB,KAAK,CAAC,uBAAuB,CAAEb,IAAI,CAAC,CAE5C,GAAI,CAAA8D,YAAY,CAAG,6BAA6B,CAChD,GAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,CAAC,UAAU,CAAC,CAAE,CACpCD,YAAY,CAAG,uFAAuF,CACxG,CAAC,IAAM,IAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,CAAE,CACxCD,YAAY,CAAG,qEAAqE,CACtF,CAEAhD,QAAQ,CAACgD,YAAY,CAAC,CACxB,CAAC,OAAS,CACRlD,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAoD,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,SAAS,CAAGrC,MAAM,CAACsC,YAAY,CAAC,CAAC,CACvC,KAAM,CAAAhB,IAAI,CAAGe,SAAS,CAACE,QAAQ,CAAC,CAAC,CAACX,IAAI,CAAC,CAAC,CACxCxC,eAAe,CAACkC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAkB,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAIrD,YAAY,CAAE,CAChB;AACA,KAAM,CAAAsD,aAAa,CAAGzC,MAAM,CAAC0C,QAAQ,CAACC,MAAM,CAACR,QAAQ,CAAC,YAAY,CAAC,CACnE,KAAM,CAAAS,YAAY,CAAG5C,MAAM,CAAC0C,QAAQ,CAACG,QAAQ,CAACV,QAAQ,CAAC,QAAQ,CAAC,CAChE,KAAM,CAAAW,eAAe,CAAGC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,GAAK,MAAM,CACzE,KAAM,CAAAC,eAAe,CAAG/C,QAAQ,CAACgD,QAAQ,CAACf,QAAQ,CAAC,QAAQ,CAAC,CAE5D;AACA,KAAM,CAAAgB,aAAa,CAAGvD,OAAO,EAAI6C,aAAa,EAAIG,YAAY,EAAIE,eAAe,EAAIG,eAAe,EAC/EjD,MAAM,CAAC0C,QAAQ,CAACU,IAAI,CAACjB,QAAQ,CAAC,OAAO,CAAC,EAAI;AAC1CjC,QAAQ,CAACgD,QAAQ,CAACf,QAAQ,CAAC,OAAO,CAAC,CAAO;AAE/D,KAAM,CAAAkB,YAAY,CAAGF,aAAa,CAAG,gBAAgB,CAAG,UAAU,CAElE1D,QAAQ,CAAC4D,YAAY,CAAE,CACrBC,KAAK,CAAE,CACLC,UAAU,CAAEpE,YAAY,CACxBT,SAAS,CAAEiB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEjB,SAAS,CACjC8E,gBAAgB,CAAE,IAAI,CACtBC,WAAW,CAAE,KAAK,CAClBC,cAAc,CAAEP,aAAa,CAAE;AAC/B5E,KAAK,CAAEA,KAAM;AACf,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAoF,WAAW,CAAGA,CAAA,GAAM,CACxB3D,MAAM,CAAC4D,QAAQ,CAAC,CACdC,GAAG,CAAE,CAAC,CACNC,QAAQ,CAAE,QACZ,CAAC,CAAC,CACJ,CAAC,CAED,mBACE7F,KAAA,QAAK8F,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eAEzC/F,KAAA,QAAK8F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/F,KAAA,QAAK8F,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCjG,IAAA,WAAQkG,OAAO,CAAE5F,MAAO,CAAC0F,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC9CjG,IAAA,CAACJ,KAAK,CAACuG,eAAe,GAAE,CAAC,CACnB,CAAC,cACTjG,KAAA,QAAK8F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/F,KAAA,OAAI8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC3BjG,IAAA,SAAMgG,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAE5F,IAAI,CAAC+F,IAAI,CAAO,CAAC,CAC7C/F,IAAI,CAACoC,IAAI,EACR,CAAC,cACLvC,KAAA,MAAG8F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAC5B5F,IAAI,CAAC0C,IAAI,CAACiB,WAAW,CAAC,CAAC,CAAC,0BAC3B,EAAG,CAAC,EACD,CAAC,EACH,CAAC,cAEN9D,KAAA,QAAK8F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAE9BjG,IAAA,CAACF,gBAAgB,EACfuG,SAAS,CAAC,SAAS,CACnB1F,SAAS,CAAEA,SAAU,CACrBF,OAAO,CAAEA,OAAQ,CACjB6F,UAAU,CAAC,aAAa,CACxBC,UAAU,CAAC,cAAI,CACfC,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,OAAO,CACZC,WAAW,CAAGC,EAAE,EAAK,CACnBpE,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEmE,EAAE,CAAC,CAC1C;AACA,GAAI9F,qBAAqB,CAAE,CACzBA,qBAAqB,CAAC8F,EAAE,CAAC,CAC3B,CACF,CAAE,CACH,CAAC,CAEDjG,OAAO,EAAIA,OAAO,CAAC0C,MAAM,CAAG,CAAC,eAC5BpD,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAM3E,qBAAqB,CAAC,CAACD,kBAAkB,CAAE,CAC1D0E,SAAS,CAAC,uBAAuB,CACjCY,KAAK,CAAEtF,kBAAkB,CAAG,cAAc,CAAG,cAAe,CAAA2E,QAAA,cAE5DjG,IAAA,CAACJ,KAAK,CAACiH,YAAY,GAAE,CAAC,CAChB,CACT,CAGAzF,YAAY,eACXlB,KAAA,WAAQgG,OAAO,CAAEzB,mBAAoB,CAACuB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACvEjG,IAAA,CAACJ,KAAK,CAACkH,cAAc,GAAE,CAAC,mBAE1B,EAAQ,CACT,EACE,CAAC,EACH,CAAC,cAGN5G,KAAA,QAAK8F,SAAS,yBAAAlC,MAAA,CAA0B,CAACxC,kBAAkB,CAAG,gBAAgB,CAAG,EAAE,CAAG,CAAA2E,QAAA,eAEpF/F,KAAA,QAAK8F,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EACvCjF,SAAS,eACRd,KAAA,QAAK8F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BjG,IAAA,QAAKgG,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvChG,IAAA,MAAAiG,QAAA,CAAG,qBAAmB,CAAG,CAAC,EACvB,CACN,CAEA/E,KAAK,eACJhB,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,CAACJ,KAAK,CAACmH,eAAe,GAAE,CAAC,cACzB7G,KAAA,QAAA+F,QAAA,eACEjG,IAAA,OAAAiG,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/BjG,IAAA,MAAAiG,QAAA,CAAI/E,KAAK,CAAI,CAAC,CACbA,KAAK,CAACkD,QAAQ,CAAC,MAAM,CAAC,eACrBlE,KAAA,QAAK8F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,eAC7B,cAAAjG,IAAA,WAAAiG,QAAA,CAAQ,MAAI,CAAQ,CAAC,wHAC1B,EAAK,CACN,EACE,CAAC,EACH,CACN,CAEA,CAACjF,SAAS,EAAI,CAACE,KAAK,EAAIJ,OAAO,eAC9Bd,IAAA,QAAKgG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxCjG,IAAA,QACEgG,SAAS,CAAC,iBAAiB,CAC3BgB,uBAAuB,CAAE,CAAEC,MAAM,CAAEnG,OAAQ,CAAE,CAC7CoG,SAAS,CAAE7C,mBAAoB,CAC/B8C,OAAO,CAAE9C,mBAAoB,CAC9B,CAAC,CACC,CACN,cAGDnE,KAAA,QAAK8F,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC/F,KAAA,QAAK8F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BjG,IAAA,SAAMgG,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,oBAAG,CAAM,CAAC,cAC7CjG,IAAA,SAAAiG,QAAA,CAAM,0CAAwC,CAAM,CAAC,EAClD,CAAC,cACN/F,KAAA,QAAK8F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BjG,IAAA,SAAMgG,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cAC5CjG,IAAA,SAAAiG,QAAA,CAAM,sEAAoE,CAAM,CAAC,EAC9E,CAAC,EACH,CAAC,CAGL7E,YAAY,eACXlB,KAAA,QAAK8F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/F,KAAA,QAAK8F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BjG,IAAA,SAAMgG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cAC1C/F,KAAA,SAAA+F,QAAA,EAAM,iBAAe,CAAC7E,YAAY,CAACgC,MAAM,CAAC,cAAY,EAAM,CAAC,EAC1D,CAAC,cACNlD,KAAA,QAAK8F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/B7E,YAAY,CAACgG,SAAS,CAAC,CAAC,CAAE,GAAG,CAAC,CAC9BhG,YAAY,CAACgC,MAAM,CAAG,GAAG,EAAI,KAAK,EAChC,CAAC,EACH,CACN,EACE,CAAC,CAGL9B,kBAAkB,eACjBtB,IAAA,QAAKgG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BvF,OAAO,EAAIA,OAAO,CAAC0C,MAAM,CAAG,CAAC,eAC5BpD,IAAA,CAACH,oBAAoB,EACnBc,SAAS,CAAEA,SAAU,CACrBD,OAAO,CAAEA,OAAQ,CACjB2G,WAAW,CAAE,IAAK,CAClBC,cAAc,CAAE,IAAK,CACrBC,QAAQ,CAAE,CAAA3F,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE2F,QAAQ,GAAI,EAAG,CACtC3G,aAAa,CAAEA,aAAc,CAC7B4G,cAAc,CAAEA,CAAA,GAAM,CACpBjF,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAE7B,SAAS,CAAC,CACtD,CAAE,CACH,CACF,CACE,CACN,CAGAa,eAAe,eACdxB,IAAA,WACEkG,OAAO,CAAEN,WAAY,CACrBI,SAAS,CAAC,sBAAsB,CAChCY,KAAK,CAAC,eAAe,CACrB,aAAW,eAAe,CAAAX,QAAA,cAE1BjG,IAAA,CAACJ,KAAK,CAAC6H,aAAa,GAAE,CAAC,CACjB,CACT,EACE,CAAC,EACD,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}