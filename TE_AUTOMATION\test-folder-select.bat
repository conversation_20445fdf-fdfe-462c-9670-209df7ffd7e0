@echo off
setlocal enabledelayedexpansion

echo Testing folder selection...
echo.

set "DOWNLOADS_PATH=%USERPROFILE%\Downloads"
echo Downloads path: %DOWNLOADS_PATH%
echo.

if not exist "%DOWNLOADS_PATH%" (
    echo ERROR: Downloads folder not found
    pause
    exit /b 1
)

echo Listing folders in Downloads:
echo.

set "count=0"
for /f "delims=" %%d in ('dir "%DOWNLOADS_PATH%" /b /ad 2^>nul') do (
    set /a count+=1
    echo [!count!] %%d
    set "folder_!count!=%%d"
)

echo.
echo Total folders found: !count!
echo.

if !count! equ 0 (
    echo No folders found!
    pause
    exit /b 1
)

set /p "folder_num=Enter folder number: "

set "selected_folder=!folder_%folder_num%!"

if not defined selected_folder (
    echo Invalid selection!
    pause
    exit /b 1
)

set "BATCH_DIR=%DOWNLOADS_PATH%\!selected_folder!"

echo.
echo Selected: !BATCH_DIR!
echo.

pause

