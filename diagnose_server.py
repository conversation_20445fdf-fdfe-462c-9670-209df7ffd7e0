#!/usr/bin/env python3
"""
Server diagnostics script - checks server status without making changes
"""
import subprocess
import json

def check_server_status():
    """Check if the Flask app is running on the server"""
    print("=== SERVER STATUS DIAGNOSTICS ===")
    
    # Check if server is reachable
    print("1. Testing server connectivity...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem", 
            "<EMAIL>",
            "echo 'Server reachable'"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Server is reachable via SSH")
        else:
            print("❌ Server not reachable via SSH")
            return
    except Exception as e:
        print(f"❌ SSH connection failed: {e}")
        return
    
    # Check if Flask app is running
    print("\n2. Checking Flask application status...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem",
            "<EMAIL>",
            "ps aux | grep -E '(python|flask|app.py)' | grep -v grep"
        ], capture_output=True, text=True, timeout=10)
        
        if result.stdout.strip():
            print("✅ Python/Flask processes found:")
            print(result.stdout)
        else:
            print("❌ No Python/Flask processes running")
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
    
    # Check if port 4999 is listening
    print("\n3. Checking if port 4999 is listening...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem",
            "<EMAIL>",
            "netstat -tlnp | grep :4999 || ss -tlnp | grep :4999"
        ], capture_output=True, text=True, timeout=10)
        
        if result.stdout.strip():
            print("✅ Port 4999 is listening:")
            print(result.stdout)
        else:
            print("❌ Port 4999 is not listening")
    except Exception as e:
        print(f"❌ Error checking port: {e}")
    
    # Check PM2 status (if using PM2)
    print("\n4. Checking PM2 status...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem",
            "<EMAIL>",
            "pm2 list"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ PM2 status:")
            print(result.stdout)
        else:
            print("❌ PM2 not available or error")
    except Exception as e:
        print(f"❌ Error checking PM2: {e}")
    
    # Check recent logs
    print("\n5. Checking recent application logs...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem",
            "<EMAIL>",
            "tail -20 app.log 2>/dev/null || tail -20 *.log 2>/dev/null || echo 'No log files found'"
        ], capture_output=True, text=True, timeout=10)
        
        if result.stdout.strip():
            print("📋 Recent logs:")
            print(result.stdout)
        else:
            print("❌ No recent logs found")
    except Exception as e:
        print(f"❌ Error checking logs: {e}")

def test_api_endpoints():
    """Test API endpoints from server side"""
    print("\n=== API ENDPOINT TESTING ===")
    
    # Test health endpoint from server
    print("6. Testing API health from server...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem",
            "<EMAIL>",
            "curl -s -w 'HTTP_CODE:%{http_code}' http://localhost:4999/api/health || echo 'Curl failed'"
        ], capture_output=True, text=True, timeout=15)
        
        if result.stdout:
            print(f"Health endpoint response: {result.stdout}")
        else:
            print("❌ No response from health endpoint")
    except Exception as e:
        print(f"❌ Error testing health endpoint: {e}")
    
    # Test login endpoint from server
    print("\n7. Testing login endpoint from server...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem",
            "<EMAIL>",
            "curl -s -X POST -H 'Content-Type: application/json' -d '{\"username\":\"test\",\"password\":\"test\"}' -w 'HTTP_CODE:%{http_code}' http://localhost:4999/api/auth/login"
        ], capture_output=True, text=True, timeout=15)
        
        if result.stdout:
            print(f"Login endpoint response: {result.stdout}")
        else:
            print("❌ No response from login endpoint")
    except Exception as e:
        print(f"❌ Error testing login endpoint: {e}")

def check_firewall_and_security():
    """Check firewall and security group settings"""
    print("\n=== FIREWALL & SECURITY DIAGNOSTICS ===")
    
    # Check iptables
    print("8. Checking iptables rules...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem",
            "<EMAIL>",
            "sudo iptables -L -n | grep 4999 || echo 'No iptables rules for port 4999'"
        ], capture_output=True, text=True, timeout=10)
        
        print(f"Iptables check: {result.stdout}")
    except Exception as e:
        print(f"❌ Error checking iptables: {e}")
    
    # Check if firewalld is running
    print("\n9. Checking firewalld status...")
    try:
        result = subprocess.run([
            "ssh", "-i", "../exampletest.pem",
            "<EMAIL>",
            "sudo systemctl status firewalld 2>/dev/null || echo 'Firewalld not running'"
        ], capture_output=True, text=True, timeout=10)
        
        print(f"Firewalld status: {result.stdout}")
    except Exception as e:
        print(f"❌ Error checking firewalld: {e}")

def test_external_connectivity():
    """Test external connectivity to the server"""
    print("\n=== EXTERNAL CONNECTIVITY TEST ===")
    
    # Test HTTP connection from local machine
    print("10. Testing HTTP connection from local machine...")
    try:
        result = subprocess.run([
            "curl", "-s", "-w", "HTTP_CODE:%{http_code}", 
            "--connect-timeout", "10",
            "http://************:4999/api/health"
        ], capture_output=True, text=True, timeout=15)
        
        if result.stdout:
            print(f"External HTTP test: {result.stdout}")
        else:
            print("❌ No response from external HTTP test")
    except FileNotFoundError:
        print("❌ curl not available on local machine")
    except Exception as e:
        print(f"❌ Error testing external connectivity: {e}")

def main():
    """Main diagnostic function"""
    print("🔍 FLASK SERVER DIAGNOSTICS")
    print("=" * 50)
    print("This script will diagnose the Flask server issues")
    print("without making any changes to the server.")
    print("=" * 50)
    
    check_server_status()
    test_api_endpoints()
    check_firewall_and_security()
    test_external_connectivity()
    
    print("\n" + "=" * 50)
    print("📋 DIAGNOSIS SUMMARY")
    print("=" * 50)
    print("Common issues and solutions:")
    print("1. If Flask app is not running:")
    print("   - Check if app.py crashed")
    print("   - Restart with: python3 app.py or pm2 restart app")
    print("2. If port 4999 not listening:")
    print("   - App might be running on different port")
    print("   - Check app configuration")
    print("3. If CORS errors:")
    print("   - Flask app needs CORS configuration")
    print("   - Check Flask-CORS setup in app.py")
    print("4. If 502 errors:")
    print("   - App crashed or not responding")
    print("   - Check application logs")

if __name__ == "__main__":
    main()
