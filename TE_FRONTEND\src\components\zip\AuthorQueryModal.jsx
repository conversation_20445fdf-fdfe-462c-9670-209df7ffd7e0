import React, { useState, useEffect } from 'react';
import { Icons } from '../common';
import './AuthorQueryModal.css';

// Single source of truth for query types and templates
const QUERY_TYPES = {
  'copyright_no': {
    label: 'Author has not agreed to copyright terms',
    template: "The author(s) {names} has/have not agreed to the copyright terms and conditions which were sent on the author's email address."
  },
  'missing_in_system': {
    label: 'Missing authors in system',
    template: "The following author(s) {names} are missing in the system, which prevents verification of the copyright status. Kindly provide necessary details (email ID, affiliations) so we can update the system and proceed with sending the copyright email."
  },
  'missing_in_manuscript': {
    label: 'Author missing in firstpage file',
    template: "Author name {names} given in copyright information provided through mail (system). However, author name(s) is missing in author list provided in firstpage."
  }
};

const AuthorQueryModal = ({ articleId, authors, onClose, onSubmit, zipFiles = [], onZipModified }) => {
  const [formData, setFormData] = useState({
    selectedAuthors: <AUTHORS>
    manualAuthorNames: '',
    queryType: '',
    previewText: '',
    selectedFile: '' // New: file to write query to
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  // Filter files that can have queries written to them
  const writableFiles = zipFiles.filter(file =>
    ['docx', 'doc', 'txt'].includes(file.type?.toLowerCase())
  );

  // Auto-select manuscript file if available
  useEffect(() => {
    if (writableFiles.length > 0 && !formData.selectedFile) {
      const manuscriptFile = writableFiles.find(f =>
        f.name.toLowerCase().includes('manuscript')
      );
      setFormData(prev => ({
        ...prev,
        selectedFile: manuscriptFile ? manuscriptFile.name : writableFiles[0].name
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [writableFiles.length]); // Only re-run when number of files changes

  // Determine if no authors are present in system
  const noAuthorsInSystem = !authors || authors.length === 0;

  // Determine if we should show manual entry (for missing_in_system query type OR when no authors exist)
  const shouldShowManualEntry = noAuthorsInSystem || formData.queryType === 'missing_in_system';

  // Auto-set query type when no authors in system
  useEffect(() => {
    if (noAuthorsInSystem) {
      setFormData(prev => ({
        ...prev,
        queryType: 'missing_in_system',
        previewText: QUERY_TYPES['missing_in_system'].template.replace('{names}', prev.manualAuthorNames.trim() || '[Enter author names]')
      }));
    }
  }, [noAuthorsInSystem]);

  // Update preview text when selection changes
  useEffect(() => {
    if (!formData.queryType) {
        setFormData(prev => ({ ...prev, previewText: '' }));
        return;
    }

    const queryConfig = QUERY_TYPES[formData.queryType];
    if (!queryConfig) return;

    const template = queryConfig.template;

    let namesStr = '';

    if (shouldShowManualEntry) {
        // Use manual author names when no authors in system OR for missing_in_system query
        namesStr = formData.manualAuthorNames.trim() || '[Enter author names]';
    } else {
        // Use selected authors
        if (formData.selectedAuthors.length > 0) {
            namesStr = formData.selectedAuthors.join(', ');
        } else {
            namesStr = '[Select Authors]';
        }
    }

    // Update preview
    setFormData(prev => ({
        ...prev,
        previewText: template.replace('{names}', namesStr)
    }));

  }, [formData.queryType, formData.selectedAuthors, formData.manualAuthorNames, shouldShowManualEntry]);

  // Copyright Helper: Auto-select authors with 'no' copyright
  useEffect(() => {
    if (authors && authors.length > 0) {
      const noCopyrightAuthors = authors
        .filter(a => {
           const status = a.copyright_status ? a.copyright_status.toLowerCase() : '';
           return status === 'no' || status === 'false';
        })
        .map(a => a.fullName || a.name); // Support both fullName (new) and name (old)

      if (noCopyrightAuthors.length > 0) {
        console.log('🔴 [AuthorQueryModal] Auto-selecting authors with NO copyright:', noCopyrightAuthors);
        setFormData(prev => ({
          ...prev,
          selectedAuthors: <AUTHORS>
          queryType: 'copyright_no'
        }));
      }
    }
  }, [authors]);

  const handleAuthorToggle = (authorName) => {
    setFormData(prev => ({
      ...prev,
      selectedAuthors: <AUTHORS>
        ? prev.selectedAuthors.filter(name => name !== authorName)
        : [...prev.selectedAuthors, authorName]
    }));
    if (errors.selectedAuthors) {
      setErrors(prev => ({ ...prev, selectedAuthors: <AUTHORS>
    }
  };

  const handleManualAuthorNamesChange = (e) => {
    setFormData(prev => ({ ...prev, manualAuthorNames: e.target.value }));
    if (errors.manualAuthorNames) {
      setErrors(prev => ({ ...prev, manualAuthorNames: '' }));
    }
  };


  
  const handlePreviewChange = (e) => {
    setFormData(prev => ({ ...prev, previewText: e.target.value }));
  };

  const handleQueryTypeChange = (e) => {
    const newQueryType = e.target.value;

    // Clear appropriate fields when switching query types
    setFormData(prev => {
      const updates = { queryType: newQueryType };

      // If switching TO missing_in_system, clear selected authors
      if (newQueryType === 'missing_in_system') {
        updates.selectedAuthors = [];
      }
      // If switching FROM missing_in_system, clear manual author names
      else if (prev.queryType === 'missing_in_system') {
        updates.manualAuthorNames = '';
      }

      return { ...prev, ...updates };
    });

    if (errors.queryType) {
      setErrors(prev => ({ ...prev, queryType: '' }));
    }
  };

  const handleClearDefaults = () => {
    if (noAuthorsInSystem) {
      // If no authors in system, reset to the default state for that scenario
      setFormData({
        selectedAuthors: <AUTHORS>
        manualAuthorNames: '',
        queryType: 'missing_in_system',
        previewText: QUERY_TYPES['missing_in_system'].template.replace('{names}', '[Enter author names]')
      });
    } else {
      setFormData({
        selectedAuthors: <AUTHORS>
        manualAuthorNames: '',
        queryType: '',
        previewText: ''
      });
    }
    setErrors({});
  };

  const validateForm = () => {
    const newErrors = {};

    if (shouldShowManualEntry) {
        // Validate manual author names when using manual entry
        if (!formData.manualAuthorNames.trim()) {
            newErrors.manualAuthorNames = 'Please enter author names (comma-separated)';
        }
    } else {
        // Validate author selection when using checkbox selection
        if (formData.selectedAuthors.length === 0) {
            newErrors.selectedAuthors = 'Please select at least one author';
        }
    }

    if (!formData.queryType) {
      newErrors.queryType = 'Please select a query type';
    }

    // Also validate preview text is not empty
    if (!formData.previewText.trim()) {
        newErrors.previewText = 'Query text cannot be empty';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // Submit the query to database
      await onSubmit({
        articleId,
        queryType: formData.queryType,
        authors: shouldShowManualEntry ? [formData.manualAuthorNames] : formData.selectedAuthors,
        queryText: formData.previewText
      });

      // If a file is selected and onZipModified callback exists, modify the ZIP
      if (formData.selectedFile && onZipModified && writableFiles.length > 0) {
        try {
          await handleWriteQueryToFile();
        } catch (fileError) {
          console.error('Error writing query to file:', fileError);
          alert(`Query saved to database, but failed to write to file: ${fileError.message}`);
        }
      }

      onClose();
    } catch (error) {
      console.error('Error submitting author query:', error);
        alert(`Failed to submit query: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleWriteQueryToFile = async () => {
    const selectedFileObj = zipFiles.find(f => f.name === formData.selectedFile);
    if (!selectedFileObj || !selectedFileObj.zipEntry) {
      throw new Error('Selected file not found in ZIP');
    }

    // Use only the query text (no template/header)
    const queryText = formData.previewText;

    // Read the file content
    const fileBuffer = await selectedFileObj.zipEntry.async('arraybuffer');

    if (selectedFileObj.type === 'txt') {
      // For text files, simply prepend the query
      const decoder = new TextDecoder('utf-8');
      const originalContent = decoder.decode(fileBuffer);
      const modifiedContent = queryText + '\n\n' + originalContent;

      // Notify parent to update the ZIP
      await onZipModified(formData.selectedFile, modifiedContent, 'text');
    } else if (selectedFileObj.type === 'docx' || selectedFileObj.type === 'doc') {
      // For DOCX files, modify the document.xml inside the DOCX with yellow highlight
      await onZipModified(formData.selectedFile, queryText, 'docx', fileBuffer);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="author-query-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>
            <Icons.ExclamationTriangleIcon className="modal-icon" />
            Raise Author Query
          </h2>
          <button onClick={onClose} className="modal-close">
            <Icons.XIcon />
          </button>
        </div>

        <div className="modal-body">
          <div className="article-info">
            <p><strong>Article ID:</strong> {articleId}</p>
            <button type="button" onClick={handleClearDefaults} className="btn-secondary btn-sm">
              Clear Defaults
            </button>
          </div>

          <form onSubmit={handleSubmit} className="modal-form">
            {/* Author Selection or Manual Entry */}
            <div className="form-group">
              <label>
                {shouldShowManualEntry ? 'Enter Missing Author Names *' : 'Select Authors *'}
              </label>

              {shouldShowManualEntry ? (
                <div className="manual-author-entry">
                  <input
                    type="text"
                    className="form-control"
                    value={formData.manualAuthorNames}
                    onChange={handleManualAuthorNamesChange}
                    placeholder="Enter author names separated by commas (e.g., Dr. Smith, Dr. Jones)"
                    style={{width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc'}}
                  />
                  <p className="info-text" style={{fontSize: '0.85em', color: '#666', marginTop: '4px'}}>
                    {noAuthorsInSystem
                      ? 'No authors found in system. Please enter the missing author names.'
                      : 'Enter the names of authors that are missing from the system.'}
                  </p>
                  {errors.manualAuthorNames && <span className="error-text">{errors.manualAuthorNames}</span>}
                </div>
              ) : (
                <div>
                  <div className="authors-list">
                    {authors.map((author, index) => {
                      // Support both fullName (new) and name (old)
                      const authorName = author.fullName || author.name;

                      return (
                        <div key={index} className="author-item">
                        <label className="checkbox-label">
                            <input
                            type="checkbox"
                            checked={formData.selectedAuthors.includes(authorName)}
                            onChange={() => handleAuthorToggle(authorName)}
                            />
                            <div className="author-details">
                            <span className="author-name">{authorName}</span>
                            <span className="author-email">{author.email}</span>
                            <span className={`copyright-status ${author.copyright_status?.toLowerCase()}`}>
                                Copyright: {author.copyright_status || 'Unknown'}
                            </span>
                            </div>
                        </label>
                        </div>
                      );
                    })}
                  </div>
                  {errors.selectedAuthors && <span className="error-text">{errors.selectedAuthors}</span>}
                </div>
              )}
            </div>

            {/* Query Type Selection */}
            <div className="form-group">
              <label htmlFor="queryType">Query Type *</label>
              <select
                id="queryType"
                value={formData.queryType}
                onChange={handleQueryTypeChange}
                className={errors.queryType ? 'error' : ''}
              >
                <option value="">Select a query type</option>
                {Object.entries(QUERY_TYPES).map(([value, config]) => (
                  <option key={value} value={value}>
                    {config.label}
                  </option>
                ))}
              </select>
              {errors.queryType && <span className="error-text">{errors.queryType}</span>}
            </div>

            {/* File Selection - NEW */}
            {writableFiles.length > 0 && (
              <div className="form-group">
                <label htmlFor="selectedFile">
                  Write Query to File (Optional)
                  <span className="info-icon" title="The query will be written to the top of the selected file in the ZIP">ℹ️</span>
                </label>
                <select
                  id="selectedFile"
                  value={formData.selectedFile}
                  onChange={(e) => setFormData(prev => ({ ...prev, selectedFile: e.target.value }))}
                  className="form-control"
                >
                  <option value="">-- Don't write to file --</option>
                  {writableFiles.map((file) => (
                    <option key={file.name} value={file.name}>
                      {file.name} ({file.type.toUpperCase()})
                    </option>
                  ))}
                </select>
                <p className="info-text" style={{fontSize: '0.85em', color: '#666', marginTop: '4px'}}>
                  {formData.selectedFile
                    ? `Query will be written to the top of "${formData.selectedFile}"`
                    : 'Select a file to write the query text at the beginning of the document'}
                </p>
              </div>
            )}

            {/* Preview - Editable */}
            {formData.previewText && (
              <div className="form-group">
                <label>Query Preview (Editable)</label>
                <div className="query-preview-container">
                    <textarea
                        className="query-preview-textarea"
                        value={formData.previewText}
                        onChange={handlePreviewChange}
                        rows={4}
                        style={{width: '100%', padding: '10px', borderRadius: '4px', border: '1px solid #ccc', fontFamily: 'inherit'}}
                    />
                </div>
                {errors.previewText && <span className="error-text">{errors.previewText}</span>}
              </div>
            )}

            {/* Form Actions */}
            <div className="form-actions">
              <button type="button" onClick={onClose} disabled={isSubmitting} className="btn-secondary">
                Cancel
              </button>
              <button type="submit" disabled={isSubmitting} className="btn-primary">
                {isSubmitting ? 'Submitting...' : 'Confirm & Save Query'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AuthorQueryModal;
