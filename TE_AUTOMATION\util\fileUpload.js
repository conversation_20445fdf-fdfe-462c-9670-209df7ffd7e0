const fs = require('fs');
const path = require('path');
const { google } = require('googleapis');
require('dotenv').config();

// Load API keys from multiple possible sources
let apikeys;
let serviceAccountPath;

try {
    // Try loading from service_account.json (GitHub Actions)
    const githubActionsPath = path.join(__dirname, '../service_account.json');
    const localPath1 = path.join(__dirname, './apikeys.json');
    const localPath2 = path.join(__dirname, 'apikeys.json');

    if (fs.existsSync(githubActionsPath)) {
        serviceAccountPath = githubActionsPath;
        console.log(`📁 Loading service account from: ${serviceAccountPath}`);
        const fileContent = fs.readFileSync(serviceAccountPath, 'utf8');
        console.log(`📊 File size: ${fileContent.length} bytes`);

        if (fileContent.trim().length === 0) {
            throw new Error('Service account JSON file is empty!');
        }

        apikeys = JSON.parse(fileContent);

        if (!apikeys.client_email || !apikeys.private_key) {
            throw new Error('Service account JSON is missing required fields (client_email or private_key)');
        }

        console.log(`✅ Service account loaded: ${apikeys.client_email}`);
    }
    // Try loading from apikeys.json (local development)
    else if (fs.existsSync(localPath1)) {
        serviceAccountPath = localPath1;
        console.log(`📁 Loading API keys from: ${serviceAccountPath}`);
        apikeys = require('./apikeys.json');
        console.log(`✅ API keys loaded: ${apikeys.client_email}`);
    }
    // Try loading from util/apikeys.json (alternative local path)
    else if (fs.existsSync(localPath2)) {
        serviceAccountPath = localPath2;
        console.log(`📁 Loading API keys from: ${serviceAccountPath}`);
        apikeys = require('./apikeys.json');
        console.log(`✅ API keys loaded: ${apikeys.client_email}`);
    }
    else {
        throw new Error(`No service account JSON file found. Checked paths:
  - ${githubActionsPath}
  - ${localPath1}
  - ${localPath2}`);
    }
} catch (error) {
    console.error('❌ Error loading API keys:', error.message);
    if (serviceAccountPath) {
        console.error(`   File path: ${serviceAccountPath}`);
    }
    throw error;
}

const SCOPE = ['https://www.googleapis.com/auth/drive'];

// Use environment variable or fallback to hardcoded folder
const DUMP_FOLDER_ID = process.env.GOOGLE_DRIVE_DUMP_FOLDER_ID || '1xHhAfSIgcX5k7LQStSvszlQmN7OP52di';

// A Function that can provide access to google drive api
async function authorize() {
    const jwtClient = new google.auth.JWT(
        apikeys.client_email,
        null,
        apikeys.private_key,
        SCOPE
    );

    await jwtClient.authorize();

    return jwtClient;
}

// A Function that will upload the desired file to google drive folder
async function uploadFile(authClient, filePath) {
    return new Promise((resolve, rejected) => {
        const drive = google.drive({ version: 'v3', auth: authClient });
        
        // Extract actual filename from path
        const filename = path.basename(filePath);

        var fileMetaData = {
            name: filename,  // Use actual filename, not timestamp
            parents: [DUMP_FOLDER_ID] // Upload to dump folder
        }

        drive.files.create({
            resource: fileMetaData,
            media: {
                body: fs.createReadStream(filePath),
                mimeType: 'application/zip'  // Correct MIME type for ZIP files
            },
            fields: 'id, name, webViewLink'
        }, function (error, file) {
            if (error) {
                console.error('Upload error:', error);
                return rejected(error);
            }
            const fileData = {
                id: file.data.id,
                name: file.data.name,
                url: `https://drive.google.com/file/d/${file.data.id}/view?usp=sharing`
            };
            console.log(`✅ Uploaded to Drive: ${filename} (ID: ${file.data.id})`);
            resolve(fileData);
        })
    });
}


const uploadFileToDrive = async (filePath) => {
    console.log("📤 Uploading to Drive:", filePath);
    try {
        const authClient = await authorize();
        const result = await uploadFile(authClient, filePath);
        return result;
    } catch (error) {
        console.error("❌ Drive upload error:", error);
        throw error;
    }
}

module.exports = { uploadFileToDrive };