/**
 * Query Messages and Templates
 * Centralized location for all query-related messages and email templates
 */

export const SUBJECT_OPTIONS = [
  'Manuscript File Missing',
  'Multiple Manuscript Files Found',
  'First Page File Missing',
  'Missing Both Manuscript and First Page',
  'Multiple First Page Files Found',
  'Missing Author Details',
  'Missing Author Affiliations',
  'Missing Corresponding Author Details',
  'File Naming Convention Issue',
  'ZIP Archive Structure Problem',
  'Other Validation Issue'
];

/**
 * Generate email description based on subject and validation result
 * @param {string} subject - Selected subject from dropdown
 * @param {string} articleId - Article ID
 * @param {Object} validationResult - Validation result object
 * @returns {string} Generated email description
 */
export const getDescriptionForSubject = (subject, articleId, validationResult) => {
  const greeting = `Dear Author,\n\nDuring our initial review of your article (ID: ${articleId}), we identified an issue that requires your attention:\n\n`;

  switch (subject) {
    case 'Manuscript File Missing':
      return greeting +
        `ISSUE: No manuscript file was found.\n\n` +
        `Files received: ${validationResult?.files?.join(', ') || 'None listed'}\n\n` +
        `REQUIRED ACTION:\n` +
        `Please resubmit therticle with the corrected file structure.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'Multiple Manuscript Files Found':
      return greeting +
        `ISSUE: Multiple manuscript files were found.\n\n` +
        `Manuscript files detected: ${validationResult?.manuscript_files?.join(', ') || 'Multiple files'}\n\n` +
        `Please clarify which file is the primary manuscript for processing.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'First Page File Missing':
      return greeting +
        `ISSUE: The First Page file is missing.\n\n` +
        `Please include the First Page file.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'Missing Both Manuscript and First Page':
      return greeting +
        `ISSUE: Both the manuscript and First Page files are missing.\n\n` +
        `Files received: ${validationResult?.files?.join(', ') || 'None listed'}\n\n` +
        `These are essential files for processing the article submission.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'Multiple First Page Files Found':
      return greeting +
        `ISSUE: Multiple First Page files were found in your submission.\n\n` +
        `First Page files detected: ${validationResult?.fp_files?.join(', ') || 'Multiple files'}\n\n` +
        `Please clarify which First Page file should be used.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'File Naming Convention Issue':
      return greeting +
        `ISSUE: The manuscript file may not follow our naming convention requirements.\n\n` +
        `Files received: ${validationResult?.files?.join(', ') || 'None listed'}\n\n` +
        `REQUIRED ACTION:\n` +
        `Please ensure your manuscript file contains the word "manuscript" in the filename (e.g., "manuscript.docx", "article_manuscript.doc").\n\n` +
        `This helps our editorial system properly identify and process your submission.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'ZIP Archive Structure Problem':
      return greeting +
        `ISSUE: There appears to be an issue with your submission file structure.\n\n` +
        `Total files received: ${validationResult?.total_files || 'Unknown'}\n` +
        `Files detected: ${validationResult?.files?.join(', ') || 'None listed'}\n\n` +
        `REQUIRED ACTION:\n` +
        `Please review your file organization and ensure all required files are properly included in your ZIP archive.\n\n` +
        `If you continue to experience issues, please contact our technical support team.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'Missing Author Details':
      return greeting +
        `ISSUE: Author information appears to be incomplete.\n\n` +
        `REQUIRED INFORMATION:\n` +
        `• Complete author names (first and last names)\n` +
        `• Author email addresses\n` +
        `• Author ORCID IDs (if available)\n\n` +
        `REQUIRED ACTION:\n` +
        `Please provide the complete author details for all contributors to proceed with processing.\n\n` +
        `This information is essential for proper attribution and correspondence.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'Missing Author Affiliations':
      return greeting +
        `ISSUE: Author affiliation information is missing or incomplete.\n\n` +
        `REQUIRED INFORMATION:\n` +
        `• Complete institutional affiliations for all authors\n` +
        `• Department/division information\n` +
        `• Complete institutional addresses\n\n` +
        `REQUIRED ACTION:\n` +
        `Please provide complete affiliation details for all authors listed in your manuscript.\n\n` +
        `This information is required for publication and proper institutional credit.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'Missing Corresponding Author Details':
      return greeting +
        `ISSUE: Corresponding author information is missing or incomplete.\n\n` +
        `REQUIRED INFORMATION:\n` +
        `• Corresponding author designation\n` +
        `• Complete contact email address\n` +
        `• Phone number (if required by journal)\n` +
        `• Complete mailing address\n\n` +
        `REQUIRED ACTION:\n` +
        `Please provide complete corresponding author contact information.\n\n` +
        `This information is essential for editorial correspondence and publication.\n\n` +
        `Best regards,\nEditorial Team`;

    case 'Other Validation Issue':
      return greeting +
        `ISSUE: A validation issue was detected that requires manual review.\n\n` +
        `Files received: ${validationResult?.files?.join(', ') || 'None listed'}\n` +
        `Manuscript files detected: ${validationResult?.manuscript_files?.join(', ') || 'None'}\n\n` +
        `REQUIRED ACTION:\n` +
        `Please review your submission and provide additional details about any specific requirements or corrections needed.\n\n` +
        `Our editorial team will assist you in resolving this issue promptly.\n\n` +
        `Best regards,\nEditorial Team`;

    default:
      return greeting +
        `ISSUE: A validation issue was detected with your submission.\n\n` +
        `Files received: ${validationResult?.files?.join(', ') || 'None listed'}\n` +
        `Manuscript files detected: ${validationResult?.manuscript_files?.join(', ') || 'None'}\n\n` +
        `REQUIRED ACTION:\n` +
        `Please review your submission and contact our editorial team for specific guidance.\n\n` +
        `We are here to help ensure your submission meets all requirements.\n\n` +
        `Best regards,\nEditorial Team`;
  }
};

/**
 * Get the appropriate subject based on validation error type
 * @param {Object} validationResult - Validation result object
 * @returns {string} Suggested subject
 */
export const getSuggestedSubject = (validationResult) => {
  if (!validationResult || validationResult.can_proceed) {
    return '';
  }

  switch (validationResult.error_type) {
    case 'no_manuscript':
      return 'Manuscript File Missing';
    case 'multiple_manuscripts':
      return 'Multiple Manuscript Files Found';
    case 'missing_fp':
      return 'First Page File Missing';
    case 'no_manuscript_and_fp':
      return 'Missing Both Manuscript and First Page';
    case 'multiple_fp':
      return 'Multiple First Page Files Found';
    case 'validation_failed':
    case 'fp_validation_failed':
      return 'ZIP Archive Structure Problem';
    default:
      return 'Other Validation Issue';
  }
};

/**
 * Validation warning messages for UI display
 */
export const VALIDATION_MESSAGES = {
  no_manuscript: {
    title: 'Manuscript Validation Issues Detected',
    description: 'No manuscript files were found in this ZIP archive.',
    details: 'Expected files containing "manuscript" in the filename'
  },
  multiple_manuscripts: {
    title: 'Manuscript Validation Issues Detected',
    description: 'Multiple manuscript files were found in this ZIP archive.',
    details: 'Expected exactly 1 manuscript file'
  },
  missing_fp: {
    title: 'First Page Validation Issues Detected',
    description: 'First Page file is missing from this ZIP archive.',
    details: 'Expected files containing "FP", "FirstPage", or "TitlePage" in the filename'
  },
  no_manuscript_and_fp: {
    title: 'Manuscript & First Page Validation Issues Detected',
    description: 'Both manuscript and First Page files are missing from this ZIP archive.',
    details: 'Expected both manuscript and FP files'
  },
  multiple_fp: {
    title: 'First Page Validation Issues Detected',
    description: 'Multiple First Page files were found in this ZIP archive.',
    details: 'Expected exactly 1 First Page file'
  },
  validation_failed: {
    title: 'Manuscript Validation Issues Detected',
    description: 'Unable to properly validate the ZIP archive structure.',
    details: 'Technical validation error occurred'
  },
  fp_validation_failed: {
    title: 'First Page Validation Issues Detected',
    description: 'Unable to properly validate First Page files.',
    details: 'Technical FP validation error occurred'
  }
};

/**
 * Get validation warning message for UI display
 * @param {Object} validationResult - Validation result object
 * @returns {Object} Message object with title, description, and details
 */
export const getValidationMessage = (validationResult) => {
  if (!validationResult || validationResult.can_proceed) {
    return null;
  }
  
  const messageTemplate = VALIDATION_MESSAGES[validationResult.error_type] || VALIDATION_MESSAGES.validation_failed;
  
  return {
    ...messageTemplate,
    description: validationResult.error_message || messageTemplate.description
  };
};
