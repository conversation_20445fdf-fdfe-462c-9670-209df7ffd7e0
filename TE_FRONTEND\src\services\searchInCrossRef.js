import { crossRef } from "../constants/urls";
import {
  validate<PERSON><PERSON>or<PERSON>ames,
  delayedFetch,
  abbreviatePageNumbers,
  customEndsWith,
  capitalizeAfterColon,
} from "./styling";
import { searchInGenAI, searchJournalInGenAI } from "./fetchFromGenAI";
// Journal mappings are now handled automatically by the database system

async function fetchAndExtractCrossRefData(searchTerm, ref) {
  try {
    // Remove leading index (e.g., '9. ' or '12. ')
    const cleanedSearchTerm = (searchTerm || '').replace(/^\d+\.\s*/, '');
    // Construct the API URL dynamically
    const baseUrl = `${crossRef}?query=`;
    const encodedSearchTerm = encodeURIComponent(cleanedSearchTerm);
    const fullUrl = `${baseUrl}${encodedSearchTerm}&rows=1`;

    // Fetch data from the API
    const response = await delayedFetch(fullUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    // Parse the JSON response
    const data = await response.json();

    // Extract citation details
    return await extractCitationDetails(data, ref);
  } catch (error) {
    // Handle error fetching CrossRef data
    return null;
  }
}

// Helper function to extract details
async function extractCitationDetails(data, ref) {
  if (
    !data ||
    !data.message ||
    !data.message.items ||
    data.message.items.length === 0
  ) {
    return null; // Return null if no valid data is found
  }
  const actualSearchTerm = data?.message?.query?.["search-terms"];
  const searchTerm = actualSearchTerm.toLowerCase();

  const item = data.message.items[0]; // Extract first item from response

  // Extracting required fields

  let authors = "";
  if (item.author) {
    authors = item.author
      .map((a) => {
        if (!a?.given || !a?.family) {
          return a?.family || a?.given || "";
        }
        const splitArr = a.given.split(" ");
        const name = splitArr
          ?.map((element) => {
            return element?.substring(0, 1)?.toUpperCase();
          })
          .join("");
        return `${a.family} ${name ? name.substring(0, 2) : ""}`;
      })
      .filter(author => author.trim() !== "") // Remove empty authors
      .join(", ");
  } else if (ref?.extractedData?.authors) {
    authors = ref.extractedData.authors;
  }

  const tempTitle =
    item?.["container-title"] || item?.["short-container-title"];

  const Jtitle = tempTitle[0].replaceAll("&amp;", "&");

  // Use database-backed journal system
  const journalToLookup = Jtitle || actualSearchTerm;
  const journalName = await searchJournalInGenAI(journalToLookup);

  // Journal mapping is now handled automatically by searchJournalInGenAI

  const year = getPublishedOrIndexedYear(data) || "";

  const volume = item.volume || item["edition-number"] || "";
  let page = item?.page || "";
  // Check if page needs to be taken from extracted data
  if (!page.includes("-") && ref.extractedData.pages?.includes("-")) {
    page = ref.extractedData.pages;
  }
  page = abbreviatePageNumbers(page);

  let title = "";
  if (item.title && item.subtitle) {
    title = `${item.title[0]}: ${item.subtitle[0]}`;
  } else if (item.title) {
    title = item.title[0];
  }

  const findTitle = searchTerm.includes(title.toLowerCase());
  if (!findTitle) {
    return null;
  }

  if (!customEndsWith(title, ".")) {
    title += ".";
  }
  title = capitalizeAfterColon(title);
  let finalAutor = validateAuthorNames(authors);
  if (!customEndsWith(finalAutor, ".")) {
    finalAutor += ".";
  }

  return {
    finalStr: `${finalAutor || ""} ${title || ""} ${
      journalName ||
      item?.["short-container-title"] ||
      item?.["container-title"]
    } ${year ? year + ";" : ""}${volume || ""}${page ? ":" + page : ""}.`,
    data,
  };
}

// Example Usage
function getPublishedOrIndexedYear(data) {
  if (!data?.message?.items?.length) return null;

  const item = data.message.items[0];

  // Priority order: published -> issued -> indexed
  const yearSources = [
    item?.["published-print"]?.["date-parts"],
    item?.published?.["date-parts"],
    item?.issued?.["date-parts"],
    item?.indexed?.["date-parts"],
  ];

  for (const datePart of yearSources) {
    if (
      Array.isArray(datePart) &&
      datePart.length > 0 &&
      datePart[0].length > 0
    ) {
      return datePart[0][0]; // Extract the year
    }
  }

  return null;
}

export const searchInCrossRef = async (references, setProgressStep) => {
  const crossRef = [];
  const notFoundSCArray = [];
  let crossrefCurrent = 0;
  const total = references.length;
  for (const ref of references) {
    const found = await fetchAndExtractCrossRefData(ref.term, ref).then(
      (data) => data
    );
    crossrefCurrent++;
    if (setProgressStep) {
      setProgressStep({
        step: 'crossref',
        description: `Searching CrossRef ${crossrefCurrent}/${total}`,
        current: crossrefCurrent,
        total
      });
    }
    if (found) {
      crossRef.push({ ...ref, ...found, type: "CROSSREF" });
    } else {
      ref.type = "NOT_FOUND";
      notFoundSCArray.push(ref);
    }
  }
  const { genAIArray } = await searchInGenAI(notFoundSCArray, true);
  return { crossRef, notFoundSCArray: genAIArray };
};
