.individual-zip-upload {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #f9fafb;
  min-height: 100vh;
}

.upload-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.upload-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.upload-icon {
  font-size: 2.5rem;
}

.upload-description {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  margin-bottom: 1.5rem;
}

.error-message svg {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.upload-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.required {
  color: #ef4444;
}

.drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 3rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background-color: #fafafa;
  cursor: pointer;
}

.drop-zone:hover,
.drop-zone.drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon-large {
  width: 3rem;
  height: 3rem;
  color: #9ca3af;
}

.drop-text {
  font-size: 1rem;
  color: #374151;
  margin: 0;
}

.browse-link {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 500;
}

.browse-link:hover {
  color: #1d4ed8;
}

.drop-subtext {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.file-input-hidden {
  display: none;
}

.selected-files {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.files-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.file-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.file-size {
  font-size: 0.75rem;
  color: #6b7280;
  flex-shrink: 0;
}

.remove-button {
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  transition: background-color 0.2s ease;
}

.remove-button:hover {
  background-color: #dc2626;
}

.json-upload-area {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background-color: #fafafa;
  transition: border-color 0.2s ease;
}

.json-upload-area:hover {
  border-color: #3b82f6;
}

.json-upload-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  flex: 1;
}

.json-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #3b82f6;
  flex-shrink: 0;
}

.json-text {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.remove-json-button {
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.remove-json-button:hover {
  background-color: #dc2626;
}

.json-error {
  color: #ef4444;
  font-size: 0.75rem;
  margin: 0.5rem 0 0 0;
}

.json-success {
  color: #059669;
  font-size: 0.75rem;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
}

.json-info {
  color: #6b7280;
  font-size: 0.75rem;
  margin: 0.5rem 0 0 0;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover:not(:disabled) {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

.back-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.proceed-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.proceed-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.proceed-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .individual-zip-upload {
    padding: 1rem;
  }
  
  .upload-header {
    padding: 1.5rem;
  }
  
  .upload-section {
    padding: 1.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 1rem;
  }
  
  .back-button,
  .proceed-button {
    width: 100%;
    justify-content: center;
  }
  
  .drop-zone {
    padding: 2rem 1rem;
  }
}
