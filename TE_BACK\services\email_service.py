"""
Email Service for TE Assignment notifications
Based on TE_AUTOMATION/emailer.js pattern
"""

import os
import smtplib
import logging
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class EmailService:
    """Email service for sending TE assignment notifications"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str, use_tls: bool = True):
        """
        Initialize email service
        
        Args:
            smtp_server: SMTP server hostname
            smtp_port: SMTP server port
            username: SMTP username
            password: SMTP password
            use_tls: Whether to use TLS encryption
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_tls = use_tls
    
    def create_te_assignment_email(self, te_name: str, te_email: str, article_ids: List[str],
                                 drive_link: str, batch_name: str, assigned_by: str,
                                 articles_with_authors: Optional[List[Dict]] = None,
                                 queries_file_path: Optional[str] = None,
                                 author_queries: Optional[List[Dict]] = None) -> MIMEMultipart:
        """
        Create HTML email for TE assignment notification

        Args:
            te_name: TE's name
            te_email: TE's email address
            article_ids: List of article IDs assigned
            drive_link: Google Drive folder link
            batch_name: Batch folder name
            assigned_by: Name of person who made the assignment
            articles_with_authors: Optional list of articles with author information
            queries_file_path: Optional path to queries.txt file to attach
            author_queries: Optional list of author queries for summary table

        Returns:
            MIMEMultipart email message
        """
        # Create message
        msg = MIMEMultipart('alternative')
        msg['From'] = self.username
        msg['To'] = te_email
        msg['Cc'] = "<EMAIL>, <EMAIL>, <EMAIL>"
        msg['Subject'] = f"TE Assignment - {batch_name} - {len(article_ids)} Articles"
        
        # Debug logging for email table generation
        logger.info(f"📧 Email table generation for {len(article_ids)} articles:")
        logger.info(f"   - articles_with_authors provided: {articles_with_authors is not None}")
        if articles_with_authors:
            logger.info(f"   - articles_with_authors count: {len(articles_with_authors)}")
            logger.info(f"   - articles with authors: {len([a for a in articles_with_authors if a.get('authors')])}")
        else:
            logger.info(f"   - articles_with_authors is None/empty - using simple table")

        # Create article table rows with individual author rows
        if articles_with_authors:
            # Enhanced table with individual rows for each author (simplified)
            table_headers = """
                        <tr style='background-color: #e9ecef;'>
                            <th style='border: 1px solid #dddddd; padding: 12px; text-align: center;'>Article ID</th>
                            <th style='border: 1px solid #dddddd; padding: 12px; text-align: center;'>Author Name</th>
                            <th style='border: 1px solid #dddddd; padding: 12px; text-align: center;'>Author Email</th>
                            <th style='border: 1px solid #dddddd; padding: 12px; text-align: center;'>Copyright</th>
                        </tr>"""

            table_rows = ""
            for article in articles_with_authors:
                article_id = article.get('article_id', 'Unknown')
                authors = article.get('authors', [])

                if authors:
                    # Create a row for each author
                    for idx, author in enumerate(authors):
                        author_name = author.get('name', 'Unknown Author')
                        author_email = author.get('email', 'N/A')
                        copyright_status = author.get('copyright_status', 'UNKNOWN')

                        # Format copyright status with color coding
                        if copyright_status == 'YES':
                            copyright_html = """<span style='background-color: #d4edda;
                                               color: #155724;
                                               padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;'>
                                YES
                            </span>"""
                        elif copyright_status == 'NO':
                            copyright_html = """<span style='background-color: #f8d7da;
                                               color: #721c24;
                                               padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;'>
                                NO
                            </span>"""
                        else:
                            copyright_html = """<span style='background-color: #e2e3e5;
                                               color: #6c757d;
                                               padding: 4px 8px; border-radius: 4px; font-size: 12px;'>
                                UNKNOWN
                            </span>"""

                        # Only show article ID in the first row for each article
                        if idx == 0:
                            article_id_display = f"<strong>{article_id}</strong>"
                        else:
                            article_id_display = ""

                        table_rows += f"""
                        <tr>
                            <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'>{article_id_display}</td>
                            <td style='border: 1px solid #dddddd; padding: 8px; text-align: left;'>{author_name}</td>
                            <td style='border: 1px solid #dddddd; padding: 8px; text-align: left;'>{author_email}</td>
                            <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'>{copyright_html}</td>
                        </tr>"""
                else:
                    # No authors - show article with empty author info
                    table_rows += f"""
                    <tr>
                        <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'><strong>{article_id}</strong></td>
                        <td style='border: 1px solid #dddddd; padding: 8px; text-align: center; color: #6c757d; font-style: italic;'>No author information</td>
                        <td style='border: 1px solid #dddddd; padding: 8px; text-align: center; color: #6c757d;'>N/A</td>
                        <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'>
                            <span style='background-color: #e2e3e5; color: #6c757d; padding: 4px 8px; border-radius: 4px; font-size: 12px;'>N/A</span>
                        </td>
                    </tr>"""
        else:
            # Simple table (fallback to original format)
            table_headers = """
                        <tr style='background-color: #e9ecef;'>
                            <th style='border: 1px solid #dddddd; padding: 12px; text-align: center;'>#</th>
                            <th style='border: 1px solid #dddddd; padding: 12px; text-align: center;'>Article ID</th>
                        </tr>"""

            table_rows = ""
            for index, article_id in enumerate(article_ids, 1):
                table_rows += f"""
                <tr>
                    <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'>{index}</td>
                    <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'>{article_id}</td>
                </tr>"""
        
        # HTML email template (similar to TE automation)
        html_content = f"""
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px;'>
                    TE Assignment Notification
                </h2>
                
                <p>Hi <strong>{te_name}</strong>,</p>

                <p>You have been assigned <strong>{len(article_ids)} articles</strong> for technical editing.</p>

                <div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <h3 style='margin-top: 0; color: #495057;'>Assignment Details:</h3>
                    <ul style='margin: 10px 0;'>
                        <li><strong>Batch:</strong> {batch_name}</li>
                        <li><strong>Articles Count:</strong> {len(article_ids)}</li>
                        <li><strong>Assigned By:</strong> {assigned_by}</li>
                        <li><strong>Assignment Date:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                    </ul>
                </div>

                <h3 style='color: #495057;'>Article List:</h3>
                {"<p style='font-size: 13px; color: #666; margin: 5px 0;'><em>Note: Each article may have multiple authors listed separately in the table below.</em></p>" if articles_with_authors else ""}
                <table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>
                    <thead>
                        {table_headers}
                    </thead>
                    <tbody>
                        {table_rows}
                    </tbody>
                </table>

                {"<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 15px 0;'><small><strong>Note:</strong> Please review the copyright status for each author. Articles with authors showing 'NO' copyright consent may require additional follow-up.</small></div>" if articles_with_authors else ""}

                {self._generate_queries_section(author_queries)}

                <div style='background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <h3 style='margin-top: 0; color: #155724;'>📁 Access Your Files:</h3>
                    <p style='margin: 10px 0;'>
                        All article files have been uploaded to Google Drive. Click the link below to access them:
                    </p>
                    <p style='text-align: center; margin: 20px 0;'>
                        <a href="{drive_link}" 
                           style='display: inline-block; background-color: #28a745; color: white; padding: 12px 24px; 
                                  text-decoration: none; border-radius: 5px; font-weight: bold;'>
                            🔗 Open Google Drive Folder
                        </a>
                    </p>
                    <p style='font-size: 12px; color: #6c757d; margin-top: 15px;'>
                        <strong>Note:</strong> You can download all files or work directly from Google Drive. 
                        Please ensure you have access to the shared folder.
                    </p>
                </div>
                
                <div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <h4 style='margin-top: 0; color: #856404;'>📋 Next Steps:</h4>
                    <ol style='margin: 10px 0; padding-left: 20px;'>
                        <li>Access the Google Drive folder using the link above</li>
                        <li>Download the article files to your local system</li>
                        <li>Complete the technical editing for all assigned articles</li>
                        <li>Upload the completed files back to the system when ready</li>
                    </ol>
                </div>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #dee2e6;'>
                
                <p style='font-size: 12px; color: #6c757d; text-align: center;'>
                    This email was automatically generated by the EDITINK TE Assignment System.<br>
                    If you have any questions, please contact the editorial team.
                </p>
                
                <p style='font-size: 12px; color: #6c757d; text-align: center; margin-top: 20px;'>
                    <strong>Assignment ID:</strong> {batch_name}<br>
                    <strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                </p>
            </div>
        </body>
        </html>
        """
        
        # Create plain text version
        text_content = f"""
        TE Assignment Notification
        
        Hi {te_name},
        
        You have been assigned {len(article_ids)} articles for technical editing.
        
        Assignment Details:
        - Batch: {batch_name}
        - Articles Count: {len(article_ids)}
        - Assigned By: {assigned_by}
        - Assignment Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        
        Article List:
        {chr(10).join([f"{i+1}. {article_id}" for i, article_id in enumerate(article_ids)])}
        
        Access Your Files:
        All article files have been uploaded to Google Drive.
        Link: {drive_link}
        
        Next Steps:
        1. Access the Google Drive folder using the link above
        2. Download the article files to your local system
        3. Complete the technical editing for all assigned articles
        4. Upload the completed files back to the system when ready
        
        This email was automatically generated by the EDITINK TE Assignment System.
        Assignment ID: {batch_name}
        Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        # Attach both versions
        msg.attach(MIMEText(text_content, 'plain'))
        msg.attach(MIMEText(html_content, 'html'))

        # Attach queries file if provided
        if queries_file_path and os.path.exists(queries_file_path):
            try:
                with open(queries_file_path, 'rb') as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {os.path.basename(queries_file_path)}'
                    )
                    msg.attach(part)
                    logger.info(f"Attached queries file: {queries_file_path}")
            except Exception as e:
                logger.error(f"Failed to attach queries file {queries_file_path}: {e}")

        return msg

    def _generate_queries_section(self, author_queries: Optional[List[Dict]]) -> str:
        """
        Generate HTML section for author queries summary

        Args:
            author_queries: List of author query dictionaries

        Returns:
            HTML string for queries section
        """
        if not author_queries or len(author_queries) == 0:
            return ""

        # Filter out validation_error queries (they're not author-related)
        author_queries = [q for q in author_queries if q.get('query_type') != 'validation_error']

        if len(author_queries) == 0:
            return ""

        # Group queries by article ID for better display
        queries_by_article = {}
        for query in author_queries:
            article_id = query.get('article_id', 'Unknown')
            if article_id not in queries_by_article:
                queries_by_article[article_id] = []
            queries_by_article[article_id].append(query)

        # Generate table rows
        table_rows = ""
        for article_id, queries in queries_by_article.items():
            # Group queries by type and text for this article
            grouped_queries = {}
            for query in queries:
                query_type = query.get('query_type', 'unknown').replace('_', ' ').title()
                query_text = query.get('query_text', 'No details available')
                author_name = query.get('author_name', 'Unknown')

                # Create a key based on type and text
                key = f"{query_type}|{query_text}"
                if key not in grouped_queries:
                    grouped_queries[key] = {
                        'type': query_type,
                        'text': query_text,
                        'authors': []
                    }
                grouped_queries[key]['authors'].append(author_name)

            # Generate rows for grouped queries
            for idx, (key, group) in enumerate(grouped_queries.items()):
                # Only show article ID in the first row for each article
                article_id_display = f"<strong>{article_id}</strong>" if idx == 0 else ""

                # Show the full query text (Details from attachment)
                query_details = group['text'] if group['text'] and group['text'] != 'No details available' else 'No details available'

                table_rows += f"""
                <tr>
                    <td style='border: 1px solid #dddddd; padding: 8px; text-align: center; vertical-align: top;'>{article_id_display}</td>
                    <td style='border: 1px solid #dddddd; padding: 8px; text-align: left;'>{query_details}</td>
                </tr>"""

        return f"""
        <div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
            <h3 style='margin-top: 0; color: #856404;'>⚠️ Author Queries Raised:</h3>
            <p style='margin: 10px 0; color: #856404;'>
                The following queries have been raised regarding author information for some articles.
                Please review these carefully during your editing process.
            </p>
            <table style='border-collapse: collapse; width: 100%; margin: 15px 0;'>
                <thead>
                    <tr style='background-color: #ffeaa7;'>
                        <th style='border: 1px solid #dddddd; padding: 12px; text-align: center; width: 20%;'>Article ID</th>
                        <th style='border: 1px solid #dddddd; padding: 12px; text-align: left; width: 80%;'>Query Details</th>
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
            </table>
            <p style='font-size: 12px; color: #856404; margin-top: 15px;'>
                <strong>Note:</strong> A text file with the same query details is also attached for your reference.
            </p>
        </div>"""

    def send_email(self, message: MIMEMultipart) -> bool:
        """
        Send email message

        Args:
            message: MIMEMultipart email message

        Returns:
            True if sent successfully, False otherwise
        """
        try:
            logger.info(f"Attempting to send email to {message['To']} with CC: {message.get('Cc', 'None')}")
            logger.info(f"SMTP Config: {self.smtp_server}:{self.smtp_port}, TLS: {self.use_tls}")

            # Create SMTP connection
            if self.use_tls:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()

            # Login and send
            server.login(self.username, self.password)
            server.send_message(message)
            server.quit()

            logger.info(f"✅ Email sent successfully to {message['To']} (CC: {message.get('Cc', 'None')})")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to send email to {message['To']}: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def send_te_assignment_notification(self, te_name: str, te_email: str, article_ids: List[str],
                                     drive_link: str, batch_name: str, assigned_by: str,
                                     articles_with_authors: Optional[List[Dict]] = None,
                                     queries_file_path: Optional[str] = None,
                                     author_queries: Optional[List[Dict]] = None) -> bool:
        """
        Send TE assignment notification email

        Args:
            te_name: TE's name
            te_email: TE's email address
            article_ids: List of article IDs assigned
            drive_link: Google Drive folder link
            batch_name: Batch folder name
            assigned_by: Name of person who made the assignment
            articles_with_authors: Optional list of articles with author information
            queries_file_path: Optional path to queries.txt file to attach
            author_queries: Optional list of author queries for summary table

        Returns:
            True if sent successfully, False otherwise
        """
        try:
            message = self.create_te_assignment_email(
                te_name, te_email, article_ids, drive_link, batch_name, assigned_by,
                articles_with_authors, queries_file_path, author_queries
            )
            return self.send_email(message)
            
        except Exception as e:
            logger.error(f"Failed to send TE assignment notification: {e}")
            return False

def create_email_service() -> EmailService:
    """
    Factory function to create EmailService instance with environment config
    """
    smtp_server = os.getenv('SMTP_SERVER', 'smtpout.secureserver.net')
    smtp_port = int(os.getenv('SMTP_PORT', '465'))
    smtp_username = os.getenv('SMTP_USERNAME')
    smtp_password = os.getenv('SMTP_PASSWORD')
    smtp_use_tls = os.getenv('SMTP_USE_TLS', 'True').lower() == 'true'

    logger.info(f"Creating EmailService with server={smtp_server}, port={smtp_port}, username={smtp_username}, use_tls={smtp_use_tls}")

    if not smtp_username or not smtp_password:
        logger.error("SMTP_USERNAME or SMTP_PASSWORD not found in environment variables")
        raise ValueError("SMTP_USERNAME and SMTP_PASSWORD environment variables are required")

    logger.info("✅ EmailService created successfully")
    return EmailService(smtp_server, smtp_port, smtp_username, smtp_password, smtp_use_tls)
