const jwt = require("jsonwebtoken");
require("dotenv").config();

// Google Sheets Configuration
const GOOGLE_SHEETS_SUBSCRIBERS_ID = process.env.GOOGLE_SHEETS_ID || '1sdERmtt3I6QDV3Xtw1NzJswQ0MhrbiGg6mf5mTiYlSU';
const GOOGLE_SHEETS_SUBSCRIBERS_PAGE = process.env.GOOGLE_SHEETS_PAGE || 'TESTAUTOMATION!A2';
const GOOGLE_SHEETS_SERVICE_ACCOUNT = process.env.GOOGLE_SHEETS_SERVICE_ACCOUNT || '115083737441987478188';
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

async function getGoogleSheetsAccessToken() {
    const iat = Math.floor(Date.now() / 1000)
    const exp = iat + 3600
    const jwtToken = jwt.sign(
        {
            iss: GOOGLE_SHEETS_SERVICE_ACCOUNT,
            scope: "https://www.googleapis.com/auth/spreadsheets",
            aud: "https://accounts.google.com/o/oauth2/token",
            exp,
            iat,
        },
        GOOGLE_SHEETS_PRIVATE_KEY,
        { algorithm: "RS256" },
    )
    const { access_token } = await fetch(
        "https://accounts.google.com/o/oauth2/token",
        {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: new URLSearchParams({
                grant_type:
                    "urn:ietf:params:oauth:grant-type:jwt-bearer",
                assertion: jwtToken,
            }),
        },
    ).then((response) => response.json())
    return access_token
}

/**
 * Append data to Google Sheets
 * @param {Array<Array<string>>} values - 2D array of values to append
 * @param {Object} options - Optional configuration
 * @param {string} options.sheetPage - Override default sheet page
 * @returns {Promise<Object>} Response from Google Sheets API
 */
async function appendDataToGoogleSheet(values, options = {}) {
    try {
        console.log('📊 Updating Google Sheets with download data...');

        if (!values || !Array.isArray(values) || values.length === 0) {
            throw new Error('Values must be a non-empty 2D array');
        }

        const accessToken = await getGoogleSheetsAccessToken();
        const sheetPage = options.sheetPage || GOOGLE_SHEETS_SUBSCRIBERS_PAGE;

        const response = await fetch(
            `https://sheets.googleapis.com/v4/spreadsheets/${GOOGLE_SHEETS_SUBSCRIBERS_ID}/values/${sheetPage}:append?valueInputOption=USER_ENTERED`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                },
                body: JSON.stringify({
                    range: sheetPage,
                    values
                }),
            },
        );

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Google Sheets API error: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        console.log(`✅ Successfully updated Google Sheets: ${result.updates?.updatedRows || 0} rows added`);
        return result;

    } catch (error) {
        console.error('❌ Failed to update Google Sheets:', error.message);
        throw error;
    }
}



/**
 * Update status for an existing article in Google Sheets
 * Only requires Article ID - automatically sets Received Date to today and calculates Due Date (+3 days)
 * @param {string} articleId - Article ID to find and update
 * @param {Object} options - Optional configuration
 * @param {string} options.sheetPage - Override default sheet page
 * @returns {Promise<Object>} Response from Google Sheets API
 */
async function updateArticleStatus(articleId, options = {}) {
    try {
        console.log(`📝 Updating status for article: ${articleId}`);

        if (!articleId) {
            throw new Error('Article ID is required');
        }

        // Automatically set received date to today
        const receivedDate = new Date().toLocaleDateString('en-GB'); // DD/MM/YYYY format

        const accessToken = await getGoogleSheetsAccessToken();
        const sheetPage = options.sheetPage || GOOGLE_SHEETS_SUBSCRIBERS_PAGE;

        // Extract sheet name from page reference (e.g., "TESTAUTOMATION!A2" -> "TESTAUTOMATION")
        const sheetName = sheetPage.split('!')[0];

        // First, get all data from the sheet to find the article
        const getResponse = await fetch(
            `https://sheets.googleapis.com/v4/spreadsheets/${GOOGLE_SHEETS_SUBSCRIBERS_ID}/values/${sheetName}`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                }
            }
        );

        if (!getResponse.ok) {
            const errorText = await getResponse.text();
            throw new Error(`Failed to read Google Sheets: ${getResponse.status} - ${errorText}`);
        }

        const sheetData = await getResponse.json();
        const rows = sheetData.values || [];

        // Find the row with matching Article ID (assuming Article ID is in column B, index 1)
        let targetRowIndex = -1;
        for (let i = 0; i < rows.length; i++) {
            if (rows[i] && rows[i][1] === articleId) {
                targetRowIndex = i + 1; // Google Sheets uses 1-based indexing
                break;
            }
        }

        if (targetRowIndex === -1) {
            throw new Error(`Article ID ${articleId} not found in Google Sheets`);
        }

        // Calculate due date (received date + 3 days)
        const [day, month, year] = receivedDate.split('/');
        const receivedDateObj = new Date(year, month - 1, day);
        const dueDateObj = new Date(receivedDateObj);
        dueDateObj.setDate(dueDateObj.getDate() + 3);

        const dueDate = dueDateObj.toLocaleDateString('en-GB'); // DD/MM/YYYY format

        // Update the specific row with new status data
        // Assuming columns: A=Date, B=Article ID, C=Journal, D=Status, E=Author Count, F=Target Date, G=Received Date, H=Due Date
        const updateRange = `${sheetName}!G${targetRowIndex}:H${targetRowIndex}`;
        const updateValues = [[receivedDate, dueDate]];

        const updateResponse = await fetch(
            `https://sheets.googleapis.com/v4/spreadsheets/${GOOGLE_SHEETS_SUBSCRIBERS_ID}/values/${updateRange}?valueInputOption=USER_ENTERED`,
            {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                },
                body: JSON.stringify({
                    range: updateRange,
                    values: updateValues
                }),
            }
        );

        if (!updateResponse.ok) {
            const errorText = await updateResponse.text();
            throw new Error(`Failed to update Google Sheets: ${updateResponse.status} - ${errorText}`);
        }

        const result = await updateResponse.json();
        console.log(`✅ Successfully updated status for ${articleId}: Received=${receivedDate}, Due=${dueDate}`);
        return result;

    } catch (error) {
        console.error(`❌ Failed to update article status for ${articleId}:`, error.message);
        throw error;
    }
}

// Export functions for CommonJS
module.exports = {
    appendDataToGoogleSheet,
    updateArticleStatus
};


