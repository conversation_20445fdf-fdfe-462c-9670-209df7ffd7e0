/**
 * Document Parser Service
 * Handles parsing of .docx files to extract text content and author information
 */

/**
 * Parse .docx file content to extract text
 * Uses mammoth.js for .docx parsing
 * @param {ArrayBuffer} fileBuffer - File buffer from ZIP entry
 * @returns {Promise<string>} Extracted text content
 */
export const parseDocxContent = async (fileBuffer) => {
  try {
    // For now, we'll use a simple text extraction approach
    // In production, you'd want to install mammoth.js: npm install mammoth
    
    // Since we can't install packages right now, let's create a basic parser
    // that works with the existing ZIP extraction
    
    // Convert ArrayBuffer to text (basic approach)
    const uint8Array = new Uint8Array(fileBuffer);
    let text = '';
    
    // Try to extract readable text from the binary data
    // This is a simplified approach - mammoth.js would be much better
    for (let i = 0; i < uint8Array.length; i++) {
      const char = uint8Array[i];
      // Only include printable ASCII characters
      if (char >= 32 && char <= 126) {
        text += String.fromCharCode(char);
      } else if (char === 10 || char === 13) {
        text += ' '; // Replace newlines with spaces
      }
    }
    
    // Clean up the extracted text
    text = text
      .replace(/[^\w\s.,;:()\-]/g, ' ') // Remove non-standard characters
      .replace(/\s+/g, ' ') // Collapse multiple spaces
      .trim();
    
    console.log('Extracted text length:', text.length);
    return text;
    
  } catch (error) {
    console.error('Error parsing DOCX content:', error);
    return '';
  }
};

/**
 * Enhanced DOCX parser using mammoth.js (when available)
 * @param {ArrayBuffer} fileBuffer - File buffer from ZIP entry
 * @returns {Promise<string>} Extracted text content
 */
export const parseDocxWithMammoth = async (fileBuffer) => {
  try {
    // This would be used if mammoth.js is installed
    // const mammoth = require('mammoth');
    // const result = await mammoth.extractRawText({ arrayBuffer: fileBuffer });
    // return result.value;
    
    // For now, fall back to basic parsing
    return await parseDocxContent(fileBuffer);
  } catch (error) {
    console.error('Error with mammoth parsing:', error);
    return await parseDocxContent(fileBuffer);
  }
};

/**
 * Extract author names from document content using various patterns
 * @param {string} content - Document text content
 * @param {string} articleId - Article ID for logging
 * @returns {Array} Array of extracted author names
 */
export const extractAuthorsFromContent = (content, articleId) => {
  if (!content || content.length < 10) {
    console.warn(`No content to parse for ${articleId}`);
    return [];
  }

  const authors = new Set(); // Use Set to avoid duplicates
  
  // Pattern 1: "Authors: <AUTHORS>
  const authorSectionPattern = /(?:authors?|author\s*\(s\)\s*):?\s*([^.]+)/gi;
  let match = authorSectionPattern.exec(content);
  if (match) {
    const authorText = match[1];
    const names = extractNamesFromText(authorText);
    names.forEach(name => authors.add(name));
  }

  // Pattern 2: "By:" followed by names
  const byPattern = /by:\s*([^.]+)/gi;
  match = byPattern.exec(content);
  if (match) {
    const authorText = match[1];
    const names = extractNamesFromText(authorText);
    names.forEach(name => authors.add(name));
  }

  // Pattern 3: Look for "Dr" titles in the first 500 characters
  const firstPart = content.substring(0, 500);
  const drPattern = /Dr\.?\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*)/g;
  let drMatch;
  while ((drMatch = drPattern.exec(firstPart)) !== null) {
    const name = `Dr ${drMatch[1]}`;
    if (name.length > 5 && name.length < 50) { // Reasonable name length
      authors.add(name);
    }
  }

  // Pattern 4: Look for common name patterns (First Last, First Middle Last)
  const namePattern = /\b([A-Z][a-z]+\s+(?:[A-Z][a-z]*\s+)*[A-Z][a-z]+)\b/g;
  const firstSection = content.substring(0, 300); // Look in first 300 chars
  let nameMatch;
  while ((nameMatch = namePattern.exec(firstSection)) !== null) {
    const name = nameMatch[1];
    // Filter out common false positives
    if (!isCommonWord(name) && name.split(' ').length >= 2 && name.length < 40) {
      authors.add(name);
    }
  }

  const authorArray = Array.from(authors);
  console.log(`Extracted ${authorArray.length} authors from ${articleId}:`, authorArray);
  
  return authorArray;
};

/**
 * Extract names from a text string (comma or "and" separated)
 * @param {string} text - Text containing names
 * @returns {Array} Array of names
 */
const extractNamesFromText = (text) => {
  if (!text) return [];
  
  // Split by common separators
  const names = text
    .split(/[,;&]|\sand\s|\s&\s/) // Split by comma, semicolon, "and", "&"
    .map(name => name.trim())
    .filter(name => name.length > 2)
    .map(name => {
      // Clean up each name
      name = name.replace(/^\d+\.?\s*/, ''); // Remove leading numbers
      name = name.replace(/\s+/g, ' '); // Normalize spaces
      return name.trim();
    })
    .filter(name => name.length > 2 && name.length < 50);
  
  return names;
};

/**
 * Check if a word is a common word that shouldn't be considered a name
 * @param {string} word - Word to check
 * @returns {boolean} True if it's a common word
 */
const isCommonWord = (word) => {
  const commonWords = [
    'Abstract', 'Introduction', 'Methods', 'Results', 'Discussion', 'Conclusion',
    'Background', 'Objective', 'Material', 'Study', 'Analysis', 'Research',
    'Clinical', 'Medical', 'Patient', 'Treatment', 'Therapy', 'Diagnosis',
    'Case Report', 'Original Article', 'Review Article', 'Short Communication',
    'Department', 'University', 'Hospital', 'Institute', 'College', 'School',
    'Corresponding Author', 'Email Address', 'Phone Number', 'Received Date'
  ];
  
  return commonWords.some(common => 
    word.toLowerCase().includes(common.toLowerCase()) ||
    common.toLowerCase().includes(word.toLowerCase())
  );
};

/**
 * Parse First Page file to extract author information
 * @param {Object} fpFile - First Page file object with zipEntry
 * @returns {Promise<Array>} Array of extracted author names
 */
export const parseFirstPageAuthors = async (fpFile, articleId) => {
  try {
    if (!fpFile || !fpFile.zipEntry) {
      console.warn(`No FP file or zipEntry for ${articleId}`);
      return [];
    }

    // Extract file content
    const fileBuffer = await fpFile.zipEntry.async('arraybuffer');
    
    // Parse content based on file type
    let content = '';
    if (fpFile.type === 'docx') {
      content = await parseDocxContent(fileBuffer);
    } else if (fpFile.type === 'txt') {
      const uint8Array = new Uint8Array(fileBuffer);
      content = new TextDecoder().decode(uint8Array);
    } else {
      console.warn(`Unsupported FP file type: ${fpFile.type} for ${articleId}`);
      return [];
    }

    // Extract authors from content
    return extractAuthorsFromContent(content, articleId);
    
  } catch (error) {
    console.error(`Error parsing FP file for ${articleId}:`, error);
    return [];
  }
};

/**
 * Get file content as text for validation purposes
 * @param {Object} file - File object with zipEntry
 * @returns {Promise<string>} File content as text
 */
export const getFileContent = async (file) => {
  try {
    if (!file || !file.zipEntry) {
      return '';
    }

    const fileBuffer = await file.zipEntry.async('arraybuffer');
    
    if (file.type === 'docx') {
      return await parseDocxContent(fileBuffer);
    } else if (file.type === 'txt') {
      const uint8Array = new Uint8Array(fileBuffer);
      return new TextDecoder().decode(uint8Array);
    } else {
      return '';
    }
  } catch (error) {
    console.error('Error getting file content:', error);
    return '';
  }
};
