import React, { useState } from 'react';
import '../../App.css';
import { getCurrentRole } from '../../utils/appUtils';

/**
 * Reference Quality Checker Component
 * Analyzes reference quality and provides suggestions
 */
import { getOpenAIReview } from '../../services/fetchFromGenAI';

import { checkQuality } from '../../utils/quality';

/**
 * Enhanced Reference Editor Component
 * Provides inline editing capabilities for references
 */
const ReferenceEditor = ({ reference, onSave, onCancel }) => {
  const [editedReference, setEditedReference] = useState(reference.finalStr || reference.term);
  const [isEditing, setIsEditing] = useState(false);
  const currentRole = getCurrentRole();
  const isAdminMode = currentRole === 'admin';

  const handleSave = () => {
    onSave(reference.ind, editedReference);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedReference(reference.finalStr || reference.term);
    setIsEditing(false);
    if (onCancel) onCancel();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  if (!isEditing) {
    return (
      <div className="reference-display">
        <div className="reference-text">{reference.finalStr || reference.term}</div>
        {isAdminMode && (
          <div className="reference-actions">
            <button
              onClick={() => setIsEditing(true)}
              title="Edit reference (Click to edit)"
              className="edit-button"
            >
              ✏️ Edit
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="reference-editor">
      <textarea
        value={editedReference}
        onChange={(e) => setEditedReference(e.target.value)}
        onKeyDown={handleKeyPress}
        className="reference-textarea"
        rows={3}
        autoFocus
      />
      <div className="editor-buttons">
        <button
          onClick={handleSave}
          className="action-button save-button"
          title="Save changes (Ctrl+Enter)"
        >
          ✅
        </button>
        <button
          onClick={handleCancel}
          className="action-button cancel-button"
          title="Cancel editing (Escape)"
        >
          ❌
        </button>
      </div>
      <div className="editor-help">
        <small>Ctrl+Enter to save, Escape to cancel</small>
      </div>
    </div>
  );
};

export const ReferenceQualityChecker = ({ reference, onSave }) => {
  const [aiReview, setAiReview] = React.useState(null);
  const [loadingAI, setLoadingAI] = React.useState(false);
  const [errorAI, setErrorAI] = React.useState(null);

  // Use only the imported checkQuality
  const quality = checkQuality(reference);

  const getQualityClass = (score) => {
    if (score >= 85) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'warning';
    return 'error';
  };

  const getQualityIcon = (score) => {
    if (score >= 85) return '🌟';
    if (score >= 70) return '✅';
    if (score >= 50) return '⚠️';
    return '❌';
  };

  const qualityClass = getQualityClass(quality.score);
  const qualityIcon = getQualityIcon(quality.score);

  // Handler for AI review
  const handleAIReview = async () => {
    setLoadingAI(true);
    setErrorAI(null);
    try {
      const result = await getOpenAIReview(reference.term, reference.finalStr || reference.term);
      setAiReview(result);
    } catch (err) {
      setErrorAI('AI review failed');
    } finally {
      setLoadingAI(false);
    }
  };

  // Handler to accept AI suggestion
  const handleAcceptSuggestion = () => {
    if (aiReview && aiReview.suggestion && onSave) {
      onSave(reference.ind, aiReview.suggestion);
      setAiReview(null);
    }
  };

  return (
    <div className={`quality-indicator ${qualityClass}`}>
      <span
        title={`Quality Score: ${quality.score}%\n` +
               `Improvement: ${quality.breakdown.improvement}%\n` +
               `Structure: ${quality.breakdown.structure}%\n` +
               `Completeness: ${quality.breakdown.completeness}%\n` +
               (quality.issues.length > 0 ? `\nIssues: ${quality.issues.join(', ')}\n` : '') +
               (quality.suggestions.length > 0 ? `Suggestions: ${quality.suggestions.join(', ')}` : '')
        }
        className="quality-score"
      >
        {qualityIcon} {quality.score}%
      </span>
      {/* Show AI review button for low scores */}
      {quality.score < 90 && !aiReview && !loadingAI && (
        <button className="action-badge badge-search" style={{marginLeft: 8}} onClick={handleAIReview}>
          Get AI Suggestion
        </button>
      )}
      {loadingAI && <span style={{marginLeft: 8}}>AI reviewing...</span>}
      {errorAI && <span style={{marginLeft: 8, color: 'red'}}>{errorAI}</span>}
      {/* Show only AI suggestion and accept button */}
      {aiReview && aiReview.suggestion && (
        <div style={{marginTop: 8, background: '#f9fafb', padding: 8, borderRadius: 8}}>
          <div><b>AI Suggestion:</b> {aiReview.suggestion}</div>
          {/* AI score is intentionally hidden */}
          <button className="action-badge badge-save" style={{marginTop: 6}} onClick={handleAcceptSuggestion}>
            Accept Suggestion
          </button>
        </div>
      )}
    </div>
  );
};

/**
 * Reference Statistics Component
 * Shows statistics about the reference collection with clickable filtering
 */
export const ReferenceStatistics = ({ references, onFilterChange, activeFilter }) => {
  // Always compute score using checkQuality for accurate stats
  const stats = {
    total: references.length,
    pubmed: references.filter(r => r.type === 'FOUND').length,
    crossref: references.filter(r => r.type === 'CROSSREF').length,
    notFound: references.filter(r => r.type === 'NOT_FOUND').length,
    duplicates: references.filter(r => r.MarkType === 'DUPLICATE').length,
    urls: references.filter(r => r.type === 'URL').length,
    highConfidence: references.filter(r => checkQuality(r).score >= 90 && r.type !== 'NOT_FOUND').length,
    // Needs review: score < 90 OR type is NOT_FOUND
    needsReview: references.filter(r => checkQuality(r).score < 90 || r.type === 'NOT_FOUND').length,
  };

  const handleStatClick = (filterType) => {
    if (onFilterChange) {
      onFilterChange(filterType);
    }
  };

  // Define all stats except total in an array for easy filtering
  const statItems = [
    {
      key: 'highConfidence',
      label: 'High Confidence',
      value: stats.highConfidence,
      className: '',
      valueStyle: { color: '#28a745' },
      filter: 'high-confidence',
    },
    {
      key: 'needsReview',
      label: 'Needs Review',
      value: stats.needsReview,
      className: '',
      valueStyle: { color: '#f59e0b' },
      filter: 'needs-review',
    },
    {
      key: 'pubmed',
      label: 'PubMed:',
      value: stats.pubmed,
      className: 'pubmed',
      valueStyle: {},
      filter: 'pubmed',
    },
    {
      key: 'crossref',
      label: 'CrossRef:',
      value: stats.crossref,
      className: 'crossref',
      valueStyle: {},
      filter: 'crossref',
    },
    {
      key: 'notFound',
      label: 'Not Found:',
      value: stats.notFound,
      className: 'not-found',
      valueStyle: {},
      filter: 'not-found',
    },
    {
      key: 'duplicates',
      label: 'Duplicates:',
      value: stats.duplicates,
      className: 'duplicates',
      valueStyle: {},
      filter: 'duplicates',
    },
    {
      key: 'urls',
      label: 'URLs:',
      value: stats.urls,
      className: 'urls',
      valueStyle: {},
      filter: 'urls',
    },
  ];

  return (
    <div className="reference-statistics">
      <h3>Reference Statistics {activeFilter !== 'all' && <span className="filter-indicator">- Filtered by {activeFilter}</span>}</h3>
      <div className="stats-grid">
        {/* Always show total */}
        <div
          className={`stat-item ${stats.total > 0 ? 'clickable' : 'disabled'} ${activeFilter === 'all' ? 'active' : ''}`}
          onClick={stats.total > 0 ? () => handleStatClick('all') : undefined}
          title={stats.total > 0 ? "Click to show all references" : "No references available"}
        >
          <span className="stat-label">Total:</span>
          <span className="stat-value">{stats.total}</span>
        </div>
        {/* Render only stats with value > 0 */}
        {statItems.filter(item => item.value > 0).map(item => (
          <div
            key={item.key}
            className={`stat-item clickable ${activeFilter === item.filter ? 'active' : ''}`}
            onClick={() => handleStatClick(item.filter)}
            title={`Click to filter ${item.label.toLowerCase()}`}
          >
            <span className={`stat-label`}>{item.label}</span>
            <span className={`stat-value ${item.className}`} style={item.valueStyle}>{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReferenceEditor;
