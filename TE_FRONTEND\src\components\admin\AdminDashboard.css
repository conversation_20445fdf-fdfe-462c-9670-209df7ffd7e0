.admin-dashboard {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background-color: #f8fafc;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.dashboard-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.date-range-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.date-range-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-banner {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  margin-bottom: 2rem;
}

.error-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card-content {
  display: flex;
  flex-direction: column;
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stat-card-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0;
}

.stat-card-icon {
  width: 1.5rem;
  height: 1.5rem;
}

.stat-card-blue .stat-card-icon { color: #3b82f6; }
.stat-card-green .stat-card-icon { color: #10b981; }
.stat-card-purple .stat-card-icon { color: #8b5cf6; }
.stat-card-orange .stat-card-icon { color: #f59e0b; }

.stat-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

/* Chart Section */
.chart-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: end;
  padding: 1rem 0;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 0.5rem;
  width: 100%;
  height: 100%;
}

.chart-bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
}

.chart-bar {
  background: linear-gradient(to top, #3b82f6, #60a5fa);
  border-radius: 4px 4px 0 0;
  width: 100%;
  min-height: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.chart-bar:hover {
  background: linear-gradient(to top, #2563eb, #3b82f6);
}

.chart-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
  text-align: center;
}

/* Articles Section */
.articles-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.search-container {
  position: relative;
  width: 300px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  color: #9ca3af;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.articles-table-container {
  overflow-x: auto;
}

.articles-table {
  width: 100%;
  border-collapse: collapse;
}

.articles-table th {
  text-align: left;
  padding: 0.75rem;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  background-color: #f9fafb;
}

.articles-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.articles-table tr:hover {
  background-color: #f9fafb;
}

.article-id-cell {
  font-weight: 500;
}

.article-id {
  color: #3b82f6;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.reference-count-cell {
  text-align: center;
}

.reference-count {
  background-color: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.date-cell {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Enhanced Articles Data Table Customization */
.articles-data-table .article-id {
  color: #3b82f6;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Clickable Article ID */
.clickable-article-id {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
}

.clickable-article-id:hover {
  background-color: #dbeafe;
  color: #1e40af;
  text-decoration: underline;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.clickable-article-id:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.article-id-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.journal-name {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

/* Reference Summary */
.reference-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.reference-count-main {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.quality-breakdown {
  display: flex;
  gap: 0.25rem;
}

/* Quality Badges */
.quality-badge {
  padding: 0.125rem 0.375rem;
  border-radius: 8px;
  font-size: 0.625rem;
  font-weight: 600;
  text-align: center;
  min-width: 1.5rem;
}

.quality-badge-high {
  background-color: #dcfce7;
  color: #166534;
}

.quality-badge-review {
  background-color: #fef3c7;
  color: #92400e;
}

.quality-badge-empty {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Quality Score Cell */
.quality-score-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.quality-score {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.quality-rates {
  display: flex;
  gap: 0.375rem;
  font-size: 0.625rem;
}

.success-rate {
  color: #059669;
  font-weight: 500;
}

.review-rate {
  color: #d97706;
  font-weight: 500;
}

/* Source Breakdown */
.source-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.source-badge {
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-size: 0.625rem;
  font-weight: 500;
  text-align: center;
}

.source-pubmed {
  background-color: #dbeafe;
  color: #1e40af;
}

.source-crossref {
  background-color: #fef3c7;
  color: #92400e;
}

.source-not-found {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Status and Priority Badges */
.status-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-badge, .priority-badge {
  padding: 0.125rem 0.375rem;
  border-radius: 12px;
  font-size: 0.625rem;
  font-weight: 600;
  text-transform: uppercase;
  text-align: center;
}

.status-badge-green {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge-blue {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-badge-yellow {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge-red {
  background-color: #fee2e2;
  color: #dc2626;
}

.status-badge-orange {
  background-color: #fed7aa;
  color: #c2410c;
}

.status-badge-gray {
  background-color: #f3f4f6;
  color: #6b7280;
}

.priority-badge-red {
  background-color: #fee2e2;
  color: #dc2626;
}

.priority-badge-yellow {
  background-color: #fef3c7;
  color: #92400e;
}

.priority-badge-green {
  background-color: #dcfce7;
  color: #166534;
}

.priority-badge-gray {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Processing Info */
.processing-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.processed-by {
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
}

.processing-source {
  font-size: 0.625rem;
  color: #6b7280;
  font-style: italic;
}

.articles-data-table .date-cell {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 1rem;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .search-container {
    width: 100%;
  }

  .chart-bars {
    gap: 0.25rem;
  }

  .chart-label {
    font-size: 0.625rem;
  }
}
