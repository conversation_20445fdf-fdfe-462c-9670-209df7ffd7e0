/**
 * Utility functions for extracting metadata from ZIP files
 */

/**
 * Extract article metadata from a ZIP file
 * @param {File} zipFile - The ZIP file object
 * @returns {Promise<Object|null>} - The metadata object or null if not found
 */
export const extractMetadataFromZip = async (zipFile) => {
  try {
    const JSZip = (await import('jszip')).default;
    const zip = new JSZip();
    
    // Load the ZIP file
    const zipContent = await zip.loadAsync(zipFile);
    
    // Look for article_metadata.json file
    const metadataFile = zipContent.file('article_metadata.json');
    
    if (metadataFile) {
      const metadataText = await metadataFile.async('text');
      const metadata = JSON.parse(metadataText);
      
      console.log('✅ Found embedded metadata in ZIP:', metadata.article_id);
      return metadata;
    } else {
      console.log('⚠️ No embedded metadata found in ZIP');
      return null;
    }
  } catch (error) {
    console.error('❌ Error extracting metadata from ZIP:', error);
    return null;
  }
};

/**
 * Extract author information from ZIP metadata
 * @param {File} zipFile - The ZIP file object
 * @returns {Promise<Array>} - Array of author objects
 */
export const extractAuthorsFromZip = async (zipFile) => {
  try {
    const metadata = await extractMetadataFromZip(zipFile);
    
    if (metadata && metadata.authors && Array.isArray(metadata.authors)) {
      return metadata.authors;
    }
    
    return [];
  } catch (error) {
    console.error('❌ Error extracting authors from ZIP:', error);
    return [];
  }
};

/**
 * Check if a ZIP file contains embedded metadata
 * @param {File} zipFile - The ZIP file object
 * @returns {Promise<boolean>} - True if metadata is found
 */
export const hasEmbeddedMetadata = async (zipFile) => {
  try {
    const JSZip = (await import('jszip')).default;
    const zip = new JSZip();
    
    const zipContent = await zip.loadAsync(zipFile);
    const metadataFile = zipContent.file('article_metadata.json');
    
    return metadataFile !== null;
  } catch (error) {
    console.error('❌ Error checking for embedded metadata:', error);
    return false;
  }
};

/**
 * Get article summary from embedded metadata
 * @param {File} zipFile - The ZIP file object
 * @returns {Promise<Object|null>} - Article summary object
 */
export const getArticleSummaryFromZip = async (zipFile) => {
  try {
    const metadata = await extractMetadataFromZip(zipFile);
    
    if (metadata) {
      return {
        articleId: metadata.article_id,
        journal: metadata.journal,
        downloadDate: metadata.download_date,
        status: metadata.status,
        authors: metadata.authors || [],
        files: metadata.files || {},
        source: metadata.source || 'unknown'
      };
    }
    
    return null;
  } catch (error) {
    console.error('❌ Error getting article summary from ZIP:', error);
    return null;
  }
};

/**
 * Batch extract metadata from multiple ZIP files
 * @param {Array<File>} zipFiles - Array of ZIP file objects
 * @returns {Promise<Array>} - Array of metadata objects
 */
export const batchExtractMetadata = async (zipFiles) => {
  const results = [];
  
  for (const zipFile of zipFiles) {
    try {
      const metadata = await extractMetadataFromZip(zipFile);
      results.push({
        filename: zipFile.name,
        metadata: metadata,
        hasMetadata: metadata !== null
      });
    } catch (error) {
      console.error(`❌ Error processing ${zipFile.name}:`, error);
      results.push({
        filename: zipFile.name,
        metadata: null,
        hasMetadata: false,
        error: error.message
      });
    }
  }
  
  return results;
};
