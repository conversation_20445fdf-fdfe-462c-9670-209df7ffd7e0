@echo off
setlocal enabledelayedexpansion

:: Set console colors and title
title TE Automation - Upload to Drive
color 0B

echo.
echo ========================================
echo   TE AUTOMATION - UPLOAD TO DRIVE
echo ========================================
echo.

:: Ask user to choose folder selection method
echo How would you like to select the batch folder?
echo.
echo [1] Use default location (incoming folder)
echo [2] Browse and select custom folder
echo.
set /p "folder_choice=Enter your choice (1 or 2) [Press ENTER for 1]: "

if "!folder_choice!"=="" set "folder_choice=1"

if !folder_choice! EQU 2 goto SelectFromDownloads

:: Option 1 - Default location
:: Get current date in DD-MM-YYYY format
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value 2^>nul') do set "dt=%%a"
set "YYYY=!dt:~0,4!" & set "MM=!dt:~4,2!" & set "DD=!dt:~6,2!"
set "current_date=!DD!-!MM!-!YYYY!"

echo Current date: !current_date!
echo.
echo Enter the target date (DD-MM-YYYY) of the batch to upload
set /p "user_date=Target Date [Press ENTER for !current_date!]: "

if "!user_date!"=="" (
    set "target_date=!current_date!"
) else (
    set "target_date=!user_date!"
)
echo Using date: !target_date!
echo.

:: Ask for batch number
echo Enter the batch number (usually 1, 2, 3, etc.)
set /p "batch_num=Batch Number [Press ENTER for 1]: "

if "!batch_num!"=="" (
    set "batch_num=1"
)
echo Using batch number: !batch_num!
echo.

:: Construct the batch directory path
set "BATCH_DIR=incoming\!target_date!\!batch_num!"
echo.
echo Using default location: !BATCH_DIR!
echo.
goto ValidateBatchDir

:ValidateBatchDir
echo.
echo DEBUG: Final BATCH_DIR = !BATCH_DIR!
echo.

:: Check if the batch directory exists
if not exist "!BATCH_DIR!" (
    echo.
    echo ERROR: Batch directory not found: !BATCH_DIR!
    echo.
    echo Please ensure the folder contains batch files.
    echo.
    pause
    exit /b 1
)

:: Check if batch_summary.json exists
if not exist "!BATCH_DIR!\batch_summary.json" (
    echo.
    echo ERROR: batch_summary.json not found in !BATCH_DIR!
    echo.
    echo The selected folder must contain a batch_summary.json file.
    echo This file is created when articles are downloaded.
    echo.
    echo Please select a valid batch folder or run the download process first.
    echo.
    pause
    exit /b 1
)

:: Count articles in batch
for /f %%i in ('type "!BATCH_DIR!\batch_summary.json" ^| find /c "article_id"') do set "article_count=%%i"

echo.
echo ========================================
echo      BATCH INFORMATION
echo ========================================
echo.
echo 📁 Batch Directory: !BATCH_DIR!
echo 📊 Articles found: !article_count!
echo.

:: Confirm upload
set /p "confirm=Proceed with upload to Google Drive? (Y/N): "

if /i not "!confirm!"=="Y" (
    echo.
    echo ⏭️  Upload cancelled.
    echo.
    pause
    exit /b 0
)

:: Set environment variables for the upload test
set "BATCH_DIR_PATH=!BATCH_DIR!"

:: Create or update .env file
echo # TE Automation - Upload Configuration > .env
echo BATCH_DIR_PATH=!BATCH_DIR! >> .env

echo.
echo ========================================
echo     STARTING UPLOAD TO DRIVE
echo ========================================
echo.
echo 📁 Upload Path: !BATCH_DIR!
echo.

:: Check if node_modules exists
if not exist "node_modules" (
    echo ⚠️  Node modules not found. Installing dependencies...
    call npm install
    if errorlevel 1 (
        echo ❌ ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully!
)

:: Run Playwright upload test
echo 🚀 Launching Playwright UI for Drive Upload...
echo.

call npx playwright test tests/drive-upload.spec.js --ui

:: Check if the command was successful
if errorlevel 1 (
    echo.
    echo ❌ ERROR: Upload test failed!
    echo.
) else (
    echo.
    echo ✅ Upload phase completed!
    echo.
    echo ☁️  Files uploaded to Google Drive dump folder.
    echo 📁 Check batch_summary.json for Drive file IDs.
    echo.
)

echo.
echo ========================================
echo          SESSION COMPLETE
echo ========================================
echo.
pause
goto :eof

:SelectFromDownloads
echo.
echo ========================================
echo   SELECT BATCH FOLDER FROM DOWNLOADS
echo ========================================
echo.

set "DOWNLOADS_PATH=%USERPROFILE%\Downloads"

echo Scanning Downloads folder for batch folders...
echo Location: %DOWNLOADS_PATH%
echo.

if not exist "%DOWNLOADS_PATH%" (
    echo ERROR: Downloads folder not found at %DOWNLOADS_PATH%
    echo.
    pause
    exit /b 1
)

echo Available folders:
echo.
set "count=0"
for /f "delims=" %%d in ('dir "%DOWNLOADS_PATH%" /b /ad 2^>nul') do (
    set /a count+=1
    echo [!count!] %%d
    set "folder_!count!=%%d"
)

echo.
echo DEBUG: Found !count! folders
echo.

if !count! equ 0 (
    echo No folders found in Downloads!
    echo.
    pause
    exit /b 1
)

echo.
echo [C] Enter custom path instead
echo [Q] Cancel and exit
echo.
set /p "folder_num=Enter folder number to select: "

if /i "%folder_num%"=="C" goto CustomPath
if /i "%folder_num%"=="Q" goto CancelSelection

set "selected_folder=!folder_%folder_num%!"
if not defined selected_folder (
    echo.
    echo ERROR: Invalid selection!
    echo.
    pause
    exit /b 1
)

set "BATCH_DIR=%DOWNLOADS_PATH%\!selected_folder!"
goto SelectionComplete

:CustomPath
echo.
set /p "custom_path=Enter full path to batch folder: "
if not exist "%custom_path%" (
    echo.
    echo ERROR: Path does not exist: %custom_path%
    echo.
    pause
    exit /b 1
)
set "BATCH_DIR=%custom_path%"
goto SelectionComplete

:CancelSelection
echo.
echo Cancelled by user.
echo.
pause
exit /b 0

:SelectionComplete
echo.
echo ========================================
echo Selected folder: %BATCH_DIR%
echo ========================================
echo.
goto ValidateBatchDir
