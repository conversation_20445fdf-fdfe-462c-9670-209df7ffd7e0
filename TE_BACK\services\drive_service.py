"""
Google Drive Service for TE Assignment uploads
Based on TE_AUTOMATION/util/fileUpload.js pattern
"""

import os
import json
import logging
from datetime import datetime
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload, MediaIoBaseUpload
from googleapiclient.errors import HttpError
import io
import zipfile
from typing import List, Dict, Optional, Tuple

logger = logging.getLogger(__name__)

class DriveService:
    """Google Drive service for uploading TE assignment files"""
    
    def __init__(self, service_account_file: str, parent_folder_id: str):
        """
        Initialize Google Drive service
        
        Args:
            service_account_file: Path to service account JSON file
            parent_folder_id: Google Drive parent folder ID for uploads
        """
        self.service_account_file = service_account_file
        self.parent_folder_id = parent_folder_id
        self.service = None
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Google Drive API using service account"""
        try:
            # Load service account credentials
            credentials = service_account.Credentials.from_service_account_file(
                self.service_account_file,
                scopes=['https://www.googleapis.com/auth/drive']
            )
            
            # Build the Drive service
            self.service = build('drive', 'v3', credentials=credentials)
            logger.info("Google Drive authentication successful")
            
        except Exception as e:
            logger.error(f"Google Drive authentication failed: {e}")
            raise
    
    def generate_batch_name(self) -> str:
        """
        Generate unique batch folder name with timestamp
        Format: batch-YYYYMMDD-HHMMSS-XXX
        """
        timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
        # Add random suffix for uniqueness
        import random
        suffix = f"{random.randint(100, 999)}"
        return f"batch-{timestamp}-{suffix}"
    
    def create_batch_folder(self, batch_name: str) -> Tuple[str, str]:
        """
        Create a new batch folder in Google Drive
        
        Args:
            batch_name: Name for the batch folder
            
        Returns:
            Tuple of (folder_id, shareable_link)
        """
        try:
            # Create folder metadata
            folder_metadata = {
                'name': batch_name,
                'parents': [self.parent_folder_id],
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            # Create the folder
            folder = self.service.files().create(
                body=folder_metadata,
                fields='id'
            ).execute()
            
            folder_id = folder.get('id')
            
            # Make folder shareable (anyone with link can view)
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }
            
            self.service.permissions().create(
                fileId=folder_id,
                body=permission
            ).execute()
            
            # Generate shareable link
            shareable_link = f"https://drive.google.com/drive/folders/{folder_id}?usp=sharing"
            
            logger.info(f"Created batch folder: {batch_name} (ID: {folder_id})")
            return folder_id, shareable_link
            
        except HttpError as e:
            logger.error(f"Failed to create batch folder {batch_name}: {e}")
            raise
    
    def upload_zip_file(self, zip_file_path: str, folder_id: str, filename: str = None) -> Tuple[str, str]:
        """
        Upload a ZIP file to the specified Google Drive folder
        
        Args:
            zip_file_path: Local path to ZIP file
            folder_id: Google Drive folder ID to upload to
            filename: Optional custom filename (defaults to original filename)
            
        Returns:
            Tuple of (file_id, shareable_link)
        """
        try:
            if not filename:
                filename = os.path.basename(zip_file_path)
            
            # File metadata
            file_metadata = {
                'name': filename,
                'parents': [folder_id]
            }
            
            # Upload the file
            media = MediaFileUpload(
                zip_file_path,
                mimetype='application/zip',
                resumable=True
            )
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()
            
            file_id = file.get('id')
            
            # Make file shareable
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }
            
            self.service.permissions().create(
                fileId=file_id,
                body=permission
            ).execute()
            
            # Generate shareable link
            shareable_link = f"https://drive.google.com/file/d/{file_id}/view?usp=sharing"
            
            logger.info(f"Uploaded file: {filename} (ID: {file_id})")
            return file_id, shareable_link
            
        except HttpError as e:
            logger.error(f"Failed to upload file {zip_file_path}: {e}")
            raise

    def upload_zip_file_from_memory(self, zip_file_obj, folder_id: str, filename: str = None) -> Tuple[str, str]:
        """
        Upload a ZIP file from memory (file object) to Google Drive

        Args:
            zip_file_obj: File object (from Flask request.files)
            folder_id: Google Drive folder ID to upload to
            filename: Optional custom filename (defaults to original filename)

        Returns:
            Tuple of (file_id, shareable_link)
        """
        try:
            if not filename:
                filename = zip_file_obj.filename

            # File metadata
            file_metadata = {
                'name': filename,
                'parents': [folder_id]
            }

            # Create a BytesIO object from the file data
            zip_file_obj.seek(0)  # Reset file pointer to beginning
            file_data = zip_file_obj.read()
            file_stream = io.BytesIO(file_data)

            # Upload the file from memory
            media = MediaIoBaseUpload(
                file_stream,
                mimetype='application/zip',
                resumable=True
            )

            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()

            file_id = file.get('id')

            # Make file shareable
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }

            self.service.permissions().create(
                fileId=file_id,
                body=permission
            ).execute()

            # Generate shareable link
            shareable_link = f"https://drive.google.com/file/d/{file_id}/view?usp=sharing"

            logger.info(f"Uploaded file from memory: {filename} (ID: {file_id})")
            return file_id, shareable_link

        except HttpError as e:
            logger.error(f"Failed to upload file from memory {filename}: {e}")
            raise

    def upload_zip_files_batch(self, zip_file_paths: List[str], batch_name: str = None) -> Dict:
        """
        Upload multiple ZIP files to a new batch folder
        
        Args:
            zip_file_paths: List of local ZIP file paths
            batch_name: Optional batch name (auto-generated if not provided)
            
        Returns:
            Dict with batch info and upload results
        """
        try:
            # Generate batch name if not provided
            if not batch_name:
                batch_name = self.generate_batch_name()
            
            # Create batch folder
            folder_id, folder_link = self.create_batch_folder(batch_name)
            
            # Upload each ZIP file
            uploaded_files = []
            for zip_path in zip_file_paths:
                if os.path.exists(zip_path):
                    file_id, file_link = self.upload_zip_file(zip_path, folder_id)
                    uploaded_files.append({
                        'filename': os.path.basename(zip_path),
                        'file_id': file_id,
                        'file_link': file_link,
                        'local_path': zip_path
                    })
                else:
                    logger.warning(f"ZIP file not found: {zip_path}")
            
            result = {
                'batch_name': batch_name,
                'folder_id': folder_id,
                'folder_link': folder_link,
                'uploaded_files': uploaded_files,
                'upload_count': len(uploaded_files),
                'uploaded_at': datetime.utcnow().isoformat()
            }
            
            logger.info(f"Batch upload completed: {batch_name} ({len(uploaded_files)} files)")
            return result
        except Exception as e:
            logger.error(f"Batch upload failed: {e}")
            raise

    def get_resumable_upload_url(self, filename: str, mime_type: str, folder_id: str) -> str:
        """
        Get a resumable upload URL for direct-to-Drive uploading

        Args:
            filename: Name of the file to create
            mime_type: MIME type of the file
            folder_id: Parent folder ID

        Returns:
            Resumable upload URL
        """
        try:
            # File metadata
            file_metadata = {
                'name': filename,
                'parents': [folder_id],
                'mimeType': mime_type
            }

            # Create the request but don't execute it yet
            # We need to construct a robust resumable upload request manually
            # or use the internal functionality of the library if accessible.

            # Using a lower-level approach with the authorized http object
            # to initiate the resumable upload and get the Location header.

            from googleapiclient.http import HttpRequest
            import json

            # Construct the URL
            url = f'https://www.googleapis.com/upload/drive/v3/files?uploadType=resumable'

            # Headers
            headers = {
                'Content-Type': 'application/json; charset=UTF-8',
                'X-Upload-Content-Type': mime_type
                # 'X-Upload-Content-Length': file_size  # Optional but good if known
            }

            # Body
            body = json.dumps(file_metadata).encode('utf-8')

            # Use the service's authorized http transport
            # .request() signature: (uri, method, body, headers)
            resp, content = self.service._http.request(
                url,
                method='POST',
                body=body,
                headers=headers
            )

            if resp.status == 200:
                upload_url = resp['location']
                logger.info(f"Generated resumable upload URL for: {filename}")
                return upload_url
            else:
                logger.error(f"Failed to get upload URL: {resp.status} - {content}")
                raise HttpError(resp, content)

        except Exception as e:
            logger.error(f"Error generating resumable upload URL: {e}")
            raise

    def move_file_to_folder(self, file_id: str, new_folder_id: str) -> str:
        """
        Move file to a different folder (updates parent only, no file transfer)
        
        Args:
            file_id: Google Drive file ID
            new_folder_id: Target folder ID
            
        Returns:
            Updated file link
        """
        try:
            # Get current parents
            file = self.service.files().get(
                fileId=file_id,
                fields='parents'
            ).execute()
            
            previous_parents = ",".join(file.get('parents', []))
            
            # Move file (remove from old parent, add to new parent)
            self.service.files().update(
                fileId=file_id,
                addParents=new_folder_id,
                removeParents=previous_parents,
                fields='id, parents'
            ).execute()
            
            logger.info(f"Moved file {file_id} to folder {new_folder_id}")
            
            return f"https://drive.google.com/file/d/{file_id}/view?usp=sharing"
            
        except HttpError as e:
            logger.error(f"Failed to move file {file_id}: {e}")
            raise

    def search_files_by_name(self, folder_id: str, filenames: list) -> dict:
        """
        Search for files by name in a specific folder

        Args:
            folder_id: Google Drive folder ID to search in
            filenames: List of filenames to search for

        Returns:
            Dict mapping filename to file info {id, name, webViewLink}
        """
        try:
            file_mapping = {}

            logger.info(f"Starting search for {len(filenames)} files in folder {folder_id}")

            for filename in filenames:
                # Search for file in folder
                query = f"name='{filename}' and '{folder_id}' in parents and trashed=false"
                logger.debug(f"Search query: {query}")

                results = self.service.files().list(
                    q=query,
                    fields='files(id, name, webViewLink)',
                    pageSize=1
                ).execute()

                files = results.get('files', [])
                if files:
                    file_mapping[filename] = {
                        'file_id': files[0]['id'],
                        'file_name': files[0]['name'],
                        'file_link': files[0].get('webViewLink', f"https://drive.google.com/file/d/{files[0]['id']}/view?usp=sharing")
                    }
                    logger.info(f"✓ Found file: {filename} (ID: {files[0]['id']})")
                else:
                    logger.warning(f"✗ File not found in dump folder: '{filename}' (folder: {folder_id})")

            logger.info(f"Search complete: Found {len(file_mapping)}/{len(filenames)} files")
            return file_mapping

        except HttpError as e:
            logger.error(f"Error searching files: {e}")
            raise

def create_drive_service() -> DriveService:
    """
    Factory function to create DriveService instance with environment config
    """
    service_account_file = os.getenv('GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE', 'service_account.json')
    parent_folder_id = os.getenv('GOOGLE_DRIVE_PARENT_FOLDER_ID')
    
    if not parent_folder_id:
        raise ValueError("GOOGLE_DRIVE_PARENT_FOLDER_ID environment variable is required")
    
    # Make service account file path absolute
    if not os.path.isabs(service_account_file):
        service_account_file = os.path.join(os.path.dirname(__file__), '..', service_account_file)
    
    return DriveService(service_account_file, parent_folder_id)
