# TE Automation Environment Configuration
# Copy this file to .env and update with your actual credentials

# TE Portal Credentials
TE_USERNAME=<EMAIL>
TE_PASSWORD=Editing@1234

# TE Portal URLs
TE_LOGIN_URL=https://production.jow.medknow.com/login
TE_MYTASK_URL=https://production.jow.medknow.com/mytask

# Directory Configuration
INCOMING_DIR=./incoming
DOWNLOADS_DIR=./downloads

# Optional: Custom batch directory path for uploads
# Use this to specify a custom folder location (e.g., Downloads folder)
# Can be absolute path (C:\Users\<USER>\Downloads\batch) or relative (./custom-folder)
# Leave empty to use default incoming/{date}/{batch} structure
BATCH_DIR_PATH=

# Optional: Date override (format: DD-MM-YYYY)
# Leave empty to use today's date
TARGET_DATE=02-09-2025
