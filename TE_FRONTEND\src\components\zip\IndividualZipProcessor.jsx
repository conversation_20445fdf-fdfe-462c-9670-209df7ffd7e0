import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import J<PERSON>Zip from 'jszip';
import { useZipQueue } from '../../context/ZipQueueContext';
import { useArticle } from '../../context/ArticleContext';
import { useRole } from '../../context/AuthContext';
import FileListViewer from './FileListViewer';
import DocumentPreview from './DocumentPreview';
import { Icons } from '../common';

import './IndividualZipProcessor.css';

const IndividualZipProcessor = () => {
  const { zipId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { isAdmin } = useRole();
  const { getZipById, updateZipStatus, markZipAsProcessed, currentFolder } = useZipQueue();
  const { setArticleId, setZipFiles, resetArticleData } = useArticle();

  // Set admin context in sessionStorage for child components
  useEffect(() => {
    if (isAdmin || window.location.pathname.includes('/admin')) {
      sessionStorage.setItem('adminContext', 'true');
    }
  }, [isAdmin]);
  
  const [currentStep, setCurrentStep] = useState('loading'); // 'loading', 'validating', 'files', 'preview', 'completed'
  const [selectedFile, setSelectedFile] = useState(null);
  const [zipData, setZipData] = useState(null);
  const [extractedFiles, setExtractedFiles] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [copiedReferences, setCopiedReferences] = useState('');
  const [initialized, setInitialized] = useState(false);
  const [authorData, setAuthorData] = useState(null);
  const [modifiedZipContent, setModifiedZipContent] = useState(null); // Store modified ZIP

  /**
   * Extract author data from JSON summary file (batch summary)
   * This is the ONLY source for author data (XML data is incorrect)
   */
  const extractAuthorDataFromSummary = useCallback(async (articleId) => {
    if (!currentFolder?.jsonSummaryFile) {
      return null;
    }

    try {
      const fileReader = new FileReader();
      const fileContent = await new Promise((resolve, reject) => {
        fileReader.onload = (e) => resolve(e.target.result);
        fileReader.onerror = reject;
        fileReader.readAsText(currentFolder.jsonSummaryFile);
      });

      const summaryData = JSON.parse(fileContent);
      let articleData = null;

      if (Array.isArray(summaryData)) {
        articleData = summaryData.find(article => article.article_id === articleId);
      } else if (summaryData.articles && Array.isArray(summaryData.articles)) {
        articleData = summaryData.articles.find(article => article.article_id === articleId);
      } else if (summaryData.article_id === articleId) {
        articleData = summaryData;
      }

      return articleData?.authors || null;
    } catch (error) {
      console.error('Error parsing JSON summary file:', error);
      return null;
    }
  }, [currentFolder]);

  const getFileIcon = (filename) => {
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
      case 'docx':
      case 'doc':
        return '📄';
      case 'pdf':
        return '📕';
      case 'txt':
        return '📝';
      case 'xlsx':
      case 'xls':
        return '📊';
      case 'pptx':
      case 'ppt':
        return '📊';
      default:
        return '📎';
    }
  };

  /**
   * Extract files from ZIP content (either original or modified)
   * @param {JSZip} zipContent - JSZip instance with loaded content
   * @returns {Array} Array of file objects
   */
  const extractFilesFromZipContent = async (zipContent) => {
    const files = [];
    for (const [relativePath, zipEntry] of Object.entries(zipContent.files)) {
      if (!zipEntry.dir) {
        const fileData = {
          name: relativePath,
          path: relativePath,
          size: zipEntry._data ? zipEntry._data.uncompressedSize : 0,
          icon: getFileIcon(relativePath),
          zipEntry: zipEntry,
          type: relativePath.toLowerCase().split('.').pop(),
          // Add additional properties for compatibility
          content: null, // Will be loaded when needed
          isSupported: ['docx', 'doc', 'txt', 'pdf'].includes(relativePath.toLowerCase().split('.').pop())
        };
        files.push(fileData);
      }
    }

    // Sort files by type (docx first, then others)
    files.sort((a, b) => {
      if (a.type === 'docx' && b.type !== 'docx') return -1;
      if (a.type !== 'docx' && b.type === 'docx') return 1;
      return a.name.localeCompare(b.name);
    });

    return files;
  };

  const extractZipFile = async (zip) => {
    setIsProcessing(true);
    setError(null);

    try {
      // Update status to processing
      updateZipStatus(zipId, 'processing');

      const jsZip = new JSZip();
      const zipContent = await jsZip.loadAsync(zip.file);

      console.log('Extracting files from ZIP...');
      const files = await extractFilesFromZipContent(zipContent);
      console.log('Extracted files:', files.length, files.map(f => ({ name: f.name, type: f.type, supported: f.isSupported })));

      setExtractedFiles(files);
      setArticleId(zip.articleId);
      setZipFiles(files);

      // Update ZIP with extracted files
      updateZipStatus(zipId, 'processing', { extractedFiles: files });

      // Validation is now handled directly in FileListViewer

      // Always proceed to files step - validation is informational only
      setCurrentStep('files');

    } catch (err) {
      console.error('Error extracting ZIP file:', err);
      setError('Failed to extract ZIP file. Please try again.');
      updateZipStatus(zipId, 'pending', { error: err.message });
    } finally {
      setIsProcessing(false);
    }
  };

  // Initialize ZIP processor after extractZipFile is defined
  useEffect(() => {
    const initializeZipProcessor = async () => {
      if (initialized) return; // Prevent multiple initializations

      const zip = getZipById(zipId);
      if (!zip) {
        setError('ZIP file not found');
        return;
      }

      setZipData(zip);
      setInitialized(true);

      // Check if file data is missing (after page refresh)
      if (!zip.file) {
        setError('ZIP file data is no longer available. Please re-upload the folder to continue processing.');
        return;
      }

      // Extract author data - ONLY from batch summary JSON (XML data is incorrect)
      if (zip.articleId) {
        const authors = await extractAuthorDataFromSummary(zip.articleId);
        setAuthorData(authors || null);
      }

      // If ZIP is already processed, show files directly
      if (zip.extractedFiles && zip.extractedFiles.length > 0) {
        // Check if extracted files have zipEntry data
        const hasZipEntries = zip.extractedFiles.some(file => file.zipEntry);
        if (!hasZipEntries) {
          // Re-extract if zipEntry data is missing
          await extractZipFile(zip);
        } else {
          setExtractedFiles(zip.extractedFiles);
          setArticleId(zip.articleId);
          setZipFiles(zip.extractedFiles);
          setCurrentStep('files');
        }
      } else {
        // Extract ZIP file
        await extractZipFile(zip);
      }
    };

    if (zipId && !initialized) {
      initializeZipProcessor();
    }
  }, [zipId, initialized, extractAuthorDataFromSummary, getZipById, setArticleId, setZipFiles, extractZipFile, updateZipStatus, markZipAsProcessed]);

  const handleFileSelect = (file) => {
    console.log('File selected:', file);
    console.log('File has zipEntry:', !!file.zipEntry);
    console.log('File type:', file.type);
    setSelectedFile(file);
    setCurrentStep('preview');
  };

  const handleBackToFiles = () => {
    setSelectedFile(null);
    setCurrentStep('files');
  };

  const handleBackToQueue = () => {
    resetArticleData();
    // Navigate to appropriate queue based on context
    const queuePath = isAdmin ? '/admin/zip-queue' : '/zip-queue';
    navigate(queuePath);
  };

  const handleCopyReferences = (text) => {
    setCopiedReferences(text);
    console.log('References copied:', text.substring(0, 100) + '...');
  };

  const handleProcessReferences = () => {
    if (copiedReferences && zipData.articleId) {
      // Navigate to processing page with the copied references
      navigate('/process', {
        state: {
          references: copiedReferences,
          articleId: zipData.articleId,
          fromZipProcessor: true,
          zipId: zipId
        }
      });
    } else {
      setError('Please copy references from a document before processing.');
    }
  };

  const handleMarkAsProcessed = () => {
    if (window.confirm('Mark this ZIP as processed? It will be moved to the "Ready for Assignment" section.')) {
      try {
        markZipAsProcessed(zipId);

        // Navigate to appropriate queue based on context
        const queuePath = isAdmin ? '/admin/zip-queue' : '/zip-queue';
        navigate(queuePath);
      } catch (error) {
        console.error('Error marking ZIP as processed:', error);
        setError('Failed to mark ZIP as processed. Please try again.');
      }
    }
  };

  const handleValidationQuerySent = (articleId) => {
    console.log(`Validation query sent for article: ${articleId}`);
    // Update ZIP status to mark that a validation query was sent
    updateZipStatus(zipId, 'processing', { validationQuerySent: true });
  };

  /**
   * Handle ZIP modification when query is written to a file
   * @param {string} filename - Name of the file to modify
   * @param {string} content - Content to prepend
   * @param {string} type - Type of modification ('text' or 'docx')
   * @param {ArrayBuffer} originalFileBuffer - Original file buffer (for DOCX) - only used on first modification
   */
  const handleZipModified = async (filename, content, type, originalFileBuffer = null) => {
    try {
      // Load the current ZIP (use modified version if it exists)
      const jsZip = new JSZip();
      const zipContent = modifiedZipContent || await jsZip.loadAsync(zipData.file);

      // Find the file to modify
      const fileToModify = zipContent.file(filename);
      if (!fileToModify) {
        throw new Error(`File "${filename}" not found in ZIP`);
      }

      if (type === 'text') {
        // For text files, simply replace with new content
        zipContent.file(filename, content);
      } else if (type === 'docx') {
        // For DOCX files, get the current file buffer (which may already be modified)
        const currentFileBuffer = await fileToModify.async('arraybuffer');

        // Modify the document.xml inside the DOCX
        const modifiedDocx = await modifyDocxContent(currentFileBuffer, content);
        zipContent.file(filename, modifiedDocx);
      }

      // Store the modified ZIP content
      setModifiedZipContent(zipContent);

      // Re-extract files from the modified ZIP to update the file list
      const updatedFiles = await extractFilesFromZipContent(zipContent);
      setExtractedFiles(updatedFiles);
      setZipFiles(updatedFiles);

      // Update the ZIP file object in the queue
      const modifiedBlob = await zipContent.generateAsync({ type: 'blob' });
      const modifiedFile = new File([modifiedBlob], zipData.filename, { type: 'application/zip' });

      // Update the ZIP in the queue with the modified file and updated extracted files
      updateZipStatus(zipId, zipData.status, {
        file: modifiedFile,
        modified: true,
        modifiedAt: new Date().toISOString(),
        extractedFiles: updatedFiles
      });

      alert(`✅ Query written to "${filename}" successfully!\n\nThe modified ZIP will be uploaded when assigned to a TE.`);

    } catch (error) {
      console.error('Error modifying ZIP:', error);
      throw error;
    }
  };

  /**
   * Modify DOCX file by prepending text to the document.xml with yellow highlight
   * @param {ArrayBuffer} docxBuffer - Original DOCX file buffer
   * @param {string} textToPrepend - Text to prepend to the document
   * @returns {Promise<Blob>} Modified DOCX file
   */
  const modifyDocxContent = async (docxBuffer, textToPrepend) => {
    const docxZip = new JSZip();
    const docxContent = await docxZip.loadAsync(docxBuffer);

    // Read the document.xml file
    const documentXml = await docxContent.file('word/document.xml').async('string');

    // Convert text to Word XML paragraphs with yellow highlight
    const textLines = textToPrepend.split('\n').filter(line => line.trim()); // Remove empty lines

    const xmlParagraphs = textLines.map(line => {
      // Escape XML special characters
      const escapedLine = line
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&apos;');

      // Create a Word paragraph with yellow highlight (w:highlight w:val="yellow")
      // and preserve spaces
      return `<w:p><w:r><w:rPr><w:highlight w:val="yellow"/></w:rPr><w:t xml:space="preserve">${escapedLine}</w:t></w:r></w:p>`;
    }).join('');

    // Add an empty paragraph after the highlighted text for separation
    const xmlWithSeparator = xmlParagraphs + '<w:p/>';

    // Find the body tag and insert the new paragraphs at the beginning
    const modifiedXml = documentXml.replace(
      /(<w:body[^>]*>)/,
      `$1${xmlWithSeparator}`
    );

    // Update the document.xml in the DOCX
    docxContent.file('word/document.xml', modifiedXml);

    // Generate the modified DOCX
    return await docxContent.generateAsync({ type: 'blob' });
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'loading':
        return (
          <div className="zip-loading">
            <div className="zip-spinner"></div>
            <p>Loading ZIP file...</p>
          </div>
        );


      case 'files':
        return (
          <FileListViewer
            onFileSelect={handleFileSelect}
            onBack={handleBackToQueue}
            zipFile={zipData?.file || null}
            onValidationQuerySent={handleValidationQuerySent}
          />
        );

      case 'preview':
        return (
          <DocumentPreview
            file={selectedFile}
            onBack={handleBackToFiles}
            onCopyReferences={handleCopyReferences}
            zipId={zipId}
            zipFile={zipData?.file}
            authors={authorData}
            articleId={zipData?.articleId}
            onZipModified={handleZipModified}
            onValidationQuerySent={handleValidationQuerySent}
          />
        );

      default:
        return (
          <div className="zip-error">
            <Icons.ExclamationTriangleIcon />
            <p>Unknown step: {currentStep}</p>
          </div>
        );
    }
  };

  if (error) {
    return (
      <div className="zip-processor-container">
        <div className="zip-error-state">
          <div className="error-icon">⚠️</div>
          <h2>Error Processing ZIP</h2>
          <p>{error}</p>
          <div className="error-actions">
            <button onClick={handleBackToQueue} className="back-to-queue-button">
              <Icons.ChevronLeftIcon />
              Back to Queue
            </button>
            {error.includes('no longer available') && (
              <button
                onClick={() => navigate('/admin/folder-upload')}
                className="reupload-button"
              >
                <Icons.UploadIcon />
                Re-upload Folder
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (!zipData) {
    return (
      <div className="zip-processor-container">
        <div className="zip-loading">
          <div className="zip-spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="zip-processor-container">
      {/* Header */}
      <div className="zip-processor-header">
        <div className="header-left">
          <button onClick={handleBackToQueue} className="back-to-queue-button">
            <Icons.ChevronLeftIcon />
          </button>
          <div className="zip-info">
            <h1 className="zip-title">
              <span className="zip-icon">📦</span>
              {zipData.filename}
            </h1>
            <p className="zip-subtitle">
              Article ID: {zipData.articleId} • {extractedFiles.length} files
            </p>
          </div>
        </div>
        <div className="header-actions">
          {copiedReferences && (
            <button onClick={handleProcessReferences} className="process-refs-button">
              <Icons.PlayIcon />
              Process References
            </button>
          )}
          <button onClick={handleMarkAsProcessed} className="mark-processed-button">
            <Icons.CheckCircleIcon />
            Mark as Processed
          </button>
        </div>
      </div>

      {/* Processing indicator */}
      {isProcessing && (
        <div className="processing-indicator">
          <div className="processing-spinner"></div>
          <span>Extracting ZIP file...</span>
        </div>
      )}

      {/* Main Content */}
      <div className="zip-processor-content">
        {renderCurrentStep()}
      </div>
    </div>
  );
};

export default IndividualZipProcessor;
