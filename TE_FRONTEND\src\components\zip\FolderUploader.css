/* Folder Uploader Styles */
.folder-uploader-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.folder-uploader-header {
  text-align: center;
  margin-bottom: 2rem;
}

.folder-uploader-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.folder-icon {
  font-size: 2rem;
}

.folder-uploader-description {
  color: #6b7280;
  font-size: 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.folder-error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.75rem;
  color: #dc2626;
  margin-bottom: 1.5rem;
}

.folder-error-message svg {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.folder-drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  background-color: #f9fafb;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.folder-drop-zone:hover {
  border-color: #9ca3af;
  background-color: #f3f4f6;
}

.folder-drop-zone.drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
  transform: scale(1.02);
}

.folder-drop-zone.processing {
  border-color: #10b981;
  background-color: #ecfdf5;
  cursor: not-allowed;
}

.folder-upload-icon {
  margin-bottom: 1rem;
}

.folder-upload-icon svg {
  width: 4rem;
  height: 4rem;
  color: #9ca3af;
}

.folder-upload-text {
  max-width: 400px;
}

.folder-main-text {
  font-size: 1.125rem;
  color: #374151;
  margin-bottom: 0.5rem;
}

.folder-browse-link {
  color: #3b82f6;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.folder-browse-link:hover {
  color: #2563eb;
}

.folder-file-input {
  display: none;
}

.folder-sub-text {
  color: #6b7280;
  font-size: 0.875rem;
}

.folder-processing {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.folder-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: folder-spin 1s linear infinite;
}

@keyframes folder-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.folder-processing p {
  margin: 0;
  color: #374151;
  font-weight: 500;
}

.folder-name {
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

.folder-help-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.folder-help-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.folder-help-steps {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.folder-help-step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.folder-help-step span:last-child {
  color: #4b5563;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .folder-uploader-container {
    padding: 1rem;
  }
  
  .folder-drop-zone {
    padding: 2rem 1rem;
  }
  
  .folder-uploader-title {
    font-size: 1.5rem;
  }
  
  .folder-upload-icon svg {
    width: 3rem;
    height: 3rem;
  }
  
  .folder-main-text {
    font-size: 1rem;
  }
}

/* Validation Summary Styles */
.validation-summary {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin: 2rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.validation-summary h3 {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.validation-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-item.success .stat-number {
  color: #059669;
}

.stat-item.error .stat-number {
  color: #dc2626;
}

.stat-item.total .stat-number {
  color: #4f46e5;
}

.validation-details {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.validation-item {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  padding: 1rem;
}

.validation-item.success {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
}

.validation-item.error {
  background-color: #fef2f2;
  border-color: #fecaca;
}

.validation-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.validation-icon {
  font-size: 1.25rem;
}

.article-id {
  font-weight: 600;
  color: #1f2937;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.validation-status {
  margin-left: auto;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.validation-item.success .validation-status {
  background-color: #d1fae5;
  color: #065f46;
}

.validation-item.error .validation-status {
  background-color: #fee2e2;
  color: #991b1b;
}

.validation-error-details {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #fecaca;
}

.validation-error-details p {
  color: #374151;
  margin-bottom: 0.5rem;
}

.file-list {
  margin-top: 0.5rem;
}

.file-list strong {
  color: #111827;
  font-weight: 600;
}

.file-list ul {
  margin-top: 0.25rem;
  padding-left: 1.5rem;
  color: #6b7280;
}

.file-list li {
  margin-bottom: 0.125rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.validation-actions {
  display: flex;
  /* justify-content: center; */
  padding-bottom: 1rem;
}

.close-summary-button {
  padding: 0.5rem 1.5rem;
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-summary-button:hover {
  background-color: #4b5563;
}
