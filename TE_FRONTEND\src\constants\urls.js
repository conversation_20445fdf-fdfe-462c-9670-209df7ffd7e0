// External APIs
export const esearch='https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi';
export const esummary='https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi';
export const crossRef='https://api.crossref.org/works';
export const nlmCatalog='https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=nlmcatalog&term=';
export const nlmCatalogSummary='https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?db=nlmcatalog&id=';

// Backend API URLs - use environment variable or fallback to proxy
const API_BASE = process.env.REACT_APP_API_URL || '';
export const upload = `${API_BASE}/upload`;
export const extract = `${API_BASE}/extract`;
export const uploadToDriveUrl = `${API_BASE}/upload-to-drive`;


// Database API endpoints
export const referencesApi = `${API_BASE}/api/references`;
export const journalsApi = `${API_BASE}/api/journals`;
export const articleIdsApi = `${API_BASE}/api/article-ids`;

// Email API endpoints
export const sendQueryEmailApi = `${API_BASE}/api/send-query-email`;

