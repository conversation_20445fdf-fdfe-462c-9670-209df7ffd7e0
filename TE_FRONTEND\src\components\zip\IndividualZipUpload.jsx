import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useZipQueue } from '../../context/ZipQueueContext';
import { Icons } from '../common';
import './IndividualZipUpload.css';

const IndividualZipUpload = () => {
  const navigate = useNavigate();
  const { addZipQueue } = useZipQueue();
  
  const [zipFiles, setZipFiles] = useState([]);
  const [jsonSummaryFile, setJsonSummaryFile] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [jsonError, setJsonError] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);

  // Handle ZIP file selection
  const handleZipFileSelect = (e) => {
    const files = Array.from(e.target.files);
    const zipFiles = files.filter(file => file.name.toLowerCase().endsWith('.zip'));
    
    if (zipFiles.length === 0) {
      setError('Please select ZIP files only.');
      return;
    }
    
    setZipFiles(zipFiles);
    setError(null);
  };

  // Handle JSON summary file selection
  const handleJsonFileSelect = (e) => {
    const file = e.target.files[0];
    setJsonError('');
    
    if (!file) {
      setJsonSummaryFile(null);
      return;
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.json')) {
      setJsonError('Please select a JSON file.');
      setJsonSummaryFile(null);
      return;
    }

    // Validate file name contains summary or batch
    const fileName = file.name.toLowerCase();
    if (!fileName) {
      setJsonError('Please select a JSON file (filename should contain the article).');
      setJsonSummaryFile(null);
      return;
    }

    setJsonSummaryFile(file);
  };

  // Handle drag and drop
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const zipFiles = files.filter(file => file.name.toLowerCase().endsWith('.zip'));
    const jsonFiles = files.filter(file => 
      file.name.toLowerCase().endsWith('.json') && 
      (file.name.toLowerCase().includes('summary') || file.name.toLowerCase().includes('batch'))
    );
    
    if (zipFiles.length > 0) {
      setZipFiles(zipFiles);
    }
    
    if (jsonFiles.length === 1) {
      setJsonSummaryFile(jsonFiles[0]);
      setJsonError('');
    } else if (jsonFiles.length > 1) {
      setJsonError('Multiple JSON summary files detected. Please select only one.');
    }
    
    setError(null);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  // Process files and add to queue
  const handleProceedToQueue = async () => {
    if (zipFiles.length === 0) {
      setError('Please select at least one ZIP file.');
      return;
    }

    if (!jsonSummaryFile) {
      setJsonError('Please select a JSON summary file. This is required for enhanced TE assignment emails.');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Create queue items for the ZIP files
      const queueItems = zipFiles.map((file, index) => {
        const articleId = file.name.replace(/\.zip$/i, '');
        return {
          id: `zip_${Date.now()}_${index}`,
          name: file.name,
          filename: file.name,
          file: file,
          articleId: articleId,
          status: 'pending', // Start with pending status to go through normal workflow
          uploadDate: new Date().toISOString(),
          validationStatus: 'pending'
        };
      });

      // Add to queue with folder info
      const folderData = {
        folderName: 'Individual ZIP Upload',
        zipQueue: queueItems,
        totalFiles: zipFiles.length,
        processedDate: new Date().toISOString(),
        jsonSummaryFile: jsonSummaryFile // Include JSON file for TE assignment
      };

      addZipQueue(folderData);
      
      // Navigate to queue dashboard
      navigate('/admin/zip-queue');
      
    } catch (err) {
      console.error('Error processing files:', err);
      setError('Failed to process files. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const removeZipFile = (index) => {
    setZipFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeJsonFile = () => {
    setJsonSummaryFile(null);
    setJsonError('');
  };

  return (
    <div className="individual-zip-upload">
      <div className="upload-header">
        <h1 className="upload-title">
          <span className="upload-icon">📦</span>
          Individual ZIP Upload
        </h1>
        <p className="upload-description">
          Upload individual ZIP files with a JSON summary file for TE assignment
        </p>
      </div>

      {error && (
        <div className="error-message">
          <Icons.ExclamationTriangleIcon />
          <span>{error}</span>
        </div>
      )}

      {/* ZIP Files Upload Section */}
      <div className="upload-section">
        <h2 className="section-title">
          1. Select ZIP Files <span className="required">*</span>
        </h2>
        <div
          className={`drop-zone ${isDragOver ? 'drag-over' : ''}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <div className="drop-zone-content">
            <Icons.UploadIcon className="upload-icon-large" />
            <p className="drop-text">
              Drop ZIP files here, or{' '}
              <label className="browse-link">
                browse
                <input
                  type="file"
                  accept=".zip"
                  multiple
                  onChange={handleZipFileSelect}
                  className="file-input-hidden"
                />
              </label>
            </p>
            <p className="drop-subtext">Supports multiple ZIP files</p>
          </div>
        </div>

        {zipFiles.length > 0 && (
          <div className="selected-files">
            <h3 className="files-title">Selected ZIP Files ({zipFiles.length})</h3>
            <div className="files-list">
              {zipFiles.map((file, index) => (
                <div key={index} className="file-item">
                  <span className="file-icon">📦</span>
                  <span className="file-name">{file.name}</span>
                  <span className="file-size">
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </span>
                  <button
                    onClick={() => removeZipFile(index)}
                    className="remove-button"
                    title="Remove file"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* JSON Summary File Upload Section */}
      <div className="upload-section">
        <h2 className="section-title">
          2. Select JSON Summary File <span className="required">*</span>
        </h2>
        <div className="json-upload-area">
          <label className="json-upload-label">
            <Icons.DocumentIcon className="json-icon" />
            <span className="json-text">
              {jsonSummaryFile ? jsonSummaryFile.name : 'Choose JSON summary file'}
            </span>
            <input
              type="file"
              accept=".json"
              onChange={handleJsonFileSelect}
              className="file-input-hidden"
            />
          </label>
          {jsonSummaryFile && (
            <button
              onClick={removeJsonFile}
              className="remove-json-button"
              title="Remove JSON file"
            >
              ×
            </button>
          )}
        </div>
        
        {jsonError && (
          <p className="json-error">{jsonError}</p>
        )}
        
        {jsonSummaryFile && (
          <p className="json-success">
            ✅ {jsonSummaryFile.name} selected ({(jsonSummaryFile.size / 1024).toFixed(1)} KB)
          </p>
        )}
        
        <p className="json-info">
          Upload a JSON summary file containing article metadata and author information. 
          File name should contain "summary" or "batch".
        </p>
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <button
          onClick={() => navigate('/admin/dashboard')}
          className="back-button"
          disabled={isProcessing}
        >
          ← Back to Dashboard
        </button>
        
        <button
          onClick={handleProceedToQueue}
          className="proceed-button"
          disabled={isProcessing || zipFiles.length === 0 || !jsonSummaryFile}
        >
          {isProcessing ? (
            <>
              <div className="spinner"></div>
              Adding to Queue...
            </>
          ) : (
            <>
              Add to Processing Queue →
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default IndividualZipUpload;
