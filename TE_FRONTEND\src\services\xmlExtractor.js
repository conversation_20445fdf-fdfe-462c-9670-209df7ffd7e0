/**
 * XML Extractor Service
 * Handles extraction of author data from different XML formats (manuscript and article)
 */

/**
 * Detect the XML format type
 * @param {string} xmlString - XML content as string
 * @returns {string} - 'manuscript' or 'article'
 */
export const detectXmlFormat = (xmlString) => {
  if (xmlString.includes('<manuscript>')) {
    return 'manuscript';
  } else if (xmlString.includes('<article dtd-version')) {
    return 'article';
  }
  throw new Error('Unknown XML format');
};

/**
 * Get text content from XML element
 * @param {Element} element - XML element
 * @param {string} tagName - Tag name to search for
 * @returns {string} - Text content or empty string
 */
const getElementText = (element, tagName) => {
  const el = element.querySelector(tagName);
  return el ? el.textContent.trim() : '';
};

/**
 * Extract authors from manuscript format XML
 * @param {Document} xmlDoc - Parsed XML document
 * @returns {Array} - Array of author objects
 */
const extractAuthorsFromManuscript = (xmlDoc) => {
  const authors = [];
  const authorElements = xmlDoc.querySelectorAll('manuscript > authors > author');
  
  authorElements.forEach((authorEl, index) => {
    const title = getElementText(authorEl, 'title');
    const givenName = getElementText(authorEl, 'fname');
    const middleName = getElementText(authorEl, 'mname');
    const surname = getElementText(authorEl, 'lname');
    const email = getElementText(authorEl, 'email');
    const orcid = getElementText(authorEl, 'orcid');
    const isCorrespondingText = getElementText(authorEl, 'is_corresponding');
    const authorOrderText = getElementText(authorEl, 'author_order');
    const phone = getElementText(authorEl, 'phonenumber') || getElementText(authorEl, 'mobilenumber');
    const copyrightAgreement = getElementText(authorEl, 'copyright_agreement');

    // Build full name (include title)
    const nameParts = [title, givenName, middleName, surname].filter(part => part);
    const fullName = nameParts.join(' ');

    // Determine author order
    let authorOrder = index + 1;
    if (authorOrderText === 'Primary') {
      authorOrder = 1;
    } else if (authorOrderText && !isNaN(parseInt(authorOrderText))) {
      authorOrder = parseInt(authorOrderText) + 1; // Adjust for 0-based indexing
    }

    // Build affiliation object
    const affiliation = {
      department: getElementText(authorEl, 'department'),
      institution: getElementText(authorEl, 'institute'),
      city: getElementText(authorEl, 'city'),
      state: getElementText(authorEl, 'state'),
      country: getElementText(authorEl, 'country')
    };

    authors.push({
      title,
      givenName,
      middleName,
      surname,
      fullName,
      email,
      orcid,
      isCorresponding: isCorrespondingText === 'Y' || isCorrespondingText === 'Yes',
      authorOrder,
      affiliation,
      phone: phone.replace(/^91\s*/, ''), // Remove country code prefix
      copyright_status: copyrightAgreement || 'Unknown'
    });
  });
  
  return authors;
};

/**
 * Extract authors from article format XML (JATS/NLM DTD)
 * @param {Document} xmlDoc - Parsed XML document
 * @returns {Array} - Array of author objects
 */
const extractAuthorsFromArticle = (xmlDoc) => {
  const authors = [];
  const contribElements = xmlDoc.querySelectorAll('contrib[contrib-type="author"]');
  
  contribElements.forEach((contribEl, index) => {
    const nameEl = contribEl.querySelector('name');
    if (!nameEl) return;
    
    const surname = getElementText(nameEl, 'surname');
    const givenNames = getElementText(nameEl, 'given-names');
    const prefix = getElementText(nameEl, 'prefix');
    const degrees = getElementText(contribEl, 'degrees');
    const email = getElementText(contribEl, 'email');
    const orcidEl = contribEl.querySelector('contrib-id[contrib-id-type="orcid"]');
    const orcid = orcidEl ? orcidEl.textContent.trim() : '';
    const isCorresponding = contribEl.getAttribute('corresp') === 'yes';
    
    // Get role/order
    const roleEl = contribEl.querySelector('role');
    const roleContent = roleEl ? roleEl.getAttribute('content-type') : null;
    const authorOrder = roleContent ? parseInt(roleContent) : index + 1;
    
    // Build full name
    const nameParts = [prefix, givenNames, surname].filter(part => part);
    const fullName = nameParts.join(' ');
    
    // Extract affiliation
    const affRefEl = contribEl.querySelector('xref[ref-type="aff"]');
    const affId = affRefEl ? affRefEl.getAttribute('rid') : null;
    let affiliation = {
      department: '',
      institution: '',
      city: '',
      state: '',
      country: ''
    };
    
    if (affId) {
      const affEl = xmlDoc.querySelector(`aff[id="${affId}"]`);
      if (affEl) {
        // Try to get institution from institution-wrap or institution tag
        const institutionWrap = affEl.querySelector('institution-wrap institution');
        const institution = institutionWrap || affEl.querySelector('institution');

        affiliation = {
          department: getElementText(affEl, 'institution[content-type="dept"]'),
          institution: institution ? institution.textContent.trim() : '',
          city: getElementText(affEl, 'addr-line[content-type="city"]'),
          state: getElementText(affEl, 'addr-line[content-type="state"]'),
          country: getElementText(affEl, 'country')
        };
      }
    }

    // Extract phone
    const phoneEl = xmlDoc.querySelector(`aff[id="${affId}"] phone[content-type="primary"]`);
    const phone = phoneEl ? phoneEl.textContent.trim() : '';

    // Split given names into first and middle
    const nameArray = givenNames.split(' ');
    const firstName = nameArray[0] || '';
    const middleName = nameArray.slice(1).join(' ');

    authors.push({
      title: prefix || degrees || '',
      givenName: firstName,
      middleName,
      surname,
      fullName,
      email,
      orcid,
      isCorresponding,
      authorOrder,
      affiliation,
      phone,
      copyright_status: 'Unknown' // JATS format typically doesn't include copyright status
    });
  });

  return authors;
};

/**
 * Main extraction function - detects format and extracts authors
 * @param {string} xmlString - XML content as string
 * @returns {Object} - Object containing authors array and metadata
 */
export const extractAuthorsFromXml = (xmlString) => {
  try {
    // Parse XML string
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlString, 'text/xml');

    // Check for parsing errors
    const parserError = xmlDoc.querySelector('parsererror');
    if (parserError) {
      throw new Error('XML parsing error: ' + parserError.textContent);
    }

    // Detect format
    const format = detectXmlFormat(xmlString);

    // Extract authors based on format
    let authors = [];
    let metadata = {};

    if (format === 'manuscript') {
      authors = extractAuthorsFromManuscript(xmlDoc);

      // Extract manuscript metadata
      const manuscriptEl = xmlDoc.querySelector('manuscript');
      if (manuscriptEl) {
        metadata = {
          id: getElementText(manuscriptEl, 'id'),
          title: getElementText(manuscriptEl, 'title'),
          articleType: getElementText(manuscriptEl, 'articletype'),
          doi: getElementText(manuscriptEl, 'doi'),
          keywords: getElementText(manuscriptEl, 'keywords'),
          currentPhase: getElementText(manuscriptEl, 'CurrentPhase'),
          accepted: getElementText(manuscriptEl, 'Accepted'),
          submissionDate: getElementText(manuscriptEl, 'submission_date')
        };
      }
    } else if (format === 'article') {
      authors = extractAuthorsFromArticle(xmlDoc);

      // Extract article metadata
      const articleMetaEl = xmlDoc.querySelector('article-meta');
      if (articleMetaEl) {
        const journalId = getElementText(xmlDoc, 'journal-id[journal-id-type="publisher"]');
        const articleId = getElementText(articleMetaEl, 'article-id[pub-id-type="manuscript"]');

        metadata = {
          id: articleId,
          journalId,
          title: getElementText(articleMetaEl, 'article-title'),
          articleType: getElementText(articleMetaEl, 'subj-group[subj-group-type="Article Type"] subject'),
          doi: getElementText(articleMetaEl, 'article-id[pub-id-type="doi"]')
        };
      }
    }

    return {
      format,
      authors,
      metadata
    };
  } catch (error) {
    console.error('Error extracting authors from XML:', error);
    throw error;
  }
};

/**
 * Extract all manuscripts/articles from a multi-document XML file
 * @param {string} xmlString - XML content containing multiple manuscripts/articles
 * @returns {Array} - Array of extraction results
 */
export const extractAllFromXml = (xmlString) => {
  try {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlString, 'text/xml');

    const results = [];

    // Extract all manuscripts
    const manuscripts = xmlDoc.querySelectorAll('manuscript');
    manuscripts.forEach(manuscript => {
      const manuscriptXml = new XMLSerializer().serializeToString(manuscript);
      try {
        const extracted = extractAuthorsFromXml(manuscriptXml);
        results.push(extracted);
      } catch (error) {
        console.error('Error extracting manuscript:', error);
      }
    });

    // Extract all articles
    const articles = xmlDoc.querySelectorAll('article');
    articles.forEach(article => {
      const articleXml = new XMLSerializer().serializeToString(article);
      try {
        const extracted = extractAuthorsFromXml(articleXml);
        results.push(extracted);
      } catch (error) {
        console.error('Error extracting article:', error);
      }
    });

    return results;
  } catch (error) {
    console.error('Error extracting all from XML:', error);
    throw error;
  }
};

/**
 * Get journal name from manuscript ID
 * @param {string} manuscriptId - Manuscript ID (e.g., "IJO_2526_25")
 * @returns {string} - Journal abbreviation
 */
export const getJournalFromManuscriptId = (manuscriptId) => {
  if (!manuscriptId) return '';

  // Extract journal code from ID (format: JOURNAL_NUMBER_YEAR)
  const parts = manuscriptId.split('_');
  return parts[0] || '';
};


