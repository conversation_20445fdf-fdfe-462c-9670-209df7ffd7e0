const fs = require("fs");
const path = require("path");
const AdmZip = require("adm-zip");
const { test, expect } = require("@playwright/test");
const { folderManager } = require("../util/folderManager");
const { appendDataToGoogleSheet } = require("../sheetAuth");
require("dotenv").config();

// Special run mode: download_articles_by_ids
// When active, the suite will ignore discovery/time filters and only process IDs from ARTICLE_IDS env var.
const DOWNLOAD_MODE = process.env.DOWNLOAD_MODE || "";
const isByIdsMode = DOWNLOAD_MODE === "download_articles_by_ids";
const ARTICLE_IDS = process.env.ARTICLE_IDS
  ? process.env.ARTICLE_IDS.split(",").map((s) => s.trim()).filter(Boolean)
  : [];

// Configuration
const INCOMING_DIR = process.env.INCOMING_DIR || "./incoming";
const TE_LOGIN_URL =
  process.env.TE_LOGIN_URL || "https://production.jow.medknow.com/login";
const TE_MYTASK_URL =
  process.env.TE_MYTASK_URL || "https://production.jow.medknow.com/mytask";
const USERNAME = process.env.TE_USERNAME || "<EMAIL>";
const PASSWORD = process.env.TE_PASSWORD || "Editing@1234";
const TARGET_DATE =
  process.env.TARGET_DATE ||
  new Date().toLocaleDateString("en-GB").split("/").join("-");
const ARTICLE_STATUS = process.env.ARTICLE_STATUS || "Yet-to-Start";

// Ensure incoming directory exists
if (!fs.existsSync(INCOMING_DIR)) {
  fs.mkdirSync(INCOMING_DIR, { recursive: true });
}

// Setup date folder
const DATE_DIR = path.join(INCOMING_DIR, TARGET_DATE);
if (!fs.existsSync(DATE_DIR)) {
  fs.mkdirSync(DATE_DIR, { recursive: true });
}

// Clean up any stale locks from previous runs
folderManager.cleanupStaleLocks();

// BATCH_DIR will be created dynamically in beforeAll to prevent race conditions
let BATCH_DIR;

test.describe.configure({ mode: "serial" });

test.describe("TE Download Phase", () => {
  let page;
  let articlesToDownload = [];
  let batchSummary = [];

  test.beforeAll(async ({ browser }) => {
    // Create batch directory safely to prevent race conditions
    BATCH_DIR = await folderManager.getSingletonBatchDir(DATE_DIR);
    console.log(`📁 Using batch directory: ${BATCH_DIR}`);

    const context = await browser.newContext({ downloadsPath: "./downloads" });
    page = await context.newPage();

    // Login
    await page.goto(TE_LOGIN_URL);
    await page
      .getByRole("textbox", { name: "User ID / Email ID" })
      .fill(USERNAME);
    await page.getByPlaceholder("Password").fill(PASSWORD);
    await page.getByRole("link", { name: "Login" }).click();

    console.log("✅ Successfully logged into TE portal");
    // If running in download_articles_by_ids mode, populate articlesToDownload from ARTICLE_IDS
    if (isByIdsMode) {
      if (!ARTICLE_IDS || ARTICLE_IDS.length === 0) {
        console.warn("⚠️ DOWNLOAD MODE active but no ARTICLE_IDS provided. No articles will be processed.");
      } else {
        articlesToDownload = ARTICLE_IDS.map((id) => ({
          articleId: id,
          journal: "",
          downloadDate: new Date().toISOString(),
        }));
        console.log(`🧭 download_articles_by_ids mode: will process ${articlesToDownload.length} article(s)`);
        articlesToDownload.forEach((a) => console.log(`   - ${a.articleId}`));
      }
    }
  });

  // Skip discovery when running in by-ids mode (we'll directly process ARTICLE_IDS)
  test.skip(isByIdsMode, 'Skipping discovery: running in download_articles_by_ids mode');

  test(`Discover articles with "${ARTICLE_STATUS}" status`, async () => {
    await page.goto(TE_MYTASK_URL);
    await page.waitForLoadState();

    await page.locator("#mytaskTable_length select").selectOption("100");

    console.log(`🗓️ Searching for articles with date: ${TARGET_DATE}`);
    console.log(`📋 Looking for articles with status: ${ARTICLE_STATUS}`);
    await page.getByLabel("Search:").fill(TARGET_DATE);

    await page
      .getByLabel("Schedule Start Date: activate to sort column ascending")
      .click();
    await page
      .getByLabel("Schedule Start Date: activate to sort column descending")
      .click();

    const rows = await page.locator("#mytaskTable tbody tr");
    const rowCount = await rows.count();

    console.log(`📊 Found ${rowCount} rows in task table`);

    for (let i = 0; i < rowCount; i++) {
      const columns = rows.nth(i).locator("td");
      const scheduleStartDate = (await columns.nth(6).innerText()).trim();

      if (scheduleStartDate === TARGET_DATE) {
        const status = (await columns.nth(5).innerText()).trim();
        // Check status: if ARTICLE_STATUS is "ANY", accept both "Yet-to-Start" and "In Progress"
        const statusMatches = ARTICLE_STATUS === "ANY"
          ? (status === "Yet-to-Start" || status === "In Progress")
          : (status === ARTICLE_STATUS);
        
        if (statusMatches) {
          const journalId = (await columns.nth(1).innerText()).trim();
          const articleId = (await columns.nth(3).innerText()).trim();

          articlesToDownload.push({
            articleId,
            journal: journalId,
            downloadDate: new Date().toISOString(),
          });
        }
      }
    }

    console.log(
      `🎯 Found ${articlesToDownload.length} articles with "${ARTICLE_STATUS}" status:`
    );
    articlesToDownload.forEach((a) =>
      console.log(`   - ${a.articleId} (${a.journal})`)
    );

    expect(articlesToDownload.length).toBeGreaterThan(0);
  });

  // If running download-by-ids mode but no ARTICLE_IDS were provided, skip processing to avoid a failing assertion.
  test.skip(isByIdsMode && ARTICLE_IDS.length === 0, 'No ARTICLE_IDS provided for download_articles_by_ids mode; skipping processing');

  test("Download and process all articles", async () => {
    console.log(
      `📥 Starting download phase for ${articlesToDownload.length} articles`
    );

    let successCount = 0;
    let errorCount = 0;

    for (const article of articlesToDownload) {
      try {
        const metadata = await downloadAndProcessArticle(page, article);
        batchSummary.push(metadata);
        if (metadata.status === "success") {
          successCount++;
        } else {
          errorCount++;
        }
      } catch (error) {
        console.error(
          `❌ Failed to process ${article.articleId}:`,
          error.message
        );
        errorCount++;
      }
    }

    const summaryPath = path.join(BATCH_DIR, "batch_summary.json");
    const summaryObj = {
      target_date: TARGET_DATE,
      batch_number: path.basename(BATCH_DIR),
      total_articles: articlesToDownload.length,
      success_count: successCount,
      error_count: errorCount,
      processed_at: new Date().toISOString(),
      articles: batchSummary,
    };

    fs.writeFileSync(summaryPath, JSON.stringify(summaryObj, null, 2));

    console.log(`\n📊 Batch Summary saved: ${summaryPath}`);

    // Update Google Sheets with download data (original simple approach)
    /* 
    // DISABLED: Moving to Single Source of Truth in TE_BACK
    try {
      console.log("\n📊 Updating Google Sheets with download data...");
      const targetDate = new Date();
      targetDate.setDate(targetDate.getDate() + 4);

      const formattedDate = targetDate.toLocaleDateString("en-GB");
      // Create simple data rows for each article (matching original format)
      const sheetsData = batchSummary.map((article) => [
        article.article_id,
        new Date().toLocaleDateString("en-GB"), // Date in DD/MM/YYYY format
        formattedDate,
      ]);

      if (sheetsData.length > 0) {
        await appendDataToGoogleSheet(sheetsData);
        console.log(
          `✅ Google Sheets updated with ${sheetsData.length} articles`
        );
      }
    } catch (sheetsError) {
      console.warn(
        "⚠️ Failed to update Google Sheets (continuing anyway):",
        sheetsError.message
      );
      // Don't fail the test if Google Sheets update fails
    }
    */

    expect(successCount).toBeGreaterThan(0);
  });
});

/**
 * Download + process one article
 */
async function downloadAndProcessArticle(page, article) {
  const { articleId, journal, downloadDate } = article;
  const zipPath = path.join(BATCH_DIR, `${articleId}.zip`);
  const jsonPath = path.join(BATCH_DIR, `${articleId}.json`);

  console.log(`\n🔄 Processing article: ${articleId}`);

  try {
    await page.goto(TE_MYTASK_URL);
    await page.waitForLoadState();

    await page.getByLabel("Search:").fill(articleId);
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);
    await expect(page.getByText(/Showing 1 to 1/)).toBeVisible();

    await page.getByRole("link", { name: "Start" }).click();
    await page.waitForLoadState();

    const authors = await extractAuthorDetails(page);

    // Normal download
    const downloadPromise = page.waitForEvent("download");
    await page.getByRole("link", { name: " Download" }).click();
    await page.waitForTimeout(1000);
    // 🚫 File Not Found check
    const fileNotFound = await page.getByText("File Not Found").count();
    if (fileNotFound > 0) {
      console.log(`🚫 File not found: ${articleId}`);

      const errorMetadata = {
        article_id: articleId,
        journal,
        download_date: downloadDate,
        files: { zip_path: null, error: "file_not_found" },
        authors,
        status: "file_not_found",
        source: "TE_portal",
        log: ["Portal displayed 'File Not Found'"],
      };

      // No ZIP file to embed metadata in for file not found case
      fs.writeFileSync(jsonPath, JSON.stringify(errorMetadata, null, 2));
      return errorMetadata;
    }
    try {
      const download = await downloadPromise;
      await download.saveAs(zipPath);
      console.log(`📦 Downloaded ZIP: ${zipPath}`);
    } catch (downloadError) {
      console.log(
        "Error downloading the file:",
        downloadError.message,
        articleId
      );
    }

    const metadata = await createArticleMetadata(
      articleId,
      journal,
      downloadDate,
      zipPath,
      authors
    );

    // Embed metadata JSON inside the ZIP file
    await embedMetadataInZip(zipPath, metadata);
    console.log(`📄 Embedded metadata inside ZIP: ${zipPath}`);

    // Still create external JSON for batch processing compatibility
    fs.writeFileSync(jsonPath, JSON.stringify(metadata, null, 2));
    console.log(`📄 Created external metadata: ${jsonPath}`);

    await page.getByRole("link", { name: "Cancel" }).click();
    console.log(`✅ Successfully processed: ${articleId}`);

    return metadata;
  } catch (error) {
    console.error(`❌ Error processing ${articleId}: ${error.message}`);

    const errorMetadata = {
      article_id: articleId,
      journal,
      download_date: downloadDate,
      files: {
        zip_path: `${TARGET_DATE}/${path.basename(BATCH_DIR)}/${articleId}.zip`,
        error: "processing_failed",
      },
      authors: [],
      status: "error",
      source: "TE_portal",
      log: [`Error: ${error.message}`],
    };

    // Try to embed metadata in ZIP if it exists
    if (fs.existsSync(zipPath)) {
      await embedMetadataInZip(zipPath, errorMetadata);
    }

    fs.writeFileSync(jsonPath, JSON.stringify(errorMetadata, null, 2));
    return errorMetadata;
  }
}

async function extractAuthorDetails(page) {
  const authors = [];
  try {
    await page
      .getByRole("button", { name: "Author Details" })
      .click({ force: true });
    await expect(
      page.getByRole("heading", { name: "Author Details" })
    ).toBeVisible();

    const tableElement = page.locator("#articleCommentTable tbody");
    const rowCount = await tableElement.locator("tr").count();

    for (let i = 0; i < rowCount; i++) {
      const columns = tableElement.locator("tr").nth(i).locator("td");
      authors.push({
        name: (await columns.nth(1).innerText()).trim().replace(/\s+/g, ' '),
        email: (await columns.nth(2).innerText()).trim().replace(/\s+/g, ' '),
        affiliation: (await columns.nth(3).innerText()).trim().replace(/\s+/g, ' '),
        copyright_status: (await columns.nth(6).innerText()).trim().replace(/\s+/g, ' '),
      });
    }
    await page.getByRole("button", { name: "×" }).click();
  } catch (err) {
    console.warn(`⚠️ Could not extract author details: ${err.message}`);
  }
  return authors;
}

async function createArticleMetadata(
  articleId,
  journal,
  downloadDate,
  zipPath,
  authors
) {
  const metadata = {
    article_id: articleId,
    journal,
    download_date: downloadDate,
    files: {
      zip_path: `${TARGET_DATE}/${path.basename(BATCH_DIR)}/${articleId}.zip`,
      manuscript_file: "missing",
      fp_file: "missing",
    },
    authors,
    status: "success",
    source: "TE_portal",
    log: [],
  };

  try {
    const zipContents = await analyzeZipContents(zipPath);
    metadata.files.manuscript_file = zipContents.manuscriptFile || "missing";
    metadata.files.fp_file = zipContents.fpFile || "missing";

    if (zipContents.error) {
      metadata.files.error = zipContents.error;
      metadata.log.push(`ZIP analysis error: ${zipContents.error}`);
    }
  } catch (err) {
    metadata.files.error = "corrupt zip";
    metadata.log.push(`ZIP corruption error: ${err.message}`);
  }
  return metadata;
}

async function analyzeZipContents(zipPath) {
  try {
    const zip = new AdmZip(zipPath);
    const zipEntries = zip.getEntries();

    let manuscriptFile = null;
    let fpFile = null;

    zipEntries.forEach((entry) => {
      const f = entry.entryName.toLowerCase();
      if (
        f.includes("manuscript") &&
        (f.endsWith(".docx") || f.endsWith(".doc"))
      )
        manuscriptFile = entry.entryName;
      if (f.includes("fp") && (f.endsWith(".docx") || f.endsWith(".doc")))
        fpFile = entry.entryName;
    });

    return {
      manuscriptFile,
      fpFile,
      totalFiles: zipEntries.length,
      error: null,
    };
  } catch (err) {
    return {
      manuscriptFile: null,
      fpFile: null,
      totalFiles: 0,
      error: err.message,
    };
  }
}

/**
 * Embed metadata JSON file inside the article ZIP
 * @param {string} zipPath - Path to the ZIP file
 * @param {object} metadata - Metadata object to embed
 */
async function embedMetadataInZip(zipPath, metadata) {
  try {
    // Read the existing ZIP file
    const zip = new AdmZip(zipPath);

    // Add the metadata JSON file to the ZIP
    const metadataJson = JSON.stringify(metadata, null, 2);
    zip.addFile("article_metadata.json", Buffer.from(metadataJson, "utf8"));

    // Write the updated ZIP file back
    zip.writeZip(zipPath);

    console.log(`✅ Successfully embedded metadata in ${path.basename(zipPath)}`);
  } catch (error) {
    console.error(`❌ Failed to embed metadata in ${path.basename(zipPath)}:`, error.message);
    // Don't throw - we want the process to continue even if embedding fails
  }
}
