/**
 * Centralized utility functions for app-wide functionality
 */

/**
 * Get URL search parameters
 * @returns {URLSearchParams} - URL search parameters object
 */
export const getSearchParams = () => {
  return new URLSearchParams(window.location.search);
};

/**
 * Check if app is in database mode
 * @returns {boolean} - True if db mode is enabled
 */
export const isDbMode = () => {
  return getSearchParams().get('db') === 'true';
};

/**
 * Check if app is in test mode
 * @returns {boolean} - True if test mode is enabled
 */
export const isTestMode = () => {
  return getSearchParams().has('test');
};

/**
 * Check if specific test parameter exists
 * @param {string} testParam - Test parameter to check
 * @returns {boolean} - True if test parameter exists
 */
export const hasTestParam = (testParam = 'test') => {
  return getSearchParams().has(testParam);
};

/**
 * Check if user is in admin role (authenticated admin)
 * Note: This function should be replaced with useRole hook in components
 * @returns {boolean} - True if admin role is enabled
 */
export const isAdminRole = () => {
  // For backward compatibility, check URL parameters
  return hasTestParam("admin");
};

/**
 * Check if user is in TE role
 * @returns {boolean} - True if TE role is enabled
 */
export const isTeRole = () => {
  return hasTestParam("te");
};

/**
 * Get current user role (legacy function - use useRole hook instead)
 * @returns {string} - Current role: 'admin', 'te', or 'default'
 */
export const getCurrentRole = () => {
  if (isAdminRole()) return 'admin';
  if (isTeRole()) return 'te';
  return 'default';
};

/**
 * Loading states enum for better consistency
 */
export const LOADING_STATES = {
  IDLE: 0,
  UPLOADING: 1,
  PROCESSING: 2,
  COMPLETE: 3,
  ERROR: -1
};

/**
 * Reference types enum
 */
export const REFERENCE_TYPES = {
  FOUND: 'FOUND',
  CROSSREF: 'CROSSREF',
  NOT_FOUND: 'NOT_FOUND',
  URL: 'URL',
  DUPLICATE: 'DUPLICATE'
};

/**
 * Mark types enum
 */
export const MARK_TYPES = {
  DUPLICATE: 'DUPLICATE',
  MISSING_CITATION: 'MISSING_CITATION'
};

/**
 * Filter types for reference filtering
 */
export const FILTER_TYPES = {
  ALL: 'all',
  PUBMED: 'pubmed',
  CROSSREF: 'crossref',
  NOT_FOUND: 'not-found',
  DUPLICATES: 'duplicates',
  URLS: 'urls',
  HIGH_CONFIDENCE: 'high-confidence',
  NEEDS_REVIEW: 'needs-review'
};

/**
 * Progress step types
 */
export const PROGRESS_STEPS = {
  EXTRACTING: 'extracting',
  FILTERING: 'filtering',
  PUBMED: 'pubmed',
  CROSSREF: 'crossref',
  GENAI: 'genai',
  COMPLETE: 'complete'
};
