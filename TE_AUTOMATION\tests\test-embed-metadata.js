/**
 * Test script to verify metadata embedding functionality
 * Run with: node tests/test-embed-metadata.js
 */

const fs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');

// Test function to create a sample ZIP with embedded metadata
async function testEmbedMetadata() {
  console.log('🧪 Testing metadata embedding functionality...\n');

  // Create a test directory
  const testDir = path.join(__dirname, 'test-output');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir);
  }

  // Sample metadata
  const sampleMetadata = {
    article_id: "PMC123456",
    journal: "Test Journal",
    download_date: new Date().toISOString(),
    files: {
      zip_path: "test/PMC123456.zip",
      manuscript_file: "manuscript.docx",
      fp_file: "first_page.pdf"
    },
    authors: [
      {
        name: "Dr. <PERSON>",
        email: "<EMAIL>",
        affiliation: "University of Science"
      },
      {
        name: "Dr. <PERSON>", 
        email: "<EMAIL>",
        affiliation: "Research Institute"
      }
    ],
    status: "success",
    source: "TE_portal",
    log: ["Downloaded successfully", "Metadata embedded"]
  };

  // Create a sample ZIP file with some dummy content
  const zip = new AdmZip();
  
  // Add some sample files
  zip.addFile("manuscript.docx", Buffer.from("Sample manuscript content", "utf8"));
  zip.addFile("first_page.pdf", Buffer.from("Sample PDF content", "utf8"));
  zip.addFile("readme.txt", Buffer.from("This is a test article ZIP file", "utf8"));
  
  // Add the metadata JSON file
  const metadataJson = JSON.stringify(sampleMetadata, null, 2);
  zip.addFile("article_metadata.json", Buffer.from(metadataJson, "utf8"));

  // Write the ZIP file
  const zipPath = path.join(testDir, 'PMC123456_with_metadata.zip');
  zip.writeZip(zipPath);

  console.log(`✅ Created test ZIP with embedded metadata: ${zipPath}`);

  // Verify the metadata can be extracted
  await verifyEmbeddedMetadata(zipPath);
}

// Function to verify metadata extraction
async function verifyEmbeddedMetadata(zipPath) {
  console.log('\n🔍 Verifying metadata extraction...');

  try {
    const zip = new AdmZip(zipPath);
    const entries = zip.getEntries();

    console.log('\n📁 ZIP Contents:');
    entries.forEach(entry => {
      console.log(`   - ${entry.entryName} (${entry.header.size} bytes)`);
    });

    // Look for metadata file
    const metadataEntry = zip.getEntry('article_metadata.json');
    
    if (metadataEntry) {
      const metadataContent = metadataEntry.getData().toString('utf8');
      const metadata = JSON.parse(metadataContent);
      
      console.log('\n✅ Successfully extracted metadata:');
      console.log(`   📄 Article ID: ${metadata.article_id}`);
      console.log(`   📰 Journal: ${metadata.journal}`);
      console.log(`   👥 Authors: <AUTHORS>
      console.log(`   📊 Status: ${metadata.status}`);
      console.log(`   📅 Downloaded: ${metadata.download_date}`);
      
      // Display authors
      if (metadata.authors && metadata.authors.length > 0) {
        console.log('\n👥 Authors: <AUTHORS>
        metadata.authors.forEach((author, index) => {
          console.log(`   ${index + 1}. ${author.name} (${author.email})`);
          console.log(`      ${author.affiliation}`);
        });
      }
      
      console.log('\n🎉 Test PASSED: Metadata embedding and extraction works correctly!');
    } else {
      console.log('❌ Test FAILED: No metadata file found in ZIP');
    }

  } catch (error) {
    console.error('❌ Test FAILED: Error extracting metadata:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testEmbedMetadata().catch(console.error);
}

module.exports = { testEmbedMetadata, verifyEmbeddedMetadata };
