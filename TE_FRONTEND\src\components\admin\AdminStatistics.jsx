import { useState, useEffect } from 'react';
import { Icons } from '../common';

const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:4999';

const AdminStatistics = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/api/admin/statistics`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
        setError(null);
      } else {
        setError('Failed to fetch statistics data');
      }
    } catch (error) {
      setError('Network error while fetching statistics');
      console.error('Statistics fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toString() || '0';
  };



  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading comprehensive statistics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 flex items-center justify-center h-96">
        <div className="text-center">
          <Icons.AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Statistics</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchStatistics}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">📊 Reference Processing Analytics</h1>
          <p className="text-gray-600">Simplified insights into reference processing performance and quality</p>
        </div>

        {/* Simplified Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">High Confidence</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(stats?.references?.quality_breakdown?.high_confidence || 0)}
                </p>
                <p className="text-xs text-gray-500">
                  {stats?.references?.review_status?.total_high_confidence > 0
                    ? ((stats?.references?.quality_breakdown?.high_confidence / (stats?.references?.quality_breakdown?.high_confidence + stats?.references?.quality_breakdown?.medium_confidence + stats?.references?.quality_breakdown?.low_confidence)) * 100).toFixed(1)
                    : 0}% quality rate
                </p>
              </div>
              <Icons.CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Needs Review</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(stats?.references?.quality_breakdown?.needs_review || 0)}
                </p>
                <p className="text-xs text-gray-500">
                  {stats?.references?.review_status?.review_ratio || 0}% review ratio
                </p>
              </div>
              <Icons.AlertCircle className="h-8 w-8 text-red-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total References</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(stats?.references?.source_breakdown?.total_references || 0)}
                </p>
                <p className="text-xs text-gray-500">
                  Successfully matched
                </p>
              </div>
              <Icons.BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Database Journals</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(stats?.journals?.database_journals || 0)}
                </p>
                <p className="text-xs text-gray-500">
                  {formatNumber(stats?.journals?.manual_journals || 0)} manual
                </p>
              </div>
              <Icons.FileText className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Simplified Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Quality Breakdown */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 Quality Breakdown</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-700">High Confidence</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">
                    {formatNumber(stats?.references?.quality_breakdown?.high_confidence || 0)}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded-full bg-yellow-500"></div>
                  <span className="text-sm font-medium text-gray-700">Medium Confidence</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">
                    {formatNumber(stats?.references?.quality_breakdown?.medium_confidence || 0)}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded-full bg-red-500"></div>
                  <span className="text-sm font-medium text-gray-700">Low Confidence</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">
                    {formatNumber(stats?.references?.quality_breakdown?.low_confidence || 0)}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between border-t pt-3">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded-full bg-orange-500"></div>
                  <span className="text-sm font-medium text-gray-700">Needs Review</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">
                    {formatNumber(stats?.references?.quality_breakdown?.needs_review || 0)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Source Breakdown */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🔍 Source Breakdown</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                  <span className="text-sm font-medium text-gray-700">PubMed Found</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">
                    {formatNumber(stats?.references?.source_breakdown?.pubmed_found || 0)}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-700">CrossRef Found</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">
                    {formatNumber(stats?.references?.source_breakdown?.crossref_found || 0)}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded-full bg-red-500"></div>
                  <span className="text-sm font-medium text-gray-700">Not Found</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">
                    {formatNumber(stats?.references?.source_breakdown?.not_found || 0)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Review Status & Journal Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Review Status */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📋 Review Status</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-green-900">Total High Confidence</div>
                  <div className="text-xs text-green-600">Successfully processed</div>
                </div>
                <div className="text-xl font-bold text-green-700">
                  {formatNumber(stats?.references?.review_status?.total_high_confidence || 0)}
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-orange-900">Total Needs Review</div>
                  <div className="text-xs text-orange-600">Manual intervention required</div>
                </div>
                <div className="text-xl font-bold text-orange-700">
                  {formatNumber(stats?.references?.review_status?.total_needs_review || 0)}
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-blue-900">Review Ratio</div>
                  <div className="text-xs text-blue-600">Needs review vs high confidence</div>
                </div>
                <div className="text-xl font-bold text-blue-700">
                  {stats?.references?.review_status?.review_ratio || 0}%
                </div>
              </div>
            </div>
          </div>

          {/* Journal Analytics */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📚 Journal Analytics</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-purple-900">Database Journals</div>
                  <div className="text-xs text-purple-600">From journal database</div>
                </div>
                <div className="text-xl font-bold text-purple-700">
                  {formatNumber(stats?.journals?.database_journals || 0)}
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-gray-900">Manual Journals</div>
                  <div className="text-xs text-gray-600">Manually added</div>
                </div>
                <div className="text-xl font-bold text-gray-700">
                  {formatNumber(stats?.journals?.manual_journals || 0)}
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-indigo-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-indigo-900">Total Journals</div>
                  <div className="text-xs text-indigo-600">Complete journal collection</div>
                </div>
                <div className="text-xl font-bold text-indigo-700">
                  {formatNumber((stats?.journals?.database_journals || 0) + (stats?.journals?.manual_journals || 0))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminStatistics;
