/**
 * Query File Generator
 * Generates [article_id]_query.txt files with validation issues
 */

/**
 * Generate query text for author mismatch issues
 * @param {Object} authorValidation - Result from validateAuthors()
 * @returns {Array} Array of query strings
 */
export const generateAuthorQueries = (authorValidation) => {
  const queries = [];

  // Authors missing in system
  if (authorValidation.missing_in_system && authorValidation.missing_in_system.length > 0) {
    const authorNames = authorValidation.missing_in_system.join(', ');
    const query = `QUERY: Author Mismatch - Missing in System

The following author(s) '${authorNames}' are missing in the system, which prevents verification of the copyright status.

Details:
- Found in First Page: ${authorNames}
- Missing from system records
- Action Required: Add authors to system or verify First Page content

Article ID: ${authorValidation.article_id}
Validation Date: ${new Date(authorValidation.validation_timestamp).toLocaleString()}`;

    queries.push(query);
  }

  // Authors missing in FP
  if (authorValidation.missing_in_fp && authorValidation.missing_in_fp.length > 0) {
    const authorNames = authorValidation.missing_in_fp.join(', ');
    const query = `QUERY: Author Mismatch - Missing in First Page

Author name '${authorNames}' given in system is missing in the manuscript's author list.

Details:
- Found in system: ${authorNames}
- Missing from First Page
- Action Required: Update First Page or verify system records

Article ID: ${authorValidation.article_id}
Validation Date: ${new Date(authorValidation.validation_timestamp).toLocaleString()}`;

    queries.push(query);
  }

  return queries;
};

/**
 * Generate query text for copyright issues
 * @param {Object} copyrightValidation - Result from validateCopyright()
 * @returns {Array} Array of query strings
 */
export const generateCopyrightQueries = (copyrightValidation) => {
  const queries = [];

  if (copyrightValidation.authors_without_copyright && copyrightValidation.authors_without_copyright.length > 0) {
    const authorDetails = copyrightValidation.authors_without_copyright.map(author => 
      `${author.name} (Status: ${author.status})`
    ).join(', ');

    const authorNames = copyrightValidation.authors_without_copyright.map(a => a.name).join(', ');

    const query = `QUERY: Copyright Agreement Missing

The author(s) '${authorNames}' has/have not agreed to the copyright terms.

Details:
${copyrightValidation.authors_without_copyright.map(author => 
  `- ${author.name}: Copyright Status = ${author.status}`
).join('\n')}

Total Authors: <AUTHORS>
Authors Without Copyright: ${copyrightValidation.authors_without_copyright.length}

Action Required: Obtain copyright agreement from listed authors

Article ID: ${copyrightValidation.article_id}
Validation Date: ${new Date(copyrightValidation.validation_timestamp).toLocaleString()}`;

    queries.push(query);
  }

  return queries;
};

/**
 * Generate complete query file content
 * @param {Object} validationResults - Combined validation results
 * @param {string} articleId - Article identifier
 * @returns {string} Complete query file content
 */
export const generateQueryFileContent = (validationResults, articleId) => {
  const queries = [];
  const timestamp = new Date().toLocaleString();

  // Header
  const header = `VALIDATION QUERIES FOR ARTICLE: ${articleId}
Generated: ${timestamp}
${'='.repeat(60)}

`;

  queries.push(header);

  // Author validation queries
  if (validationResults.author_validation && validationResults.author_validation.status === 'error') {
    const authorQueries = generateAuthorQueries(validationResults.author_validation);
    queries.push(...authorQueries);
  }

  // Copyright validation queries
  if (validationResults.copyright_validation && validationResults.copyright_validation.status === 'error') {
    const copyrightQueries = generateCopyrightQueries(validationResults.copyright_validation);
    queries.push(...copyrightQueries);
  }

  // If no queries, add a note
  if (queries.length === 1) { // Only header
    queries.push('No validation queries required - all checks passed.');
  }

  // Footer
  const footer = `

${'='.repeat(60)}
End of Validation Queries
Generated by TE Reference Processing Tool`;

  queries.push(footer);

  return queries.join('\n\n');
};

/**
 * Generate filename for query file
 * @param {string} articleId - Article identifier
 * @returns {string} Query filename
 */
export const generateQueryFilename = (articleId) => {
  return `${articleId}_query.txt`;
};

/**
 * Create downloadable query file blob
 * @param {string} content - Query file content
 * @param {string} filename - Query filename
 * @returns {Object} Blob and download URL
 */
export const createQueryFileBlob = (content, filename) => {
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  
  return {
    blob,
    url,
    filename,
    download: () => {
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };
};

/**
 * Main function to generate and download query file
 * @param {Object} validationResults - Combined validation results
 * @param {string} articleId - Article identifier
 * @returns {Object} Query file information
 */
export const generateAndDownloadQueryFile = (validationResults, articleId) => {
  const content = generateQueryFileContent(validationResults, articleId);
  const filename = generateQueryFilename(articleId);
  const fileInfo = createQueryFileBlob(content, filename);
  
  console.log(`Generated query file for ${articleId}:`, {
    filename,
    contentLength: content.length,
    hasAuthorQueries: validationResults.author_validation?.status === 'error',
    hasCopyrightQueries: validationResults.copyright_validation?.status === 'error'
  });
  
  return fileInfo;
};
