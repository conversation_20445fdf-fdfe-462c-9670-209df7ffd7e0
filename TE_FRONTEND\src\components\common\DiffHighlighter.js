import React from 'react';
import { diffChars } from 'diff';

/**
 * Component to highlight differences between two text strings
 * @param {Object} props - Component props
 * @param {string} props.text1 - Original text
 * @param {string} props.text2 - Modified text
 * @returns {JSX.Element} - Rendered component
 */
const DiffHighlighter = ({ text1, text2 }) => {
  if (!text1 || !text2) {
    return <p className="word-wrap break-words">{text2 || text1 || ''}</p>;
  }

  const diff = diffChars(text1, text2);

  return (
    <p className="word-wrap break-words">
      {diff.map((part, index) => (
        <span
          key={index}
          style={{ 
            color: part.added ? "green" : part.removed ? "red" : "grey" 
          }}
        >
          {part.value}
        </span>
      ))}
    </p>
  );
};

export default DiffHighlighter;
