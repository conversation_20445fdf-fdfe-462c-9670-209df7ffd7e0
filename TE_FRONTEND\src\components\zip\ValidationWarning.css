/* Validation Warning Styles - Modern Redesign */
.validation-warning-section {
  margin-bottom: 1.5rem;
}

.validation-warning-card {
  position: relative;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe5e5 100%);
  border: 2px solid #ff6b6b;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 8px 16px rgba(255, 107, 107, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;
}

.validation-warning-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(180deg, #ff6b6b 0%, #ee5a6f 100%);
}

.validation-warning-card:hover {
  box-shadow: 0 12px 24px rgba(255, 107, 107, 0.2);
  transform: translateY(-2px);
}

.validation-warning-content {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Header Section */
.validation-warning-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 111, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 107, 107, 0.2);
}

.validation-warning-icon {
  font-size: 2rem;
  flex-shrink: 0;
  animation: shake 2s ease-in-out infinite;
}

@keyframes shake {
  0%, 100% { transform: rotate(0deg); }
  10%, 30%, 50%, 70%, 90% { transform: rotate(-5deg); }
  20%, 40%, 60%, 80% { transform: rotate(5deg); }
}

.validation-warning-title {
  flex: 1;
}

.validation-warning-title h4 {
  color: #c92a2a;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  letter-spacing: -0.02em;
}

.validation-warning-title p {
  color: #e03131;
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
  font-weight: 500;
}

/* Body Section */
.validation-warning-body {
  padding: 1.5rem;
}

.validation-details {
  background: #ffffff;
  border-radius: 8px;
  padding: 1rem 1.25rem;
  margin: 0.75rem 0;
  border: 1px solid rgba(255, 107, 107, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.validation-details p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.validation-details p:first-child {
  margin-top: 0;
}

.validation-details p:last-child {
  margin-bottom: 0;
}

.validation-details strong {
  color: #c92a2a;
  font-weight: 600;
  min-width: 140px;
  display: inline-block;
}

.file-list {
  margin: 0.75rem 0 0 0;
  padding: 0;
  list-style: none;
}

.file-list li {
  color: #495057;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Courier New', monospace;
  font-size: 0.85rem;
  margin: 0.5rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.6rem 0.9rem;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.file-list li:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  border-color: #adb5bd;
  transform: translateX(4px);
}

.file-list li::before {
  content: '📄';
  font-size: 1rem;
  flex-shrink: 0;
}

/* Actions Section */
.validation-actions {
  display: flex;
  justify-content: flex-start;
  gap: 1rem;
}

/* Button styles moved to RaiseQueryButton.css */

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
  padding: 0.35rem 0.75rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.status-badge.success {
  background: linear-gradient(135deg, #d3f9d8 0%, #b2f2bb 100%);
  color: #2b8a3e;
  border: 1px solid #8ce99a;
}

.status-badge.error {
  background: linear-gradient(135deg, #ffe3e3 0%, #ffc9c9 100%);
  color: #c92a2a;
  border: 1px solid #ffa8a8;
}

.status-badge.warning {
  background: linear-gradient(135deg, #fff3bf 0%, #ffec99 100%);
  color: #e67700;
  border: 1px solid #ffd43b;
}
