import React, { useState, useEffect, useCallback, useMemo } from "react";
import { InfoIcon } from "../common/Icons";
import "./SmartBatchingModal.css";

// Premium journal codes that should be assigned to Subrata
const PREMIUM_JOURNAL_CODES = [
  "AOMD",
  "W<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "JOACP",
  "NI",
  "IJO",
  "IJA",
  "SMJ",
  "Neurol-India",
  "JFMPC",
  "INDIANJPSYCHIATRY",
  "JPGM",
  "IDOJ",
  "<PERSON>AN",
  "DSHMJ",
  "ABR",
];

// <PERSON><PERSON>'s TE ID for validation query articles (HIGHEST PRIORITY)
const LEENA_TE_ID = 6;

// Subrata's TE ID for premium article pre-selection (SECOND PRIORITY)
const SUBRATA_TE_ID = 9;

// TE Capacity Information (minimum articles per TE)
const TE_CAPACITY_INFO = [
  { name: "<PERSON><PERSON><PERSON>", minArticles: 5 },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", minArticles: 5 },
  { name: "<PERSON><PERSON><PERSON>", minArticles: 2 },
  { name: "<PERSON><PERSON><PERSON>", minArticles: 5 },
];

// Helper function to check if an article is premium
const isPremiumArticle = (articleId) => {
  if (!articleId) return false;
  const upperArticleId = articleId.toUpperCase();
  return PREMIUM_JOURNAL_CODES.some((code) => upperArticleId.includes(code));
};

// Helper function to check if an article has validation queries
const hasValidationQuery = (zip) => {
  // Check if validation query was sent for this article
  return zip.validationQuerySent === true;
};

const SmartBatchingModal = ({
  isOpen,
  onClose,
  completedArticles = [],
  availableTEs = [],
  onAssignmentComplete,
  jsonSummaryFile = null, // JSON summary file for author information
  batchSummary = null, // Batch summary with Drive IDs
}) => {
  const [batches, setBatches] = useState([]);
  const [selectedJournals, setSelectedJournals] = useState(new Set());
  const [smartBatchingEnabled, setSmartBatchingEnabled] = useState(false); // Disabled by default
  const [customBatchSize, setCustomBatchSize] = useState("");
  const [loading, setLoading] = useState(false);
  const [assignmentProgress, setAssignmentProgress] = useState({}); // { batchIndex: 'pending' | 'uploading' | 'completed' | 'failed' }
  const [uploadProgress, setUploadProgress] = useState({}); // { batchIndex: percentage }
  const [assignmentResults, setAssignmentResults] = useState(null);

  // API base URL
  const API_BASE = process.env.REACT_APP_API_URL || "http://localhost:4999";

  /**
   * Extract author information from JSON summary file
   * Returns a map of article_id -> article data with authors
   */
  const extractAuthorDataFromSummary = useCallback(async () => {
    if (!jsonSummaryFile) {
      console.warn('No JSON summary file provided');
      return null;
    }

    try {
      const fileReader = new FileReader();
      const fileContent = await new Promise((resolve, reject) => {
        fileReader.onload = (e) => resolve(e.target.result);
        fileReader.onerror = reject;
        fileReader.readAsText(jsonSummaryFile);
      });

      const summaryData = JSON.parse(fileContent);
      const articlesMap = new Map();

      // Handle different JSON structures
      let articles = [];
      if (Array.isArray(summaryData)) {
        articles = summaryData;
      } else if (summaryData.articles && Array.isArray(summaryData.articles)) {
        articles = summaryData.articles;
      }

      // Create map of article_id -> article data
      articles.forEach(article => {
        if (article.article_id) {
          articlesMap.set(article.article_id, {
            article_id: article.article_id,
            journal: article.journal || 'Unknown',
            authors: article.authors || [],
            status: article.status || 'unknown'
          });
        }
      });

      console.log(`📊 Extracted author data for ${articlesMap.size} articles from JSON summary`);
      return articlesMap;
    } catch (error) {
      console.error('Error parsing JSON summary file:', error);
      return null;
    }
  }, [jsonSummaryFile]);

  // Convert ZIP files to article format for the backend
  const articlesFromZips = React.useMemo(() => {
    return completedArticles.map((zip, index) => {
      const articleId = zip.articleId || zip.id;
      return {
        article_id: articleId,
        journal_code: smartBatchingEnabled ? "unknown" : `zip_${index}`, // Unique codes when not grouping
        journal_name: smartBatchingEnabled
          ? "Unknown Journal"
          : `ZIP File ${index + 1}`,
        zip_id: zip.id,
        filename: zip.filename,
        is_premium: isPremiumArticle(articleId), // Flag premium articles
        has_validation_query: hasValidationQuery(zip), // Flag articles with validation queries
      };
    });
  }, [completedArticles, smartBatchingEnabled]);

  // Get unique journal codes from articles (will be mostly 'unknown' for ZIPs)
  const uniqueJournals = React.useMemo(() => {
    const journals = new Map();
    articlesFromZips.forEach((article) => {
      const code = article.journal_code || "unknown";
      const name = article.journal_name || "Unknown Journal";
      if (!journals.has(code)) {
        journals.set(code, { code, name, count: 0 });
      }
      journals.get(code).count++;
    });
    return Array.from(journals.values());
  }, [articlesFromZips]);

  // Initialize selected journals (all selected by default)
  useEffect(() => {
    if (uniqueJournals.length > 0) {
      setSelectedJournals(new Set(uniqueJournals.map((j) => j.code)));
    }
  }, [uniqueJournals]);

  // Get filtered articles based on selected journals
  const filteredArticles = React.useMemo(() => {
    return articlesFromZips.filter((article) =>
      selectedJournals.has(article.journal_code || "unknown")
    );
  }, [articlesFromZips, selectedJournals]);

  // Separate articles by priority: Validation Query > Premium > Regular
  // PRIORITY 1: Articles with validation queries (assigned to Leena)
  const validationQueryArticles = useMemo(() => {
    return filteredArticles.filter((article) => article.has_validation_query);
  }, [filteredArticles]);

  // PRIORITY 2: Premium articles (assigned to Subrata) - EXCLUDING validation query articles
  const premiumArticles = useMemo(() => {
    return filteredArticles.filter(
      (article) => article.is_premium && !article.has_validation_query
    );
  }, [filteredArticles]);

  // PRIORITY 3: Regular articles - EXCLUDING both validation query and premium articles
  const regularArticles = useMemo(() => {
    return filteredArticles.filter(
      (article) => !article.is_premium && !article.has_validation_query
    );
  }, [filteredArticles]);

  // Create smart batches
  const createSmartBatches = useCallback(async () => {
    if (filteredArticles.length === 0 || availableTEs.length === 0) return;

    setLoading(true);
    try {
      const allBatches = [];
      let batchCounter = 1;

      // Step 1: Create validation query batch if validation query articles exist (HIGHEST PRIORITY)
      if (validationQueryArticles.length > 0) {
        const leenaTE = availableTEs.find((te) => te.id === LEENA_TE_ID);

        const validationBatch = {
          batch_id: `batch_${batchCounter}`,
          batch_number: batchCounter++,
          article_ids: validationQueryArticles.map((a) => a.article_id),
          articles: validationQueryArticles,
          article_count: validationQueryArticles.length,
          default_te_id: leenaTE?.id || availableTEs[0].id,
          default_te_name: leenaTE?.username || availableTEs[0].username,
          default_te_email: leenaTE?.email || availableTEs[0].email,
          batch_type: "validation_query",
          status: "pending",
        };

        allBatches.push(validationBatch);
      }

      // Step 2: Create premium batch if premium articles exist (SECOND PRIORITY)
      if (premiumArticles.length > 0) {
        const subrataTE = availableTEs.find((te) => te.id === SUBRATA_TE_ID);

        const premiumBatch = {
          batch_id: `batch_${batchCounter}`,
          batch_number: batchCounter++,
          article_ids: premiumArticles.map((a) => a.article_id),
          articles: premiumArticles,
          article_count: premiumArticles.length,
          default_te_id: subrataTE?.id || availableTEs[0].id,
          default_te_name: subrataTE?.username || availableTEs[0].username,
          default_te_email: subrataTE?.email || availableTEs[0].email,
          batch_type: "premium",
          status: "pending",
        };

        allBatches.push(premiumBatch);
      }

      // Step 3: Create regular batches if regular articles exist (LOWEST PRIORITY)
      if (regularArticles.length > 0) {
        const response = await fetch(
          `${API_BASE}/api/te-assignments/smart-batches`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({
              article_ids: regularArticles.map((a) => a.article_id),
              articles_data: regularArticles,
              batch_size: customBatchSize ? parseInt(customBatchSize) : null,
              group_by_journal: smartBatchingEnabled,
            }),
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // Mark as regular batches and adjust batch numbers
            const regularBatches = data.batches.map((batch, index) => ({
              ...batch,
              batch_type: "regular",
              batch_number: batchCounter + index, // Continue numbering after validation query and premium batches
            }));
            allBatches.push(...regularBatches);
          } else {
            console.error("Failed to create regular batches:", data.error);
          }
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      }

      // Set all batches (validation query + premium + regular)
      setBatches(allBatches);
    } catch (error) {
      console.error("Error creating smart batches:", error);
    } finally {
      setLoading(false);
    }
  }, [
    filteredArticles,
    validationQueryArticles,
    premiumArticles,
    regularArticles,
    availableTEs,
    customBatchSize,
    smartBatchingEnabled,
    API_BASE,
  ]);

  // Create batches when modal opens or settings change
  useEffect(() => {
    if (isOpen && filteredArticles.length > 0 && availableTEs.length > 0) {
      createSmartBatches();
    }
  }, [isOpen, createSmartBatches]);

  // Handle journal selection
  const handleJournalToggle = (journalCode) => {
    const newSelected = new Set(selectedJournals);
    if (newSelected.has(journalCode)) {
      newSelected.delete(journalCode);
    } else {
      newSelected.add(journalCode);
    }
    setSelectedJournals(newSelected);
  };

  // Handle moving articles between batches
  const moveArticle = (articleId, fromBatchIndex, toBatchIndex) => {
    if (fromBatchIndex === toBatchIndex) return;

    const newBatches = [...batches];
    const fromBatch = newBatches[fromBatchIndex];
    const toBatch = newBatches[toBatchIndex];

    const articleIndex = fromBatch.articles.findIndex(
      (a) => a.article_id === articleId
    );
    if (articleIndex === -1) return;

    const [article] = fromBatch.articles.splice(articleIndex, 1);
    toBatch.articles.push(article);

    // Update article_ids and counts
    fromBatch.article_ids = fromBatch.articles.map((a) => a.article_id);
    fromBatch.article_count = fromBatch.articles.length;
    toBatch.article_ids = toBatch.articles.map((a) => a.article_id);
    toBatch.article_count = toBatch.articles.length;

    setBatches(newBatches);
  };

  // Handle TE selection for a batch
  const handleTESelection = (batchIndex, teId) => {
    const newBatches = [...batches];
    const selectedTE = availableTEs.find((te) => te.id === parseInt(teId));
    if (selectedTE) {
      newBatches[batchIndex].default_te_id = selectedTE.id;
      newBatches[batchIndex].default_te_name = selectedTE.username;
      newBatches[batchIndex].default_te_email = selectedTE.email;
      setBatches(newBatches);
    }
  };

  // Assign single batch
  const assignBatch = async (batchIndex) => {
    const batch = batches[batchIndex];
    if (!batch || batch.articles.length === 0) return;

    setAssignmentProgress((prev) => ({ ...prev, [batchIndex]: "assigning" }));

    try {
      const batchName =
        batch.batch_name || `batch-${Date.now()}-${batchIndex + 1}`;

      // Get filenames for this batch
      const filenames = [];
      batch.article_ids.forEach((articleId) => {
        const zipData = completedArticles.find(
          (z) => z.articleId === articleId || z.id === articleId
        );
        if (zipData && zipData.filename) {
          filenames.push(zipData.filename);
        }
      });

      if (filenames.length === 0) {
        throw new Error("No files found for this batch");
      }

      // Get file IDs from dump folder
      const fileMapping = await getFileIdsFromDump(filenames);

      // Get drive file IDs
      const driveFileIds = filenames
        .map((filename) => fileMapping[filename]?.file_id)
        .filter(Boolean);

      if (driveFileIds.length === 0) {
        throw new Error("No files found in dump folder");
      }

      // Extract author information from JSON summary file
      let articlesWithAuthors = null;
      if (jsonSummaryFile) {
        try {
          const authorDataMap = await extractAuthorDataFromSummary();
          if (authorDataMap && authorDataMap.size > 0) {
            // Build articles_with_authors array for this batch
            articlesWithAuthors = batch.article_ids
              .map(articleId => authorDataMap.get(articleId))
              .filter(Boolean); // Remove any undefined entries

            console.log(`📧 Sending author data for ${articlesWithAuthors.length} articles in batch ${batchIndex + 1}`);
          }
        } catch (error) {
          console.error('Error extracting author data:', error);
          // Continue without author data - email will use simple table
        }
      }

      // Create assignment
      const response = await fetch(
        `${API_BASE}/api/te-assignments/create-from-dump`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({
            te_id: batch.default_te_id,
            batch_name: batchName,
            article_ids: batch.article_ids,
            drive_file_ids: driveFileIds,
            notes: `Smart batch assignment #${batch.batch_number}`,
            articles_with_authors: articlesWithAuthors, // Include author information
          }),
        }
      );

      if (!response.ok) throw new Error(`Backend error: ${response.status}`);
      const result = await response.json();
      if (!result.success)
        throw new Error(result.error || "Failed to create assignment");

      const assignmentId = result.assignment_id;

      setAssignmentProgress((prev) => ({ ...prev, [batchIndex]: "completed" }));
      setUploadProgress((prev) => ({ ...prev, [batchIndex]: 100 }));

      // Update batch status
      const newBatches = [...batches];
      newBatches[batchIndex].status = "completed";
      newBatches[batchIndex].assignment_id = assignmentId;
      setBatches(newBatches);
    } catch (error) {
      console.error(`Error assigning batch ${batchIndex + 1}:`, error);
      setAssignmentProgress((prev) => ({ ...prev, [batchIndex]: "failed" }));
      setUploadProgress((prev) => ({ ...prev, [batchIndex]: 0 }));
      alert(`Failed to assign batch: ${error.message}`);
    }
  };

  // Get file IDs from batch summary or dump folder for given filenames
  const getFileIdsFromDump = async (filenames) => {
    // First, try to get IDs from batch summary if available
    if (batchSummary?.articles) {
      console.log('📊 Using Drive IDs from batch summary');
      const fileMapping = {};
      filenames.forEach(filename => {
        const article = batchSummary.articles.find(a => a.filename === filename);
        if (article?.drive_file_id) {
          fileMapping[filename] = {
            file_id: article.drive_file_id,
            file_link: article.drive_file_link
          };
        }
      });

      // If we found all files in batch summary, return it
      if (Object.keys(fileMapping).length === filenames.length) {
        console.log(`✅ Found all ${filenames.length} files in batch summary`);
        return fileMapping;
      }

      console.log(`⚠️ Only found ${Object.keys(fileMapping).length}/${filenames.length} files in batch summary, falling back to dump folder`);
    }

    // Fallback: Get from dump folder
    console.log('📁 Fetching file IDs from dump folder...');
    const response = await fetch(
      `${API_BASE}/api/drive/get-file-ids-by-names`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ filenames }),
      }
    );

    if (!response.ok)
      throw new Error("Failed to get file IDs from dump folder");
    const data = await response.json();
    if (!data.success) throw new Error(data.error || "Failed to get file IDs");

    return data.file_mapping;
  };

  // Assign all batches using files from dump folder
  const assignAllBatches = async () => {
    setLoading(true);

    // Filter out batches with no articles
    const validBatches = batches.filter(
      (batch) => batch.articles && batch.articles.length > 0
    );

    if (validBatches.length === 0) {
      alert("No batches with articles to assign.");
      setLoading(false);
      return;
    }

    try {
      // Step 1: Get all unique filenames from all batches
      const allFilenames = [];
      validBatches.forEach((batch) => {
        batch.article_ids.forEach((articleId) => {
          const zipData = completedArticles.find(
            (z) => z.articleId === articleId || z.id === articleId
          );
          if (zipData && zipData.filename) {
            allFilenames.push(zipData.filename);
          }
        });
      });

      // Step 2: Get file IDs from dump folder (single API call for all files)
      console.log(
        `🔍 Looking up ${allFilenames.length} files in dump folder...`
      );
      const fileMapping = await getFileIdsFromDump(allFilenames);
      console.log(
        `✅ Found ${Object.keys(fileMapping).length} files in dump folder`
      );

      // Step 3: Create assignments for each batch
      const results = [];
      for (let i = 0; i < validBatches.length; i++) {
        const batch = validBatches[i];
        const batchName = batch.batch_name || `batch-${Date.now()}-${i + 1}`;

        // Get file IDs for this batch's articles
        const driveFileIds = [];
        batch.article_ids.forEach((articleId) => {
          const zipData = completedArticles.find(
            (z) => z.articleId === articleId || z.id === articleId
          );
          if (zipData && fileMapping[zipData.filename]) {
            driveFileIds.push(fileMapping[zipData.filename].file_id);
          }
        });

        if (driveFileIds.length === 0) {
          console.warn(`No files found in dump for batch ${i + 1}`);
          continue;
        }

        // Extract author information from JSON summary file
        let articlesWithAuthors = null;
        if (jsonSummaryFile) {
          try {
            const authorDataMap = await extractAuthorDataFromSummary();
            if (authorDataMap && authorDataMap.size > 0) {
              // Build articles_with_authors array for this batch
              articlesWithAuthors = batch.article_ids
                .map(articleId => authorDataMap.get(articleId))
                .filter(Boolean); // Remove any undefined entries

              console.log(`📧 Sending author data for ${articlesWithAuthors.length} articles in batch ${i + 1}`);
            }
          } catch (error) {
            console.error('Error extracting author data:', error);
            // Continue without author data - email will use simple table
          }
        }

        // Update status
        setAssignmentProgress((prev) => ({ ...prev, [i]: "assigning" }));

        // Create assignment (backend moves files from dump to batch folder)
        const response = await fetch(
          `${API_BASE}/api/te-assignments/create-from-dump`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            credentials: "include",
            body: JSON.stringify({
              te_id: batch.default_te_id,
              batch_name: batchName,
              article_ids: batch.article_ids,
              drive_file_ids: driveFileIds,
              notes: `Smart batch assignment #${batch.batch_number}`,
              articles_with_authors: articlesWithAuthors, // Include author information
            }),
          }
        );

        if (!response.ok) throw new Error(`Backend error: ${response.status}`);

        const result = await response.json();
        if (!result.success)
          throw new Error(result.error || "Failed to create assignment");

        results.push({
          assignment_id: result.assignment_id,
          batch_name: batchName,
          folder_link: result.folder_link,
          article_ids: batch.article_ids,
          te_id: batch.default_te_id,
          te_name: batch.default_te_name,
          te_email: batch.default_te_email,
        });

        setAssignmentProgress((prev) => ({ ...prev, [i]: "completed" }));
        console.log(`✅ Batch ${i + 1} assigned successfully`);
      }

      // Notify parent component
      if (onAssignmentComplete) {
        onAssignmentComplete({
          successful: results,
          failed: [],
          summary: { successful_batches: results.length, failed_batches: 0 },
        });
      }

      alert(
        `✅ Successfully assigned ${results.length} batches!\n\n` +
          `📊 ${results.reduce(
            (sum, r) => sum + r.article_ids.length,
            0
          )} articles assigned\n` +
          `📁 Files moved from dump folder to batch folders\n` +
          `📧 Email notifications sent`
      );
    } catch (error) {
      console.error("Error assigning batches:", error);
      alert(`❌ Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="smart-batching-modal-overlay">
      <div className="smart-batching-modal">
        <div className="modal-header">
          <h2>Smart Batching - TE Assignment</h2>
          <button className="close-button" onClick={onClose}>
            ×
          </button>
        </div>

        {/* TE Capacity Information Header */}
        <div className="te-capacity-info-header">
          <div className="capacity-info-title">
            <InfoIcon className="w-5 h-5" />
            <span>TE Capacity Guidelines</span>
          </div>
          <div className="capacity-info-list">
            {TE_CAPACITY_INFO.map((te, index) => (
              <div key={index} className="capacity-info-item">
                <span className="te-name">{te.name}:</span>
                <span className="te-capacity">{te.minArticles} articles</span>
              </div>
            ))}
          </div>
          <p className="capacity-info-note">
            These are minimum capacity guidelines. The system does not enforce
            these limits.
          </p>
        </div>

        {/* Article Selection - Only show when journal grouping is enabled */}
        {smartBatchingEnabled && (
          <div className="journal-selection">
            <h3>Select Journals ({filteredArticles.length} articles)</h3>
            <div className="journal-checkboxes">
              {uniqueJournals.map((journal) => (
                <label key={journal.code} className="journal-checkbox">
                  <input
                    type="checkbox"
                    checked={selectedJournals.has(journal.code)}
                    onChange={() => handleJournalToggle(journal.code)}
                  />
                  <span>
                    {journal.name} ({journal.count})
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Simple article count when not grouping by journal */}
        {!smartBatchingEnabled && (
          <div className="article-summary">
            <h3>Articles Ready for Assignment: {filteredArticles.length}</h3>
            {validationQueryArticles.length > 0 && (
              <p style={{ color: '#e74c3c', fontWeight: 'bold' }}>
                🔴 {validationQueryArticles.length} article(s) with validation queries → Leena
              </p>
            )}
            {premiumArticles.length > 0 && (
              <p style={{ color: '#f39c12', fontWeight: 'bold' }}>
                🟡 {premiumArticles.length} premium article(s) → Subrata
              </p>
            )}
            {regularArticles.length > 0 && (
              <p style={{ color: '#3498db' }}>
                🔵 {regularArticles.length} regular article(s) → Other TEs
              </p>
            )}
          </div>
        )}

        {/* Batching Controls */}
        <div className="batching-controls">
          <label className="smart-batching-toggle">
            <input
              type="checkbox"
              checked={smartBatchingEnabled}
              onChange={(e) => setSmartBatchingEnabled(e.target.checked)}
            />
            <span>Group by journal (not recommended for ZIP files)</span>
          </label>

          <div className="batch-size-control">
            <label>
              Custom batch size:
              <input
                type="number"
                min="1"
                max="50"
                value={customBatchSize}
                onChange={(e) => setCustomBatchSize(e.target.value)}
                placeholder="Auto"
              />
            </label>
            <div className="batch-size-info">
              Auto: {filteredArticles.length} articles ÷ {availableTEs.length}{" "}
              TEs ={" "}
              {Math.ceil(
                filteredArticles.length / Math.max(availableTEs.length, 1)
              )}{" "}
              articles per batch
            </div>
          </div>

          <button
            className="refresh-batches-btn"
            onClick={createSmartBatches}
            disabled={loading}
          >
            {loading ? "Creating..." : "Refresh Batches"}
          </button>
        </div>

        {/* Batch List */}
        {batches.length > 0 && (
          <div className="batches-container">
            <h3>Batches ({batches.length})</h3>
            <div className="batches-grid">
              {batches.map((batch, batchIndex) => (
                <BatchCard
                  key={`batch-${batchIndex}`}
                  batch={batch}
                  batchIndex={batchIndex}
                  availableTEs={availableTEs}
                  allBatches={batches}
                  onTESelection={handleTESelection}
                  onAssignBatch={assignBatch}
                  onMoveArticle={moveArticle}
                  assignmentProgress={assignmentProgress[batchIndex]}
                  uploadProgress={uploadProgress[batchIndex]}
                />
              ))}
            </div>
          </div>
        )}

        {/* Footer Actions */}
        <div className="modal-footer">
          <button
            className="assign-all-btn"
            onClick={assignAllBatches}
            disabled={
              loading ||
              batches.length === 0 ||
              batches.filter(
                (batch) => batch.articles && batch.articles.length > 0
              ).length === 0
            }
          >
            {loading
              ? "Assigning..."
              : (() => {
                  const validBatches = batches.filter(
                    (batch) => batch.articles && batch.articles.length > 0
                  );
                  return validBatches.length === 0
                    ? "No Valid Batches"
                    : `Assign All (${validBatches.length} batches)`;
                })()}
          </button>

          <button className="cancel-btn" onClick={onClose}>
            Cancel
          </button>
        </div>

        {/* Assignment Results */}
        {assignmentResults && (
          <div className="assignment-results">
            <h3>Assignment Results</h3>
            <div className="results-summary">
              <p>
                ✅ Successful:{" "}
                {assignmentResults.created.summary.successful_batches}/
                {assignmentResults.created.total_batches} batches
              </p>
              <p>
                📧 Emails sent:{" "}
                {assignmentResults.completed.summary.emails_sent}
              </p>
              <p>
                📁 Drive uploads:{" "}
                {assignmentResults.completed.summary.drive_uploads}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Batch Card Component
const BatchCard = ({
  batch,
  batchIndex,
  availableTEs,
  allBatches,
  onTESelection,
  onAssignBatch,
  onMoveArticle,
  assignmentProgress,
  uploadProgress,
}) => {
  const [selectedArticles, setSelectedArticles] = useState(new Set());
  const [showMoveOptions, setShowMoveOptions] = useState(false);

  const handleArticleSelect = (articleId) => {
    const newSelected = new Set(selectedArticles);
    if (newSelected.has(articleId)) {
      newSelected.delete(articleId);
    } else {
      newSelected.add(articleId);
    }
    setSelectedArticles(newSelected);
  };

  const handleMoveSelectedArticles = (toBatchIndex) => {
    selectedArticles.forEach((articleId) => {
      onMoveArticle(articleId, batchIndex, toBatchIndex);
    });
    setSelectedArticles(new Set());
    setShowMoveOptions(false);
  };

  return (
    <div
      className={`batch-card ${batch.status || "pending"} ${
        batch.batch_type === "validation_query" ? "validation-query-batch" :
        batch.batch_type === "premium" ? "premium-batch" : ""
      }`}
    >
      <div className="batch-header">
        <h4>
          Batch {batch.batch_number}
          {batch.batch_type === "validation_query" && (
            <span className="validation-query-badge"> 🔴 Validation Query</span>
          )}
          {batch.batch_type === "premium" && (
            <span className="premium-badge"> ⭐ Premium</span>
          )}
        </h4>
        <span className="article-count">{batch.article_count} articles</span>
      </div>
      <div className="te-selection">
        <label>Assign to TE:</label>
        <select
          value={batch.default_te_id}
          onChange={(e) => onTESelection(batchIndex, e.target.value)}
        >
          {availableTEs.map((te) => (
            <option key={te.id} value={te.id}>
              {te.username} ({te.email})
            </option>
          ))}
        </select>
      </div>

      <div className="articles-list">
        <div className="articles-header">
          <span>Articles</span>
          {selectedArticles.size > 0 && (
            <div className="article-actions">
              <button
                className="move-articles-btn"
                onClick={() => setShowMoveOptions(!showMoveOptions)}
              >
                Move ({selectedArticles.size})
              </button>
              {showMoveOptions && (
                <div className="move-options">
                  {allBatches.map(
                    (targetBatch, targetIndex) =>
                      targetIndex !== batchIndex && (
                        <button
                          key={targetIndex}
                          className="move-option"
                          onClick={() =>
                            handleMoveSelectedArticles(targetIndex)
                          }
                        >
                          → Batch {targetBatch.batch_number}
                        </button>
                      )
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {batch.articles && batch.articles.length > 0 ? (
          batch.articles.map((article) => (
            <div
              key={article.article_id}
              className={`article-item ${
                selectedArticles.has(article.article_id) ? "selected" : ""
              }`}
              onClick={() => handleArticleSelect(article.article_id)}
            >
              <input
                type="checkbox"
                checked={selectedArticles.has(article.article_id)}
                onChange={() => handleArticleSelect(article.article_id)}
                onClick={(e) => e.stopPropagation()}
              />
              <span className="article-id">{article.article_id}</span>
              <span className="article-journal">{article.journal_name}</span>
            </div>
          ))
        ) : (
          <div className="empty-batch-message">
            <span>No articles in this batch</span>
          </div>
        )}
      </div>

      <div className="batch-actions">
        <button
          className="assign-batch-btn"
          onClick={() => onAssignBatch(batchIndex)}
          disabled={
            assignmentProgress === "assigning" ||
            batch.status === "completed" ||
            !batch.articles ||
            batch.articles.length === 0
          }
        >
          {assignmentProgress === "uploading" ? (
            <>
              <span>
                Uploading {uploadProgress ? `${uploadProgress}%` : "..."}
              </span>
              {uploadProgress > 0 && (
                <div
                  style={{
                    position: "absolute",
                    bottom: 0,
                    left: 0,
                    height: "4px",
                    backgroundColor: "#4caf50",
                    width: `${uploadProgress}%`,
                    transition: "width 0.3s ease",
                  }}
                />
              )}
            </>
          ) : assignmentProgress === "completed" ? (
            "Assigned ✓"
          ) : assignmentProgress === "failed" ? (
            "Failed ✕"
          ) : !batch.articles || batch.articles.length === 0 ? (
            "No Articles"
          ) : (
            "Assign Batch"
          )}
        </button>
      </div>
    </div>
  );
};

export default SmartBatchingModal;
