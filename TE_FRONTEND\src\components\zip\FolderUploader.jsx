import React, { useState, useCallback } from 'react';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { Icons } from '../common';
import { runCompleteValidation } from '../../utils/manuscriptValidator';
import './FolderUploader.css';

const FolderUploader = ({ onFolderProcessed }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [folderName, setFolderName] = useState('');
  const [validationResults, setValidationResults] = useState([]);
  const [showValidationSummary, setShowValidationSummary] = useState(false);

  const extractArticleId = (filename) => {
    // Extract article ID from ZIP filename (remove .zip extension)
    const nameWithoutExt = filename.replace(/\.zip$/i, '');
    return nameWithoutExt;
  };

  const processFolder = useCallback(async (files) => {
    setIsProcessing(true);
    setError(null);

    try {
      // Filter for ZIP files and JSON summary files
      const zipFiles = Array.from(files).filter(file =>
        file.name.toLowerCase().endsWith('.zip')
      );

      const jsonFiles = Array.from(files).filter(file =>
        file.name.toLowerCase().endsWith('.json') &&
        (file.name.toLowerCase().includes('summary') || file.name.toLowerCase().includes('batch'))
      );

      if (zipFiles.length === 0) {
        setError('No ZIP files found in the selected folder.');
        return;
      }

      // Check for JSON summary file
      if (jsonFiles.length === 0) {
        setError('No JSON summary file found. Please include a summary or batch JSON file in your folder for enhanced TE assignment functionality.');
        return;
      }

      if (jsonFiles.length > 1) {
        setError(`Multiple JSON summary files found (${jsonFiles.map(f => f.name).join(', ')}). Please include only one summary file.`);
        return;
      }

      // Get folder name from first file's path
      const firstFile = files[0];
      const pathParts = firstFile.webkitRelativePath.split('/');
      const folderName = pathParts.length > 1 ? pathParts[0] : 'Individual ZIP Files';
      setFolderName(folderName);

      // Validate each ZIP file before creating queue items
      const validationResults = [];
      const zipQueue = [];

      for (let i = 0; i < zipFiles.length; i++) {
        const file = zipFiles[i];
        const articleId = extractArticleId(file.name);

        try {
          // Extract and validate ZIP contents
          const jsZip = new JSZip();
          const zipContent = await jsZip.loadAsync(file);

          // Extract file list
          const files = [];
          for (const [relativePath, zipEntry] of Object.entries(zipContent.files)) {
            if (!zipEntry.dir) {
              files.push({
                name: relativePath,
                path: relativePath,
                size: zipEntry._data ? zipEntry._data.uncompressedSize : 0,
                type: relativePath.toLowerCase().split('.').pop()
              });
            }
          }

          // Run complete validation (manuscript + FP)
          const completeValidation = runCompleteValidation(files, file.name);
          validationResults.push(completeValidation);

          // Simplified validation result - just pass the primary error if it exists
          const validationForUI = completeValidation.primary_error || null;

          // Create queue item with validation result (non-blocking)
          const queueItem = {
            id: `zip_${Date.now()}_${i}`,
            file: file,
            filename: file.name,
            articleId: articleId,
            size: file.size,
            status: 'pending', // Always set to pending - validation is informational only
            uploadedAt: new Date().toISOString(),
            processedAt: null,
            extractedFiles: files,
            error: null, // Don't set error for validation issues
            validationResult: validationForUI
          };

          zipQueue.push(queueItem);

        } catch (err) {
          console.error(`Error validating ZIP file ${file.name}:`, err);

          // Create failed validation result
          const failedValidation = {
            article_id: articleId,
            status: 'error',
            error_type: 'validation_failed',
            error_message: `Failed to process ZIP file: ${err.message}`,
            files: [],
            manuscript_files: [],
            can_proceed: false
          };

          validationResults.push(failedValidation);

          // Create queue item with error (non-blocking)
          zipQueue.push({
            id: `zip_${Date.now()}_${i}`,
            file: file,
            filename: file.name,
            articleId: articleId,
            size: file.size,
            status: 'pending', // Always set to pending - validation is informational only
            uploadedAt: new Date().toISOString(),
            processedAt: null,
            extractedFiles: null,
            error: null, // Don't set error for validation issues
            validationResult: failedValidation
          });
        }
      }

      // Store validation results for summary display
      setValidationResults(validationResults);

      // Show validation summary if there are any errors
      const hasErrors = validationResults.some(result => !result.can_proceed);
      if (hasErrors) {
        setShowValidationSummary(true);
      }

      // Call parent callback with processed folder data
      if (onFolderProcessed) {
        onFolderProcessed({
          folderName,
          zipQueue,
          totalZips: zipQueue.length,
          validationResults,
          hasValidationErrors: hasErrors,
          jsonSummaryFile: jsonFiles[0] // Include the JSON summary file for TE assignments
        });
      }

    } catch (err) {
      console.error('Error processing folder:', err);
      setError('Failed to process folder. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  }, [onFolderProcessed]);

  const handleFileSelect = useCallback((e) => {
    try {
      const files = e.target.files;
      if (files && files.length > 0) {
        // Convert FileList to Array and add webkitRelativePath for compatibility
        const fileArray = Array.from(files).map(file => {
          // Create a new file object with webkitRelativePath for compatibility
          const fileWithPath = new File([file], file.name, { type: file.type });
          Object.defineProperty(fileWithPath, 'webkitRelativePath', {
            value: file.name, // Use filename as path for individual files
            writable: false
          });
          return fileWithPath;
        });
        processFolder(fileArray);
      } else {
        setError('No files selected. Please select ZIP files.');
      }
    } catch (err) {
      console.error('Error handling file selection:', err);
      setError('Failed to process selected files. Please try again.');
    }
    // Reset the input value to allow selecting the same files again
    e.target.value = '';
  }, [processFolder]);

  const handleFolderSelect = useCallback((e) => {
    try {
      const files = e.target.files;
      if (files && files.length > 0) {
        processFolder(files);
      } else {
        setError('No files selected. Please select a folder containing ZIP files.');
      }
    } catch (err) {
      console.error('Error handling folder selection:', err);
      setError('Failed to process selected folder. Please try again.');
    }
    // Reset the input value to allow selecting the same folder again
    e.target.value = '';
  }, [processFolder]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);

    try {
      const items = Array.from(e.dataTransfer.items);
      const files = [];

      // Handle dropped folder
      items.forEach(item => {
        if (item.kind === 'file') {
          const entry = item.webkitGetAsEntry();
          if (entry && entry.isDirectory) {
            // This is a folder drop - we'll need to traverse it
            traverseDirectory(entry, files).then(() => {
              if (files.length > 0) {
                processFolder(files);
              } else {
                setError('No ZIP files found in the dropped folder.');
              }
            }).catch(err => {
              console.error('Error traversing directory:', err);
              setError('Failed to read folder contents. Please try again.');
            });
          }
        }
      });

      // Fallback for direct file drops
      if (files.length === 0) {
        const droppedFiles = Array.from(e.dataTransfer.files);
        if (droppedFiles.length > 0) {
          processFolder(droppedFiles);
        } else {
          setError('Please drop a folder containing ZIP files.');
        }
      }
    } catch (err) {
      console.error('Error handling drop:', err);
      setError('Failed to process dropped items. Please try again.');
    }
  }, [processFolder]);

  const traverseDirectory = async (entry, files) => {
    return new Promise((resolve) => {
      if (entry.isFile) {
        entry.file((file) => {
          if (file.name.toLowerCase().endsWith('.zip')) {
            files.push(file);
          }
          resolve();
        });
      } else if (entry.isDirectory) {
        const reader = entry.createReader();
        reader.readEntries((entries) => {
          const promises = entries.map(childEntry => 
            traverseDirectory(childEntry, files)
          );
          Promise.all(promises).then(() => resolve());
        });
      } else {
        resolve();
      }
    });
  };

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className="folder-uploader-container">
      <div className="folder-uploader-header">
        <h2 className="folder-uploader-title">
          <span className="folder-icon">📁</span>
          Upload ZIP Files
        </h2>
        <p className="folder-uploader-description">
          Upload single ZIP files or select a folder containing multiple ZIP files for batch processing
        </p>
      </div>

      {error && (
        <div className="folder-error-message">
          <Icons.ExclamationTriangleIcon />
          <span>{error}</span>
        </div>
      )}

      <div
        className={`folder-drop-zone ${isDragOver ? 'drag-over' : ''} ${isProcessing ? 'processing' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {isProcessing ? (
          <div className="folder-processing">
            <div className="folder-spinner"></div>
            <p>Processing folder...</p>
            {folderName && <p className="folder-name">Folder: {folderName}</p>}
          </div>
        ) : (
          <>
            <div className="folder-upload-icon">
              <Icons.FolderIcon />
            </div>
            <div className="folder-upload-text">
              <p className="folder-main-text">
                Drop ZIP files or folder here, or{' '}
                <label className="folder-browse-link">
                  browse
                  <input
                    type="file"
                    accept=".zip,.json"
                    multiple
                    onChange={handleFileSelect}
                    className="folder-file-input"
                  />
                </label>
                {' / '}
                <label className="folder-browse-link">
                  browse folder
                  <input
                    type="file"
                    webkitdirectory=""
                    directory=""
                    multiple
                    onChange={handleFolderSelect}
                    className="folder-file-input"
                  />
                </label>
              </p>
              <p className="folder-sub-text">
                Upload ZIP files + JSON summary file, or select a folder containing both
              </p>
              <p className="folder-sub-text" style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                📋 Required: Include a summary.json or batch_summary.json file with article metadata
              </p>
            </div>
          </>
        )}
      </div>

      {showValidationSummary && validationResults.length > 0 && (
        <div className="validation-summary">
          <h3>Manuscript Validation Results</h3>
          <div className="validation-stats">
            <div className="stat-item success">
              <span className="stat-number">
                {validationResults.filter(r => r.can_proceed).length}
              </span>
              <span className="stat-label">Passed</span>
            </div>
            <div className="stat-item error">
              <span className="stat-number">
                {validationResults.filter(r => !r.can_proceed).length}
              </span>
              <span className="stat-label">Failed</span>
            </div>
            <div className="stat-item total">
              <span className="stat-number">{validationResults.length}</span>
              <span className="stat-label">Total</span>
            </div>
          </div>

          <div className="validation-details">
            {validationResults.map((result, index) => (
              <div
                key={index}
                className={`validation-item ${result.can_proceed ? 'success' : 'error'}`}
              >
                <div className="validation-header">
                  <span className="validation-icon">
                    {result.can_proceed ? '✅' : '❌'}
                  </span>
                  <span className="article-id">{result.article_id}</span>
                  <span className="validation-status">
                    {result.can_proceed ? 'Valid' : result.error_type}
                  </span>
                </div>

                {!result.can_proceed && (
                  <div className="validation-error-details">
                    <p>{result.error_message}</p>
                    {result.error_type === 'no_manuscript' && (
                      <div className="file-list">
                        <strong>Files found:</strong>
                        <ul>
                          {result.files.map((file, i) => (
                            <li key={i}>{file}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {result.error_type === 'multiple_manuscripts' && (
                      <div className="file-list">
                        <strong>Manuscript files found:</strong>
                        <ul>
                          {result.manuscript_files.map((file, i) => (
                            <li key={i}>{file}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="validation-actions">
            <button
              onClick={() => setShowValidationSummary(false)}
              className="close-summary-button"
            >
              Close Summary
            </button>
          </div>
        </div>
      )}

      <div className="folder-help-section">
        <h3>How it works:</h3>
        <div className="folder-help-steps">
          <div className="folder-help-step">
            <span className="step-number">1</span>
            <span>Upload individual ZIP files or select a folder containing multiple ZIP files</span>
          </div>
          <div className="folder-help-step">
            <span className="step-number">2</span>
            <span>Each ZIP will be added to the processing queue</span>
          </div>
          <div className="folder-help-step">
            <span className="step-number">3</span>
            <span>Process each ZIP individually with full workflow</span>
          </div>
          <div className="folder-help-step">
            <span className="step-number">4</span>
            <span>References are automatically saved and ZIPs marked as processed</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FolderUploader;
