/**
 * Email Service for TE Reference Processing Tool
 * Handles sending validation query emails via backend API
 */

import { sendQueryEmailApi } from '../constants/urls';

/**
 * Send a validation query email
 * @param {Object} emailData - Email data object
 * @param {string} emailData.to - Recipient email address
 * @param {string} emailData.subject - Email subject
 * @param {string} emailData.description - Email description/body
 * @param {string} emailData.articleId - Article ID
 * @param {Object} emailData.validationResult - Validation result object
 * @param {File} emailData.zipFile - Optional ZIP file attachment
 * @returns {Promise<Object>} API response
 */
export const sendValidationQueryEmail = async (emailData) => {
  try {
    let response;

    if (emailData.zipFile) {
      // Send as multipart form data with file attachment
      console.log('Sending email with ZIP file attachment:', emailData.zipFile.name, 'Size:', emailData.zipFile.size);

      const formData = new FormData();
      formData.append('to', emailData.to);
      formData.append('subject', emailData.subject);
      formData.append('description', emailData.description);
      formData.append('articleId', emailData.articleId);
      formData.append('validationResult', JSON.stringify(emailData.validationResult));
      formData.append('zipFile', emailData.zipFile);

      console.log('FormData contents:');
      for (let [key, value] of formData.entries()) {
        if (key === 'zipFile') {
          console.log(`${key}:`, value.name, value.size, 'bytes');
        } else {
          console.log(`${key}:`, value);
        }
      }

      response = await fetch(sendQueryEmailApi, {
        method: 'POST',
        credentials: 'include',
        body: formData
      });
    } else {
      // Send as JSON (no file attachment)
      response = await fetch(sendQueryEmailApi, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          to: emailData.to,
          subject: emailData.subject,
          description: emailData.description,
          articleId: emailData.articleId,
          validationResult: emailData.validationResult,
        })
      });
    }

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || `HTTP error! status: ${response.status}`);
    }

    return result;
  } catch (error) {
    console.error('Email service error:', error);
    throw error;
  }
};

/**
 * Validate email data before sending
 * @param {Object} emailData - Email data to validate
 * @returns {Object} Validation result with isValid boolean and errors array
 */
export const validateEmailData = (emailData) => {
  const errors = [];
  
  if (!emailData.to || !emailData.to.trim()) {
    errors.push('Recipient email address is required');
  } else if (!isValidEmail(emailData.to)) {
    errors.push('Please enter a valid email address');
  }
  
  if (!emailData.subject || !emailData.subject.trim()) {
    errors.push('Subject is required');
  }
  
  if (!emailData.description || !emailData.description.trim()) {
    errors.push('Description is required');
  }
  
  if (!emailData.articleId || !emailData.articleId.trim()) {
    errors.push('Article ID is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
};

/**
 * Simple email validation
 * @param {string} email - Email address to validate
 * @returns {boolean} True if email format is valid
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Format validation result for email display
 * @param {Object} validationResult - Validation result object
 * @returns {string} Formatted validation details
 */
export const formatValidationForEmail = (validationResult) => {
  if (!validationResult) return 'No validation details available';
  
  let details = `Validation Status: ${validationResult.status}\n`;
  
  if (validationResult.error_type) {
    details += `Error Type: ${validationResult.error_type}\n`;
  }
  
  if (validationResult.error_message) {
    details += `Error Message: ${validationResult.error_message}\n`;
  }
  
  if (validationResult.files && validationResult.files.length > 0) {
    details += `\nFiles Found (${validationResult.files.length}):\n`;
    validationResult.files.forEach((file, index) => {
      details += `${index + 1}. ${file}\n`;
    });
  }
  
  if (validationResult.manuscript_files && validationResult.manuscript_files.length > 0) {
    details += `\nManuscript Files:\n`;
    validationResult.manuscript_files.forEach((file, index) => {
      details += `${index + 1}. ${file}\n`;
    });
  }
  
  if (validationResult.fp_files && validationResult.fp_files.length > 0) {
    details += `\nFirst Page Files:\n`;
    validationResult.fp_files.forEach((file, index) => {
      details += `${index + 1}. ${file}\n`;
    });
  }
  
  return details;
};

/**
 * Get default recipient email based on validation type
 * @param {Object} validationResult - Validation result object
 * @returns {string} Default recipient email
 */
export const getDefaultRecipient = (validationResult) => {
  // You can customize this based on your organization's workflow
  // For now, return a default email
  return '<EMAIL>';
};

/**
 * Email service status and configuration
 */
export const emailServiceConfig = {
  isEnabled: true,
  maxAttachmentSize: 25 * 1024 * 1024, // 25MB
  supportedAttachmentTypes: ['.zip', '.docx', '.doc', '.pdf'],
  defaultSender: '<EMAIL>',
};
