{"version": 3, "file": "static/css/main.53930e38.css", "mappings": "AAAA;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,yEAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,qBAAc,CAAd,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAEd,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,gBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,cAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,4BAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,yCAAmB,CAAnB,wCAAmB,CAAnB,+NAAmB,CAAnB,mCAAmB,CAAnB,wMAAmB,CAAnB,+CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,qCAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,gFAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,8BAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,oDAAmB,CAAnB,sCAAmB,CAAnB,oDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,uCAAmB,CAAnB,sDAAmB,CAAnB,uCAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,sDAAmB,CAAnB,qCAAmB,CAAnB,sDAAmB,CAAnB,qCAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,qDAAmB,CAAnB,wCAAmB,CAAnB,yDAAmB,CAAnB,4CAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,oDAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,8BAAmB,CAAnB,mDAAmB,CAAnB,+BAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,oDAAmB,CAAnB,+BAAmB,CAAnB,oDAAmB,CAAnB,+BAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,qDAAmB,CAAnB,gCAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,qDAAmB,CAAnB,6BAAmB,CAAnB,sDAAmB,CAAnB,4BAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,oDAAmB,CAAnB,2BAAmB,CAAnB,sDAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,gCAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,oDAAmB,CAAnB,kCAAmB,CAAnB,6FAAmB,CAAnB,qFAAmB,CAAnB,yEAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,yEAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,gEAAmB,CAAnB,sGAAmB,CAAnB,oEAAmB,CAAnB,oEAAmB,CAAnB,oEAAmB,CAAnB,qEAAmB,CAAnB,qEAAmB,CAAnB,sEAAmB,CAAnB,sEAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,oCAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,mDAAmB,CAAnB,2CAAmB,CAAnB,8CAAmB,CAAnB,mDAAmB,CAAnB,0CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,8GAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,mCAAmB,CAAnB,mCAAmB,CAAnB,qCAAmB,CAAnB,yBAAmB,CAAnB,kCAAmB,CAAnB,sCAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,mCAAmB,CAAnB,2CAAmB,CAAnB,mCAAmB,CAAnB,2CAAmB,CAAnB,mCAAmB,CAAnB,2CAAmB,CAAnB,mCAAmB,CAAnB,0CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,oCAAmB,CAAnB,4CAAmB,CAAnB,oCAAmB,CAAnB,4CAAmB,CAAnB,oCAAmB,CAAnB,4CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,iCAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,+BAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,wEAAmB,CAAnB,+FAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,gDAAmB,CAAnB,oCAAmB,CAAnB,4BAAmB,CAAnB,wLAAmB,CAAnB,+CAAmB,CAAnB,8QAAmB,CAAnB,sQAAmB,CAAnB,kMAAmB,CAAnB,6IAAmB,CAAnB,mMAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,0EAAmB,CAAnB,kDAAmB,CAAnB,+DAAmB,CAEnB,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CAGA,iBAIE,wBAA6B,CAA7B,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAQA,gBACE,gCACF,CApCA,kDAgDA,CAhDA,0PAgDA,CAhDA,0DAgDA,CAhDA,mDAgDA,CAhDA,sDAgDA,CAhDA,2CAgDA,CAhDA,sDAgDA,CAhDA,2CAgDA,CAhDA,oDAgDA,CAhDA,2CAgDA,CAhDA,sDAgDA,CAhDA,2CAgDA,CAhDA,sDAgDA,CAhDA,0CAgDA,CAhDA,sDAgDA,CAhDA,4CAgDA,CAhDA,oDAgDA,CAhDA,0CAgDA,CAhDA,sDAgDA,CAhDA,uFAgDA,CAhDA,yDAgDA,CAhDA,iEAgDA,CAhDA,mFAgDA,CAhDA,+CAgDA,CAhDA,0CAgDA,CAhDA,+CAgDA,CAhDA,0CAgDA,CAhDA,uFAgDA,CAhDA,iGAgDA,CAhDA,+FAgDA,CAhDA,kGAgDA,CAhDA,qFAgDA,CAhDA,+FAgDA,CAhDA,wFAgDA,CAhDA,kGAgDA,CAhDA,+CAgDA,CAhDA,kGAgDA,CAhDA,mDAgDA,CAhDA,qDAgDA,CAhDA,mDAgDA,CAhDA,kDAgDA,CAhDA,kBAgDA,CAhDA,+HAgDA,CAhDA,wGAgDA,CAhDA,uEAgDA,CAhDA,wFAgDA,CAhDA,+CAgDA,CAhDA,sDAgDA,CAhDA,sDAgDA,CAhDA,yDAgDA,CAhDA,yCAgDA,CAhDA,wDAgDA,CAhDA,sDAgDA,CAhDA,4DAgDA,CAhDA,0CAgDA,CAhDA,+CAgDA,CAhDA,wBAgDA,CAhDA,oBAgDA,CAhDA,8DAgDA,CAhDA,gCAgDA,CAhDA,oCAgDA,CAhDA,kDAgDA,CAhDA,6BAgDA,CAhDA,oBAgDA,EAhDA,uFAgDA,CAhDA,8DAgDA,EAhDA,qDAgDA,CAhDA,oBAgDA,CAhDA,8DAgDA,CAhDA,8DAgDA,CAhDA,gCAgDA,CAhDA,oCAgDA,CAhDA,qBAgDA,CAhDA,2BAgDA,CAhDA,kBAgDA,EC7CA,OAKE,qBAAyB,CAIzB,wBAAyB,CAPzB,uBAAyB,CAIzB,kBAAmB,CAHnB,gBAAiB,CAKjB,4DAAiF,CAJjF,eAAgB,CAGhB,eAGF,CAGA,UACE,wBAAyB,CAIzB,+BAAgC,CAHhC,aAAc,CAKd,iBAAmB,CAJnB,eAAgB,CAKhB,mBAAoB,CAJpB,mBAAqB,CAErB,eAGF,CAEA,sBACE,2BACF,CAEA,qBACE,4BACF,CAGA,UACE,oCACF,CAEA,sBACE,wBACF,CAEA,0CACE,8BACF,CAEA,yCACE,+BACF,CAGA,UAEE,+BAAgC,CAChC,aAAc,CACd,iBAAmB,CACnB,mBAEF,CAEA,8BACE,kBACF,CAGA,sBAGE,kDAA6D,CAE7D,8BAA+B,CAD/B,eAAgB,CAFhB,iBAAkB,CADlB,WAKF,CAKA,UACE,wBACF,CAGA,oCACE,OACE,cACF,CACA,oBAEE,WACF,CACF,CAuBA,yLAIE,iBACF,CAMA,QAME,iCAAkC,CAFlC,0BAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAG7B,+BAA+C,CAL/C,WAAY,CADZ,UAOF,CAQA,mBAEE,kBAEF,CAMA,kBACE,UACF,CAEA,oBAIE,wBAAyB,CACzB,iBAAkB,CAClB,mBAAoB,CACpB,cAAe,CALf,eAAgB,CAChB,WAAY,CAKZ,eAAgB,CAPhB,UAQF,CAEA,gBACE,YAAa,CACb,OAAQ,CACR,cACF,CAEA,aAEE,UAAW,CADX,cAEF,CAEA,eACE,eAAgB,CAIhB,iBAAkB,CAClB,cAAe,CAFf,eAGF,CAEA,qBACE,wBACF,CAEA,mBACE,wBACF,CAEA,mBACE,wBACF,CAEA,qBACE,wBACF,CAGA,mBACE,oBAAqB,CACrB,eACF,CAEA,6BACE,aACF,CAEA,4BACE,aACF,CAEA,0BACE,aACF,CAGA,sBACE,eAAmB,CAInB,wBAAyB,CAFzB,kBAAmB,CAGnB,sDAA2E,CAF3E,eAAgB,CAFhB,cAKF,CAEA,yBAEE,aAAc,CACd,kBAAmB,CACnB,eAAgB,CAHhB,eAIF,CAEA,YAEE,wDAEF,CAEA,WAKE,kBAAmB,CAEnB,wBAAyB,CADzB,mBAAqB,CAJrB,6BAA8B,CAE9B,mBAAqB,CAIrB,uBACF,CAEA,iBACE,kBAAmB,CAEnB,4DAAiF,CADjF,0BAEF,CAEA,qBACE,cAAe,CACf,wBAAiB,CAAjB,gBACF,CAEA,2BACE,kBAAmB,CACnB,oBACF,CAEA,kBACE,kBAAmB,CACnB,oBAAqB,CACrB,4BACF,CAEA,oBAEE,kBAAmB,CADnB,UAAY,CAEZ,mBACF,CAEA,0BACE,kBAAmB,CAEnB,sDAA2E,CAD3E,cAEF,CAEA,kBAEE,aAAc,CADd,iBAAmB,CAEnB,eACF,CAQA,YAEE,kBAAmB,CADnB,eAEF,CAEA,mBACE,aACF,CAEA,qBACE,aACF,CAEA,sBACE,aACF,CAEA,uBACE,aACF,CAEA,iBACE,aACF,CAGA,cAKE,mBAAqB,CAMrB,gCAA0C,CAJ1C,eAAgB,CAJhB,OAAQ,CAMR,qBAAuB,CACvB,UAAW,CANX,gBAAiB,CAQjB,uBAAyB,CACzB,kBACF,CAEA,oBAEE,8BAAyC,CADzC,0BAEF,CAOA,wBAJE,gBAAkB,CAClB,aAQF,CALA,YAEE,eAAgB,CAEhB,+BACF,CAGA,cACE,wBAAyB,CACzB,UACF,CAEA,gBACE,wBAAyB,CACzB,UACF,CAEA,iBACE,wBAAyB,CACzB,UACF,CAEA,WACE,wBAAyB,CACzB,UACF,CAEA,iBAGE,2BAA4B,CAF5B,wBAAyB,CAGzB,wBAAyB,CAEzB,8BAA4C,CAJ5C,UAAY,CAGZ,eAEF,CAEA,gBACE,wBAAyB,CACzB,UACF,CAGA,eAEE,kBAAmB,CAGnB,kBAAmB,CAInB,8BAAwC,CACxC,WAAY,CATZ,mBAAoB,CAKpB,cAAe,CAHf,OAAQ,CAKR,UAAW,CAJX,eAOF,CAEA,iBACE,kDAAqD,CACrD,UACF,CAEA,YACE,kDAAqD,CACrD,UACF,CAEA,eACE,kDAAqD,CACrD,aACF,CAEA,YACE,kDAAqD,CACrD,UACF,CAGA,cAEE,kBAAmB,CAUnB,kBAAmB,CAPnB,WAAY,CACZ,qBAAuB,CAQvB,gCAA0C,CAD1C,aAAc,CAJd,cAAe,CARf,mBAAoB,CAMpB,gBAAkB,CAClB,eAAgB,CALhB,OAAQ,CAOR,UAAW,CANX,eAAgB,CAOhB,uBAAyB,CAIzB,kBACF,CAEA,mCAEE,8BAAyC,CADzC,0BAEF,CAEA,uBAEE,kBAAmB,CADnB,UAEF,CAEA,kBACE,wBAAyB,CACzB,UACF,CAEA,kBACE,wBAAyB,CACzB,UACF,CAEA,oBACE,wBAAyB,CACzB,UACF,CAEA,oBACE,wBAAyB,CACzB,UACF,CAEA,kBACE,wBAAyB,CACzB,UACF,CAEA,oBACE,wBAAyB,CACzB,UACF,CAGA,gBAEE,kBAAmB,CAInB,kBAAmB,CACnB,wBAAyB,CAFzB,kBAAmB,CAJnB,mBAAoB,CAEpB,OAAQ,CACR,eAIF,CAEA,wBAGE,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CAGX,eAAgB,CAJhB,UAKF,CAEA,mBAEE,iDAAoD,CAEpD,iBAAkB,CAHlB,WAAY,CAEZ,yBAEF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAGA,iBACE,GAAK,SAAY,CACjB,IAAM,UAAc,CACpB,GAAO,SAAY,CACrB,CAGA,iBAIE,kBAAmB,CAHnB,YAAa,CACb,cAAe,CACf,SAAW,CAEX,gBACF,CAGA,mBAGE,SACF,CAEA,gBAEE,aACF,CAMA,kBAEE,kBAAmB,CAMnB,qBAAuB,CADvB,UAAY,CANZ,mBAAoB,CAQpB,gBAAkB,CAClB,eAAgB,CALhB,aAAc,CAFd,sBAAuB,CAQvB,kBAAoB,CACpB,uBAAyB,CARzB,YASF,CAGA,2CAVE,wBAYF,CAEA,2BACE,wBACF,CAEA,0BACE,wBACF,CAMA,iBACE,yBACF,CAcA,qBACE,YAAa,CACb,sBAAuB,CACvB,kBACF,CAEA,qBACE,kDAAqD,CAIrD,kBAAmB,CAInB,+BAA6C,CAF7C,cAAe,CACf,eAAgB,CAJhB,iBAAkB,CAMlB,uBACF,CAEA,2BACE,kDAAqD,CAErD,+BAA6C,CAD7C,0BAEF,CAUA,sCANE,YAAa,CACb,qBAAsB,CACtB,OASF,CALA,mBAIE,UACF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,OAEF,CAEA,aACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,cAAe,CAHf,eAAgB,CAIhB,8BACF,CAEA,mBACE,kBACF,CAEA,gBACE,QAAO,CACP,WACF,CAGA,OAEE,UACF,CAEA,UAEE,YAAa,CADb,kBAEF,CAkBA,sDAEE,SACF,CAKA,gHAEE,SACF,CACA,wDAEE,SACF,CAGA,oCACE,OACE,cACF,CAEA,UACE,gBACF,CAEA,sBACE,UACF,CAEA,eAEE,cAAe,CADf,gBAEF,CAEA,kBACE,OACF,CACF,CAGA,mBAGE,kBAAmB,CAGnB,+CAA6D,CAG7D,wBAAyB,CAFzB,kBAAmB,CAGnB,+BAA0C,CAT1C,YAAa,CACb,qBAAsB,CAEtB,QAAS,CAIT,aAAc,CAHd,iBAMF,CAEA,qBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,QAAS,CAIT,iBACF,CAGA,kBACE,iBAAkB,CAElB,eAAgB,CADhB,UAEF,CAEA,eAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAEhB,oBACF,CAEA,gBAEE,6BAA8B,CAG9B,eAAgB,CADhB,iBAEF,CAEA,eAKE,QAAO,CACP,UAAY,CACZ,uBACF,CAEA,sBACE,SAAU,CACV,oBACF,CAEA,yBACE,UACF,CAEA,WASE,kBAAmB,CALnB,kBAAmB,CACnB,iBAAkB,CAGlB,YAAa,CAPb,gBAAiB,CAMjB,WAAY,CAGZ,sBAAuB,CARvB,mBAAqB,CACrB,aAAe,CAQf,uBAAyB,CALzB,UAMF,CAEA,iCACE,kBAAmB,CAEnB,+BAA8C,CAD9C,UAEF,CAEA,oCACE,kBAAmB,CACnB,UACF,CAEA,WAGE,aAAc,CAFd,gBAAkB,CAClB,eAAgB,CAGhB,eAAgB,CADhB,iBAEF,CAEA,iCACE,aAAc,CACd,eACF,CAEA,oCACE,aACF,CAEA,gBAGE,aAAc,CAFd,QAAO,CACP,eAEF,CAOA,yBACE,2CAGE,aAAc,CACd,eACF,CAEA,YACE,cACF,CAEA,mBACE,OACF,CAEA,iBACE,OACF,CAGA,4CAEE,SACF,CAOA,4FAEE,WACF,CAEA,UACE,cACF,CACF,CAEA,oBACE,sBACF,CAMA,qBAGE,kBAAmB,CAFnB,YAAa,CAGb,0BAA2B,CAF3B,sBAGF,CAEA,gBACE,qBAAsB,CAEtB,kBAAmB,CACnB,+BAA2C,CAE3C,eAAgB,CAJhB,YAAa,CAGb,iBAEF,CAEA,iBACE,aAAc,CACd,cAAe,CACf,kBACF,CAEA,gBAEE,UAAW,CADX,cAEF,CAEA,QAGE,sBAAuB,CAFvB,YAAa,CAGb,QAAS,CAFT,4BAA6B,CAI7B,QAAS,CADT,SAEF,CAGA,yBACE,QACE,qBAAsB,CACtB,QACF,CACF,CAIA,QAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yBAEF,CAEA,yBACE,QACE,6BACF,CACF,CAEA,kBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAEhB,QACF,CAEA,wBACE,QACF,CAEA,cAGE,wBAAyB,CACzB,oBAAsB,CACtB,iBAAmB,CAHnB,mBAAqB,CAIrB,uBAAyB,CALzB,UAMF,CAEA,oBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,eAEE,kBAAmB,CAInB,kDAA6D,CAE7D,WAAY,CACZ,oBAAsB,CAKtB,+BAA8C,CAP9C,UAAY,CAKZ,cAAe,CAXf,YAAa,CAUb,iBAAmB,CADnB,eAAgB,CANhB,SAAW,CADX,sBAAuB,CAEvB,qBAAuB,CAQvB,uBAEF,CAEA,qBAEE,+BAA8C,CAD9C,0BAEF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,aAIE,yBAA0B,CAC1B,oBAAsB,CAItB,mBAAoB,CAHpB,iBAAmB,CAJnB,eAAgB,CAChB,mBAAqB,CAIrB,eAAgB,CAChB,uBAAyB,CAPzB,UASF,CAEA,mBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,0BACE,aACF,CAGA,eACE,wBAAyB,CAGzB,WAAY,CACZ,kBAAmB,CAKnB,4DAAiF,CARjF,UAAc,CAId,cAAe,CACf,iBAAmB,CACnB,eAAgB,CALhB,kBAAoB,CAMpB,uBAEF,CAEA,qBACE,wBAAyB,CAEzB,8DAAmF,CADnF,0BAEF,CAEA,sBAEE,4DAAiF,CADjF,uBAEF,CAGA,kBACE,YAAa,CAGb,cAAe,CADf,SAAW,CADX,wBAGF,CAGA,wBAEE,kBAAmB,CAInB,wBAAyB,CAEzB,wBAAyB,CADzB,kBAAmB,CAEnB,sDAA2E,CAR3E,YAAa,CAEb,6BAA8B,CAC9B,oBAAqB,CACrB,YAKF,CAEA,uCACE,YACF,CAGA,mBAEE,aAAc,CACd,kBAAmB,CACnB,eAAgB,CAHhB,QAIF,CAGA,cAEE,SAEF,CAGA,2BAJE,kBAAmB,CAFnB,YAmBF,CAbA,aAME,qBAAyB,CACzB,wBAAyB,CACzB,mBAAqB,CAIrB,gCAA2C,CAH3C,aAAc,CACd,cAAe,CALf,aAAc,CAFd,sBAAuB,CAQvB,uBAAyB,CAPzB,YASF,CAEA,mBACE,wBAAyB,CACzB,oBAAqB,CAGrB,4DAAiF,CAFjF,aAAc,CACd,0BAEF,CAEA,oBAEE,gCAA2C,CAD3C,uBAEF,CAEA,oBACE,wBAAyB,CAEzB,oBAAqB,CACrB,+BAA8C,CAF9C,UAGF,CAGA,OACE,+BACF,CAcA,mBACE,+BACF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,kBACE,+BACF,CAGA,cAEE,oBAAqB,CACrB,2BAA4B,CAF5B,mBAAoB,CAGpB,eACF,CAGA,oCACE,UACF,CAEA,0CACE,kBAAmB,CACnB,iBACF,CAEA,0CACE,kBAAmB,CACnB,iBACF,CAEA,gDACE,kBACF,CAGA,0BACE,wBACF,CAGA,gBAGE,wBAA0B,CAF1B,uBAAwB,CACxB,kDAEF,CAEA,cACE,uBACF,CAEA,cACE,uBACF,CAEA,cACE,uBACF,CAGA,qBAEE,kDAA6D,CAD7D,gBAEF,CAEA,kBACE,eAAmB,CAEnB,+BAAgC,CADhC,8BAEF,CAEA,gBAGE,cACF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,cACF,CAEA,iBAGE,aAAc,CAFd,gBAIF,CAEA,gBAEE,kBAAmB,CACnB,QACF,CAEA,aAEE,kBAAmB,CAEnB,WAAY,CADZ,aAAc,CAFd,kBAQF,CAEA,mBACE,kBAAmB,CACnB,0BACF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,oBAGE,aAAc,CADd,cAAe,CADf,aAGF,CAIA,cAEE,aAAc,CADd,gBAAiB,CAEjB,iBACF,CAEA,gBAIE,cAGF,CAMA,eAME,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CAEb,SAAW,CAHX,oBAIF,CAEA,qBAGE,aAAc,CADd,cAAe,CADf,aAGF,CAEA,aAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yBAEF,CAEA,yBACE,aACE,6BACF,CACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cAIE,aAAc,CAHd,aAAc,CACd,iBAAmB,CACnB,eAEF,CAEA,iBAEE,yBAA0B,CAC1B,oBAAsB,CAItB,cAAe,CAHf,YAAa,CAHb,iBAAkB,CAIlB,iBAAkB,CAClB,uBAEF,CAEA,wBACE,oBACF,CAEA,iBAEE,wBAAyB,CADzB,oBAEF,CAEA,kBAEE,wBAAyB,CADzB,oBAEF,CAEA,YAKE,QAAS,CAIT,cAAe,CAFf,WAAY,CAJZ,MAAO,CAKP,SAAU,CAPV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAIN,UAIF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,SACF,CAEA,sBAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,kBAGE,aAAc,CAFd,iBAAmB,CACnB,eAAgB,CAEhB,QACF,CAEA,iBAEE,aAAc,CADd,gBAAkB,CAElB,QACF,CAEA,oBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,SACF,CAEA,0BAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,mBAGE,aAAc,CAFd,iBAAmB,CACnB,eAAgB,CAEhB,QACF,CAEA,sBAEE,aAAc,CADd,gBAAkB,CAElB,QACF,CAEA,YAIE,wBAAyB,CACzB,oBAAsB,CACtB,iBAAmB,CAJnB,YAAa,CACb,mBAAqB,CAIrB,WAAY,CACZ,uBAAyB,CAPzB,UAQF,CAEA,kBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,gBAME,cAAe,CAFf,QAAS,CAFT,sBAAuB,CAGvB,eAEF,CAEA,+BANE,kBAAmB,CAFnB,YAsBF,CAdA,eAKE,kDAA6D,CAE7D,WAAY,CACZ,oBAAsB,CAKtB,+BAA8C,CAP9C,UAAc,CAKd,cAAe,CADf,iBAAmB,CADnB,eAAgB,CANhB,SAAW,CACX,mBAAqB,CAQrB,uBAEF,CAEA,oCAEE,+BAA8C,CAD9C,0BAEF,CAEA,wBACE,kBAAmB,CAGnB,eAAgB,CAFhB,kBAAmB,CACnB,cAEF,CAEA,qBAEE,WAAY,CADZ,UAEF,CAEA,cAEE,kBAAmB,CAEnB,WAAY,CACZ,oBAAsB,CAFtB,aAAc,CAKd,cAAe,CADf,iBAAmB,CADnB,eAAgB,CALhB,qBAAuB,CAQvB,uBACF,CAEA,oBACE,kBACF,CAEA,iBAIE,0BAA6B,CAA7B,qBAGF,CAGA,iBACE,eAAmB,CACnB,kBAAmB,CACnB,8BAAyC,CACzC,cAAe,CACf,uBACF,CAEA,eAME,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CAJb,iBAAkB,CAClB,eAAgB,CAKhB,SAAW,CAHX,oBAIF,CAEA,qBAGE,aAAc,CADd,cAAe,CADf,aAGF,CAEA,eAME,QAAS,CADT,cAEF,CAEA,eAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,cACE,aAAc,CACd,iBAAmB,CACnB,QACF,CAEA,iBAGE,wBAAyB,CADzB,mBAAqB,CADrB,eAGF,CAEA,eAGE,eAAmB,CADnB,wBAAyB,CADzB,UAGF,CAEA,cACE,+BACF,CAEA,UAKE,kBAAmB,CADnB,aAAc,CAEd,iBAAmB,CAHnB,eAAgB,CADhB,mBAAqB,CADrB,eAMF,CAEA,cACE,UACF,CAEA,WACE,+BAAgC,CAChC,oCACF,CAEA,iBACE,wBACF,CAEA,UAGE,aAAc,CADd,iBAAmB,CADnB,YAAa,CAGb,kBACF,CAEA,YAEE,aAAc,CADd,eAAgB,CAEhB,iBACF,CAEA,kBACE,eACF,CAEA,eACE,eACF,CAEA,eACE,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAGA,eAEE,cAAe,CADf,iBAEF,CAEA,qBAGE,aAAc,CADd,WAAY,CAEZ,kBAAmB,CAHnB,UAIF,CAEA,aAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAEhB,gBACF,CAEA,gBACE,aAAc,CACd,QACF,CAEA,aACE,eAAmB,CACnB,kBAAmB,CACnB,8BAAyC,CACzC,YAAa,CACb,iBACF,CAEA,mBAGE,aAAc,CADd,WAAY,CAEZ,kBAAmB,CAHnB,UAIF,CAEA,mBAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAEhB,gBACF,CAEA,sBACE,aAAc,CAGd,eAAgB,CADhB,aAAc,CADd,eAGF,CAGA,gBACE,6BACF,CAcA,oCACE,SACF,CAEA,0CACE,kBAAmB,CACnB,iBACF,CAEA,0CACE,kBAAmB,CACnB,iBACF,CAEA,gDACE,kBACF,CAGA,aAEE,kDAA6D,CAE7D,WAAY,CACZ,mBAAqB,CAKrB,8BAA8C,CAP9C,UAAY,CAKZ,cAAe,CAFf,iBAAmB,CACnB,eAAgB,CANhB,kBAAoB,CAQpB,uBAEF,CAEA,mBAEE,+BAA+C,CAD/C,0BAEF,CAEA,gBAEE,kBAAmB,CACnB,8BAAyC,CACzC,kBAAmB,CAEnB,eAAgB,CADhB,uBAEF,CAEA,sBACE,+BACF,CAEA,0BACE,kBACF,CAEA,eAEE,kBAAmB,CAGnB,kDAA6D,CAC7D,+BAAgC,CALhC,YAAa,CAEb,6BAA8B,CAC9B,cAGF,CAEA,uBAEE,kBAAmB,CAEnB,WAAY,CACZ,mBAAqB,CAFrB,aAAc,CAId,cAAe,CADf,iBAAmB,CALnB,kBAAoB,CAOpB,uBACF,CAEA,6BACE,kBACF,CAEA,gBACE,cACF,CAEA,qBAEE,kBAAmB,CAGnB,kDAA6D,CAE7D,WAAY,CACZ,oBAAsB,CAKtB,+BAA8C,CAP9C,UAAY,CAKZ,cAAe,CAVf,YAAa,CASb,iBAAmB,CADnB,eAAgB,CANhB,SAAW,CAWX,oBAAqB,CAVrB,qBAAuB,CAQvB,uBAGF,CAEA,2BAEE,+BAA8C,CAD9C,0BAEF,CAEA,2BAEE,WAAY,CADZ,UAEF,CAGA,kBAQE,kBAAmB,CANnB,yBAA0B,CAC1B,oBAAsB,CAItB,cAAe,CAHf,YAAa,CAHb,iBAAkB,CAIlB,iBAAkB,CAClB,uBAGF,CAEA,wBAEE,kBAAmB,CADnB,oBAEF,CAEA,8BAKE,QAAS,CAIT,cAAe,CAFf,WAAY,CAJZ,MAAO,CAKP,SAAU,CAPV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAIN,UAIF,CAGA,oCACE,aAEE,QAEF,CAEA,6BAHE,sBAAuB,CAFvB,qBASF,CAJA,gBAEE,SAEF,CAEA,eAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,aACE,gBAAkB,CAClB,mBACF,CACF,CAEA,oBAEE,wBAAyB,CACzB,oBAAqB,CACrB,WAAY,CACZ,eAAgB,CAChB,eAAgB,CALhB,UAMF,CAEA,cACE,wBAAyB,CAEzB,oBAAqB,CAIrB,aAAc,CADd,kBAAmB,CAJnB,WAAY,CAGZ,iBAAkB,CADlB,gCAIF,CCt2DA,gCACE,YAAa,CACb,qBAAsB,CACtB,UAAY,CAEZ,aAAc,CADd,aAEF,CAEA,6BAGE,aAAc,CADd,iBAAmB,CADnB,eAGF,CAEA,8BAGE,cAAW,CAFX,YAAa,CAEb,SAAW,CADX,mCAEF,CAEA,6BAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAKb,iBAAmB,CAHnB,SAAW,CAEX,wBAAiB,CAAjB,gBAEF,CAEA,kDAIE,cAAe,CAFf,WAAY,CACZ,QAAS,CAET,mBAAqB,CAErB,6BAA+B,CAD/B,yBAA2B,CAE3B,4BAA8B,CAP9B,UAQF,CAGA,iBACE,YAAa,CACb,qCACF,CAEA,iBACE,YAAa,CACb,iCACF,CChDA,wBAEE,aAAc,CADd,eAAgB,CAEhB,YACF,CAEA,qBAEE,kBAAmB,CADnB,iBAEF,CAEA,oBAEE,kBAAmB,CAKnB,aAAc,CANd,YAAa,CAIb,gBAAiB,CACjB,eAAgB,CAFhB,UAAY,CADZ,sBAAuB,CAKvB,mBACF,CAEA,UACE,iBACF,CAEA,0BACE,aAAc,CACd,cAAe,CACf,QACF,CAGA,eAKE,kBAAmB,CAJnB,yBAA0B,CAC1B,kBAAmB,CAKnB,cAAe,CACf,oBAAqB,CALrB,iBAAkB,CAClB,iBAAkB,CAElB,uBAGF,CAEA,qBAEE,kBAAmB,CADnB,oBAEF,CAEA,yBAEE,kBAAmB,CADnB,oBAAqB,CAErB,qBACF,CAEA,0BAEE,kBAAmB,CADnB,oBAAqB,CAErB,kBACF,CAGA,iBAOE,kBAAmB,CAHnB,kBAAmB,CACnB,iBAAkB,CAIlB,aAAc,CAHd,YAAa,CAIb,gBAAiB,CARjB,WAAY,CAMZ,sBAAuB,CALvB,oBAAqB,CAFrB,UAUF,CAEA,sCACE,kBAAmB,CACnB,aACF,CAGA,iBACE,kBACF,CAEA,eAEE,aAAc,CADd,kBAAmB,CAEnB,mBACF,CAEA,iBACE,aAAc,CAEd,cAAe,CADf,eAAgB,CAGhB,iBAAkB,CADlB,yBAEF,CAEA,uBACE,aACF,CAEA,gBAKE,cAAe,CADf,WAAY,CAFZ,SAAU,CADV,iBAAkB,CAElB,UAGF,CAEA,cAEE,aAAc,CADd,iBAAmB,CAEnB,QACF,CAGA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,QACF,CAEA,aAIE,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAOA,kBAEE,aAAc,CADd,cAAe,CAEf,QACF,CAGA,WAKE,kBAAmB,CACnB,wBAAyB,CACzB,mBAAqB,CAErB,iBAAmB,CANnB,SAAW,CAOX,oBAAqB,CANrB,mBAOF,CAGA,UACE,kBAAmB,CACnB,wBAAyB,CACzB,oBAAsB,CAGtB,QAAS,CAFT,aAIF,CAEA,yBAHE,kBAAmB,CAFnB,YAYF,CAPA,eAME,aAAc,CADd,iBAAmB,CAFnB,UAAY,CACZ,oBAGF,CAEA,0BACE,eACF,CAEA,eAEE,aAAc,CADd,cAEF,CAGA,yBACE,wBACE,YACF,CAEA,eACE,iBACF,CAMA,qCAHE,iBAOF,CAJA,iBAEE,WAAY,CADZ,UAGF,CAEA,eACE,cACF,CACF,CCnNA,qBAME,0BAAoC,CAKpC,YACF,CAEA,YACE,eAAiB,CACjB,kBAAmB,CACnB,gEAAqF,CAGrF,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAChB,iBAAkB,CAHlB,SAIF,CAEA,mBAGE,kBAAmB,CAEnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAI9B,kBAAmB,CAFnB,mBAGF,CAEA,sBAIE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAFhB,QAIF,CAEA,cAIE,iBAAkB,CADlB,WAKF,CAEA,oBACE,wBAEF,CAEA,kBAEE,WAAY,CADZ,UAEF,CAEA,iBACE,mBACF,CAEA,YAIE,kBACF,CAQA,0DAOE,qBAAuB,CAFvB,iBAAkB,CAGlB,aAAc,CALd,gBAAiB,CAMjB,2BACF,CAEA,4EAIE,oBAAqB,CACrB,8BACF,CAEA,gBACE,kCAAoC,CAGpC,8BAAgC,CAFhC,uBAAyB,CACzB,kBAEF,CAiBA,qBAOE,gBAAiB,CALjB,iBAAkB,CAGlB,cAAe,CAFf,iBAAmB,CACnB,eAAgB,CAHhB,gBAAiB,CAKjB,kBAEF,CAEA,kCACE,qBAAuB,CAEvB,oBAAqB,CADrB,aAEF,CAEA,uDACE,wBAAyB,CACzB,oBACF,CAEA,kCACE,wBAAyB,CAEzB,oBAAqB,CADrB,UAEF,CAEA,uDACE,wBAAyB,CACzB,oBACF,CAEA,8BAEE,kBAAmB,CADnB,UAEF,CAEA,iBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,oBAIE,aAAc,CAFd,iBAAmB,CACnB,eAAgB,CAFhB,cAIF,CAEA,mBAGE,aAAc,CADd,gBAAkB,CAElB,eAAgB,CAHhB,QAIF,CAEA,sBAOE,kBAAmB,CANnB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAGlB,YAAa,CAEb,OAAQ,CAHR,kBAAmB,CADnB,YAKF,CAEA,4BACE,aAAc,CACd,cACF,CAEA,4BAEE,aAAc,CADd,gBAAkB,CAElB,eACF,CAGA,yBACE,YAEE,gBAAiB,CADjB,SAEF,CAEA,oCAEE,iBAAkB,CAClB,kBACF,CAEA,cACE,qBACF,CAEA,qBACE,UACF,CACF,CC7NA,oBAME,iBAAkB,CALlB,mBAAoB,CAWpB,mBAAoB,CALpB,eAAgB,CAJhB,sBAAuB,CAQvB,eAAgB,CADhB,iBAAkB,CADlB,uBAIF,CAEA,6BACE,UAEF,CAGA,aACE,eAEF,CAEA,0BAHE,aAKF,CAGA,2BAEE,gBAAkB,CADlB,mBAEF,CAEA,4BAEE,gBAAkB,CADlB,kBAEF,CAEA,2BAEE,cAAe,CADf,mBAEF,CAKA,6BACE,kDAA6D,CAE7D,+BAA+C,CAD/C,UAEF,CAEA,oCAOE,uDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,0CACE,SACF,CAEA,mCACE,kDAA6D,CAE7D,+BAA+C,CAD/C,0BAEF,CAEA,oCAEE,8BAA8C,CAD9C,uBAEF,CAGA,+BACE,kDAA6D,CAE7D,+BAA8C,CAD9C,UAEF,CAEA,qCACE,kDAA6D,CAE7D,+BAA8C,CAD9C,0BAEF,CAEA,sCAEE,8BAA6C,CAD7C,uBAEF,CAGA,6BACE,kDAA6D,CAE7D,+BAA8C,CAD9C,UAEF,CAEA,mCACE,kDAA6D,CAE7D,+BAA8C,CAD9C,0BAEF,CAEA,oCAEE,8BAA6C,CAD7C,uBAEF,CAGA,4BACE,kDAA6D,CAE7D,+BAA6C,CAD7C,UAEF,CAEA,kCACE,kDAA6D,CAE7D,+BAA6C,CAD7C,0BAEF,CAEA,mCAEE,8BAA4C,CAD5C,uBAEF,CAGA,yBACE,2BAEE,gBAAkB,CADlB,qBAEF,CAEA,4BAEE,eAAiB,CADjB,kBAEF,CAEA,2BAEE,eAAiB,CADjB,oBAEF,CACF,CCnJA,yBAEE,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAEnB,+BAAgD,CAChD,eAAgB,CAFhB,SAAU,CAJV,iBAAkB,CAOlB,uBACF,CAEA,gCAOE,kDAA6D,CAN7D,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,SAGF,CAEA,+BACE,gCAAgD,CAChD,0BACF,CAEA,4BAEE,qBAAsB,CACtB,KACF,CAGA,2BAEE,kBAAmB,CAGnB,sDAA+F,CAC/F,iCAAiD,CALjD,YAAa,CAEb,QAAS,CACT,sBAGF,CAEA,yBAGE,uCAAwC,CAFxC,cAGF,CAEA,iBACE,MAAW,sBAAyB,CACpC,oBAA0B,uBAA0B,CACpD,gBAAqB,sBAAyB,CAChD,CAEA,0BACE,QACF,CAEA,6BACE,aAAc,CACd,iBAAkB,CAClB,eAAgB,CAEhB,qBAAuB,CADvB,iBAEF,CAEA,4BACE,aAAc,CAEd,gBAAkB,CAElB,eAAgB,CADhB,eAAgB,CAFhB,QAIF,CAGA,yBACE,cACF,CAEA,oBACE,eAAmB,CAInB,0BAA0C,CAH1C,iBAAkB,CAIlB,8BAAyC,CAFzC,eAAiB,CADjB,oBAIF,CAEA,sBAKE,kBAAmB,CAFnB,aAAc,CACd,YAAa,CAFb,eAAiB,CAIjB,SAAW,CALX,cAMF,CAEA,kCACE,YACF,CAEA,iCACE,eACF,CAEA,2BACE,aAAc,CAGd,oBAAqB,CADrB,eAEF,CAEA,WAGE,eAAgB,CAFhB,iBAAqB,CACrB,SAEF,CAEA,cAUE,kBAAmB,CALnB,kDAA6D,CAG7D,wBAAyB,CADzB,iBAAkB,CANlB,aAAc,CAQd,YAAa,CAPb,sEAAsF,CACtF,gBAAkB,CAQlB,SAAW,CAPX,cAAgB,CAEhB,mBAAsB,CAMtB,uBACF,CAEA,oBACE,kDAA6D,CAC7D,oBAAqB,CACrB,yBACF,CAEA,qBACE,YAAa,CAEb,aAAc,CADd,cAEF,CAGA,oBAEE,0BAEF,CAKA,cAEE,kBAAmB,CAGnB,iBAAkB,CAJlB,mBAAoB,CAKpB,gBAAkB,CAHlB,UAAY,CAKZ,iBAAmB,CAJnB,qBAKF,CAEA,sBACE,kDAA6D,CAE7D,wBAAyB,CADzB,aAEF,CAEA,oBACE,kDAA6D,CAE7D,wBAAyB,CADzB,aAEF,CAEA,sBACE,kDAA6D,CAE7D,wBAAyB,CADzB,aAEF,CC1LA,sBACE,oBACF,CAEA,mBAKE,mCAA6C,CAD7C,eAEF,CAQA,mBACE,aAEF,CAEA,uBAEE,aAAc,CADd,YAEF,CAEA,mBACE,QACF,CASA,qBACE,aAAc,CAGd,eACF,CAEA,qBAME,eAIF,CAEA,2BAGE,8BACF,CC9DA,qBAEE,aAAc,CADd,gBAAiB,CAEjB,cACF,CAGA,kBAGE,+BAAgC,CAFhC,kBAAmB,CACnB,mBAEF,CAEA,yBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAqBA,iBAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAGb,gBAAiB,CACjB,eAAgB,CAFhB,SAAW,CAIX,iBACF,CAEA,cACE,iBACF,CAEA,oBACE,aAAc,CACd,iBAAmB,CACnB,QACF,CAOA,gCACE,kBACF,CAEA,gBAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAGb,kBAAmB,CACnB,eAAgB,CAFhB,SAAW,CAIX,kBAAmB,CACnB,uBACF,CAEA,0BAME,iBAAkB,CALlB,cAAe,CAGf,mBAAqB,CACrB,oBAAsB,CAFtB,oBAAuB,CADvB,wBAAiB,CAAjB,gBAKF,CAEA,gCACE,oBAA+B,CAC/B,aACF,CAEA,iCACE,oBACF,CAEA,eACE,cAEF,CAEA,4BAHE,6BAQF,CALA,aAGE,aAAc,CADd,eAAiB,CADjB,gBAIF,CAGA,WAGE,aAAS,CACT,gCAAkC,CAHlC,YAAa,CAEb,QAAS,CADT,yDAA4D,CAG5D,oBACF,CAEA,qBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,WAKE,eAAmB,CAEnB,oBAAsB,CACtB,cAAe,CALf,QAAS,CACT,YAAa,CAKb,uBACF,CAEA,iBACE,oBAAqB,CACrB,mCAA6C,CAC7C,0BACF,CAEA,oBAEE,kBAAmB,CADnB,oBAAqB,CAErB,mCACF,CAEA,oBAEE,kBAAmB,CADnB,UAEF,CAEA,0BACE,oBAAqB,CACrB,eAAgB,CAChB,cACF,CAGA,qBAOE,kBAAmB,CAHnB,kBAAmB,CACnB,mBAAqB,CACrB,YAAa,CALb,aAAc,CAEd,WAAY,CAKZ,sBAAuB,CANvB,UAOF,CAEA,WACE,gBACF,CAGA,cACE,QAAO,CACP,WACF,CAEA,WAGE,oBAAsB,CACtB,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAEA,WAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,gBAAkB,CADlB,UAGF,CAEA,WACE,eACF,CAEA,WACE,aACF,CAGA,cACE,aACF,CAEA,gBAEE,kBAAmB,CAGnB,kBAAmB,CAEnB,WAAY,CACZ,qBAAuB,CAFvB,UAAY,CAKZ,cAAe,CAVf,YAAa,CAQb,gBAAkB,CAClB,eAAgB,CAPhB,WAAa,CACb,oBAAuB,CAQvB,oCACF,CAEA,sBACE,kBACF,CAEA,eAEE,aAAc,CADd,gBAAkB,CAElB,iBACF,CAGA,sBACE,kBACF,CAEA,mBAEE,kBAAmB,CAGnB,kDAA6D,CAC7D,wBAAyB,CACzB,oBAAsB,CANtB,YAAa,CAOb,QAAS,CALT,6BAA8B,CAC9B,cAKF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAGb,QAAO,CADP,QAEF,CAEA,mBAEE,aAAc,CADd,cAEF,CAEA,sBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,iBAIF,CAEA,qBAGE,aAAc,CADd,iBAAmB,CADnB,QAGF,CAEA,qBAEE,kBAAmB,CAGnB,kBAAmB,CAEnB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAKZ,cAAe,CAVf,YAAa,CAYb,aAAc,CAJd,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CACX,qBAAuB,CAQvB,uBAEF,CAEA,2BACE,kBAAmB,CAEnB,+BAA8C,CAD9C,0BAEF,CAEA,yBAEE,WAAY,CADZ,UAEF,CAGA,mBACE,kBAAmB,CACnB,wBAAyB,CACzB,oBAAsB,CACtB,cACF,CAEA,kBAIE,oBAGF,CAYA,yBACE,qBACE,YACF,CAEA,WACE,yBACF,CAEA,WACE,cACF,CAEA,qBAEE,aAAc,CADd,YAEF,CAMA,4BACE,iBACF,CAEA,gBACE,cACF,CAEA,mBAEE,mBAAoB,CADpB,qBAAsB,CAEtB,QAAS,CACT,YACF,CAEA,sBACE,qBAAsB,CAEtB,UAAY,CADZ,iBAEF,CAEA,sBACE,iBACF,CAEA,qBACE,gBACF,CAEA,qBAEE,sBAAuB,CADvB,UAEF,CACF,CAGA,4BACE,oBACF,CAEA,yBAEE,sBAAuB,CAGvB,kDAA6D,CAC7D,wBAAyB,CACzB,oBAAsB,CACtB,8BAA6C,CAP7C,YAAa,CAEb,QAAS,CACT,YAKF,CAEA,4BAEE,sBAAuB,CADvB,YAAa,CAGb,QAAO,CADP,UAEF,CAEA,yBAEE,aAAc,CADd,gBAAiB,CAEjB,kBACF,CAEA,yBACE,QACF,CAEA,4BACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,gBACF,CAEA,2BACE,aAAc,CACd,iBAAmB,CAEnB,eAAgB,CADhB,gBAEF,CAEA,oBAGE,4BAA6B,CAF7B,iBAAmB,CACnB,kBAEF,CAEA,sBAEE,kBAAoB,CADpB,eAEF,CAEA,2BACE,aAEF,CAEA,oBAGE,SACF,CAEA,oBAKE,kBAAmB,CAGnB,mBAAqB,CAKrB,aAAc,CAHd,iBAAmB,CAPnB,SAAW,CACX,sBAAwB,CAQxB,uBAEF,CAEA,0BACE,kBAAmB,CAEnB,8BAA4C,CAD5C,0BAEF,CAEA,wBAEE,WAAY,CADZ,UAEF,CAGA,mCACE,+BACF,CAEA,qBACE,GACE,4BACF,CACA,IACE,+BACF,CACA,GACE,4BACF,CACF,CAKA,qBAQE,kBAAmB,CAFnB,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,aACE,eAAiB,CACjB,oBAAsB,CACtB,qCAA+C,CAG/C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,UAIF,CAEA,oBAEE,kBAAmB,CAGnB,+BAAgC,CAJhC,YAAa,CAEb,6BAA8B,CAC9B,0BAEF,CAEA,uBACE,aAAc,CACd,iBAAkB,CAClB,eAAgB,CAChB,QACF,CAEA,mBAEE,kBAAmB,CAInB,eAAgB,CAChB,WAAY,CAGZ,qBAAuB,CAFvB,aAAc,CACd,cAAe,CARf,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CAQvB,uBAAyB,CAPzB,UAQF,CAEA,yBACE,kBAAmB,CACnB,aACF,CAEA,uBAEE,cAAe,CADf,aAEF,CAEA,YACE,cACF,CAEA,YACE,oBACF,CAEA,kBACE,aAAc,CAId,mBACF,CAEA,0DAKE,wBAAyB,CACzB,mBAAqB,CACrB,iBAAmB,CAHnB,cAAgB,CAIhB,uBAAyB,CALzB,UAMF,CAEA,4EAIE,oBAAqB,CACrB,8BAA4C,CAF5C,YAGF,CAEA,qBAGE,mBAAoB,CACpB,eAAgB,CAFhB,gBAAiB,CADjB,eAIF,CAEA,oBAKE,4BAA6B,CAJ7B,YAAa,CACb,QAAS,CACT,wBAAyB,CACzB,gBAEF,CAEA,eAEE,kBAAmB,CAEnB,wBAAyB,CACzB,mBAAqB,CAFrB,aAAc,CAId,cAAe,CADf,eAAgB,CALhB,qBAAuB,CAOvB,uBACF,CAEA,qBACE,kBACF,CAEA,mBAEE,kBAAmB,CAEnB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAIZ,cAAe,CADf,eAAgB,CALhB,qBAAuB,CAOvB,uBACF,CAEA,wCACE,kBACF,CAEA,4BAEE,kBAAmB,CADnB,UAEF,CC/oBA,oBACE,eAAiB,CACjB,kBAAmB,CACnB,gEAAqF,CAGrF,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAChB,iBAAkB,CAHlB,SAIF,CAEA,cAME,eAAkB,CAFlB,mBAGF,CAEA,iBAOE,aAAc,CAJd,OAKF,CAIA,YAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,aACE,eAAgB,CAGhB,iBAAkB,CADlB,WAKF,CAEA,mBACE,wBAEF,CAEA,iBAEE,WAAY,CADZ,UAEF,CAEA,YACE,mBACF,CAEA,cAQE,kBAAmB,CAPnB,wBAAyB,CAIzB,6BAA8B,CAF9B,iBAAkB,CAGlB,YAAa,CACb,6BAA8B,CAH9B,kBAAmB,CAFnB,iBAOF,CAEA,gBAEE,aAAc,CACd,iBAAmB,CAFnB,QAGF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,YAGE,OACF,CAEA,kBACE,eAGF,CAEA,cAKE,wBAAyB,CAJzB,wBAAyB,CACzB,iBAAkB,CAIlB,YAAa,CACb,6BAA8B,CAJ9B,gBAAiB,CACjB,eAIF,CAEA,aACE,+BAAgC,CAChC,iBACF,CAEA,wBACE,kBACF,CAEA,gBAEE,sBAAuB,CAGvB,iBAAmB,CAFnB,QAGF,CAEA,qCAIE,oBAAqB,CADrB,WAAY,CAFZ,cAAe,CACf,UAGF,CAEA,gBACE,YAAa,CAGb,QAAO,CAFP,qBAAsB,CACtB,OAEF,CAOA,cAEE,eACF,CAEA,kBAGE,eAGF,CAiBA,YAEE,iBAGF,CAEA,OAKE,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAGlB,aAAc,CAFd,iBAAmB,CAHnB,gBAAiB,CAMjB,2BACF,CAEA,aAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,aACE,oBACF,CAEA,eACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAIlB,aAAc,CAFd,iBAAmB,CACnB,eAAgB,CAFhB,YAAa,CAIb,oBACF,CAEA,cAKE,4BAA6B,CAJ7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,gBAEF,CAEA,4BAQE,gBAAiB,CALjB,iBAAkB,CAGlB,cAAe,CAFf,iBAAmB,CACnB,eAAgB,CAHhB,gBAAiB,CAKjB,kBAEF,CAEA,eACE,qBAAuB,CAEvB,oBACF,CAEA,oCACE,wBAAyB,CACzB,oBACF,CAEA,aAGE,oBACF,CAEA,kCAEE,oBACF,CAEA,QAEE,gBAAkB,CAClB,0BAAmB,CAAnB,kBAAmB,CAFnB,gBAGF,CAEA,8CAGE,kBAAmB,CADnB,UAEF,CAEA,YACE,aAAc,CAEd,cACF,CAGA,yBACE,oBAEE,gBAAiB,CADjB,SAEF,CAEA,0BAEE,iBAAkB,CAClB,kBACF,CAEA,cACE,qBACF,CAEA,4BAEE,UACF,CACF,CCjSA,gBACE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAElB,gCAA0C,CAD1C,kBAAmB,CAGnB,uBAAe,CAAf,eAAe,CACf,QAAS,CAFT,uBAGF,CAEA,sBACE,mCACF,CAEA,gCAEE,eAAgB,CADhB,eAEF,CAEA,8CACE,cACF,CAEA,oDACE,qBACF,CAEA,8CAEE,YACF,CAEA,cAGE,kBAAmB,CAEnB,cAAe,CAJf,YAAa,CACb,6BAA8B,CAE9B,iBAAkB,CAGlB,+BAAiC,CADjC,wBAAiB,CAAjB,gBAEF,CAEA,oBACE,wBACF,CAEA,aAGE,OACF,CAEA,YAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,kBACE,aACF,CAEA,aAEE,aAAc,CACd,iBAAmB,CAFnB,eAGF,CAEA,cACE,aAAc,CACd,iBACF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,OAEF,CAEA,cAME,kBAAmB,CAFnB,kBAAmB,CACnB,YAAa,CAJb,gBAAkB,CAClB,eAAgB,CAKhB,OAAQ,CAJR,eAKF,CAEA,kBACE,wBAAyB,CACzB,aACF,CAEA,iBACE,wBAAyB,CACzB,aACF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,oBAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAVf,YAAa,CAQb,gBAAkB,CAClB,eAAgB,CAPhB,OAAQ,CACR,gBAAiB,CAQjB,kBACF,CAEA,yCACE,wBAAyB,CACzB,0BACF,CAEA,6BACE,wBAAyB,CACzB,aAAc,CACd,kBAAmB,CACnB,cACF,CAEA,wBAEE,WAAY,CADZ,UAEF,CAEA,aAGE,aAAc,CADd,WAAY,CAEZ,wBAA0B,CAH1B,UAIF,CAEA,sBACE,wBACF,CAEA,eAGE,wBAAyB,CAFzB,4BAA6B,CAC7B,iBAEF,CAEA,YAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAKb,iBAAkB,CAHlB,OAAQ,CAIR,sBAAuB,CAHvB,YAIF,CAEA,cAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,eAIE,wBAAyB,CACzB,iBAAkB,CAJlB,YAAa,CACb,qBAAsB,CACtB,OAAQ,CAGR,eACF,CAEA,cAKE,wBAAyB,CAGzB,aAAc,CADd,gBAAkB,CADlB,eAAgB,CAIhB,oBAAsB,CADtB,wBAEF,CAEA,yBAVE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,iCAAkC,CAElC,iBAiBF,CARA,WAOE,kBAAmB,CAFnB,qBAAuB,CACvB,iBAEF,CAEA,aAEE,aAAc,CADd,eAEF,CAEA,cACE,aAAc,CACd,oBACF,CAEA,kBAIE,kBAAmB,CAHnB,gBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAEhB,iBAAkB,CAClB,yBAAkB,CAAlB,iBACF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAEA,0BACE,wBAAyB,CACzB,aACF,CAGA,yBACE,cACE,iBACF,CAEA,cACE,OACF,CAEA,mBACE,OACF,CAEA,oBAEE,eAAiB,CADjB,eAEF,CAEA,yBAGE,OAAQ,CADR,qCAAsC,CAEtC,gBACF,CAEA,eACE,iBACF,CACF,CAEA,yBACE,yBAGE,OAAQ,CADR,yBAEF,CAEA,gEAEE,YACF,CAEA,WAGE,sBAAuB,CAFvB,YAAa,CACb,qBAAsB,CAEtB,OACF,CAEA,cACE,gBACF,CACF,CCtSA,4BAKE,YAAa,CACb,qBAAsB,CAJtB,aAAc,CADd,cAAe,CAGf,gBAAiB,CADjB,cAIF,CAGA,sBAGE,eAAW,CAGX,iBAAkB,CALlB,YAAa,CAGb,QAAO,CADP,UAAW,CADX,+BAAgC,CAGhC,YAEF,CAEA,qCACE,yBACF,CAEA,iBAGE,8BAA+B,CAC/B,eAAgB,CAHhB,uBAAgB,CAAhB,eAAgB,CAChB,QAGF,CAGA,gBAEE,kBAAmB,CAInB,+BAAgC,CALhC,YAAa,CAMb,aAAc,CAJd,6BAA8B,CAC9B,oBAAqB,CACrB,mBAGF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,aAME,kBAMF,CAEA,mBACE,kBAAmB,CACnB,aACF,CAEA,mBACE,QACF,CAEA,eAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAGb,iBAAkB,CAClB,eAAgB,CAFhB,SAAW,CAIX,iBACF,CAEA,WACE,kBACF,CAEA,kBACE,aAAc,CACd,iBAAmB,CACnB,QACF,CAGA,iBACE,YAAa,CACb,UACF,CAEA,uBAEE,kBAAmB,CAInB,kBAAmB,CAEnB,wBAAyB,CACzB,mBAAqB,CAFrB,aAAc,CAGd,cAAe,CATf,YAAa,CAIb,aAAc,CAFd,sBAAuB,CAQvB,uBAAyB,CAPzB,YAQF,CAEA,6BACE,kBAAmB,CAEnB,oBAAqB,CADrB,aAEF,CAEA,2BAEE,cAAe,CADf,aAEF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CASb,eAAgB,CAPhB,SAAW,CACX,kBASF,CAMA,yBACE,kBACF,CAEA,+BACE,kBACF,CAGA,yBAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,oBAAqB,CADrB,YAEF,CAGA,iBAGE,kBAAmB,CAFnB,YAAa,CAIb,QAAO,CAHP,qBAAsB,CAItB,QAAS,CAFT,sBAGF,CAEA,iBAIE,wBAGF,CAOA,mBACE,aAAc,CACd,cAAe,CACf,QACF,CAGA,eAEE,kBAAmB,CAGnB,kBAAmB,CACnB,wBAAyB,CACzB,oBAAsB,CACtB,aAAc,CAPd,YAAa,CAEb,QAAS,CACT,cAKF,CAEA,kBAEE,cAAe,CACf,eAAgB,CAFhB,iBAGF,CAEA,iBAEE,iBAAmB,CADnB,QAEF,CAEA,kBAGE,kBAAmB,CACnB,wBAAyB,CACzB,mBAAqB,CACrB,aAAc,CACd,iBAAmB,CACnB,eAAgB,CAPhB,eAAgB,CAChB,YAOF,CAGA,2BAEE,eAAmB,CACnB,wBAAyB,CACzB,oBAAsB,CAEtB,YAAa,CALb,QAAO,CAMP,qBAAsB,CAFtB,eAGF,CAEA,iBACE,QAAO,CAIP,yBAA6B,CAD7B,eAAgB,CADhB,eAAgB,CADhB,YAAa,CAIb,wBAAiB,CAAjB,gBACF,CAGA,wHAME,aAAc,CAGd,eAAgB,CADhB,oBAAsB,CADtB,iBAGF,CAEA,oBAAsB,gBAAmB,CACzC,oBAAsB,iBAAoB,CAC1C,oBAAsB,kBAAqB,CAE3C,mBAEE,aAAc,CADd,kBAEF,CAEA,qBACE,kBAAmB,CAEnB,mBAAqB,CAErB,iCAAqC,CACrC,iBAAmB,CACnB,eAAgB,CAHhB,eAAgB,CAFhB,YAMF,CAEA,6BACE,kBAAmB,CACnB,aACF,CAGA,sBACE,kBAAmB,CACnB,wBAAyB,CACzB,oBAAsB,CAGtB,aAAc,CADd,kBAAmB,CADnB,YAGF,CAEA,kBAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAIb,iBAAmB,CAFnB,UAAY,CACZ,mBAGF,CAEA,6BACE,eACF,CAEA,kBAEE,aAAc,CADd,cAEF,CAGA,gBACE,kBAAmB,CACnB,wBAAyB,CACzB,oBAAsB,CAEtB,aAAc,CADd,YAEF,CAEA,kBAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAMb,iBAAmB,CAHnB,eAAgB,CADhB,SAAW,CAGX,mBAEF,CAEA,gBACE,cACF,CAEA,mBAGE,eAAmB,CAGnB,wBAAyB,CADzB,mBAAqB,CAHrB,aAAc,CAKd,iCAAqC,CANrC,iBAAmB,CAOnB,eAAgB,CAJhB,cAKF,CAGA,0BACE,sBACE,yBACF,CAEA,iBAEE,eAAgB,CAChB,QAAS,CAFT,eAGF,CACF,CAGA,sBAWE,kBAAmB,CALnB,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAPlB,WAAY,CAYZ,+BAA0C,CAP1C,UAAY,CAMZ,cAAe,CAHf,YAAa,CALb,WAAY,CAOZ,sBAAuB,CAXvB,cAAe,CAEf,UAAW,CAYX,uBAAyB,CAXzB,UAAW,CAYX,YACF,CAEA,4BACE,kBAAmB,CAEnB,2BAAyC,CADzC,0BAEF,CAEA,6BACE,uBACF,CAEA,0BAEE,cAAe,CADf,aAEF,CAEA,yBACE,4BACE,YACF,CAEA,gBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,uBACE,UACF,CAEA,iBAEE,wBAAyB,CADzB,UAEF,CAEA,iBACE,YACF,CAEA,gBACE,gBAAkB,CAClB,sBACF,CAEA,sBACE,WAAY,CACZ,UACF,CACF,CCraA,wBAEE,kBAAmB,CADnB,gBAAiB,CAEjB,iBACF,CAGA,mBACE,eAAmB,CACnB,+BAAgC,CAChC,gBAAiB,CACjB,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,UACF,CAEA,gBAKE,sBAAuB,CAHvB,aAAc,CADd,eAAgB,CAKhB,cACF,CAEA,+BALE,kBAAmB,CADnB,YAYF,CANA,eAEE,qBAAsB,CAEtB,SAAW,CACX,iBACF,CAEA,aAOE,kBAAmB,CAHnB,kBAAmB,CACnB,wBAAyB,CAFzB,iBAAkB,CAGlB,YAAa,CAJb,aAAc,CAMd,sBAAuB,CACvB,uBAAyB,CARzB,YASF,CAEA,mCACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAEA,sCACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAEA,aAEE,iBACF,CAEA,YAGE,aAAc,CAFd,gBAAkB,CAClB,eAAgB,CAEhB,iBACF,CAEA,kCACE,aACF,CAEA,qCACE,aACF,CAEA,eAGE,kBAAmB,CADnB,UAAW,CAEX,aAAc,CAHd,UAIF,CAEA,wCACE,kBACF,CAGA,kBAEE,8BAA+B,CAD/B,cAEF,CAGA,uBAUE,gCAAkC,CANlC,eAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CAJnB,WAAY,CAKZ,8DAAmF,CAEnF,eAAgB,CARhB,cAAe,CAEf,UAAW,CAKX,UAGF,CAEA,qBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,mBACF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAGb,QAAO,CADP,UAEF,CAEA,aAEE,aAAc,CADd,gBAEF,CAEA,aACE,QACF,CAEA,cAEE,aAAc,CACd,iBAAmB,CAFnB,eAAgB,CAGhB,qBACF,CAEA,iBAEE,aAAc,CADd,gBAEF,CAEA,gBACE,kBAAmB,CAEnB,WAAY,CAEZ,mBAAqB,CAGrB,cAAe,CADf,iBAAmB,CADnB,eAAgB,CAFhB,qBAAuB,CAKvB,uBAAyB,CACzB,kBACF,CAEA,sBACE,kBAAmB,CAEnB,mCAA6C,CAD7C,0BAEF,CAGA,yBACE,mBACE,cACF,CAEA,gBACE,cACF,CAEA,aAEE,WAAY,CADZ,UAEF,CAEA,aACE,gBACF,CAEA,YACE,iBACF,CAEA,eAEE,cAAgB,CADhB,UAEF,CAEA,kBACE,cACF,CAEA,uBACE,WAAY,CAEZ,SAAU,CACV,cAAe,CAFf,UAGF,CAEA,sBACE,YACF,CAEA,gBAEE,gBAAkB,CADlB,uBAEF,CACF,CAEA,yBACE,gBACE,qBAAsB,CACtB,QACF,CAEA,eAEE,WAAY,CACZ,QAAS,CAFT,SAGF,CAEA,sBAEE,mBAAoB,CADpB,qBAAsB,CAEtB,UACF,CAEA,aACE,sBACF,CACF,CCnPA,2BAEE,aAAc,CADd,eAAgB,CAEhB,YACF,CAEA,wBAEE,kBAAmB,CADnB,iBAEF,CAEA,uBAME,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CAJb,iBAAkB,CAClB,eAAgB,CAMhB,UAAY,CADZ,sBAAuB,CAHvB,mBAKF,CAMA,6BACE,aAAc,CACd,cAAe,CAEf,aAAc,CADd,eAEF,CAEA,sBAEE,kBAAmB,CAGnB,wBAAyB,CACzB,wBAAyB,CACzB,oBAAsB,CACtB,aAAc,CAPd,YAAa,CAEb,SAAW,CAMX,oBAAqB,CALrB,YAMF,CAEA,0BAGE,aAAc,CADd,cAAe,CADf,aAGF,CAEA,kBAYE,kBAAmB,CAPnB,wBAAyB,CAJzB,yBAA0B,CAC1B,kBAAmB,CAKnB,cAAe,CAGf,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAJvB,gBAAiB,CANjB,iBAAkB,CAKlB,iBAAkB,CAJlB,iBAAkB,CAElB,uBAQF,CAEA,wBAEE,wBAAyB,CADzB,oBAEF,CAEA,4BAEE,wBAAyB,CADzB,oBAAqB,CAErB,qBACF,CAEA,6BAEE,wBAAyB,CADzB,oBAAqB,CAErB,kBACF,CAEA,oBACE,kBACF,CAEA,wBAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,oBACE,eACF,CAEA,kBAEE,aAAc,CADd,kBAAmB,CAEnB,mBACF,CAEA,oBACE,aAAc,CAEd,cAAe,CADf,eAAgB,CAEhB,yBAA0B,CAC1B,yBACF,CAEA,0BACE,aACF,CAEA,mBACE,YACF,CAEA,iBACE,aAAc,CACd,iBACF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,QACF,CAEA,gBAME,wCAAyC,CAFzC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,uBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,qBAEE,aAAc,CACd,eAAgB,CAFhB,QAGF,CAEA,aAEE,aAAc,CADd,iBAAmB,CAEnB,iBACF,CAEA,qBAGE,wBAAyB,CAEzB,wBAAyB,CADzB,oBAAsB,CAHtB,eAAgB,CAChB,cAIF,CAEA,wBAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAEhB,kBACF,CAEA,mBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,UACF,CAEA,aAME,wBAMF,CAEA,kCACE,aAAc,CACd,iBACF,CAGA,yBACE,2BACE,YACF,CAEA,kBACE,iBACF,CAEA,uBACE,gBACF,CAEA,wBAEE,WAAY,CADZ,UAEF,CAEA,kBACE,cACF,CACF,CAGA,oBACE,qBAAuB,CACvB,wBAAyB,CACzB,oBAAsB,CAGtB,mCAA6C,CAD7C,aAAc,CADd,cAGF,CAEA,uBACE,aAAc,CACd,iBAAkB,CAClB,eAAgB,CAChB,kBAAmB,CACnB,iBACF,CAEA,kBAME,+BAAgC,CALhC,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,oBAAqB,CACrB,mBAEF,CAEA,WAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,UACF,CAEA,aACE,gBAEF,CAEA,YAIE,oBAAsB,CADtB,wBAEF,CAEA,gCACE,aACF,CAEA,8BACE,aACF,CAEA,8BACE,aACF,CAEA,oBAGE,kBAAmB,CAFnB,gBAAiB,CACjB,eAEF,CAEA,iBACE,wBAAyB,CACzB,mBAAqB,CACrB,oBAAsB,CACtB,YACF,CAEA,yBACE,wBAAyB,CACzB,oBACF,CAEA,uBACE,wBAAyB,CACzB,oBACF,CAEA,mBAEE,kBAAmB,CADnB,YAAa,CAEb,UAAY,CACZ,mBACF,CAEA,iBACE,iBACF,CAEA,YAEE,aAEF,CAEA,mBAGE,oBAAqB,CACrB,gBAAkB,CAClB,eAAgB,CAEhB,oBAAsB,CANtB,gBAAiB,CACjB,qBAAwB,CAIxB,wBAEF,CAEA,4CACE,wBAAyB,CACzB,aACF,CAEA,0CACE,wBAAyB,CACzB,aACF,CAEA,0BAGE,4BAA6B,CAF7B,iBAAmB,CACnB,kBAEF,CAEA,4BACE,aAAc,CACd,mBACF,CAEA,WACE,gBACF,CAEA,kBACE,aAAc,CACd,eACF,CAEA,cAGE,aAAc,CAFd,iBAAmB,CACnB,mBAEF,CAEA,cAEE,8CAAwD,CACxD,iBAAmB,CAFnB,qBAGF,CAEA,oBAGE,mBACF,CAEA,sBAEE,wBAAyB,CAEzB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAIZ,cAAe,CADf,eAAgB,CALhB,oBAAsB,CAOtB,uBACF,CAEA,4BACE,wBACF,CC7YA,8BASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,sBACE,eAAiB,CACjB,kBAAmB,CAKnB,gCAA0C,CAC1C,YAAa,CACb,qBAAsB,CAJtB,eAAgB,CADhB,gBAAiB,CAEjB,eAAgB,CAHhB,SAOF,CAGA,cAME,kBAAmB,CACnB,2BAA4B,CAH5B,iBAIF,CAEA,iBAGE,gBAEF,CAEA,cACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAFlB,aAAc,CADd,cAAe,CADf,cAAe,CAGf,eAAgB,CAEhB,kBACF,CAEA,oBACE,kBAAmB,CACnB,aACF,CAGA,yBACE,kDAA6D,CAC7D,+BAAgC,CAEhC,QAAS,CADT,iBAEF,CAEA,qBAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAMb,gBAAkB,CADlB,eAAgB,CAHhB,OAAQ,CACR,kBAIF,CAEA,yBACE,aACF,CAEA,oBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,oBAEE,kBAAmB,CAGnB,eAAiB,CAEjB,wBAAyB,CADzB,iBAAkB,CAElB,8BAA6C,CAP7C,YAAa,CAEb,OAAQ,CACR,gBAKF,CAEA,SAEE,aAAc,CACd,eAAiB,CAFjB,eAGF,CAEA,aACE,aAAc,CACd,eAAiB,CACjB,eACF,CAEA,oBAGE,aAAc,CADd,gBAAkB,CAElB,iBAAkB,CAHlB,QAAS,CAIT,UACF,CAGA,yBACE,oBACE,yBACF,CAEA,yBACE,iBACF,CACF,CAGA,mBAEE,+BAAgC,CADhC,iBAEF,CAEA,sBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,oBACE,YAAa,CACb,cAAe,CACf,QACF,CAEA,kBAEE,kBAAmB,CAGnB,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAPf,YAAa,CASb,eAAiB,CAPjB,OAAQ,CACR,gBAAiB,CAKjB,kBAEF,CAEA,wBACE,kBAAmB,CACnB,oBACF,CAEA,uCACE,QACF,CAGA,mBAEE,kBAAmB,CAGnB,kBAAmB,CACnB,+BAAgC,CALhC,YAAa,CAMb,cAAe,CAJf,QAAS,CACT,iBAIF,CAEA,uBAIE,cAEF,CAEA,iDANE,kBAAmB,CADnB,YAAa,CAIb,eAAgB,CAFhB,OAUF,CAEA,0BAGE,wBAAyB,CACzB,iBAAkB,CAClB,eAAiB,CAHjB,eAAgB,CADhB,UAKF,CAEA,iBAEE,UAAW,CADX,iBAAmB,CAGnB,iBAAkB,CADlB,gBAEF,CAEA,iBACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CAFjB,gBAGF,CAEA,mBAEE,aAAc,CACd,eAAiB,CAFjB,QAGF,CAEA,qBAKE,kBAAmB,CACnB,yBAA0B,CAC1B,iBAAkB,CAJlB,aAAc,CACd,iBAAkB,CAIlB,cAAgB,CAPhB,YAAa,CACb,iBAOF,CAQA,oDALE,wBAAyB,CACzB,aAQF,CAEA,qBACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,eAAgB,CAHhB,gBAAiB,CAIjB,yBACF,CAEA,0CACE,kBACF,CAEA,8BACE,kBAAmB,CACnB,kBACF,CAGA,mBAEE,QAAO,CADP,iBAEF,CAEA,sBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAGA,YAGE,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAElB,eAAgB,CAChB,kBACF,CAEA,kBACE,+BACF,CAEA,sBAEE,kBAAmB,CADnB,oBAEF,CAEA,mBAEE,kBAAmB,CADnB,oBAEF,CAGA,mCAEE,kDAA6D,CAD7D,wBAAyB,CAEzB,+BACF,CAEA,yCACE,+BACF,CAEA,wBAEE,kDAA6D,CAG7D,kBAAmB,CAKnB,8BAA4C,CAP5C,UAAY,CAFZ,oBAAqB,CAKrB,gBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAJhB,eAAgB,CAKhB,qBAEF,CAGA,0BAEE,kDAA6D,CAD7D,wBAAyB,CAEzB,+BACF,CAEA,gCACE,+BACF,CAEA,eAEE,kDAA6D,CAG7D,kBAAmB,CAKnB,8BAA6C,CAP7C,UAAY,CAFZ,oBAAqB,CAKrB,gBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAJhB,eAAgB,CAKhB,qBAEF,CAEA,cAGE,kBAAmB,CAEnB,kBAAmB,CACnB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,iBAGF,CAEA,iBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,QAIF,CAEA,eACE,kBAAmB,CAGnB,kBAAmB,CAFnB,UAAY,CAGZ,eAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,gBAGE,aAAc,CADd,eAGF,CAEA,8BAHE,+BAAgC,CAHhC,iBASF,CAEA,oBAIE,aAAc,CAHd,aAAc,CAId,eAAiB,CAFjB,eAAgB,CADhB,iBAIF,CAEA,qBAME,eAAiB,CAHjB,wBAAyB,CACzB,iBAAkB,CAClB,eAAiB,CAHjB,WAAY,CADZ,UAMF,CAGA,eAIE,kBAAmB,CAFnB,gBAAiB,CADjB,gBAAiB,CAEjB,eAEF,CAEA,iBAGE,kBAAmB,CAEnB,kBAAmB,CACnB,+BAAgC,CAGhC,aAAc,CARd,YAAa,CAOb,eAAiB,CADjB,eAAgB,CALhB,6BAA8B,CAE9B,gBAMF,CAEA,iBACE,iBACF,CAEA,mBACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAKZ,cAAe,CADf,eAAiB,CAFjB,eAAgB,CAIhB,yBACF,CAEA,yBACE,kBACF,CAEA,cAIE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,8BAAwC,CAExC,eAAgB,CARhB,iBAAkB,CAElB,OAAQ,CADR,QAAS,CAMT,UAEF,CAEA,aAIE,eAAgB,CAChB,WAAY,CAIZ,aAAc,CAFd,cAAe,CANf,aAAc,CAOd,eAAiB,CALjB,gBAAiB,CAGjB,eAAgB,CAIhB,yBAA2B,CAR3B,UASF,CAEA,mBACE,kBACF,CAEA,cAEE,kBAAmB,CAInB,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CARf,YAAa,CAUb,gBAAkB,CARlB,OAAQ,CAER,iBAAkB,CADlB,gBAAiB,CAMjB,kBAEF,CAEA,oBAEE,oBAAqB,CADrB,8BAEF,CAEA,uBACE,kBAAmB,CACnB,oBACF,CAEA,mCAEE,cAAe,CADf,QAEF,CAEA,YAEE,aAAc,CADd,eAEF,CAEA,iBACE,aAAc,CACd,eAAiB,CACjB,eAAgB,CAChB,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAGA,eAEE,kBAAmB,CADnB,iBAEF,CAEA,kBAGE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CAEf,eAAiB,CAHjB,eAAgB,CAHhB,YAAa,CAKb,kBAAoB,CANpB,UAQF,CAEA,iCACE,kBAAmB,CACnB,UACF,CAEA,uCACE,kBACF,CAEA,2BACE,kBAAmB,CACnB,UAAY,CACZ,kBACF,CAGA,cAGE,kBAAmB,CAGnB,kBAAmB,CACnB,2BAA4B,CAF5B,4BAA6B,CAJ7B,YAAa,CACb,6BAA8B,CAE9B,iBAIF,CAEA,gBACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAKZ,cAAe,CAEf,cAAe,CAHf,eAAgB,CAFhB,iBAAkB,CAIlB,yBAEF,CAEA,qCACE,kBACF,CAEA,yBACE,kBAAmB,CACnB,kBACF,CAEA,YACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAKZ,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CAIlB,yBACF,CAEA,kBACE,kBACF,CAGA,oBAEE,kBAAmB,CACnB,4BAA6B,CAF7B,iBAGF,CAEA,uBAEE,aAAc,CACd,gBAAiB,CAFjB,eAGF,CAEA,mBAEE,aAAc,CACd,eAAgB,CAFhB,YAGF,CAGA,yBACE,sBAEE,eAAgB,CADhB,SAEF,CAEA,cACE,yBACF,CAEA,mBAEE,mBAEF,CAEA,iCALE,qBAAsB,CAEtB,QAMF,CAEA,4BAEE,UACF,CACF,CChpBA,gBAEE,kBAAmB,CAGnB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CARd,YAAa,CAOb,gBAAkB,CALlB,OAAQ,CACR,gBAMF,CAEA,wBAGE,wBAA6B,CAC7B,WAAY,CAFZ,OAAQ,CADR,eAIF,CAEA,sEAGE,aAAc,CACd,iBACF,CAEA,sBACE,aACF,CAEA,cAGE,aAAc,CACd,aAAc,CAFd,WAAY,CADZ,UAIF,CAEA,oBACE,aACF,CAEA,cAEE,aAAc,CADd,eAEF,CAEA,sBAGE,kBAAmB,CAFnB,YAAa,CACb,OAEF,CAEA,WAIE,iBAAkB,CAHlB,gBAAkB,CAClB,eAAgB,CAGhB,aAAc,CAFd,eAGF,CAEA,eACE,wBAAyB,CACzB,aACF,CAEA,cACE,wBAAyB,CACzB,aACF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAGA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,eAEE,aAAc,CADd,eAEF,CAOA,sCAHE,kBAAmB,CADnB,YAQF,CAJA,qBAEE,OAEF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,OACF,CAEA,uBAEE,iBAAmB,CADnB,eAEF,CAEA,uBACE,iBAAmB,CAEnB,oBAAsB,CADtB,wBAEF,CAEA,2BACE,aACF,CAEA,2BACE,aACF,CAEA,0BACE,aACF,CAEA,0BACE,aACF,CAEA,+BACE,aACF,CAEA,+BACE,aACF,CAGA,yBACE,8BAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CAEA,qBACE,OACF,CAEA,uBACE,gBACF,CAEA,uBACE,eACF,CACF,CC9JA,qBAEE,aAAc,CADd,gBAAiB,CAEjB,YACF,CAEA,kBAME,+BAAgC,CADhC,qBAEF,CAEA,+BANE,sBAUF,CAEA,aAQE,qBAAuB,CADvB,mBAAqB,CAErB,aAAc,CAJd,aAAc,CAFd,sBAAuB,CACvB,YAQF,CAEA,mBACE,wBAAyB,CACzB,oBACF,CAEA,iBAEE,cAAe,CADf,aAEF,CAEA,aACE,QACF,CAEA,iBAME,kBAAmB,CADnB,YAAa,CAJb,kBAAmB,CAMnB,UAAY,CAHZ,oBAIF,CAEA,aACE,cACF,CAEA,iBACE,aAAc,CACd,iBACF,CAOA,qBAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAKZ,cAAe,CAVf,YAAa,CAQb,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CACX,kBAAoB,CAQpB,oCACF,CAEA,2BACE,wBACF,CAEA,8BACE,wBAAyB,CACzB,kBACF,CAEA,yBAEE,WAAY,CADZ,UAEF,CAEA,oBAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAKZ,cAAe,CAVf,YAAa,CAQb,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CACX,kBAAoB,CAQpB,oCACF,CAEA,0BACE,wBACF,CAEA,wBAEE,WAAY,CADZ,UAEF,CAGA,kBACE,kBACF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,eAEE,aAAc,CADd,iBAAmB,CAEnB,eACF,CAEA,cAGE,wBAAyB,CACzB,oBAAsB,CAFtB,YAAc,CAGd,eAAgB,CAJhB,UAKF,CAEA,eAEE,wBAAyB,CADzB,WAAY,CAEZ,yBACF,CAGA,YAGE,aAAS,CAAT,QAAS,CADT,wDAGF,CAEA,WAEE,kBAAmB,CAGnB,qBAAuB,CAEvB,wBAAyB,CADzB,oBAAsB,CAEtB,gCAA0C,CAP1C,YAAa,CAEb,QAMF,CAEA,mBACE,6BACF,CAEA,sBACE,6BACF,CAEA,qBACE,6BACF,CAEA,WACE,cACF,CAEA,cACE,QACF,CAEA,aAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAEhB,aACF,CAEA,YAEE,aAAc,CADd,iBAAmB,CAEnB,eACF,CAGA,gBACE,oBACF,CAEA,MAEE,+BAAgC,CADhC,YAEF,CAEA,KAEE,eAAgB,CAKhB,WAAoC,CAApC,6BAAoC,CAHpC,aAAc,CAEd,cAAe,CADf,eAAgB,CAJhB,qBAAuB,CAOvB,uBACF,CAEA,WACE,aACF,CAEA,YAEE,2BAA4B,CAD5B,aAEF,CAGA,qBACE,gBACF,CAEA,gBAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yDAEF,CAEA,UACE,qBAAuB,CACvB,wBAAyB,CACzB,oBAAsB,CAEtB,gCAA0C,CAD1C,eAAgB,CAEhB,8BACF,CAEA,gBACE,mCACF,CAEA,iBAGE,sBAAuB,CAEvB,+BAAgC,CAJhC,YAAa,CAGb,0BAEF,CAEA,2BANE,6BASF,CAEA,cAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,eAAgB,CADhB,SAAW,CAGX,oBACF,CAMA,gBAEE,aAAc,CADd,iBAEF,CAEA,iBAEE,aAAc,CADd,eAEF,CAEA,YAEE,kBAAmB,CAMnB,gBAAiB,CAHjB,oBAAqB,CAJrB,YAAa,CAKb,gBAAkB,CAClB,eAAgB,CAJhB,UAAY,CACZ,qBAKF,CAEA,aACE,iBACF,CAEA,eACE,0BACF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,SAAW,CACX,oBACF,CAEA,oBACE,kBACF,CAEA,eAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,iBAAmB,CADnB,SAGF,CAEA,eAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,aACE,YAAa,CACb,UACF,CAEA,8CAIE,kBAAmB,CAGnB,WAAY,CACZ,mBAAqB,CAGrB,cAAe,CARf,YAAa,CAMb,iBAAmB,CACnB,eAAgB,CALhB,SAAW,CACX,kBAAoB,CAMpB,uBACF,CAEA,gBACE,wBAEF,CAEA,sBACE,wBACF,CAEA,iBACE,wBAAyB,CACzB,UACF,CAEA,uBACE,wBACF,CAEA,aACE,wBAAyB,CACzB,UACF,CAEA,mBACE,wBACF,CAEA,0DAIE,WAAY,CADZ,UAEF,CAGA,gCAIE,kBAAmB,CAInB,aAAc,CANd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,YAAa,CACb,iBAEF,CAEA,4BAEE,cAAe,CACf,kBACF,CAEA,sCAIE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAEhB,mBACF,CAEA,oCAEE,iBAAmB,CACnB,oBACF,CAEA,uBAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAIZ,cAAe,CATf,YAAa,CAQb,eAAgB,CANhB,SAAW,CACX,qBAAuB,CAOvB,oCACF,CAEA,6BACE,wBACF,CAEA,2BAEE,WAAY,CADZ,UAEF,CAGA,yBACE,qBACE,YACF,CAEA,kBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,aACE,kBACF,CAEA,iBACE,gBACF,CAMA,4BACE,yBACF,CAEA,MACE,eACF,CACF,CCpeA,qBAEE,wBAAyB,CADzB,gBAEF,CAGA,uBACE,gCACF,CAEA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CCnBA,yBAEE,wBAAyB,CADzB,gBAEF,CAEA,sBAEE,+BAAgC,CAGhC,6BAA8B,CAF9B,kBAAmB,CAInB,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,UACF,CAQA,4CAZE,kBAAmB,CALnB,qBAAuB,CAGvB,YA0BF,CAZA,sBAME,wBAAyB,CACzB,mBAAqB,CAErB,aAAc,CACd,cAAe,CALf,aAAc,CAFd,sBAAuB,CAQvB,uBAAyB,CAPzB,YAQF,CAEA,4BACE,wBAAyB,CACzB,oBACF,CAEA,0BAEE,cAAe,CADf,aAEF,CAEA,UACE,QACF,CAEA,WAME,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CAHb,eAAgB,CAKhB,UAAY,CAHZ,oBAIF,CAEA,qBATE,iBAWF,CAEA,cACE,aAAc,CACd,iBACF,CAOA,4CAGE,kBAAmB,CAGnB,WAAY,CACZ,mBAAqB,CAGrB,cAAe,CARf,YAAa,CAMb,iBAAmB,CACnB,eAAgB,CALhB,SAAW,CACX,kBAAoB,CAMpB,uBACF,CAEA,qBACE,wBAAyB,CACzB,UACF,CAEA,2BACE,wBACF,CAEA,uBACE,wBAAyB,CACzB,UACF,CAEA,6BACE,wBACF,CAEA,oDAGE,WAAY,CADZ,UAEF,CAGA,sBAEE,kBAAmB,CAInB,wBAAyB,CACzB,wBAAyB,CACzB,mBAAqB,CAErB,aAAc,CATd,YAAa,CAGb,UAAY,CADZ,sBAAuB,CAMvB,gBAAiB,CAJjB,YAMF,CAEA,oBAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,cAAe,CADf,aAMF,CAQA,uBAGE,aAAc,CADd,gBAAiB,CADjB,SAGF,CAGA,aAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,YAAa,CACb,iBACF,CAEA,aAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,eACE,aAAc,CACd,cAAe,CACf,QACF,CAGA,iBAGE,kBAAmB,CAInB,qBAAuB,CAEvB,wBAAyB,CADzB,oBAAsB,CAEtB,gCAA0C,CAT1C,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,YAAa,CACb,iBAKF,CAEA,YACE,cAAe,CACf,kBACF,CAEA,oBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,mBACF,CAEA,mBACE,aAAc,CACd,kBAAmB,CACnB,eACF,CAEA,eACE,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,sBAEF,CAEA,iBAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAKZ,cAAe,CAVf,YAAa,CAQb,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CACX,qBAAuB,CAQvB,uBACF,CAEA,uBACE,wBACF,CAEA,qBAEE,WAAY,CADZ,UAEF,CAEA,WAGE,kBAAmB,CAInB,aAAc,CANd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,YAAa,CACb,iBAEF,CAEA,eAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,aACE,cAAe,CACf,QACF,CAGA,iBAME,qBAAuB,CAEvB,wBAAyB,CADzB,oBAAsB,CAJtB,QAAS,CACT,kBAAmB,CACnB,YAIF,CAEA,4BATE,kBAAmB,CADnB,YAmBF,CATA,WAKE,mBAAqB,CACrB,iBAAmB,CACnB,eAAgB,CAJhB,SAAW,CACX,kBAAoB,CAIpB,uBACF,CAEA,kBACE,wBAAyB,CACzB,UACF,CAEA,qBACE,wBAAyB,CACzB,UACF,CAEA,oBACE,wBAAyB,CACzB,aACF,CAEA,gBAGE,wBAAyB,CADzB,UAAW,CADX,UAGF,CAGA,gBAKE,qBAAuB,CACvB,4BAA6B,CAE7B,QAAS,CAJT,cAAe,CAGf,uBAAgB,CAAhB,eAEF,CAEA,2BAEE,YAAa,CACb,UACF,CAEA,eAEE,kBAAmB,CAGnB,WAAY,CACZ,mBAAqB,CAGrB,cAAe,CARf,YAAa,CAMb,iBAAmB,CACnB,eAAgB,CALhB,SAAW,CACX,qBAAuB,CAMvB,uBACF,CAEA,uBACE,wBAAyB,CACzB,UACF,CAEA,6BACE,wBACF,CAEA,yBACE,wBAAyB,CAEzB,wBAAyB,CADzB,aAEF,CAEA,+BACE,wBACF,CAEA,uBACE,wBAAyB,CACzB,UACF,CAEA,6BACE,wBACF,CAEA,mBAEE,WAAY,CADZ,UAEF,CAGA,yBACE,sBAIE,mBAAoB,CAFpB,qBAAsB,CACtB,QAAS,CAFT,YAIF,CAEA,aACE,kBACF,CAEA,WACE,iBACF,CAEA,gBACE,sBACF,CAEA,uBACE,YACF,CAEA,sBACE,WACF,CAEA,gBACE,qBAAsB,CACtB,QACF,CAEA,2BAGE,sBAAuB,CADvB,UAEF,CACF,CAGA,kBAGE,kBAAmB,CAKnB,qBAAuB,CACvB,oBAAsB,CAEtB,mCAA6C,CAV7C,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAMvB,WAAY,CALZ,eAAgB,CAChB,YAAa,CACb,iBAKF,CAEA,8BACE,cAAe,CACf,kBACF,CAEA,qBACE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAChB,oBACF,CAEA,oBACE,wBAAyB,CACzB,wBAAyB,CACzB,mBAAqB,CAErB,kBAAmB,CAEnB,eAAgB,CAHhB,cAAe,CAEf,eAAgB,CAEhB,UACF,CAEA,sBAEE,aAAc,CADd,mBAEF,CAEA,2BACE,aAAc,CACd,eACF,CAEA,mBAGE,4BAA6B,CAF7B,eAAgB,CAChB,gBAEF,CAEA,sBAGE,aAAc,CAFd,gBAAkB,CAClB,mBAEF,CAEA,sBAEE,8CAAwD,CACxD,iBAAmB,CAFnB,oBAGF,CAEA,oBAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,uBAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAIZ,cAAe,CATf,YAAa,CAQb,eAAgB,CANhB,SAAW,CACX,qBAAuB,CAOvB,uBACF,CAEA,6BACE,wBAAyB,CACzB,0BACF,CAEA,8BACE,uBACF,CCrfA,uBAIE,wBAAyB,CAFzB,aAAc,CADd,eAAgB,CAIhB,gBAAiB,CAFjB,YAGF,CAEA,eAIE,eAAiB,CACjB,kBAAmB,CACnB,mCAA6C,CAJ7C,kBAAmB,CACnB,YAAa,CAFb,iBAMF,CAEA,cAEE,kBAAmB,CAKnB,aAAc,CANd,YAAa,CAIb,cAAe,CACf,eAAgB,CAFhB,UAAY,CADZ,sBAAuB,CAKvB,mBACF,CAEA,aACE,gBACF,CAEA,oBACE,aAAc,CACd,cAAe,CACf,QACF,CAEA,eASE,oBAAqB,CALrB,YAMF,CAEA,mBAGE,aAAc,CADd,cAAe,CADf,aAGF,CAEA,gBACE,eAAiB,CACjB,kBAAmB,CAGnB,mCAA6C,CAD7C,oBAAqB,CADrB,YAGF,CAEA,eAGE,aAAc,CACd,kBACF,CAEA,UACE,aACF,CAEA,WAME,wBAAyB,CALzB,yBAA0B,CAC1B,iBAAkB,CAKlB,cAAe,CAJf,iBAAkB,CAClB,iBAAkB,CAClB,uBAGF,CAEA,sCAGE,wBAAyB,CADzB,oBAEF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,QACF,CAEA,mBAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,WAEE,aAAc,CADd,cAAe,CAEf,QACF,CAEA,aACE,aAAc,CAEd,cAAe,CACf,eAAgB,CAFhB,yBAGF,CAEA,mBACE,aACF,CAEA,cAEE,aAAc,CADd,iBAAmB,CAEnB,QACF,CAEA,mBACE,YACF,CAEA,gBAGE,4BAA6B,CAF7B,iBAAkB,CAClB,kBAEF,CAEA,aAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBACF,CAEA,YAEE,qBAEF,CAEA,uBALE,YAAa,CAEb,UAWF,CARA,WAEE,kBAAmB,CAGnB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAHlB,cAIF,CAEA,WAEE,aAAc,CADd,iBAEF,CAEA,WAGE,aAAc,CAFd,QAAO,CACP,iBAAmB,CAEnB,eACF,CAEA,WAEE,aAAc,CACd,aAAc,CAFd,gBAGF,CAEA,eASE,kBAAmB,CALnB,wBAAyB,CADzB,WAAY,CAGZ,iBAAkB,CADlB,UAAY,CAEZ,cAAe,CACf,YAAa,CAGb,cAAe,CACf,eAAiB,CAVjB,aAAc,CAQd,sBAAuB,CAGvB,oCAAsC,CAZtC,YAaF,CAEA,qBACE,wBACF,CAEA,kBAEE,kBAAmB,CAKnB,wBAAyB,CAFzB,wBAAyB,CACzB,iBAAkB,CALlB,YAAa,CAEb,QAAS,CACT,YAAa,CAIb,gCACF,CAEA,wBACE,oBACF,CAEA,mBAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAIb,QAAO,CAFP,UAGF,CAEA,WAGE,aAAc,CACd,aAAc,CAFd,aAAc,CADd,YAIF,CAEA,WAEE,aAAc,CADd,iBAAmB,CAEnB,eACF,CAEA,oBASE,kBAAmB,CALnB,wBAAyB,CADzB,WAAY,CAGZ,iBAAkB,CADlB,UAAY,CAEZ,cAAe,CACf,YAAa,CAMb,aAAc,CAHd,cAAe,CACf,eAAiB,CAVjB,aAAc,CAQd,sBAAuB,CAGvB,oCAAsC,CAZtC,YAcF,CAEA,0BACE,wBACF,CAEA,YACE,aAGF,CAEA,0BAJE,gBAAkB,CAClB,gBAQF,CALA,cACE,aAAc,CAGd,eACF,CAEA,WACE,aAAc,CACd,gBAAkB,CAElB,eAAgB,CADhB,gBAEF,CAEA,gBAGE,kBAAmB,CAGnB,eAAiB,CACjB,kBAAmB,CACnB,mCAA6C,CAJ7C,QAAS,CAFT,6BAA8B,CAG9B,YAIF,CAEA,aAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,wBAAyB,CACzB,iBAAkB,CAFlB,aAAc,CAKd,cAAe,CAVf,YAAa,CAQb,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CACX,qBAAuB,CAQvB,uBACF,CAEA,kCACE,wBAAyB,CACzB,oBACF,CAEA,sBAEE,kBAAmB,CADnB,UAEF,CAEA,gBAEE,kBAAmB,CAGnB,kDAA6D,CAE7D,WAAY,CACZ,iBAAkB,CAKlB,+BAA8C,CAP9C,UAAY,CAKZ,cAAe,CAVf,YAAa,CAQb,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CACX,mBAAqB,CAQrB,uBAEF,CAEA,qCAEE,+BAA8C,CAD9C,0BAEF,CAEA,yBAIE,eAAgB,CAFhB,kBAAmB,CADnB,UAAY,CAEZ,cAEF,CAEA,SAME,iCAAkC,CAFlC,sBAAkC,CAClC,iBAAkB,CADlB,6BAAkC,CAFlC,WAAY,CADZ,UAMF,CASA,yBACE,uBACE,YACF,CAMA,+BACE,cACF,CAEA,gBACE,qBAAsB,CACtB,QACF,CAEA,6BAGE,sBAAuB,CADvB,UAEF,CAEA,WACE,iBACF,CACF,CC7XA,iBAEE,kBAAmB,CADnB,gBAEF,CAGA,mBACE,eAAmB,CACnB,+BAAgC,CAChC,cACF,CAEA,gBAKE,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CAEjB,gBAIF,CAEA,6BAHE,kBAAmB,CAFnB,YASF,CAJA,aAGE,QACF,CAEA,gBAEE,kBAAmB,CAGnB,kBAAmB,CACnB,wBAAyB,CACzB,mBAAqB,CACrB,aAAc,CAId,cAAe,CAXf,YAAa,CASb,iBAAmB,CACnB,eAAgB,CARhB,SAAW,CACX,kBAAoB,CAKpB,oBAAqB,CAIrB,uBACF,CAEA,sBACE,kBAAmB,CACnB,aACF,CAEA,eAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,iBACF,CAEA,YAEE,aAAc,CACd,QACF,CAEA,sBAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,gBAAkB,CAGlB,eAAgB,CAJhB,SAAW,CAGX,iBAEF,CAEA,WAEE,cAAgB,CADhB,aAEF,CAEA,gBACE,YAAa,CACb,UACF,CAEA,4BAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAKZ,cAAe,CAVf,YAAa,CAQb,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CACX,kBAAoB,CAQpB,uBACF,CAEA,kCACE,wBACF,CAEA,gCAEE,WAAY,CADZ,UAEF,CAGA,oBAEE,aAAc,CADd,gBAAiB,CAEjB,mBACF,CAGA,yBAGE,aAAS,CACT,iBAAkB,CAHlB,YAAa,CAEb,QAAS,CADT,6BAGF,CAEA,iBACE,eAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CACnB,YACF,CAEA,cACE,oBACF,CAEA,iBAGE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAEhB,gBACF,CAEA,gBACE,aAAc,CACd,iBAAmB,CACnB,QACF,CAEA,mBACE,kBACF,CAEA,qBAEE,kBAAmB,CAInB,kBAAmB,CACnB,wBAAyB,CACzB,mBAAqB,CACrB,aAAc,CARd,YAAa,CASb,iBAAmB,CAPnB,SAAW,CACX,iBAAmB,CACnB,cAMF,CAEA,yBAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAGA,oBACE,oBACF,CAEA,qBAIE,wBAAyB,CACzB,oBAAsB,CACtB,iCAAqC,CACrC,iBAAmB,CACnB,eAAgB,CANhB,gBAAiB,CACjB,YAAa,CAMb,eAAgB,CAChB,gCAAkC,CATlC,UAUF,CAEA,2BAEE,oBAAqB,CACrB,4BAA6B,CAF7B,YAGF,CAEA,kCACE,aAAc,CACd,iBACF,CAEA,eAKE,aAAc,CAJd,YAAa,CAGb,gBAAkB,CAFlB,6BAA8B,CAC9B,gBAGF,CAGA,eACE,YAAa,CACb,QAAS,CACT,wBACF,CAEA,8BAGE,kBAAmB,CAGnB,WAAY,CACZ,mBAAqB,CAGrB,cAAe,CARf,YAAa,CAMb,iBAAmB,CACnB,eAAgB,CALhB,SAAW,CACX,qBAAuB,CAMvB,uBACF,CAEA,cACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,mCACE,kBACF,CAEA,gBACE,kBAAmB,CACnB,UACF,CAEA,qCACE,kBAAmB,CAEnB,mCAA6C,CAD7C,0BAEF,CAEA,gDAKE,eAAgB,CAFhB,kBAAmB,CADnB,UAAY,CAEZ,cAEF,CAGA,cACE,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CACnB,cACF,CAEA,iBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,eACF,CAEA,YAEE,qBAEF,CAEA,uBALE,YAAa,CAEb,UASF,CANA,WAEE,kBAAmB,CAGnB,aAAc,CADd,iBAEF,CAEA,aAEE,kBAAmB,CAInB,kBAAmB,CAEnB,iBAAkB,CADlB,UAAY,CANZ,YAAa,CAUb,aAAc,CAFd,gBAAkB,CAClB,eAAgB,CALhB,aAAc,CAFd,sBAAuB,CACvB,YAQF,CAGA,gBACE,eAAmB,CAGnB,wBAAyB,CAFzB,kBAAmB,CACnB,YAEF,CAGA,0BACE,yBAEE,UAAW,CADX,yBAEF,CAEA,cACE,QACF,CACF,CAEA,yBACE,oBACE,YACF,CAEA,iBACE,cACF,CAEA,gBACE,cACF,CAEA,aAEE,sBAAuB,CACvB,UACF,CAEA,4BALE,qBAOF,CAEA,8BAEE,sBACF,CACF,CAEA,yBACE,qBAEE,gBAAkB,CADlB,gBAEF,CAEA,eACE,qBAAsB,CACtB,UACF,CAEA,WAEE,sBAAuB,CADvB,qBAAsB,CAEtB,SAAW,CACX,eACF,CACF,CAGA,oBAGE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAJlB,eAAgB,CAChB,YAIF,CAEA,kBAIE,aAAc,CAHd,aAAc,CACd,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,kBAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAAkB,CAIlB,0CAA8C,CAL9C,UAMF,CAEA,wBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,aAGE,iBAAkB,CAClB,cAAe,CACf,eAAgB,CAJhB,eAAgB,CAChB,YAIF,CAEA,qBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,mBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,kBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAGA,oBAGE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAJlB,kBAAmB,CACnB,YAIF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,QAAS,CAET,kBACF,CAEA,0BAGE,wBAAyB,CACzB,iBAAkB,CAHlB,QAAO,CAIP,cAAe,CAHf,iBAAkB,CAIlB,0CACF,CAEA,gCAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,qBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,cAAe,CACf,eAAgB,CANhB,iBAAkB,CAQlB,+BAAiC,CACjC,kBACF,CAEA,0CACE,kBACF,CAEA,8BACE,kBAAmB,CACnB,kBACF,CAGA,yBACE,iBACE,SACF,CAEA,gBAIE,mBAAoB,CAFpB,qBAAsB,CACtB,QAAS,CAFT,cAIF,CAEA,aACE,kBACF,CAEA,eACE,iBACF,CAEA,gBACE,sBACF,CAEA,4BAEE,sBAAuB,CADvB,UAEF,CAEA,oBACE,YACF,CAEA,iBACE,cACF,CAEA,oBACE,gBACF,CAEA,eACE,qBAAsB,CACtB,UACF,CAEA,8BAGE,sBAAuB,CADvB,UAEF,CACF,CClhBA,uBAGE,kBAAmB,CAEnB,kDAA6D,CAH7D,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAKjB,YACF,CAEA,kBAOE,8BAAgC,CANhC,eAAiB,CACjB,kBAAmB,CACnB,gEAAqF,CAGrF,eAAgB,CAFhB,YAAa,CACb,UAGF,CAEA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,oBAEE,kBAAmB,CADnB,iBAEF,CAEA,kBACE,YAAa,CACb,sBAAuB,CACvB,kBACF,CAEA,mBAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAEhB,mBACF,CAEA,sBACE,aAAc,CACd,iBACF,CAEA,kBACE,cACF,CAEA,YACE,eACF,CAEA,YAIE,aAAc,CAHd,aAAc,CACd,iBAAmB,CACnB,eAAgB,CAEhB,mBACF,CAEA,eACE,iBACF,CAEA,YAOE,aAAc,CADd,cAAe,CAJf,WAAa,CAMb,mBAAoB,CAPpB,iBAAkB,CAElB,OAAQ,CACR,0BAA2B,CAC3B,aAIF,CAEA,YAOE,qBAAuB,CAJvB,wBAAyB,CACzB,iBAAkB,CAClB,iBAAmB,CAHnB,oCAAwC,CAIxC,uBAAyB,CALzB,UAOF,CAEA,kBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,qBACE,wBAAyB,CACzB,aAAc,CACd,kBACF,CAEA,yBACE,aACF,CAEA,eAEE,kBAAmB,CAGnB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CAPd,YAAa,CAQb,iBAAmB,CANnB,SAAW,CAOX,kBAAmB,CANnB,cAOF,CAEA,YAEE,WAAY,CADZ,UAGF,CAEA,cAGE,kBAAmB,CAInB,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAXf,YAAa,CASb,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CADX,sBAAuB,CAEvB,mBAAqB,CAQrB,uBAAyB,CAbzB,UAcF,CAEA,mCACE,wBAAyB,CAEzB,+BAA8C,CAD9C,0BAEF,CAEA,uBACE,wBAAyB,CAGzB,eAAgB,CAFhB,kBAAmB,CACnB,cAEF,CAOA,8BAHE,WAAY,CADZ,UAWF,CAPA,iBAIE,sBAAkC,CAAlC,6BAGF,CAQA,oBACE,eAAgB,CAChB,iBACF,CAEA,aAGE,wBAAyB,CAGzB,wBAAyB,CADzB,iBAAkB,CAHlB,aAAc,CADd,gBAAkB,CAGlB,aAGF,CAGA,yBACE,uBACE,aACF,CAEA,kBACE,cACF,CAEA,mBACE,gBACF,CACF,CC9MA,iBAKE,wBAAyB,CAFzB,aAAc,CADd,gBAAiB,CAEjB,gBAAiB,CAHjB,YAKF,CAEA,kBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,iBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,QACF,CAEA,oBAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,mBAIE,qBAAuB,CAFvB,wBAAyB,CACzB,iBAAkB,CAGlB,cAAe,CADf,iBAAmB,CAJnB,kBAAoB,CAMpB,gCACF,CAEA,yBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,QAAS,CAFT,sBAAuB,CACvB,gBAEF,CAEA,iBAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAQA,cAEE,kBAAmB,CAGnB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CAPd,YAAa,CAEb,SAAW,CAMX,kBAAmB,CALnB,YAMF,CAEA,YAGE,aAAc,CADd,cAAe,CADf,aAGF,CAGA,YAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAA2D,CAE3D,kBACF,CAEA,WACE,eAAiB,CACjB,kBAAmB,CAEnB,8BAAwC,CADxC,cAAe,CAEf,iDACF,CAEA,iBAEE,+BAA0C,CAD1C,0BAEF,CAEA,mBACE,YAAa,CACb,qBACF,CAEA,kBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,iBAGE,aAAc,CAFd,iBAAmB,CACnB,eAAgB,CAEhB,QACF,CAEA,gBAEE,aAAc,CADd,YAEF,CAEA,gCAAkC,aAAgB,CAClD,iCAAmC,aAAgB,CACnD,kCAAoC,aAAgB,CACpD,kCAAoC,aAAgB,CAEpD,iBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,QACF,CAGA,eACE,eAAiB,CACjB,kBAAmB,CAGnB,8BAAwC,CADxC,kBAAmB,CADnB,cAGF,CAEA,eAGE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAEhB,iBACF,CAEA,iBAGE,eAAgB,CADhB,YAAa,CADb,YAAa,CAGb,cACF,CAEA,YAEE,eAAgB,CADhB,YAAa,CAEb,SAAW,CAEX,WAAY,CADZ,UAEF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CAGb,QAAO,CAFP,qBAAsB,CAGtB,WACF,CAEA,WACE,gDAAqD,CACrD,yBAA0B,CAI1B,cAAe,CAFf,cAAe,CACf,uBAAyB,CAFzB,UAIF,CAEA,iBACE,gDACF,CAEA,aAEE,aAAc,CADd,gBAAkB,CAElB,gBAAkB,CAClB,iBACF,CAGA,kBACE,eAAiB,CACjB,kBAAmB,CAEnB,8BAAwC,CADxC,cAEF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,oBACF,CAEA,kBACE,iBAAkB,CAClB,WACF,CAEA,aAOE,aAAc,CADd,WAAY,CAJZ,WAAa,CAMb,mBAAoB,CAPpB,iBAAkB,CAElB,OAAQ,CACR,0BAA2B,CAC3B,UAIF,CAEA,cAGE,wBAAyB,CACzB,iBAAkB,CAClB,iBAAmB,CAHnB,iCAAqC,CAIrC,gCAAkC,CALlC,UAMF,CAEA,oBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,0BACE,eACF,CAEA,gBAEE,wBAAyB,CADzB,UAEF,CAEA,mBAME,wBAAyB,CADzB,+BAAgC,CADhC,aAAc,CADd,eAAgB,CADhB,cAAgB,CADhB,eAMF,CAEA,mBAEE,+BAAgC,CADhC,cAEF,CAEA,yBACE,wBACF,CAEA,iBACE,eACF,CAEA,YACE,aAAc,CACd,8CAAwD,CACxD,iBACF,CAEA,sBACE,iBACF,CAEA,iBACE,wBAAyB,CAGzB,kBAAmB,CAFnB,aAAc,CAGd,gBAAkB,CAClB,eAAgB,CAHhB,oBAIF,CAEA,WACE,aAAc,CACd,iBACF,CAGA,iCACE,aAAc,CACd,8CAAwD,CACxD,iBAAmB,CACnB,eACF,CAGA,sBACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAHlB,cAAe,CAKf,qBAAwB,CADxB,oBAAuB,CAHvB,oBAAqB,CACrB,uBAIF,CAEA,4BACE,wBAAyB,CAIzB,8BAA6C,CAH7C,aAAc,CACd,yBAA0B,CAC1B,0BAEF,CAEA,6BAEE,8BAA6C,CAD7C,uBAEF,CAEA,iBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,cAEE,aAAc,CADd,gBAAkB,CAElB,iBACF,CAGA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,UACF,CAEA,sBAGE,aAAc,CAFd,kBAAmB,CACnB,eAEF,CAEA,mBACE,YAAa,CACb,UACF,CAGA,eAEE,iBAAkB,CAClB,iBAAmB,CACnB,eAAgB,CAEhB,gBAAiB,CALjB,uBAA0B,CAI1B,iBAEF,CAEA,oBACE,wBAAyB,CACzB,aACF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAGA,oBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,UACF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,eACE,YAAa,CAEb,iBAAmB,CADnB,WAEF,CAEA,cACE,aAAc,CACd,eACF,CAEA,aACE,aAAc,CACd,eACF,CAGA,kBACE,YAAa,CACb,qBAAsB,CACtB,WACF,CAEA,cAEE,iBAAkB,CAClB,iBAAmB,CACnB,eAAgB,CAHhB,sBAAyB,CAIzB,iBACF,CAEA,eACE,wBAAyB,CACzB,aACF,CAEA,iBACE,wBAAyB,CACzB,aACF,CAEA,kBACE,wBAAyB,CACzB,aACF,CAGA,aACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,8BAEE,kBAAmB,CACnB,iBAAmB,CACnB,eAAgB,CAHhB,uBAA0B,CAK1B,iBAAkB,CADlB,wBAEF,CAEA,oBACE,wBAAyB,CACzB,aACF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAEA,kBACE,wBAAyB,CACzB,aACF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,oBACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAGA,iBACE,YAAa,CACb,qBAAsB,CACtB,WACF,CAEA,cAGE,aAAc,CAFd,gBAAkB,CAClB,eAEF,CAEA,mBAEE,aAAc,CADd,iBAAmB,CAEnB,iBACF,CAEA,gCACE,aAAc,CACd,iBACF,CAGA,yBACE,iBACE,YACF,CAEA,kBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,YACE,yBACF,CAEA,gBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,kBACE,UACF,CAEA,YACE,UACF,CAEA,aACE,iBACF,CACF,CChkBA,kBACE,kDAA6D,CAG7D,8BAAwC,CAFxC,UAAY,CACZ,cAAe,CAEf,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,WACF,CAEA,YAEE,6BAA8B,CAG9B,kBAAmB,CADnB,cAEF,CAEA,uBALE,kBAAmB,CAFnB,YAWF,CAJA,WAGE,UACF,CAEA,YAGE,UAAY,CADZ,WAAY,CADZ,UAGF,CAEA,YAGE,UAAY,CAFZ,iBAAkB,CAClB,eAEF,CAEA,WAGE,QACF,CAEA,WAGE,eAA+B,CAF/B,iBAAmB,CACnB,eAEF,CAEA,eAEE,kBAAmB,CAGnB,oBAAoC,CACpC,sBAA0C,CAC1C,iBAAkB,CAClB,UAAY,CACZ,cAAe,CARf,YAAa,CAEb,sBAAuB,CACvB,aAAe,CAMf,uBACF,CAEA,qBACE,gBAAoC,CACpC,0BACF,CAEA,aAEE,WAAY,CADZ,UAEF,CAEA,UACE,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,cACF,CAEA,UAEE,kBAAmB,CASnB,sBAA6B,CAN7B,iBAAkB,CAElB,WAA+B,CAN/B,YAAa,CAQb,iBAAmB,CADnB,eAAgB,CALhB,SAAW,CACX,qBAAuB,CAEvB,oBAAqB,CAIrB,uBAEF,CAEA,gBAEE,oBAAoC,CACpC,kBAAsC,CAFtC,UAAY,CAGZ,0BACF,CAEA,iBAEE,oBAAqC,CACrC,sBAAsC,CACtC,8BAAwC,CAHxC,UAIF,CAEA,UAGE,aAAc,CADd,cAAe,CADf,aAGF,CAEA,WACE,kBACF,CAGA,yBACE,YAGE,QACF,CAEA,sBAJE,qBAAsB,CADtB,cASF,CAJA,UAEE,SAEF,CAEA,UACE,sBAAuB,CACvB,YACF,CAEA,YACE,cACF,CAEA,WACE,QACF,CACF,CAEA,yBAKE,sBACE,eACF,CAEA,UAEE,eAAiB,CADjB,cAEF,CAEA,WACE,YACF,CAEA,UAEE,aAAc,CADd,YAEF,CACF,CCtKA,mBAGE,aAAc,CADd,gBAAiB,CADjB,YAGF,CAEA,oBACE,kBAAmB,CACnB,iBACF,CAEA,mBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,mBACF,CAEA,yBACE,aAAc,CACd,cAAe,CAEf,aAAc,CADd,eAEF,CAEA,sBACE,kBACF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAG3D,aAAc,CADd,eAEF,CAEA,aAEE,kBAAmB,CAGnB,eAAiB,CACjB,wBAAyB,CACzB,oBAAsB,CACtB,cAAe,CAPf,YAAa,CAEb,QAAS,CACT,cAAe,CAMf,eAAgB,CADhB,uBAEF,CAEA,mBACE,oBAAqB,CACrB,mCACF,CAEA,oBAEE,kBAAmB,CADnB,oBAAqB,CAErB,mCACF,CAEA,WAEE,aAAc,CADd,cAEF,CAEA,iBAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAEhB,iBACF,CAEA,gBAEE,aAAc,CADd,iBAAmB,CAEnB,QACF,CAEA,qCACE,aACF,CAEA,oCACE,aACF,CAEA,qBACE,eAAiB,CACjB,kBAAmB,CAEnB,8BAAwC,CADxC,YAEF,CAGA,yBAKE,wCACE,YACF,CAEA,mBACE,gBACF,CACF,CC3GA,qBAGE,aAAc,CADd,gBAAiB,CADjB,YAGF,CAEA,sBACE,kBAAmB,CACnB,iBACF,CAEA,qBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,mBACF,CAEA,2BACE,aAAc,CACd,cAAe,CAEf,aAAc,CADd,eAEF,CAEA,uBACE,eAAiB,CACjB,kBAAmB,CAEnB,8BAAwC,CADxC,YAEF,CAGA,yBAKE,4CACE,YACF,CAEA,qBACE,gBACF,CACF,CC5CA,iBAKE,wBAAyB,CAFzB,aAAc,CADd,gBAAiB,CAEjB,gBAAiB,CAHjB,YAKF,CAGA,wBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,mBAME,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CAJb,cAAe,CACf,eAAgB,CAKhB,UAAY,CAHZ,QAIF,CAEA,aAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,kBAEE,aAAc,CACd,cAAe,CAFf,gBAGF,CAGA,eAGE,aAAc,CADd,iBAAkB,CADlB,iBAGF,CAEA,oBAGE,aAAc,CADd,WAAY,CAEZ,kBAAmB,CAHnB,UAIF,CAEA,kBAEE,aAAc,CADd,eAEF,CAGA,OAEE,kBAAmB,CAGnB,mBAAqB,CAJrB,YAAa,CAEb,UAAY,CAGZ,oBAAqB,CAFrB,YAAa,CAGb,iBACF,CAEA,aACE,wBAAyB,CACzB,wBAAyB,CACzB,aACF,CAEA,eACE,wBAAyB,CACzB,wBAAyB,CACzB,aACF,CAEA,YAGE,aAAc,CADd,cAAe,CADf,aAGF,CAEA,aAKE,eAAgB,CAChB,WAAY,CAGZ,aAAc,CADd,cAAe,CADf,iBAAkB,CAGlB,UAAY,CATZ,iBAAkB,CAClB,YAAc,CACd,OAAQ,CACR,0BAOF,CAEA,mBACE,SACF,CAGA,iBACE,kBAAmB,CACnB,wBAAyB,CACzB,mBAAqB,CAErB,kBAAmB,CADnB,cAEF,CAEA,aAGE,eAAW,CACX,eAAgB,CAHhB,YAAa,CAEb,UAAW,CADX,iCAGF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,oBAEE,aAAc,CACd,iBAAmB,CAFnB,eAGF,CAEA,6BAGE,wBAAyB,CACzB,qBAAuB,CACvB,iBAAmB,CAHnB,cAAgB,CAIhB,2BACF,CAEA,yCAGE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,uBACE,eAAiB,CACjB,wBAAyB,CACzB,mBAAqB,CAErB,8BAAwC,CADxC,eAEF,CAEA,aAEE,wBAAyB,CADzB,UAEF,CAEA,gBACE,kBAAmB,CAKnB,+BAAgC,CADhC,aAAc,CAEd,iBAAmB,CAHnB,eAAgB,CAFhB,YAAa,CACb,eAKF,CAEA,gBAEE,+BAAgC,CADhC,YAAa,CAEb,qBACF,CAEA,gBACE,wBACF,CAEA,wBACE,UACF,CAEA,WAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,UAEE,aAAc,CADd,eAEF,CAEA,kBAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAGA,YAGE,oBAAqB,CAFrB,oBAAqB,CAGrB,gBAAkB,CAClB,eAAgB,CAEhB,oBAAsB,CALtB,qBAAwB,CAIxB,wBAEF,CAEA,kBACE,wBAAyB,CACzB,aACF,CAEA,YACE,wBAAyB,CACzB,aACF,CAEA,kBACE,wBAAyB,CACzB,aACF,CAEA,SACE,wBAAyB,CACzB,aACF,CAEA,SACE,wBAAyB,CACzB,aACF,CAEA,cACE,wBAAyB,CACzB,aACF,CAGA,cAGE,oBAAqB,CAFrB,oBAAqB,CAGrB,gBAAkB,CAClB,eAAgB,CAEhB,oBAAsB,CALtB,qBAAwB,CAIxB,wBAEF,CAEA,eACE,wBAAyB,CACzB,aACF,CAEA,iBACE,wBAAyB,CACzB,aACF,CAGA,gBACE,YAAa,CACb,SACF,CAEA,YAEE,kBAAmB,CAInB,WAAY,CACZ,qBAAuB,CACvB,cAAe,CAPf,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CAMvB,kBAAoB,CALpB,UAMF,CAEA,gBAEE,WAAY,CADZ,UAEF,CAEA,UACE,wBAAyB,CACzB,aACF,CAEA,gBACE,wBACF,CAEA,cACE,wBAAyB,CACzB,aACF,CAEA,oBACE,wBACF,CAEA,gBACE,wBAAyB,CACzB,aACF,CAEA,sBACE,wBACF,CAEA,cACE,wBAAyB,CACzB,aACF,CAEA,oBACE,wBACF,CAGA,eAGE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAEF,CAEA,cAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,UACE,iCACF,CAEA,gBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,YAKE,kBAAmB,CACnB,4BAA6B,CAJ7B,6BAA8B,CAE9B,mBAGF,CAEA,4BANE,kBAAmB,CAFnB,YAmBF,CAXA,gBAOE,eAAiB,CAFjB,wBAAyB,CACzB,qBAAuB,CAEvB,aAAc,CACd,cAAe,CANf,SAAW,CACX,kBAAoB,CAMpB,kBACF,CAEA,qCACE,wBAAyB,CACzB,oBACF,CAEA,yBAEE,kBAAmB,CADnB,UAEF,CAEA,oBAEE,WAAY,CADZ,UAEF,CAEA,iBACE,aAAc,CACd,iBACF,CAGA,eAQE,kBAAmB,CAFnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,eACE,eAAiB,CACjB,oBAAsB,CACtB,sCAAiD,CAGjD,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,UAIF,CAEA,aACE,eACF,CAEA,cAKE,+BAAgC,CAHhC,6BAA8B,CAE9B,YAEF,CAEA,+BALE,kBAAmB,CAFnB,YAeF,CARA,iBAOE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAHhB,UAAY,CACZ,QAIF,CAEA,YAGE,aAAc,CADd,cAAe,CADf,aAGF,CAEA,aAEE,kBAAmB,CAMnB,kBAAmB,CAFnB,WAAY,CACZ,qBAAuB,CAEvB,aAAc,CACd,cAAe,CATf,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CAQvB,kBAAoB,CAPpB,UAQF,CAEA,mBACE,kBAAmB,CACnB,aACF,CAEA,iBAEE,WAAY,CADZ,UAEF,CAEA,YAEE,aAAc,CADd,cAEF,CAEA,YACE,SACF,CAEA,UAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,kBACF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,kBAEE,aAAc,CACd,iBAAmB,CAFnB,eAGF,CAEA,qCAGE,wBAAyB,CACzB,qBAAuB,CACvB,iBAAmB,CAHnB,cAAgB,CAIhB,2BACF,CAEA,iDAGE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,iDAEE,oBACF,CAEA,uDAEE,wBAAyB,CACzB,aAAc,CACd,kBACF,CAEA,YACE,aAGF,CAEA,uBAJE,gBAAkB,CAClB,iBAOF,CAJA,WACE,aAGF,CAEA,gBAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAEb,SAEF,CAEA,qCAEE,QAAS,CADT,UAEF,CAEA,eAEE,aAAc,CADd,eAEF,CAEA,eAME,kBAAmB,CADnB,4BAA6B,CAJ7B,YAAa,CAEb,UAAY,CADZ,wBAAyB,CAEzB,cAGF,CAGA,KAEE,kBAAmB,CAGnB,WAAY,CACZ,qBAAuB,CAGvB,cAAe,CARf,mBAAoB,CAMpB,iBAAmB,CACnB,eAAgB,CALhB,SAAW,CACX,qBAAuB,CAOvB,oBAAqB,CADrB,kBAEF,CAEA,cAEE,kBAAmB,CADnB,UAEF,CAEA,UAEE,WAAY,CADZ,UAEF,CAEA,aACE,wBAAyB,CACzB,UACF,CAEA,kCACE,wBACF,CAEA,eACE,wBAAyB,CAEzB,wBAAyB,CADzB,aAEF,CAEA,oCACE,wBACF,CAEA,YACE,wBAAyB,CACzB,UACF,CAEA,iCACE,wBACF,CAGA,yBACE,iBACE,YACF,CAEA,wBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,aAEE,QAAS,CADT,yBAEF,CAEA,uBACE,eACF,CAEA,aACE,eACF,CAEA,UACE,yBACF,CAEA,eACE,WAAY,CACZ,cACF,CAEA,YACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CAEA,gBACE,cACF,CACF", "sources": ["index.css", "App.css", "components/PubMed/MultiSelect.css", "components/zip/ZipUploader.css", "components/zip/QueryFormModal.css", "components/zip/RaiseQueryButton.css", "components/zip/ValidationWarning.css", "components/zip/ManualEntrySection.css", "components/zip/FileListViewer.css", "components/zip/AuthorQueryModal.css", "components/zip/ArticleMetadataPanel.css", "components/zip/DocumentPreview.css", "components/zip/ZipWorkflow.css", "components/zip/FolderUploader.css", "components/zip/SmartBatchingModal.css", "components/zip/AuthorSummary.css", "components/zip/ZipQueueDashboard.css", "components/zip/FolderZipWorkflow.css", "components/zip/IndividualZipProcessor.css", "components/zip/IndividualZipUpload.css", "pages/ProcessingPage.css", "components/auth/AdminLogin.css", "components/admin/AdminDashboard.css", "components/admin/AdminNavigation.css", "components/admin/AdminUploadPage.css", "components/admin/AdminJournalsPage.css", "components/admin/UserManagement.css"], "sourcesContent": ["@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\nbody {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}\r\n\r\n/* Loading spinner animation */\r\n.loading-spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid #f3f3f3;\r\n  border-top: 2px solid #3498db;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Fade in animation */\r\n.animate-fadeIn {\r\n  animation: fadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n", "/* Custom styles for the application - Tailwind classes are handled by index.css */\n\n/* Clean Tailwind-style table */\n.table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 0;\n  margin: 1.5rem 0;\n  background-color: #ffffff;\n  border-radius: 1rem;\n  overflow: hidden;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  border: 1px solid #e5e7eb;\n}\n\n/* Clean table header */\n.table th {\n  background-color: #f9fafb;\n  color: #374151;\n  font-weight: 600;\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid #e5e7eb;\n  text-align: left;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n\n.table th:first-child {\n  border-top-left-radius: 1rem;\n}\n\n.table th:last-child {\n  border-top-right-radius: 1rem;\n}\n\n/* Clean table rows */\n.table tr {\n  transition: background-color 0.2s ease;\n}\n\n.table tbody tr:hover {\n  background-color: #f9fafb;\n}\n\n.table tbody tr:last-child td:first-child {\n  border-bottom-left-radius: 1rem;\n}\n\n.table tbody tr:last-child td:last-child {\n  border-bottom-right-radius: 1rem;\n}\n\n/* Clean table cells */\n.table td {\n  padding: 1rem;\n  border-bottom: 1px solid #f3f4f6;\n  color: #374151;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  vertical-align: top;\n}\n\n.table tbody tr:last-child td {\n  border-bottom: none;\n}\n\n/* First column styling for reference numbers */\n.table td:first-child {\n  width: 120px;\n  text-align: center;\n  background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);\n  font-weight: 600;\n  border-right: 2px solid #e9ecef;\n}\n\n/* Removed old absolute positioning for table buttons */\n\n/* Zebra stripe effect */\n.table tr{\n  background-color: #f2f2f2;\n}\n\n/* Responsive design for smaller screens */\n@media screen and (max-width: 768px) {\n  .table {\n    font-size: 14px;\n  }\n  .table th,\n  .table td {\n    padding: 8px;\n  }\n}\n/* .fixed-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.2);\n  z-index: 1;\n}\n\n.loader-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background-color: rgba(255, 255, 255, 0.8);\n} */\n\n/* .table tr[data-type=\"URL\"] {background-color:#ffff4d} */\n/* .table tr[data-type=\"NOT_FOUND\"] {background-color:#4da9ff;position: relative;} */\n/* .table tr[data-type=\"DUPLICATE\"] {background-color:#ff4d4d} */\n\n.table tr[data-type=\"NOT_FOUND\"] td:nth-child(2),\n.table tr[data-type=\"URL\"] td:nth-child(2),\n.table tr[data-type=\"CROSSREF\"] td:nth-child(2),\n.table tr[data-marktype=\"DUPLICATE\"] td:nth-child(2) {\n  position: relative;\n}\n\n/* Removed all old pseudo-element badge styles - using React components instead */\n\n\n/* Modern loader spinner */\n.loader {\n  width: 60px;\n  height: 60px;\n  border: 4px solid rgba(102, 126, 234, 0.1);\n  border-top: 4px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Reference Editor Styles */\n.reference-display {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.reference-text {\n  flex: 1;\n}\n\n.reference-editor {\n  width: 100%;\n}\n\n.reference-textarea {\n  width: 100%;\n  min-height: 60px;\n  padding: 8px;\n  border: 2px solid #007bff;\n  border-radius: 4px;\n  font-family: inherit;\n  font-size: 14px;\n  resize: vertical;\n}\n\n.editor-buttons {\n  display: flex;\n  gap: 4px;\n  margin-top: 4px;\n}\n\n.editor-help {\n  margin-top: 4px;\n  color: #666;\n}\n\n.action-button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.action-button:hover {\n  background-color: #f0f0f0;\n}\n\n.edit-button:hover {\n  background-color: #e3f2fd;\n}\n\n.save-button:hover {\n  background-color: #e8f5e8;\n}\n\n.cancel-button:hover {\n  background-color: #ffebee;\n}\n\n/* Quality Indicator Styles */\n.quality-indicator {\n  display: inline-block;\n  margin-left: 8px;\n}\n\n.quality-indicator.good span {\n  color: #28a745;\n}\n\n.quality-indicator .warning {\n  color: #ffc107;\n}\n\n.quality-indicator .error {\n  color: #dc3545;\n}\n\n/* Clean Reference Statistics Styles */\n.reference-statistics {\n  background: #ffffff;\n  padding: 1.5rem;\n  border-radius: 1rem;\n  margin: 1.5rem 0;\n  border: 1px solid #e5e7eb;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n}\n\n.reference-statistics h3 {\n  margin: 0 0 1rem 0;\n  color: #374151;\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));\n  gap: 1rem;\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 1rem;\n  background: #f9fafb;\n  border-radius: 0.5rem;\n  border: 1px solid #e5e7eb;\n  transition: all 0.2s ease;\n}\n\n.stat-item:hover {\n  background: #f3f4f6;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n}\n\n.stat-item.clickable {\n  cursor: pointer;\n  user-select: none;\n}\n\n.stat-item.clickable:hover {\n  background: #e5e7eb;\n  border-color: #d1d5db;\n}\n\n.stat-item.active {\n  background: #dbeafe;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 1px #3b82f6;\n}\n\n.stat-item.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n\n.stat-item.disabled:hover {\n  background: #f9fafb;\n  transform: none;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n}\n\n.filter-indicator {\n  font-size: 0.875rem;\n  color: #6b7280;\n  font-weight: 400;\n}\n\n.stat-label {\n  font-weight: 500;\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.stat-value {\n  font-weight: 700;\n  font-size: 1.125rem;\n}\n\n.stat-value.pubmed {\n  color: #3b82f6;\n}\n\n.stat-value.crossref {\n  color: #10b981;\n}\n\n.stat-value.not-found {\n  color: #f59e0b;\n}\n\n.stat-value.duplicates {\n  color: #ef4444;\n}\n\n.stat-value.urls {\n  color: #8b5cf6;\n}\n\n/* Modern Badge Components */\n.status-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 4px;\n  padding: 6px 10px;\n  border-radius: 0.5rem;\n  font-size: 0.75rem;\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  margin: 2px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n  transition: all 0.2s ease;\n  white-space: nowrap;\n}\n\n.status-badge:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\n}\n\n.badge-icon {\n  font-size: 0.75rem;\n  line-height: 1;\n}\n\n.badge-text {\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n/* Clean Status Badge Variants */\n.badge-pubmed {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.badge-crossref {\n  background-color: #10b981;\n  color: white;\n}\n\n.badge-not-found {\n  background-color: #f59e0b;\n  color: white;\n}\n\n.badge-url {\n  background-color: #8b5cf6;\n  color: white;\n}\n\n.badge-duplicate {\n  background-color: #ef4444;\n  color: white;\n  animation: pulse 2s infinite;\n  border: 2px solid #dc2626;\n  font-weight: 800;\n  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);\n}\n\n.badge-multiple {\n  background-color: #f97316;\n  color: white;\n}\n\n/* Quality Badge Variants */\n.quality-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 4px;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: 600;\n  margin: 2px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  cursor: help;\n}\n\n.badge-excellent {\n  background: linear-gradient(135deg, #28a745, #20c997);\n  color: white;\n}\n\n.badge-good {\n  background: linear-gradient(135deg, #17a2b8, #138496);\n  color: white;\n}\n\n.badge-warning {\n  background: linear-gradient(135deg, #ffc107, #e0a800);\n  color: #212529;\n}\n\n.badge-poor {\n  background: linear-gradient(135deg, #dc3545, #c82333);\n  color: white;\n}\n\n/* Action Badge Variants */\n.action-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 3px;\n  padding: 4px 8px;\n  border: none;\n  border-radius: 0.375rem;\n  font-size: 0.75rem;\n  font-weight: 600;\n  cursor: pointer;\n  margin: 2px;\n  transition: all 0.2s ease;\n  background: #e5e7eb;\n  color: #374151;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n  white-space: nowrap;\n}\n\n.action-badge:hover:not(.disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\n}\n\n.action-badge.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.badge-edit:hover {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.badge-copy:hover {\n  background-color: #6b7280;\n  color: white;\n}\n\n.badge-search:hover {\n  background-color: #06b6d4;\n  color: white;\n}\n\n.badge-delete:hover {\n  background-color: #ef4444;\n  color: white;\n}\n\n.badge-save:hover {\n  background-color: #10b981;\n  color: white;\n}\n\n.badge-cancel:hover {\n  background-color: #6b7280;\n  color: white;\n}\n\n/* Progress Badge */\n.progress-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 8px;\n  border-radius: 12px;\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n}\n\n.progress-bar-container {\n  width: 60px;\n  height: 8px;\n  background: #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.progress-bar-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #007bff, #28a745);\n  transition: width 0.3s ease;\n  border-radius: 4px;\n}\n\n.progress-text {\n  font-size: 10px;\n  font-weight: 600;\n  color: #495057;\n}\n\n/* Pulse animation for duplicates */\n@keyframes pulse {\n  0% { opacity: 1; }\n  50% { opacity: 0.7; }\n  100% { opacity: 1; }\n}\n\n/* Badge container for multiple badges */\n.badge-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  align-items: center;\n  margin-top: 0.5rem;\n}\n\n/* Reference content layout */\n.reference-content {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.reference-text {\n  line-height: 1.5;\n  color: #374151;\n}\n\n.reference-main {\n  flex: 1;\n}\n\n.reference-number {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 1.5rem;\n  height: 1.5rem;\n  background-color: #3b82f6;\n  color: white;\n  border-radius: 0.375rem;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.5rem;\n  transition: all 0.2s ease;\n}\n\n/* Dynamic reference number colors based on type */\n.reference-number.pubmed {\n  background-color: #3b82f6; /* Blue for PubMed */\n}\n\n.reference-number.crossref {\n  background-color: #10b981; /* Green for CrossRef */\n}\n\n.reference-number.default {\n  background-color: #6b7280; /* Gray for others */\n}\n\n/* Old upload drawer styles removed - using new Tailwind-style drawer */\n\n\n\n.results-section {\n  animation: fadeIn 0.5s ease;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Quick Upload Toggle */\n.quick-upload-toggle {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 20px;\n}\n\n.quick-toggle-button {\n  background: linear-gradient(135deg, #28a745, #20c997);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 600;\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\n  transition: all 0.3s ease;\n}\n\n.quick-toggle-button:hover {\n  background: linear-gradient(135deg, #20c997, #17a2b8);\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);\n}\n\n/* Reference content layout */\n.reference-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n/* Reference Editor Improvements */\n.reference-display {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  width: 100%;\n}\n\n.reference-actions {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n}\n\n.edit-button {\n  background: #007bff;\n  color: white;\n  border: none;\n  padding: 4px 8px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 11px;\n  transition: background 0.2s ease;\n}\n\n.edit-button:hover {\n  background: #0056b3;\n}\n\n.reference-main {\n  flex: 1;\n  min-width: 0; /* Allow text to wrap */\n}\n\n/* Enhanced table layout with optimized column widths */\n.table {\n  /* table-layout: fixed; */\n  width: 100%;\n}\n\n.table td {\n  vertical-align: top;\n  padding: 1rem;\n}\n\n/* .table th:nth-child(1),\n.table td:nth-child(1) {\n  width: 20%; \n}\n\n.table th:nth-child(2),\n.table td:nth-child(2) {\n  width: 40%; \n}\n\n.table th:nth-child(3),\n.table td:nth-child(3) {\n  width: 40%; \n} */\n\n/* Test mode: 4 columns (10% 40% 40% 10%) */\n.table-test th:nth-child(1),\n.table-test td:nth-child(1) {\n  width: 10%;\n}\n.table-test th:nth-child(2),\n.table-test td:nth-child(2) {\n  width: 40%;\n}\n.table-test th:nth-child(3),\n.table-test td:nth-child(3) {\n  width: 40%;\n}\n.table-test th:nth-child(4),\n.table-test td:nth-child(4) {\n  width: 10%;\n}\n\n/* Responsive table adjustments */\n@media screen and (max-width: 768px) {\n  .table {\n    font-size: 12px;\n  }\n\n  .table td {\n    padding: 12px 8px;\n  }\n\n  .table td:first-child {\n    width: 80px;\n  }\n\n  .toggle-button {\n    padding: 6px 12px;\n    font-size: 12px;\n  }\n\n  .download-wrapper {\n    gap: 4px;\n  }\n}\n\n/* Enhanced loading container */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n  padding: 40px 20px;\n  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);\n  border-radius: 16px;\n  margin: 20px 0;\n  border: 1px solid #e9ecef;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.loading-container p {\n  margin: 0;\n  color: #667eea;\n  font-size: 16px;\n  font-weight: 500;\n  text-align: center;\n}\n\n/* Enhanced progress display */\n.progress-details {\n  margin-top: 1.5rem;\n  width: 100%;\n  max-width: 600px;\n}\n\n.progress-main {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 1.5rem;\n}\n\n.progress-steps {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  margin-top: 1rem;\n}\n\n.progress-step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  flex: 1;\n  opacity: 0.4;\n  transition: all 0.3s ease;\n}\n\n.progress-step.active {\n  opacity: 1;\n  transform: scale(1.1);\n}\n\n.progress-step.completed {\n  opacity: 0.8;\n}\n\n.step-icon {\n  font-size: 1.5rem;\n  margin-bottom: 0.5rem;\n  padding: 0.5rem;\n  background: #f3f4f6;\n  border-radius: 50%;\n  width: 3rem;\n  height: 3rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.progress-step.active .step-icon {\n  background: #3b82f6;\n  color: white;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\n}\n\n.progress-step.completed .step-icon {\n  background: #10b981;\n  color: white;\n}\n\n.step-text {\n  font-size: 0.75rem;\n  font-weight: 500;\n  color: #6b7280;\n  text-align: center;\n  line-height: 1.2;\n}\n\n.progress-step.active .step-text {\n  color: #3b82f6;\n  font-weight: 600;\n}\n\n.progress-step.completed .step-text {\n  color: #10b981;\n}\n\n.reference-text {\n  flex: 1;\n  line-height: 1.5;\n  color: #2c3e50;\n}\n\n/* Removed old reference-number style - using new clean style */\n\n/* Old pseudo-element styles removed completely */\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .status-badge,\n  .quality-badge,\n  .action-badge {\n    font-size: 9px;\n    padding: 3px 6px;\n  }\n\n  .badge-icon {\n    font-size: 10px;\n  }\n\n  .reference-content {\n    gap: 4px;\n  }\n\n  .badge-container {\n    gap: 2px;\n  }\n\n  /* Adjust column widths for mobile */\n  .table th:nth-child(1),\n  .table td:nth-child(1) {\n    width: 15%; /* Smaller reference column on mobile */\n  }\n\n  .table th:nth-child(2),\n  .table td:nth-child(2) {\n    width: 42.5%; /* Slightly larger content columns */\n  }\n\n  .table th:nth-child(3),\n  .table td:nth-child(3) {\n    width: 42.5%;\n  }\n\n  .table td {\n    padding: 0.75rem; /* Smaller padding on mobile */\n  }\n}\n\n.css-17vezug-marker{\n  display: none !important;\n}\n\n\n/* td:first-child, th:first-child {\n  display: none;\n} */\n.not-found-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: calc(100vh - 120px);\n}\n\n.not-found-card {\n  background-color: #eee;\n  padding: 20px;\n  border-radius: 10px;\n  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);\n  text-align: center;\n  max-width: 400px;\n}\n\n.not-found-title {\n  color: #ff4d4d;\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.not-found-text {\n  font-size: 16px;\n  color: #555;\n}\n\n.upload{\n  display: flex;\n  justify-content: space-evenly;\n  align-items: flex-start;\n  gap: 20px;\n  padding: 0;\n  margin: 0;\n}\n\n/* Responsive upload layout */\n@media (max-width: 768px) {\n  .upload {\n    flex-direction: column;\n    gap: 20px;\n  }\n}\n\n\n/* Legacy upload styles - updated for modern design */\n.upload {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n@media (min-width: 768px) {\n  .upload {\n    grid-template-columns: 1fr 1fr;\n  }\n}\n\n.upload-container {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.upload-title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n}\n\n.upload-input-container {\n  margin: 0;\n}\n\n.upload-input {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.75rem;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n}\n\n.upload-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.upload-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  color: white;\n  border: none;\n  border-radius: 0.75rem;\n  font-weight: 500;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.upload-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);\n}\n\n.upload-description {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.upload-area {\n  width: 100%;\n  min-height: 8rem;\n  padding: 0.75rem 1rem;\n  border: 2px dashed #d1d5db;\n  border-radius: 0.75rem;\n  font-size: 0.875rem;\n  resize: vertical;\n  transition: all 0.2s ease;\n  font-family: inherit;\n}\n\n.upload-area:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.upload-area::placeholder {\n  color: #9ca3af;\n}\n\n/* Clean Tailwind-style buttons */\n.toggle-button {\n  background-color: #3b82f6;\n  color: #ffffff;\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 1rem;\n  cursor: pointer;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n}\n\n.toggle-button:hover {\n  background-color: #1d4ed8;\n  transform: translateY(-1px);\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n}\n\n.toggle-button:active {\n  transform: translateY(0);\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n}\n\n/* Clean download wrapper */\n.download-wrapper {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n/* Clean results header wrapper */\n.found-citation-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  background-color: #f9fafb;\n  border-radius: 1rem;\n  border: 1px solid #e5e7eb;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n}\n\n.found-citation-wrapper.loading-hidden {\n  display: none;\n}\n\n/* Results header */\n.results-header h3 {\n  margin: 0;\n  color: #374151;\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n/* Action icons container */\n.action-icons {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n\n/* Icon buttons */\n.icon-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2.5rem;\n  height: 2.5rem;\n  background-color: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.5rem;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n}\n\n.icon-button:hover {\n  background-color: #f3f4f6;\n  border-color: #d1d5db;\n  color: #374151;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n}\n\n.icon-button:active {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n}\n\n.icon-button.active{\n  background-color: #3b82f6;\n  color: #ffffff;\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n/* Smooth appearance animation for results */\n.table {\n  animation: fadeInUp 0.5s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Container for the entire results section */\n.results-container {\n  animation: fadeInUp 0.6s ease-out;\n}\n\n/* TE Reference Dashboard Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-fadeInUp {\n  animation: fadeInUp 0.5s ease-out;\n}\n\n/* Line clamp utility for text truncation */\n.line-clamp-3 {\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* Custom scrollbar for better UX */\n.overflow-x-auto::-webkit-scrollbar {\n  height: 6px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-track {\n  background: #f1f5f9;\n  border-radius: 3px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Enhanced hover effects for table rows */\n.hover\\:bg-slate-50:hover {\n  background-color: #f8fafc;\n}\n\n/* Smooth transitions for all interactive elements */\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n\n.duration-200 {\n  transition-duration: 200ms;\n}\n\n.duration-300 {\n  transition-duration: 300ms;\n}\n\n.duration-500 {\n  transition-duration: 500ms;\n}\n\n/* TE Reference Dashboard Styles */\n.dashboard-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n.dashboard-header {\n  background: #ffffff;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n.header-flex {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 1rem 0;\n}\n\n.dashboard-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.back-button {\n  padding: 0.5rem 1rem;\n  background: #f1f5f9;\n  color: #475569;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.back-button:hover {\n  background: #e2e8f0;\n  transform: translateX(-2px);\n}\n\n.header-badge {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.header-badge .icon {\n  width: 1.25rem;\n  height: 1.25rem;\n  color: #3b82f6;\n}\n\n/* Removed conflicting badge-text style */\n\n.main-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n}\n\n.upload-section {\n  background: #ffffff;\n  border-radius: 1rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  transition: all 0.3s ease;\n}\n\n.upload-section:hover {\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.section-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.section-title .icon {\n  width: 1.25rem;\n  height: 1.25rem;\n  color: #3b82f6;\n}\n\n.upload-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n@media (min-width: 768px) {\n  .upload-grid {\n    grid-template-columns: 1fr 1fr;\n  }\n}\n\n.upload-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.upload-label {\n  display: block;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #374151;\n}\n\n.upload-dropzone {\n  position: relative;\n  border: 2px dashed #d1d5db;\n  border-radius: 0.75rem;\n  padding: 2rem;\n  text-align: center;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.dropzone-default:hover {\n  border-color: #9ca3af;\n}\n\n.dropzone-active {\n  border-color: #3b82f6;\n  background-color: #eff6ff;\n}\n\n.dropzone-success {\n  border-color: #10b981;\n  background-color: #f0fdf4;\n}\n\n.file-input {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  cursor: pointer;\n}\n\n.upload-success {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.upload-success .icon {\n  width: 3rem;\n  height: 3rem;\n  color: #10b981;\n}\n\n.success-filename {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #047857;\n  margin: 0;\n}\n\n.success-message {\n  font-size: 0.75rem;\n  color: #059669;\n  margin: 0;\n}\n\n.upload-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.upload-placeholder .icon {\n  width: 3rem;\n  height: 3rem;\n  color: #9ca3af;\n}\n\n.placeholder-title {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #374151;\n  margin: 0;\n}\n\n.placeholder-subtitle {\n  font-size: 0.75rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n.text-input {\n  width: 100%;\n  height: 10rem;\n  padding: 0.75rem 1rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.75rem;\n  font-size: 0.875rem;\n  resize: none;\n  transition: all 0.2s ease;\n}\n\n.text-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* Submit Section */\n.submit-section {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 1rem;\n  margin-top: 2rem;\n  flex-wrap: wrap;\n}\n\n.submit-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 2rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  color: #ffffff;\n  border: none;\n  border-radius: 0.75rem;\n  font-weight: 500;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.submit-button:hover:not(:disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);\n}\n\n.submit-button:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.submit-button .icon {\n  width: 1rem;\n  height: 1rem;\n}\n\n.clear-button {\n  padding: 0.75rem 1.5rem;\n  background: #f1f5f9;\n  color: #475569;\n  border: none;\n  border-radius: 0.75rem;\n  font-weight: 500;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.clear-button:hover {\n  background: #e2e8f0;\n}\n\n.loading-spinner {\n  width: 1rem;\n  height: 1rem;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid #ffffff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n/* Results Section */\n.results-section {\n  background: #ffffff;\n  border-radius: 1rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  padding: 1.5rem;\n  transition: all 0.5s ease;\n}\n\n.results-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.results-title .icon {\n  width: 1.25rem;\n  height: 1.25rem;\n  color: #10b981;\n}\n\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 0;\n  gap: 1rem;\n}\n\n.large-spinner {\n  width: 3rem;\n  height: 3rem;\n  border: 4px solid #e5e7eb;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n.table-container {\n  overflow-x: auto;\n  border-radius: 0.5rem;\n  border: 1px solid #e5e7eb;\n}\n\n.results-table {\n  width: 100%;\n  border-collapse: collapse;\n  background: #ffffff;\n}\n\n.table-header {\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.table-th {\n  text-align: left;\n  padding: 0.75rem 1rem;\n  font-weight: 500;\n  color: #374151;\n  background: #f9fafb;\n  font-size: 0.875rem;\n}\n\n.index-column {\n  width: 4rem;\n}\n\n.table-row {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s ease;\n}\n\n.table-row:hover {\n  background-color: #f8fafc;\n}\n\n.table-td {\n  padding: 1rem;\n  font-size: 0.875rem;\n  color: #374151;\n  vertical-align: top;\n}\n\n.index-cell {\n  font-weight: 600;\n  color: #1e293b;\n  text-align: center;\n}\n\n.search-term-cell {\n  max-width: 20rem;\n}\n\n.citation-cell {\n  line-height: 1.5;\n}\n\n.truncate-text {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n/* Empty States */\n.empty-results {\n  text-align: center;\n  padding: 4rem 0;\n}\n\n.empty-results .icon {\n  width: 4rem;\n  height: 4rem;\n  color: #9ca3af;\n  margin: 0 auto 1rem;\n}\n\n.empty-title {\n  font-size: 1.125rem;\n  font-weight: 500;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n\n.empty-subtitle {\n  color: #64748b;\n  margin: 0;\n}\n\n.empty-state {\n  background: #ffffff;\n  border-radius: 1rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  padding: 3rem;\n  text-align: center;\n}\n\n.empty-state .icon {\n  width: 4rem;\n  height: 4rem;\n  color: #9ca3af;\n  margin: 0 auto 1rem;\n}\n\n.empty-state-title {\n  font-size: 1.125rem;\n  font-weight: 500;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n\n.empty-state-subtitle {\n  color: #64748b;\n  max-width: 28rem;\n  margin: 0 auto;\n  line-height: 1.5;\n}\n\n/* Custom animations for Tailwind */\n.animate-fadeIn {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Custom scrollbar for drawer */\n.overflow-y-auto::-webkit-scrollbar {\n  width: 6px;\n}\n\n.overflow-y-auto::-webkit-scrollbar-track {\n  background: #f1f5f9;\n  border-radius: 3px;\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Modern App.js Styles */\n.demo-button {\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n\n.demo-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n}\n\n.upload-section {\n  background: #ffffff;\n  border-radius: 1rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  margin-bottom: 2rem;\n  transition: all 0.3s ease;\n  overflow: hidden;\n}\n\n.upload-section:hover {\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.upload-section.collapsed {\n  margin-bottom: 1rem;\n}\n\n.upload-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 1.5rem;\n  background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);\n  border-bottom: 1px solid #e9ecef;\n}\n\n.section-toggle-button {\n  padding: 0.5rem 1rem;\n  background: #f1f5f9;\n  color: #475569;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.section-toggle-button:hover {\n  background: #e2e8f0;\n}\n\n.upload-content {\n  padding: 1.5rem;\n}\n\n.quick-toggle-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  border: none;\n  border-radius: 0.75rem;\n  font-weight: 500;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n  margin-bottom: 1.5rem;\n}\n\n.quick-toggle-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);\n}\n\n.quick-toggle-button .icon {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Modern Upload Components */\n.file-upload-area {\n  position: relative;\n  border: 2px dashed #d1d5db;\n  border-radius: 0.75rem;\n  padding: 2rem;\n  text-align: center;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  background: #fafafa;\n}\n\n.file-upload-area:hover {\n  border-color: #9ca3af;\n  background: #f5f5f5;\n}\n\n.file-upload-area .file-input {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  cursor: pointer;\n}\n\n/* Responsive adjustments for main app */\n@media screen and (max-width: 768px) {\n  .header-flex {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: flex-start;\n  }\n\n  .header-actions {\n    flex-direction: column;\n    gap: 0.5rem;\n    align-items: flex-start;\n  }\n\n  .upload-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: flex-start;\n  }\n\n  .demo-button {\n    font-size: 0.75rem;\n    padding: 0.4rem 0.8rem;\n  }\n}\n\n.progress-container {\n  width: 100%;\n  background-color: #e5e7eb; /* Tailwind's bg-gray-200 */\n  border-radius: 9999px;     /* Fully rounded */\n  height: 20px;              /* Equivalent to h-2.5 (~30px) */\n  margin-top: 1rem;          /* Equivalent to mt-4 */\n  overflow: hidden;\n}\n\n.progress-bar {\n  background-color: #43eb25; /* Tailwind's bg-blue-600 */\n  height: 20px;\n  border-radius: 9999px;\n  transition: width 0.3s ease-in-out;\n  text-align: center;\n  font-weight: bolder;\n  color: #f1f1f1;\n}\n\n/* Additional loading spinner for authentication */\n.loading-spinner {\n  width: 2rem;\n  height: 2rem;\n  border: 3px solid #e5e7eb;\n  border-top: 3px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}", ".multiselect-checkbox-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n  padding: 0.5rem;\r\n  max-width: 9xl;\r\n}\r\n\r\n.multiselect-checkbox-header {\r\n  font-weight: 600;\r\n  font-size: 0.875rem;\r\n  color: #4b5563;\r\n}\r\n\r\n.multiselect-checkbox-options {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 0.5rem;\r\n}\r\n\r\n.multiselect-checkbox-option {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.multiselect-checkbox-option input[type=\"checkbox\"] {\r\n  width: 1rem;\r\n  height: 1rem;\r\n  margin: 0;\r\n  cursor: pointer;\r\n  opacity: 1 !important;\r\n  position: static !important;\r\n  pointer-events: auto !important;\r\n  visibility: visible !important;\r\n}\r\n\r\n/* Column width adjustments */\r\n.column-layout-4 {\r\n  display: grid;\r\n  grid-template-columns: 10% 40% 40% 10%;\r\n}\r\n\r\n.column-layout-3 {\r\n  display: grid;\r\n  grid-template-columns: 20% 40% 40%;\r\n}", "/* ZIP Uploader Styles */\n.zip-uploader-container {\n  max-width: 600px;\n  margin: 0 auto;\n  padding: 2rem;\n}\n\n.zip-uploader-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.zip-uploader-title {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n\n.zip-icon {\n  font-size: 1.75rem;\n}\n\n.zip-uploader-description {\n  color: #6b7280;\n  font-size: 1rem;\n  margin: 0;\n}\n\n/* Drop Zone */\n.zip-drop-zone {\n  border: 2px dashed #d1d5db;\n  border-radius: 1rem;\n  padding: 3rem 2rem;\n  text-align: center;\n  background: #fafafa;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  margin-bottom: 1.5rem;\n}\n\n.zip-drop-zone:hover {\n  border-color: #3b82f6;\n  background: #f8faff;\n}\n\n.zip-drop-zone.drag-over {\n  border-color: #3b82f6;\n  background: #eff6ff;\n  transform: scale(1.02);\n}\n\n.zip-drop-zone.processing {\n  border-color: #f59e0b;\n  background: #fffbeb;\n  cursor: not-allowed;\n}\n\n/* Upload Icon */\n.zip-upload-icon {\n  width: 4rem;\n  height: 4rem;\n  margin: 0 auto 1.5rem;\n  background: #e5e7eb;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n  font-size: 1.5rem;\n}\n\n.zip-drop-zone:hover .zip-upload-icon {\n  background: #dbeafe;\n  color: #3b82f6;\n}\n\n/* Upload Text */\n.zip-upload-text {\n  margin-bottom: 1rem;\n}\n\n.zip-main-text {\n  font-size: 1.125rem;\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n\n.zip-browse-link {\n  color: #3b82f6;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: underline;\n  position: relative;\n}\n\n.zip-browse-link:hover {\n  color: #1d4ed8;\n}\n\n.zip-file-input {\n  position: absolute;\n  opacity: 0;\n  width: 100%;\n  height: 100%;\n  cursor: pointer;\n}\n\n.zip-sub-text {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n/* Processing State */\n.zip-processing {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n}\n\n.zip-spinner {\n  width: 2rem;\n  height: 2rem;\n  border: 3px solid #f3f4f6;\n  border-top: 3px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.zip-processing p {\n  font-size: 1rem;\n  color: #374151;\n  margin: 0;\n}\n\n/* Error Message */\n.zip-error {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 0.5rem;\n  color: #dc2626;\n  font-size: 0.875rem;\n  margin-bottom: 1.5rem;\n}\n\n/* Info Section */\n.zip-info {\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: .5rem;\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.zip-info-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  font-size: 0.875rem;\n  color: #475569;\n}\n\n.zip-info-item:last-child {\n  margin-bottom: 0;\n}\n\n.zip-info-icon {\n  font-size: 1rem;\n  flex-shrink: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .zip-uploader-container {\n    padding: 1rem;\n  }\n  \n  .zip-drop-zone {\n    padding: 2rem 1rem;\n  }\n  \n  .zip-uploader-title {\n    font-size: 1.25rem;\n  }\n  \n  .zip-upload-icon {\n    width: 3rem;\n    height: 3rem;\n    font-size: 1.25rem;\n  }\n  \n  .zip-main-text {\n    font-size: 1rem;\n  }\n}\n", "/* Query Form Modal Styles */\n.query-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.query-form {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  max-width: 600px;\n  width: 90%;\n  max-height: 80vh;\n  overflow-y: auto;\n  position: relative;\n}\n\n.query-form-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24px 24px 0 24px;\n  border-bottom: 1px solid #e5e7eb;\n  margin-bottom: 24px;\n}\n\n.query-form-header h3 {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #111827;\n}\n\n.close-button {\n  background: none;\n  border: none;\n  padding: 8px;\n  border-radius: 6px;\n  cursor: pointer;\n  color: #6b7280;\n  transition: all 0.2s;\n}\n\n.close-button:hover {\n  background-color: #f3f4f6;\n  color: #374151;\n}\n\n.close-button svg {\n  width: 20px;\n  height: 20px;\n}\n\n.query-form form {\n  padding: 0 24px 24px 24px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  font-weight: 500;\n  color: #374151;\n  font-size: 0.875rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 8px 12px;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  font-size: 0.875rem;\n  background-color: white;\n  color: #374151;\n  transition: border-color 0.2s;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.input-readonly {\n  background-color: #f3f4f6 !important;\n  color: #6b7280 !important;\n  cursor: not-allowed;\n  border-color: #e5e7eb !important;\n}\n\n.form-group textarea {\n  resize: vertical;\n  min-height: 120px;\n  font-family: inherit;\n  line-height: 1.5;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding-top: 16px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.form-actions button {\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  border: 1px solid;\n}\n\n.form-actions button[type=\"button\"] {\n  background-color: white;\n  color: #374151;\n  border-color: #d1d5db;\n}\n\n.form-actions button[type=\"button\"]:hover:not(:disabled) {\n  background-color: #f9fafb;\n  border-color: #9ca3af;\n}\n\n.form-actions button[type=\"submit\"] {\n  background-color: #3b82f6;\n  color: white;\n  border-color: #3b82f6;\n}\n\n.form-actions button[type=\"submit\"]:hover:not(:disabled) {\n  background-color: #2563eb;\n  border-color: #2563eb;\n}\n\n.form-actions button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.validation-info {\n  background-color: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 20px;\n}\n\n.validation-info h4 {\n  margin: 0 0 8px 0;\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.validation-info p {\n  margin: 0;\n  font-size: 0.75rem;\n  color: #6b7280;\n  line-height: 1.4;\n}\n\n.file-attachment-info {\n  background-color: #fef3c7;\n  border: 1px solid #f59e0b;\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.file-attachment-info .icon {\n  color: #f59e0b;\n  font-size: 1rem;\n}\n\n.file-attachment-info .text {\n  font-size: 0.75rem;\n  color: #92400e;\n  line-height: 1.4;\n}\n\n/* Responsive design */\n@media (max-width: 640px) {\n  .query-form {\n    width: 95%;\n    margin: 20px auto;\n  }\n\n  .query-form-header,\n  .query-form form {\n    padding-left: 16px;\n    padding-right: 16px;\n  }\n\n  .form-actions {\n    flex-direction: column;\n  }\n\n  .form-actions button {\n    width: 100%;\n  }\n}", "/* Base Button Styles */\n.raise-query-button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  font-family: inherit;\n}\n\n.raise-query-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* Button Icon */\n.button-icon {\n  font-size: 1.1em;\n  line-height: 1;\n}\n\n.button-text {\n  line-height: 1;\n}\n\n/* Size Variants */\n.raise-query-button--small {\n  padding: 0.4rem 0.8rem;\n  font-size: 0.85rem;\n}\n\n.raise-query-button--medium {\n  padding: 0.5rem 1rem;\n  font-size: 0.95rem;\n}\n\n.raise-query-button--large {\n  padding: 0.85rem 2rem;\n  font-size: 1rem;\n}\n\n/* Color Variants */\n\n/* Primary (Red/Pink - for validation/urgent queries) */\n.raise-query-button--primary {\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a6f 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);\n}\n\n.raise-query-button--primary::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  transition: left 0.5s ease;\n}\n\n.raise-query-button--primary:hover::before {\n  left: 100%;\n}\n\n.raise-query-button--primary:hover {\n  background: linear-gradient(135deg, #ee5a6f 0%, #fa5252 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);\n}\n\n.raise-query-button--primary:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);\n}\n\n/* Secondary (Blue - for general queries) */\n.raise-query-button--secondary {\n  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.raise-query-button--secondary:hover {\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);\n}\n\n.raise-query-button--secondary:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n}\n\n/* Warning (Yellow/Orange - for author queries) */\n.raise-query-button--warning {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);\n}\n\n.raise-query-button--warning:hover {\n  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);\n}\n\n.raise-query-button--warning:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);\n}\n\n/* Danger (Dark Red - for critical queries) */\n.raise-query-button--danger {\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);\n}\n\n.raise-query-button--danger:hover {\n  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);\n}\n\n.raise-query-button--danger:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .raise-query-button--large {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.95rem;\n  }\n  \n  .raise-query-button--medium {\n    padding: 0.5rem 1rem;\n    font-size: 0.9rem;\n  }\n  \n  .raise-query-button--small {\n    padding: 0.4rem 0.75rem;\n    font-size: 0.8rem;\n  }\n}\n\n", "/* Validation Warning Styles - Modern Redesign */\r\n.validation-warning-section {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.validation-warning-card {\r\n  position: relative;\r\n  background: linear-gradient(135deg, #fff5f5 0%, #ffe5e5 100%);\r\n  border: 2px solid #ff6b6b;\r\n  border-radius: 12px;\r\n  padding: 0;\r\n  box-shadow: 0 8px 16px rgba(255, 107, 107, 0.15);\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.validation-warning-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 6px;\r\n  height: 100%;\r\n  background: linear-gradient(180deg, #ff6b6b 0%, #ee5a6f 100%);\r\n}\r\n\r\n.validation-warning-card:hover {\r\n  box-shadow: 0 12px 24px rgba(255, 107, 107, 0.2);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.validation-warning-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0;\r\n}\r\n\r\n/* Header Section */\r\n.validation-warning-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1.25rem 1.5rem;\r\n  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 111, 0.05) 100%);\r\n  border-bottom: 1px solid rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n.validation-warning-icon {\r\n  font-size: 2rem;\r\n  flex-shrink: 0;\r\n  animation: shake 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes shake {\r\n  0%, 100% { transform: rotate(0deg); }\r\n  10%, 30%, 50%, 70%, 90% { transform: rotate(-5deg); }\r\n  20%, 40%, 60%, 80% { transform: rotate(5deg); }\r\n}\r\n\r\n.validation-warning-title {\r\n  flex: 1;\r\n}\r\n\r\n.validation-warning-title h4 {\r\n  color: #c92a2a;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.25rem 0;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.validation-warning-title p {\r\n  color: #e03131;\r\n  margin: 0;\r\n  font-size: 0.95rem;\r\n  line-height: 1.5;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Body Section */\r\n.validation-warning-body {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.validation-details {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  padding: 1rem 1.25rem;\r\n  margin: 0.75rem 0;\r\n  border: 1px solid rgba(255, 107, 107, 0.2);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.validation-details p {\r\n  margin: 0.5rem 0;\r\n  font-size: 0.9rem;\r\n  color: #495057;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.validation-details p:first-child {\r\n  margin-top: 0;\r\n}\r\n\r\n.validation-details p:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.validation-details strong {\r\n  color: #c92a2a;\r\n  font-weight: 600;\r\n  min-width: 140px;\r\n  display: inline-block;\r\n}\r\n\r\n.file-list {\r\n  margin: 0.75rem 0 0 0;\r\n  padding: 0;\r\n  list-style: none;\r\n}\r\n\r\n.file-list li {\r\n  color: #495057;\r\n  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Courier New', monospace;\r\n  font-size: 0.85rem;\r\n  margin: 0.5rem 0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n  padding: 0.6rem 0.9rem;\r\n  border-radius: 6px;\r\n  border: 1px solid #dee2e6;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.file-list li:hover {\r\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\r\n  border-color: #adb5bd;\r\n  transform: translateX(4px);\r\n}\r\n\r\n.file-list li::before {\r\n  content: '📄';\r\n  font-size: 1rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Actions Section */\r\n.validation-actions {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  gap: 1rem;\r\n}\r\n\r\n/* Button styles moved to RaiseQueryButton.css */\r\n\r\n/* Status Badges */\r\n.status-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 0.35rem;\r\n  padding: 0.35rem 0.75rem;\r\n  border-radius: 6px;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  margin-left: 0.5rem;\r\n}\r\n\r\n.status-badge.success {\r\n  background: linear-gradient(135deg, #d3f9d8 0%, #b2f2bb 100%);\r\n  color: #2b8a3e;\r\n  border: 1px solid #8ce99a;\r\n}\r\n\r\n.status-badge.error {\r\n  background: linear-gradient(135deg, #ffe3e3 0%, #ffc9c9 100%);\r\n  color: #c92a2a;\r\n  border: 1px solid #ffa8a8;\r\n}\r\n\r\n.status-badge.warning {\r\n  background: linear-gradient(135deg, #fff3bf 0%, #ffec99 100%);\r\n  color: #e67700;\r\n  border: 1px solid #ffd43b;\r\n}\r\n", "/* Manual Entry Section Styles */\n.manual-entry-section {\n  margin-bottom: 1.5rem;\n}\n\n.manual-entry-card {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n  border: 1px solid #f59e0b;\n  border-radius: 0.75rem;\n  padding: 1.25rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.manual-entry-content {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.manual-entry-icon {\n  color: #d97706;\n  flex-shrink: 0;\n}\n\n.manual-entry-icon svg {\n  width: 1.5rem;\n  height: 1.5rem;\n}\n\n.manual-entry-text {\n  flex: 1;\n}\n\n.manual-entry-text h4 {\n  color: #92400e;\n  font-size: 1rem;\n  font-weight: 600;\n  margin: 0 0 0.25rem 0;\n}\n\n.manual-entry-text p {\n  color: #a16207;\n  margin: 0;\n  font-size: 0.875rem;\n  line-height: 1.4;\n}\n\n.manual-entry-button {\n  background: #f59e0b;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  flex-shrink: 0;\n}\n\n.manual-entry-button:hover {\n  background: #d97706;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);\n}\n", "/* File List Viewer Styles */\n.file-list-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 1.5rem;\n}\n\n/* Header */\n.file-list-header {\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.file-list-title-section {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.back-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2.5rem;\n  height: 2.5rem;\n  background: #f3f4f6;\n  border: 1px solid #d1d5db;\n  border-radius: 0.5rem;\n  color: #374151;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.back-button:hover {\n  background: #e5e7eb;\n  color: #111827;\n}\n\n.file-list-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #374151;\n  margin: 0 0 0.25rem 0;\n}\n\n.article-icon {\n  font-size: 1.25rem;\n}\n\n.file-list-subtitle {\n  color: #6b7280;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n/* File Categories */\n.file-categories {\n  margin-bottom: 2rem;\n}\n\n.file-category {\n  margin-bottom: 2rem;\n}\n\n.category-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 1rem;\n  transition: all 0.2s ease;\n}\n\n.category-title.clickable {\n  cursor: pointer;\n  user-select: none;\n  padding: 0.5rem 0.75rem;\n  margin-left: -0.75rem;\n  margin-right: -0.75rem;\n  border-radius: 8px;\n}\n\n.category-title.clickable:hover {\n  background: rgba(0, 0, 0, 0.03);\n  color: #1f2937;\n}\n\n.category-title.clickable:active {\n  background: rgba(0, 0, 0, 0.05);\n}\n\n.category-icon {\n  font-size: 1rem;\n  transition: transform 0.2s ease;\n}\n\n.toggle-icon {\n  margin-left: auto;\n  font-size: 0.9rem;\n  color: #6b7280;\n  transition: transform 0.2s ease;\n}\n\n/* File Grid */\n.file-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 1rem;\n  animation: slideDown 0.3s ease-out;\n  transform-origin: top;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.file-item {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.file-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n  transform: translateY(-1px);\n}\n\n.file-item.selected {\n  border-color: #3b82f6;\n  background: #eff6ff;\n  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);\n}\n\n.file-item.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.file-item.disabled:hover {\n  border-color: #e5e7eb;\n  box-shadow: none;\n  transform: none;\n}\n\n/* File Icon */\n.file-icon-container {\n  flex-shrink: 0;\n  width: 3rem;\n  height: 3rem;\n  background: #f3f4f6;\n  border-radius: 0.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.file-icon {\n  font-size: 1.5rem;\n}\n\n/* File Details */\n.file-details {\n  flex: 1;\n  min-width: 0;\n}\n\n.file-name {\n  font-weight: 500;\n  color: #374151;\n  margin-bottom: 0.25rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.file-meta {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 0.75rem;\n  color: #6b7280;\n}\n\n.file-type {\n  font-weight: 500;\n}\n\n.file-size {\n  color: #9ca3af;\n}\n\n/* File Actions */\n.file-actions {\n  flex-shrink: 0;\n}\n\n.preview-button {\n  display: flex;\n  align-items: center;\n  gap: 0.375rem;\n  padding: 0.5rem 0.75rem;\n  background: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 0.375rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.preview-button:hover {\n  background: #1d4ed8;\n}\n\n.not-supported {\n  font-size: 0.75rem;\n  color: #9ca3af;\n  font-style: italic;\n}\n\n/* Manual Entry Section */\n.manual-entry-section {\n  margin-bottom: 2rem;\n}\n\n.manual-entry-card {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 1.5rem;\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n  border: 1px solid #f59e0b;\n  border-radius: 0.75rem;\n  gap: 1rem;\n}\n\n.manual-entry-content {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  flex: 1;\n}\n\n.manual-entry-icon {\n  font-size: 2rem;\n  flex-shrink: 0;\n}\n\n.manual-entry-text h4 {\n  margin: 0 0 0.25rem 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: #92400e;\n}\n\n.manual-entry-text p {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #b45309;\n}\n\n.manual-entry-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background: #f59e0b;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  flex-shrink: 0;\n}\n\n.manual-entry-button:hover {\n  background: #d97706;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);\n}\n\n.manual-entry-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Instructions */\n.file-instructions {\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.instruction-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  font-size: 0.875rem;\n  color: #475569;\n}\n\n.instruction-item:last-child {\n  margin-bottom: 0;\n}\n\n.instruction-icon {\n  font-size: 1rem;\n  flex-shrink: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .file-list-container {\n    padding: 1rem;\n  }\n  \n  .file-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .file-item {\n    padding: 0.75rem;\n  }\n  \n  .file-icon-container {\n    width: 2.5rem;\n    height: 2.5rem;\n  }\n  \n  .file-icon {\n    font-size: 1.25rem;\n  }\n  \n  .file-list-title {\n    font-size: 1.25rem;\n  }\n  \n  .category-title {\n    font-size: 1rem;\n  }\n\n  .manual-entry-card {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .manual-entry-content {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.75rem;\n  }\n\n  .manual-entry-text h4 {\n    font-size: 0.875rem;\n  }\n\n  .manual-entry-text p {\n    font-size: 0.75rem;\n  }\n\n  .manual-entry-button {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n/* Validation Warning Section */\n.validation-warning-section {\n  margin-bottom: 1.5rem;\n}\n\n.validation-warning-card {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  padding: 1rem;\n  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\n  border: 1px solid #93c5fd;\n  border-radius: 0.75rem;\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);\n}\n\n.validation-warning-content {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  flex: 1;\n}\n\n.validation-warning-icon {\n  font-size: 1.5rem;\n  flex-shrink: 0;\n  margin-top: 0.125rem;\n}\n\n.validation-warning-text {\n  flex: 1;\n}\n\n.validation-warning-text h4 {\n  color: #1e40af;\n  font-size: 1rem;\n  font-weight: 600;\n  margin: 0 0 0.5rem 0;\n}\n\n.validation-warning-text p {\n  color: #1e3a8a;\n  font-size: 0.875rem;\n  margin: 0 0 0.5rem 0;\n  line-height: 1.4;\n}\n\n.validation-details {\n  margin-top: 0.75rem;\n  padding-top: 0.75rem;\n  border-top: 1px solid #93c5fd;\n}\n\n.validation-details p {\n  margin: 0.25rem 0;\n  font-size: 0.8125rem;\n}\n\n.validation-details strong {\n  color: #1e40af;\n  font-weight: 600;\n}\n\n.validation-actions {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.raise-query-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.25rem;\n  background: #2563eb;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  flex-shrink: 0;\n}\n\n.raise-query-button:hover {\n  background: #1d4ed8;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);\n}\n\n.raise-query-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Animated pulse effect for raise query button */\n.raise-query-button.animated-pulse {\n  animation: pulseGlow 2s infinite;\n}\n\n@keyframes pulseGlow {\n  0% {\n    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);\n  }\n  70% {\n    box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);\n  }\n}\n\n\n\n/* Query Modal Styles */\n.query-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 1rem;\n}\n\n.query-modal {\n  background: white;\n  border-radius: 0.75rem;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  max-width: 600px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.query-modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 1.5rem 1.5rem 1rem 1.5rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.query-modal-header h3 {\n  color: #1f2937;\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0;\n}\n\n.query-modal-close {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2rem;\n  height: 2rem;\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  border-radius: 0.375rem;\n  transition: all 0.2s ease;\n}\n\n.query-modal-close:hover {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.query-modal-close svg {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.query-form {\n  padding: 1.5rem;\n}\n\n.form-group {\n  margin-bottom: 1.5rem;\n}\n\n.form-group label {\n  display: block;\n  color: #374151;\n  font-weight: 500;\n  font-size: 0.875rem;\n  margin-bottom: 0.5rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #2563eb;\n  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);\n}\n\n.form-group textarea {\n  resize: vertical;\n  min-height: 120px;\n  font-family: inherit;\n  line-height: 1.5;\n}\n\n.query-form-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  padding-top: 1rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.cancel-button {\n  padding: 0.75rem 1.5rem;\n  background: #f3f4f6;\n  color: #374151;\n  border: 1px solid #d1d5db;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.cancel-button:hover {\n  background: #e5e7eb;\n}\n\n.send-query-button {\n  padding: 0.75rem 1.5rem;\n  background: #2563eb;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.send-query-button:hover:not(:disabled) {\n  background: #1d4ed8;\n}\n\n.send-query-button:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n", ".author-query-modal {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  max-width: 600px;\n  width: 90%;\n  max-height: 80vh;\n  overflow-y: auto;\n  position: relative;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24px 24px 0 24px;\n  border-bottom: 1px solid #e5e7eb;\n  margin-bottom: 0px;\n}\n\n.modal-header h2 {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #111827;\n}\n\n\n\n.modal-icon {\n  width: 24px;\n  height: 24px;\n  color: #f59e0b;\n}\n\n.modal-close {\n  background: none;\n  border: none;\n  padding: 8px;\n  border-radius: 6px;\n  cursor: pointer;\n  color: #6b7280;\n  transition: all 0.2s;\n}\n\n.modal-close:hover {\n  background-color: #f3f4f6;\n  color: #374151;\n}\n\n.modal-close svg {\n  width: 20px;\n  height: 20px;\n}\n\n.modal-body {\n  padding: 0 24px 24px 24px;\n}\n\n.article-info {\n  background-color: #f8fafc;\n  padding: 12px 16px;\n  border-radius: 8px;\n  margin-bottom: 24px;\n  border-left: 4px solid #3b82f6;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.article-info p {\n  margin: 0;\n  color: #374151;\n  font-size: 0.875rem;\n}\n\n.modal-form {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.form-group label {\n  font-weight: 500;\n  color: #374151;\n  font-size: 0.875rem;\n}\n\n.authors-list {\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  max-height: 200px;\n  overflow-y: auto;\n  background-color: #fafafa;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n}\n\n.author-item {\n  border-bottom: 1px solid #e5e7eb;\n  padding: 12px 16px;\n}\n\n.author-item:last-child {\n  border-bottom: none;\n}\n\n.checkbox-label {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  cursor: pointer;\n  font-size: 0.875rem;\n}\n\n.checkbox-label input[type=\"checkbox\"] {\n  margin-top: 2px;\n  width: 16px;\n  height: 16px;\n  accent-color: #3b82f6;\n}\n\n.author-details {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  flex: 1;\n}\n\n.author-name {\n  font-weight: 500;\n  color: #111827;\n}\n\n.author-email {\n  color: #6b7280;\n  font-size: 0.8rem;\n}\n\n.copyright-status {\n  font-size: 0.75rem;\n  font-weight: 500;\n  padding: 2px 8px;\n  border-radius: 12px;\n  width: fit-content;\n}\n\n.copyright-status.yes {\n  background-color: #dcfce7;\n  color: #166534;\n}\n\n.copyright-status.no {\n  background-color: #fef2f2;\n  color: #dc2626;\n}\n\n.copyright-status.unknown {\n  background-color: #f3f4f6;\n  color: #6b7280;\n}\n\n.no-authors {\n  padding: 16px;\n  text-align: center;\n  color: #6b7280;\n  font-style: italic;\n}\n\nselect {\n  padding: 8px 12px;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  font-size: 0.875rem;\n  background-color: white;\n  color: #374151;\n  transition: border-color 0.2s;\n}\n\nselect:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\nselect.error {\n  border-color: #dc2626;\n}\n\n.query-preview {\n  background-color: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  padding: 16px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #374151;\n  white-space: pre-wrap;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding-top: 16px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.btn-secondary,\n.btn-primary {\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  border: 1px solid;\n}\n\n.btn-secondary {\n  background-color: white;\n  color: #374151;\n  border-color: #d1d5db;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: #f9fafb;\n  border-color: #9ca3af;\n}\n\n.btn-primary {\n  background-color: #3b82f6;\n  color: white;\n  border-color: #3b82f6;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #2563eb;\n  border-color: #2563eb;\n}\n\n.btn-sm {\n  padding: 4px 12px;\n  font-size: 0.75rem;\n  height: fit-content;\n}\n\n.btn-secondary:disabled,\n.btn-primary:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.error-text {\n  color: #dc2626;\n  font-size: 0.75rem;\n  margin-top: 4px;\n}\n\n/* Responsive design */\n@media (max-width: 640px) {\n  .author-query-modal {\n    width: 95%;\n    margin: 20px auto;\n  }\n\n  .modal-header,\n  .modal-body {\n    padding-left: 16px;\n    padding-right: 16px;\n  }\n\n  .form-actions {\n    flex-direction: column;\n  }\n\n  .btn-secondary,\n  .btn-primary {\n    width: 100%;\n  }\n}", ".metadata-panel {\n  background: white;\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n  margin-bottom: 16px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n  transition: all 0.2s ease;\n  position:sticky;\n  top: 50px;\n}\n\n.metadata-panel:hover {\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.metadata-panel.always-expanded {\n  position: static;\n  margin-bottom: 0;\n}\n\n.metadata-panel.always-expanded .panel-header {\n  cursor: default;\n}\n\n.metadata-panel.always-expanded .panel-header:hover {\n  background-color: white;\n}\n\n.metadata-panel.loading,\n.metadata-panel.error {\n  padding: 16px;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  cursor: pointer;\n  user-select: none;\n  transition: background-color 0.2s;\n}\n\n.panel-header:hover {\n  background-color: #f9fafb;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.panel-icon {\n  width: 20px;\n  height: 20px;\n  color: #6b7280;\n}\n\n.panel-icon.error {\n  color: #dc2626;\n}\n\n.panel-title {\n  font-weight: 500;\n  color: #111827;\n  font-size: 0.875rem;\n}\n\n.author-count {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.copyright-summary {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n}\n\n.status-count {\n  font-size: 0.75rem;\n  font-weight: 500;\n  padding: 2px 6px;\n  border-radius: 10px;\n  display: flex;\n  align-items: center;\n  gap: 2px;\n}\n\n.status-count.yes {\n  background-color: #dcfce7;\n  color: #166534;\n}\n\n.status-count.no {\n  background-color: #fef2f2;\n  color: #dc2626;\n}\n\n.status-count.unknown {\n  background-color: #f3f4f6;\n  color: #6b7280;\n}\n\n.raise-query-button {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  padding: 6px 12px;\n  background-color: #f59e0b;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.raise-query-button:hover:not(:disabled) {\n  background-color: #d97706;\n  transform: translateY(-1px);\n}\n\n.raise-query-button:disabled {\n  background-color: #d1d5db;\n  color: #9ca3af;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.raise-query-button svg {\n  width: 14px;\n  height: 14px;\n}\n\n.expand-icon {\n  width: 16px;\n  height: 16px;\n  color: #6b7280;\n  transition: transform 0.2s;\n}\n\n.expand-icon.expanded {\n  transform: rotate(180deg);\n}\n\n.panel-content {\n  border-top: 1px solid #e5e7eb;\n  padding: 16px 20px;\n  background-color: #fafafa;\n}\n\n.no-authors {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 16px;\n  color: #6b7280;\n  font-style: italic;\n  justify-content: center;\n}\n\n.warning-icon {\n  width: 16px;\n  height: 16px;\n  color: #f59e0b;\n}\n\n.authors-table {\n  display: flex;\n  flex-direction: column;\n  gap: 1px;\n  background-color: #e5e7eb;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.table-header {\n  display: grid;\n  grid-template-columns: 2fr 2fr 1fr;\n  gap: 16px;\n  padding: 12px 16px;\n  background-color: #f3f4f6;\n  font-weight: 500;\n  font-size: 0.75rem;\n  color: #374151;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.table-row {\n  display: grid;\n  grid-template-columns: 2fr 2fr 1fr;\n  gap: 16px;\n  padding: 12px 16px;\n  background-color: white;\n  font-size: 0.875rem;\n  align-items: center;\n}\n\n.author-name {\n  font-weight: 500;\n  color: #111827;\n}\n\n.author-email {\n  color: #6b7280;\n  word-break: break-all;\n}\n\n.copyright-status {\n  font-size: 0.75rem;\n  font-weight: 500;\n  padding: 4px 8px;\n  border-radius: 12px;\n  text-align: center;\n  width: fit-content;\n}\n\n.copyright-status.yes {\n  background-color: #dcfce7;\n  color: #166534;\n}\n\n.copyright-status.no {\n  background-color: #fef2f2;\n  color: #dc2626;\n}\n\n.copyright-status.unknown {\n  background-color: #f3f4f6;\n  color: #6b7280;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .panel-header {\n    padding: 12px 16px;\n  }\n  \n  .header-right {\n    gap: 8px;\n  }\n  \n  .copyright-summary {\n    gap: 4px;\n  }\n  \n  .raise-query-button {\n    padding: 4px 8px;\n    font-size: 0.7rem;\n  }\n  \n  .table-header,\n  .table-row {\n    grid-template-columns: 1.5fr 1.5fr 1fr;\n    gap: 8px;\n    padding: 8px 12px;\n  }\n  \n  .panel-content {\n    padding: 12px 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .table-header,\n  .table-row {\n    grid-template-columns: 1fr;\n    gap: 4px;\n  }\n  \n  .table-header span:nth-child(2),\n  .table-header span:nth-child(3) {\n    display: none;\n  }\n  \n  .table-row {\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n  }\n  \n  .author-email {\n    font-size: 0.75rem;\n  }\n}\n", "/* Document Preview Styles */\n.document-preview-container {\n  max-width: 100%;\n  margin: 0 auto;\n  padding: 1.5rem;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Sidebar Layout */\n.preview-with-sidebar {\n  display: grid;\n  grid-template-columns: 1fr 400px;\n  gap: 1.5rem;\n  flex: 1;\n  min-height: 0;\n  align-items: start;\n}\n\n.preview-with-sidebar.sidebar-hidden {\n  grid-template-columns: 1fr;\n}\n\n.authors-sidebar {\n  position: sticky;\n  top: 80px;\n  max-height: calc(100vh - 100px);\n  overflow-y: auto;\n}\n\n/* Header */\n.preview-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 1.5rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n  flex-shrink: 0;\n}\n\n.preview-title-section {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.back-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2.5rem;\n  height: 2.5rem;\n  background: #f3f4f6;\n  border: 1px solid #d1d5db;\n  border-radius: 0.5rem;\n  color: #374151;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.back-button:hover {\n  background: #e5e7eb;\n  color: #111827;\n}\n\n.preview-file-info {\n  flex: 1;\n}\n\n.preview-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #374151;\n  margin: 0 0 0.25rem 0;\n}\n\n.file-icon {\n  font-size: 1.125rem;\n}\n\n.preview-subtitle {\n  color: #6b7280;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n/* Actions */\n.preview-actions {\n  display: flex;\n  gap: 0.75rem;\n}\n\n.toggle-sidebar-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2.5rem;\n  height: 2.5rem;\n  background: #f3f4f6;\n  color: #6b7280;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.5rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.toggle-sidebar-button:hover {\n  background: #e5e7eb;\n  color: #374151;\n  border-color: #d1d5db;\n}\n\n.toggle-sidebar-button svg {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.process-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background: #059669;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.process-button:hover {\n  background: #047857;\n}\n\n.process-button.selected {\n  background: #7c3aed;\n}\n\n.process-button.selected:hover {\n  background: #6d28d9;\n}\n\n/* Content Wrapper */\n.preview-content-wrapper {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n  margin-bottom: 1.5rem;\n}\n\n/* Loading State */\n.preview-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  gap: 1rem;\n}\n\n.loading-spinner {\n  width: 2rem;\n  height: 2rem;\n  border: 3px solid #f3f4f6;\n  border-top: 3px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.preview-loading p {\n  color: #6b7280;\n  font-size: 1rem;\n  margin: 0;\n}\n\n/* Error State */\n.preview-error {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.5rem;\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 0.75rem;\n  color: #dc2626;\n}\n\n.preview-error h3 {\n  margin: 0 0 0.25rem 0;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.preview-error p {\n  margin: 0;\n  font-size: 0.875rem;\n}\n\n.error-suggestion {\n  margin-top: 1rem;\n  padding: 1rem;\n  background: #fef3c7;\n  border: 1px solid #fbbf24;\n  border-radius: 0.5rem;\n  color: #92400e;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n/* Content Container */\n.preview-content-container {\n  flex: 1;\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.75rem;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.preview-content {\n  flex: 1;\n  padding: 2rem;\n  overflow-y: auto;\n  line-height: 1.6;\n  font-family: 'Georgia', serif;\n  user-select: text;\n}\n\n/* Content Styling */\n.preview-content h1,\n.preview-content h2,\n.preview-content h3,\n.preview-content h4,\n.preview-content h5,\n.preview-content h6 {\n  color: #374151;\n  margin-top: 1.5rem;\n  margin-bottom: 0.75rem;\n  font-weight: 600;\n}\n\n.preview-content h1 { font-size: 1.5rem; }\n.preview-content h2 { font-size: 1.25rem; }\n.preview-content h3 { font-size: 1.125rem; }\n\n.preview-content p {\n  margin-bottom: 1rem;\n  color: #374151;\n}\n\n.preview-content pre {\n  background: #f8fafc;\n  padding: 1rem;\n  border-radius: 0.5rem;\n  overflow-x: auto;\n  font-family: 'Courier New', monospace;\n  font-size: 0.875rem;\n  line-height: 1.4;\n}\n\n.preview-content ::selection {\n  background: #dbeafe;\n  color: #1d4ed8;\n}\n\n/* Instructions */\n.preview-instructions {\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  flex-shrink: 0;\n}\n\n.instruction-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 0.5rem;\n  font-size: 0.875rem;\n  color: #475569;\n}\n\n.instruction-item:last-child {\n  margin-bottom: 0;\n}\n\n.instruction-icon {\n  font-size: 1rem;\n  flex-shrink: 0;\n}\n\n/* Selection Info */\n.selection-info {\n  background: #eff6ff;\n  border: 1px solid #bfdbfe;\n  border-radius: 0.75rem;\n  padding: 1rem;\n  flex-shrink: 0;\n}\n\n.selection-header {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-weight: 600;\n  color: #1d4ed8;\n  margin-bottom: 0.5rem;\n  font-size: 0.875rem;\n}\n\n.selection-icon {\n  font-size: 1rem;\n}\n\n.selection-preview {\n  font-size: 0.875rem;\n  color: #374151;\n  background: #ffffff;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  border: 1px solid #d1d5db;\n  font-family: 'Courier New', monospace;\n  line-height: 1.4;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .preview-with-sidebar {\n    grid-template-columns: 1fr;\n  }\n\n  .authors-sidebar {\n    position: static;\n    max-height: none;\n    order: -1; /* Show authors above content on mobile */\n  }\n}\n\n/* Scroll to Top Button */\n.scroll-to-top-button {\n  position: fixed;\n  bottom: 2rem;\n  right: 2rem;\n  width: 3rem;\n  height: 3rem;\n  background: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  transition: all 0.3s ease;\n  z-index: 1000;\n}\n\n.scroll-to-top-button:hover {\n  background: #2563eb;\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);\n}\n\n.scroll-to-top-button:active {\n  transform: translateY(0);\n}\n\n.scroll-to-top-button svg {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n@media (max-width: 768px) {\n  .document-preview-container {\n    padding: 1rem;\n  }\n\n  .preview-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n\n  .preview-title-section {\n    width: 100%;\n  }\n\n  .preview-actions {\n    width: 100%;\n    justify-content: flex-end;\n  }\n\n  .preview-content {\n    padding: 1rem;\n  }\n\n  .process-button {\n    font-size: 0.75rem;\n    padding: 0.375rem 0.75rem;\n  }\n\n  .scroll-to-top-button {\n    bottom: 1rem;\n    right: 1rem;\n  }\n}\n", "/* ZIP Workflow Styles */\n.zip-workflow-container {\n  min-height: 100vh;\n  background: #f9fafb;\n  position: relative;\n}\n\n/* Progress Indicator */\n.workflow-progress {\n  background: #ffffff;\n  border-bottom: 1px solid #e5e7eb;\n  padding: 1.5rem 0;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.progress-steps {\n  max-width: 600px;\n  margin: 0 auto;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 2rem;\n}\n\n.progress-step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n}\n\n.step-circle {\n  width: 2.5rem;\n  height: 2.5rem;\n  border-radius: 50%;\n  background: #f3f4f6;\n  border: 2px solid #d1d5db;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.progress-step.active .step-circle {\n  background: #3b82f6;\n  border-color: #3b82f6;\n  color: white;\n}\n\n.progress-step.completed .step-circle {\n  background: #10b981;\n  border-color: #10b981;\n  color: white;\n}\n\n.step-number {\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.step-label {\n  font-size: 0.75rem;\n  font-weight: 500;\n  color: #6b7280;\n  text-align: center;\n}\n\n.progress-step.active .step-label {\n  color: #3b82f6;\n}\n\n.progress-step.completed .step-label {\n  color: #10b981;\n}\n\n.progress-line {\n  width: 4rem;\n  height: 2px;\n  background: #e5e7eb;\n  margin: 0 1rem;\n}\n\n.progress-step.completed + .progress-line {\n  background: #10b981;\n}\n\n/* Main Content */\n.workflow-content {\n  padding: 2rem 0;\n  min-height: calc(100vh - 120px);\n}\n\n/* Floating Action Panel */\n.floating-action-panel {\n  position: fixed;\n  bottom: 2rem;\n  right: 2rem;\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 1rem;\n  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n  z-index: 50;\n  max-width: 400px;\n  animation: slideInUp 0.3s ease-out;\n}\n\n@keyframes slideInUp {\n  from {\n    transform: translateY(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n.action-panel-content {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem 1.5rem;\n}\n\n.action-info {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  flex: 1;\n}\n\n.action-icon {\n  font-size: 1.5rem;\n  flex-shrink: 0;\n}\n\n.action-text {\n  flex: 1;\n}\n\n.action-title {\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.875rem;\n  margin-bottom: 0.125rem;\n}\n\n.action-subtitle {\n  font-size: 0.75rem;\n  color: #6b7280;\n}\n\n.process-button {\n  background: #059669;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  font-weight: 600;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  white-space: nowrap;\n}\n\n.process-button:hover {\n  background: #047857;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .workflow-progress {\n    padding: 1rem 0;\n  }\n  \n  .progress-steps {\n    padding: 0 1rem;\n  }\n  \n  .step-circle {\n    width: 2rem;\n    height: 2rem;\n  }\n  \n  .step-number {\n    font-size: 0.75rem;\n  }\n  \n  .step-label {\n    font-size: 0.625rem;\n  }\n  \n  .progress-line {\n    width: 2rem;\n    margin: 0 0.5rem;\n  }\n  \n  .workflow-content {\n    padding: 1rem 0;\n  }\n  \n  .floating-action-panel {\n    bottom: 1rem;\n    right: 1rem;\n    left: 1rem;\n    max-width: none;\n  }\n  \n  .action-panel-content {\n    padding: 1rem;\n  }\n  \n  .process-button {\n    padding: 0.625rem 1.25rem;\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .progress-steps {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .progress-line {\n    width: 2px;\n    height: 2rem;\n    margin: 0;\n  }\n  \n  .action-panel-content {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 0.75rem;\n  }\n  \n  .action-info {\n    justify-content: center;\n  }\n}\n", "/* Folder Uploader Styles */\n.folder-uploader-container {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 2rem;\n}\n\n.folder-uploader-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.folder-uploader-title {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n}\n\n.folder-icon {\n  font-size: 2rem;\n}\n\n.folder-uploader-description {\n  color: #6b7280;\n  font-size: 1rem;\n  max-width: 500px;\n  margin: 0 auto;\n}\n\n.folder-error-message {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 0.75rem;\n  color: #dc2626;\n  margin-bottom: 1.5rem;\n}\n\n.folder-error-message svg {\n  width: 1.25rem;\n  height: 1.25rem;\n  flex-shrink: 0;\n}\n\n.folder-drop-zone {\n  border: 2px dashed #d1d5db;\n  border-radius: 1rem;\n  padding: 3rem 2rem;\n  text-align: center;\n  background-color: #f9fafb;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  position: relative;\n  min-height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.folder-drop-zone:hover {\n  border-color: #9ca3af;\n  background-color: #f3f4f6;\n}\n\n.folder-drop-zone.drag-over {\n  border-color: #3b82f6;\n  background-color: #eff6ff;\n  transform: scale(1.02);\n}\n\n.folder-drop-zone.processing {\n  border-color: #10b981;\n  background-color: #ecfdf5;\n  cursor: not-allowed;\n}\n\n.folder-upload-icon {\n  margin-bottom: 1rem;\n}\n\n.folder-upload-icon svg {\n  width: 4rem;\n  height: 4rem;\n  color: #9ca3af;\n}\n\n.folder-upload-text {\n  max-width: 400px;\n}\n\n.folder-main-text {\n  font-size: 1.125rem;\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n\n.folder-browse-link {\n  color: #3b82f6;\n  font-weight: 600;\n  cursor: pointer;\n  text-decoration: underline;\n  transition: color 0.2s ease;\n}\n\n.folder-browse-link:hover {\n  color: #2563eb;\n}\n\n.folder-file-input {\n  display: none;\n}\n\n.folder-sub-text {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.folder-processing {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n}\n\n.folder-spinner {\n  width: 3rem;\n  height: 3rem;\n  border: 3px solid #e5e7eb;\n  border-top: 3px solid #3b82f6;\n  border-radius: 50%;\n  animation: folder-spin 1s linear infinite;\n}\n\n@keyframes folder-spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.folder-processing p {\n  margin: 0;\n  color: #374151;\n  font-weight: 500;\n}\n\n.folder-name {\n  font-size: 0.875rem;\n  color: #6b7280;\n  font-style: italic;\n}\n\n.folder-help-section {\n  margin-top: 2rem;\n  padding: 1.5rem;\n  background-color: #f8fafc;\n  border-radius: 0.75rem;\n  border: 1px solid #e2e8f0;\n}\n\n.folder-help-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 1rem;\n}\n\n.folder-help-steps {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.folder-help-step {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.step-number {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 1.5rem;\n  height: 1.5rem;\n  background-color: #3b82f6;\n  color: white;\n  border-radius: 50%;\n  font-size: 0.75rem;\n  font-weight: 600;\n  flex-shrink: 0;\n}\n\n.folder-help-step span:last-child {\n  color: #4b5563;\n  font-size: 0.875rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .folder-uploader-container {\n    padding: 1rem;\n  }\n  \n  .folder-drop-zone {\n    padding: 2rem 1rem;\n  }\n  \n  .folder-uploader-title {\n    font-size: 1.5rem;\n  }\n  \n  .folder-upload-icon svg {\n    width: 3rem;\n    height: 3rem;\n  }\n  \n  .folder-main-text {\n    font-size: 1rem;\n  }\n}\n\n/* Validation Summary Styles */\n.validation-summary {\n  background-color: white;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  margin: 2rem 0;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.validation-summary h3 {\n  color: #1f2937;\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  text-align: center;\n}\n\n.validation-stats {\n  display: flex;\n  justify-content: center;\n  gap: 2rem;\n  margin-bottom: 1.5rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.stat-number {\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #6b7280;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.stat-item.success .stat-number {\n  color: #059669;\n}\n\n.stat-item.error .stat-number {\n  color: #dc2626;\n}\n\n.stat-item.total .stat-number {\n  color: #4f46e5;\n}\n\n.validation-details {\n  max-height: 400px;\n  overflow-y: auto;\n  margin-bottom: 1rem;\n}\n\n.validation-item {\n  border: 1px solid #e5e7eb;\n  border-radius: 0.5rem;\n  margin-bottom: 0.75rem;\n  padding: 1rem;\n}\n\n.validation-item.success {\n  background-color: #f0fdf4;\n  border-color: #bbf7d0;\n}\n\n.validation-item.error {\n  background-color: #fef2f2;\n  border-color: #fecaca;\n}\n\n.validation-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 0.5rem;\n}\n\n.validation-icon {\n  font-size: 1.25rem;\n}\n\n.article-id {\n  font-weight: 600;\n  color: #1f2937;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n}\n\n.validation-status {\n  margin-left: auto;\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.validation-item.success .validation-status {\n  background-color: #d1fae5;\n  color: #065f46;\n}\n\n.validation-item.error .validation-status {\n  background-color: #fee2e2;\n  color: #991b1b;\n}\n\n.validation-error-details {\n  margin-top: 0.75rem;\n  padding-top: 0.75rem;\n  border-top: 1px solid #fecaca;\n}\n\n.validation-error-details p {\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n\n.file-list {\n  margin-top: 0.5rem;\n}\n\n.file-list strong {\n  color: #111827;\n  font-weight: 600;\n}\n\n.file-list ul {\n  margin-top: 0.25rem;\n  padding-left: 1.5rem;\n  color: #6b7280;\n}\n\n.file-list li {\n  margin-bottom: 0.125rem;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.875rem;\n}\n\n.validation-actions {\n  display: flex;\n  /* justify-content: center; */\n  padding-bottom: 1rem;\n}\n\n.close-summary-button {\n  padding: 0.5rem 1.5rem;\n  background-color: #6b7280;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.close-summary-button:hover {\n  background-color: #4b5563;\n}\n", "/* Smart Batching Modal Styles */\n.smart-batching-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.smart-batching-modal {\n  background: white;\n  border-radius: 12px;\n  width: 95%;\n  max-width: 1400px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\n  display: flex;\n  flex-direction: column;\n}\n\n/* Modal Header */\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e5e7eb;\n  background: #f8fafc;\n  border-radius: 12px 12px 0 0;\n}\n\n.modal-header h2 {\n  margin: 0;\n  color: #1f2937;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.close-button {\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #6b7280;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: all 0.2s;\n}\n\n.close-button:hover {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n/* TE Capacity Information Header */\n.te-capacity-info-header {\n  background: linear-gradient(135deg, #eff6ff 0%, #e0f2fe 100%);\n  border-bottom: 2px solid #bfdbfe;\n  padding: 16px 24px;\n  margin: 0;\n}\n\n.capacity-info-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n  color: #1e40af;\n  font-weight: 600;\n  font-size: 0.95rem;\n}\n\n.capacity-info-title svg {\n  color: #3b82f6;\n}\n\n.capacity-info-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 12px;\n  margin-bottom: 12px;\n}\n\n.capacity-info-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 12px;\n  background: white;\n  border-radius: 6px;\n  border: 1px solid #bfdbfe;\n  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);\n}\n\n.te-name {\n  font-weight: 600;\n  color: #1e40af;\n  font-size: 0.9rem;\n}\n\n.te-capacity {\n  color: #3b82f6;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.capacity-info-note {\n  margin: 0;\n  font-size: 0.85rem;\n  color: #1e40af;\n  font-style: italic;\n  opacity: 0.9;\n}\n\n/* Responsive design for smaller screens */\n@media (max-width: 768px) {\n  .capacity-info-list {\n    grid-template-columns: 1fr;\n  }\n\n  .te-capacity-info-header {\n    padding: 12px 16px;\n  }\n}\n\n/* Journal Selection */\n.journal-selection {\n  padding: 20px 24px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.journal-selection h3 {\n  margin: 0 0 12px 0;\n  color: #374151;\n  font-size: 1.1rem;\n  font-weight: 600;\n}\n\n.journal-checkboxes {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.journal-checkbox {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 12px;\n  background: #f9fafb;\n  border: 1px solid #e5e7eb;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s;\n  font-size: 0.9rem;\n}\n\n.journal-checkbox:hover {\n  background: #f3f4f6;\n  border-color: #d1d5db;\n}\n\n.journal-checkbox input[type=\"checkbox\"] {\n  margin: 0;\n}\n\n/* Batching Controls */\n.batching-controls {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  padding: 16px 24px;\n  background: #f8fafc;\n  border-bottom: 1px solid #e5e7eb;\n  flex-wrap: wrap;\n}\n\n.smart-batching-toggle {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  font-weight: 500;\n}\n\n.batch-size-control label {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n}\n\n.batch-size-control input {\n  width: 80px;\n  padding: 6px 8px;\n  border: 1px solid #d1d5db;\n  border-radius: 4px;\n  font-size: 0.9rem;\n}\n\n.batch-size-info {\n  font-size: 0.875rem;\n  color: #666;\n  margin-top: 0.5rem;\n  font-style: italic;\n}\n\n.article-summary {\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n}\n\n.article-summary h3 {\n  margin: 0 0 0.5rem 0;\n  color: #1e293b;\n  font-size: 1.1rem;\n}\n\n.article-summary p {\n  margin: 0;\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.empty-batch-message {\n  padding: 1rem;\n  text-align: center;\n  color: #94a3b8;\n  font-style: italic;\n  background: #f8fafc;\n  border: 1px dashed #cbd5e1;\n  border-radius: 4px;\n  margin: 0.5rem 0;\n}\n\n.assign-batch-btn:disabled {\n  background-color: #e2e8f0;\n  color: #94a3b8;\n  cursor: not-allowed;\n}\n\n.assign-all-btn:disabled {\n  background-color: #e2e8f0;\n  color: #94a3b8;\n  cursor: not-allowed;\n}\n\n.refresh-batches-btn {\n  background: #3b82f6;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-weight: 500;\n  transition: background 0.2s;\n}\n\n.refresh-batches-btn:hover:not(:disabled) {\n  background: #2563eb;\n}\n\n.refresh-batches-btn:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* Batches Container */\n.batches-container {\n  padding: 20px 24px;\n  flex: 1;\n}\n\n.batches-container h3 {\n  margin: 0 0 16px 0;\n  color: #374151;\n  font-size: 1.1rem;\n  font-weight: 600;\n}\n\n.batches-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 16px;\n}\n\n/* Batch Card */\n.batch-card {\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n  background: white;\n  overflow: hidden;\n  transition: all 0.2s;\n}\n\n.batch-card:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.batch-card.completed {\n  border-color: #10b981;\n  background: #f0fdf4;\n}\n\n.batch-card.failed {\n  border-color: #ef4444;\n  background: #fef2f2;\n}\n\n/* Validation Query Batch Styling (HIGHEST PRIORITY) */\n.batch-card.validation-query-batch {\n  border: 2px solid #e74c3c;\n  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);\n  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);\n}\n\n.batch-card.validation-query-batch:hover {\n  box-shadow: 0 6px 16px rgba(231, 76, 60, 0.3);\n}\n\n.validation-query-badge {\n  display: inline-block;\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\n  color: white;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-left: 8px;\n  vertical-align: middle;\n  box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);\n}\n\n/* Premium Batch Styling (SECOND PRIORITY) */\n.batch-card.premium-batch {\n  border: 2px solid #f59e0b;\n  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);\n  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);\n}\n\n.batch-card.premium-batch:hover {\n  box-shadow: 0 6px 16px rgba(245, 158, 11, 0.3);\n}\n\n.premium-badge {\n  display: inline-block;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-left: 8px;\n  vertical-align: middle;\n  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);\n}\n\n.batch-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #f8fafc;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.batch-header h4 {\n  margin: 0;\n  color: #374151;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.article-count {\n  background: #3b82f6;\n  color: white;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.batch-journals {\n  padding: 12px 16px;\n  font-size: 0.9rem;\n  color: #6b7280;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.te-selection {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.te-selection label {\n  display: block;\n  margin-bottom: 6px;\n  font-weight: 500;\n  color: #374151;\n  font-size: 0.9rem;\n}\n\n.te-selection select {\n  width: 100%;\n  padding: 8px;\n  border: 1px solid #d1d5db;\n  border-radius: 4px;\n  font-size: 0.9rem;\n  background: white;\n}\n\n/* Articles List */\n.articles-list {\n  min-height: 120px;\n  max-height: 200px;\n  overflow-y: auto;\n  background: #fafbfc;\n}\n\n.articles-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background: #f3f4f6;\n  border-bottom: 1px solid #e5e7eb;\n  font-weight: 600;\n  font-size: 0.9rem;\n  color: #374151;\n}\n\n.article-actions {\n  position: relative;\n}\n\n.move-articles-btn {\n  background: #3b82f6;\n  color: white;\n  border: none;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: background 0.2s;\n}\n\n.move-articles-btn:hover {\n  background: #2563eb;\n}\n\n.move-options {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  background: white;\n  border: 1px solid #d1d5db;\n  border-radius: 4px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n  z-index: 10;\n  min-width: 120px;\n}\n\n.move-option {\n  display: block;\n  width: 100%;\n  padding: 8px 12px;\n  background: none;\n  border: none;\n  text-align: left;\n  cursor: pointer;\n  font-size: 0.8rem;\n  color: #374151;\n  transition: background 0.2s;\n}\n\n.move-option:hover {\n  background: #f3f4f6;\n}\n\n.article-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  margin-bottom: 4px;\n  background: white;\n  border: 1px solid #e5e7eb;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.2s;\n  font-size: 0.85rem;\n}\n\n.article-item:hover {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border-color: #d1d5db;\n}\n\n.article-item.selected {\n  background: #eff6ff;\n  border-color: #3b82f6;\n}\n\n.article-item input[type=\"checkbox\"] {\n  margin: 0;\n  cursor: pointer;\n}\n\n.article-id {\n  font-weight: 600;\n  color: #374151;\n}\n\n.article-journal {\n  color: #6b7280;\n  font-size: 0.8rem;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n/* Batch Actions */\n.batch-actions {\n  padding: 12px 16px;\n  background: #f8fafc;\n}\n\n.assign-batch-btn {\n  width: 100%;\n  padding: 10px;\n  border: none;\n  border-radius: 6px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  font-size: 0.9rem;\n}\n\n.assign-batch-btn:not(:disabled) {\n  background: #10b981;\n  color: white;\n}\n\n.assign-batch-btn:hover:not(:disabled) {\n  background: #059669;\n}\n\n.assign-batch-btn:disabled {\n  background: #9ca3af;\n  color: white;\n  cursor: not-allowed;\n}\n\n/* Modal Footer */\n.modal-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  border-top: 1px solid #e5e7eb;\n  background: #f8fafc;\n  border-radius: 0 0 12px 12px;\n}\n\n.assign-all-btn {\n  background: #059669;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: background 0.2s;\n  font-size: 1rem;\n}\n\n.assign-all-btn:hover:not(:disabled) {\n  background: #047857;\n}\n\n.assign-all-btn:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n}\n\n.cancel-btn {\n  background: #6b7280;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 6px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background 0.2s;\n}\n\n.cancel-btn:hover {\n  background: #4b5563;\n}\n\n/* Assignment Results */\n.assignment-results {\n  padding: 20px 24px;\n  background: #f0fdf4;\n  border-top: 1px solid #d1fae5;\n}\n\n.assignment-results h3 {\n  margin: 0 0 12px 0;\n  color: #065f46;\n  font-size: 1.1rem;\n}\n\n.results-summary p {\n  margin: 4px 0;\n  color: #047857;\n  font-weight: 500;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .smart-batching-modal {\n    width: 98%;\n    max-height: 95vh;\n  }\n  \n  .batches-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .batching-controls {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n    gap: 12px;\n  }\n  \n  .assign-all-btn,\n  .cancel-btn {\n    width: 100%;\n  }\n}\n", ".author-summary {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background-color: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  font-size: 0.75rem;\n  color: #374151;\n}\n\n.author-summary.compact {\n  padding: 4px 8px;\n  gap: 6px;\n  background-color: transparent;\n  border: none;\n}\n\n.author-summary.loading,\n.author-summary.error,\n.author-summary.no-data {\n  color: #6b7280;\n  font-style: italic;\n}\n\n.author-summary.error {\n  color: #dc2626;\n}\n\n.summary-icon {\n  width: 14px;\n  height: 14px;\n  color: #6b7280;\n  flex-shrink: 0;\n}\n\n.summary-icon.error {\n  color: #dc2626;\n}\n\n.author-count {\n  font-weight: 500;\n  color: #111827;\n}\n\n.copyright-indicators {\n  display: flex;\n  gap: 4px;\n  align-items: center;\n}\n\n.indicator {\n  font-size: 0.65rem;\n  font-weight: 500;\n  padding: 1px 4px;\n  border-radius: 8px;\n  line-height: 1;\n}\n\n.indicator.yes {\n  background-color: #dcfce7;\n  color: #166534;\n}\n\n.indicator.no {\n  background-color: #fef2f2;\n  color: #dc2626;\n}\n\n.indicator.unknown {\n  background-color: #f3f4f6;\n  color: #6b7280;\n}\n\n/* Non-compact version */\n.summary-header {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.summary-title {\n  font-weight: 500;\n  color: #111827;\n}\n\n.summary-details {\n  display: flex;\n  align-items: center;\n}\n\n.copyright-breakdown {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n}\n\n.breakdown-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 2px;\n}\n\n.breakdown-item .count {\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.breakdown-item .label {\n  font-size: 0.625rem;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.breakdown-item.yes .count {\n  color: #166534;\n}\n\n.breakdown-item.yes .label {\n  color: #16a34a;\n}\n\n.breakdown-item.no .count {\n  color: #dc2626;\n}\n\n.breakdown-item.no .label {\n  color: #ef4444;\n}\n\n.breakdown-item.unknown .count {\n  color: #6b7280;\n}\n\n.breakdown-item.unknown .label {\n  color: #9ca3af;\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .author-summary:not(.compact) {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 6px;\n  }\n  \n  .copyright-breakdown {\n    gap: 6px;\n  }\n  \n  .breakdown-item .count {\n    font-size: 0.75rem;\n  }\n  \n  .breakdown-item .label {\n    font-size: 0.6rem;\n  }\n}\n", "/* ZIP Queue Dashboard Styles */\n.zip-queue-dashboard {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 2rem;\n  padding-bottom: 1.5rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.header-left {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n}\n\n.back-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2.5rem;\n  height: 2.5rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.5rem;\n  background-color: white;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.back-button:hover {\n  background-color: #f9fafb;\n  border-color: #9ca3af;\n}\n\n.back-button svg {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.folder-info {\n  flex: 1;\n}\n\n.dashboard-title {\n  font-size: 1.875rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 0.25rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.folder-icon {\n  font-size: 2rem;\n}\n\n.folder-subtitle {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.75rem;\n}\n\n.download-all-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #10b981;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.download-all-button:hover {\n  background-color: #059669;\n}\n\n.download-all-button:disabled {\n  background-color: #6b7280;\n  cursor: not-allowed;\n}\n\n.download-all-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.clear-queue-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #dc2626;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.clear-queue-button:hover {\n  background-color: #b91c1c;\n}\n\n.clear-queue-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Progress Section */\n.progress-section {\n  margin-bottom: 2rem;\n}\n\n.progress-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.progress-text {\n  font-size: 0.875rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 0.5rem;\n  background-color: #e5e7eb;\n  border-radius: 0.25rem;\n  overflow: hidden;\n}\n\n.progress-fill {\n  height: 100%;\n  background-color: #10b981;\n  transition: width 0.3s ease;\n}\n\n/* Stats Grid */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.stat-card {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.5rem;\n  background-color: white;\n  border-radius: 0.75rem;\n  border: 1px solid #e5e7eb;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n}\n\n.stat-card.pending {\n  border-left: 4px solid #f59e0b;\n}\n\n.stat-card.processing {\n  border-left: 4px solid #3b82f6;\n}\n\n.stat-card.completed {\n  border-left: 4px solid #10b981;\n}\n\n.stat-icon {\n  font-size: 2rem;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 1.875rem;\n  font-weight: 700;\n  color: #1f2937;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n/* Tabs */\n.tabs-container {\n  margin-bottom: 1.5rem;\n}\n\n.tabs {\n  display: flex;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.tab {\n  padding: 0.75rem 1.5rem;\n  background: none;\n  border: none;\n  color: #6b7280;\n  font-weight: 500;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n  transition: all 0.2s ease;\n}\n\n.tab:hover {\n  color: #374151;\n}\n\n.tab.active {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n}\n\n/* ZIP Cards */\n.zip-cards-container {\n  min-height: 400px;\n}\n\n.zip-cards-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n  gap: 1.5rem;\n}\n\n.zip-card {\n  background-color: white;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.75rem;\n  overflow: hidden;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n  transition: box-shadow 0.2s ease;\n}\n\n.zip-card:hover {\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.zip-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 1.5rem 1.5rem 1rem;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.zip-info {\n  flex: 1;\n  justify-content: space-between;\n}\n\n.zip-filename {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 0.25rem;\n}\n\n.zip-icon {\n  font-size: 1.25rem;\n}\n\n.zip-article-id {\n  font-size: 0.875rem;\n  color: #6b7280;\n}\n\n.article-id-text {\n  font-weight: 500;\n  color: #374151;\n}\n\n.zip-status {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border: 1px solid;\n}\n\n.status-icon {\n  font-size: 0.875rem;\n}\n\n.zip-card-body {\n  padding: 1rem 1.5rem 1.5rem;\n}\n\n.zip-metadata {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  margin-bottom: 0.75rem;\n}\n\n.zip-author-summary {\n  margin-bottom: 1rem;\n}\n\n.metadata-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.875rem;\n  color: #6b7280;\n}\n\n.metadata-icon {\n  width: 1rem;\n  height: 1rem;\n  flex-shrink: 0;\n}\n\n.zip-actions {\n  display: flex;\n  gap: 0.75rem;\n}\n\n.process-button,\n.continue-button,\n.view-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.process-button {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.process-button:hover {\n  background-color: #2563eb;\n}\n\n.continue-button {\n  background-color: #f59e0b;\n  color: white;\n}\n\n.continue-button:hover {\n  background-color: #d97706;\n}\n\n.view-button {\n  background-color: #6b7280;\n  color: white;\n}\n\n.view-button:hover {\n  background-color: #4b5563;\n}\n\n.process-button svg,\n.continue-button svg,\n.view-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Empty States */\n.empty-state,\n.no-folder-message {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem;\n  text-align: center;\n  color: #6b7280;\n}\n\n.empty-icon,\n.no-folder-icon {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n}\n\n.empty-state h3,\n.no-folder-message h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n\n.empty-state p,\n.no-folder-message p {\n  font-size: 0.875rem;\n  margin-bottom: 1.5rem;\n}\n\n.back-to-upload-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.back-to-upload-button:hover {\n  background-color: #2563eb;\n}\n\n.back-to-upload-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .zip-queue-dashboard {\n    padding: 1rem;\n  }\n  \n  .dashboard-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  \n  .header-left {\n    align-items: center;\n  }\n  \n  .dashboard-title {\n    font-size: 1.5rem;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .zip-cards-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .tabs {\n    overflow-x: auto;\n  }\n}\n", "/* Folder ZIP Workflow Styles */\n.folder-zip-workflow {\n  min-height: 100vh;\n  background-color: #f9fafb;\n}\n\n/* Ensure smooth transitions between views */\n.folder-zip-workflow > * {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n", "/* Individual ZIP Processor Styles */\n.zip-processor-container {\n  min-height: 100vh;\n  background-color: #f9fafb;\n}\n\n.zip-processor-header {\n  background-color: white;\n  border-bottom: 1px solid #e5e7eb;\n  padding: .5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.back-to-queue-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2.5rem;\n  height: 2.5rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.5rem;\n  background-color: white;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.back-to-queue-button:hover {\n  background-color: #f9fafb;\n  border-color: #9ca3af;\n}\n\n.back-to-queue-button svg {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.zip-info {\n  flex: 1;\n}\n\n.zip-title {\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 0.25rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.zip-icon {\n  font-size: 1.25rem;\n}\n\n.zip-subtitle {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.75rem;\n}\n\n.process-refs-button,\n.mark-processed-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.process-refs-button {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.process-refs-button:hover {\n  background-color: #2563eb;\n}\n\n.mark-processed-button {\n  background-color: #10b981;\n  color: white;\n}\n\n.mark-processed-button:hover {\n  background-color: #059669;\n}\n\n.process-refs-button svg,\n.mark-processed-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Processing Indicator */\n.processing-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  background-color: #eff6ff;\n  border: 1px solid #bfdbfe;\n  border-radius: 0.5rem;\n  margin: 1rem 2rem;\n  color: #1d4ed8;\n}\n\n.processing-spinner {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #bfdbfe;\n  border-top: 2px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Main Content */\n.zip-processor-content {\n  padding: 0;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* Loading States */\n.zip-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n  text-align: center;\n}\n\n.zip-spinner {\n  width: 3rem;\n  height: 3rem;\n  border: 3px solid #e5e7eb;\n  border-top: 3px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n.zip-loading p {\n  color: #6b7280;\n  font-size: 1rem;\n  margin: 0;\n}\n\n/* Error States */\n.zip-error-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n  text-align: center;\n  background-color: white;\n  border-radius: 0.75rem;\n  border: 1px solid #e5e7eb;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n}\n\n.error-icon {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n}\n\n.zip-error-state h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #dc2626;\n  margin-bottom: 0.5rem;\n}\n\n.zip-error-state p {\n  color: #6b7280;\n  margin-bottom: 2rem;\n  max-width: 400px;\n}\n\n.error-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.reupload-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.reupload-button:hover {\n  background-color: #2563eb;\n}\n\n.reupload-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.zip-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  text-align: center;\n  color: #dc2626;\n}\n\n.zip-error svg {\n  width: 3rem;\n  height: 3rem;\n  margin-bottom: 1rem;\n}\n\n.zip-error p {\n  font-size: 1rem;\n  margin: 0;\n}\n\n/* Step Navigation */\n.step-navigation {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 2rem;\n  padding: 1rem;\n  background-color: white;\n  border-radius: 0.75rem;\n  border: 1px solid #e5e7eb;\n}\n\n.step-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.step-item.active {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.step-item.completed {\n  background-color: #10b981;\n  color: white;\n}\n\n.step-item.inactive {\n  background-color: #f3f4f6;\n  color: #6b7280;\n}\n\n.step-separator {\n  width: 1rem;\n  height: 1px;\n  background-color: #d1d5db;\n}\n\n/* Action Buttons */\n.action-buttons {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  background-color: white;\n  border-top: 1px solid #e5e7eb;\n  position: sticky;\n  bottom: 0;\n}\n\n.action-left,\n.action-right {\n  display: flex;\n  gap: 0.75rem;\n}\n\n.action-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.action-button.primary {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.action-button.primary:hover {\n  background-color: #2563eb;\n}\n\n.action-button.secondary {\n  background-color: #f3f4f6;\n  color: #374151;\n  border: 1px solid #d1d5db;\n}\n\n.action-button.secondary:hover {\n  background-color: #e5e7eb;\n}\n\n.action-button.success {\n  background-color: #10b981;\n  color: white;\n}\n\n.action-button.success:hover {\n  background-color: #059669;\n}\n\n.action-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .zip-processor-header {\n    padding: 1rem;\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  \n  .header-left {\n    align-items: center;\n  }\n  \n  .zip-title {\n    font-size: 1.25rem;\n  }\n  \n  .header-actions {\n    justify-content: center;\n  }\n  \n  .zip-processor-content {\n    padding: 1rem;\n  }\n  \n  .processing-indicator {\n    margin: 1rem;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .action-left,\n  .action-right {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n/* Validation Error Styles */\n.validation-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 60vh;\n  padding: 2rem;\n  text-align: center;\n  background-color: white;\n  border-radius: 0.75rem;\n  margin: 2rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.validation-error .error-icon {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n}\n\n.validation-error h2 {\n  color: #dc2626;\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n}\n\n.validation-details {\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 0.5rem;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  text-align: left;\n  max-width: 600px;\n  width: 100%;\n}\n\n.validation-details p {\n  margin-bottom: 0.5rem;\n  color: #374151;\n}\n\n.validation-details strong {\n  color: #111827;\n  font-weight: 600;\n}\n\n.error-explanation {\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #fecaca;\n}\n\n.error-explanation ul {\n  margin-top: 0.5rem;\n  padding-left: 1.5rem;\n  color: #6b7280;\n}\n\n.error-explanation li {\n  margin-bottom: 0.25rem;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.875rem;\n}\n\n.validation-actions {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.proceed-anyway-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background-color: #f59e0b;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.proceed-anyway-button:hover {\n  background-color: #d97706;\n  transform: translateY(-1px);\n}\n\n.proceed-anyway-button:active {\n  transform: translateY(0);\n}\n", ".individual-zip-upload {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 2rem;\n  background-color: #f9fafb;\n  min-height: 100vh;\n}\n\n.upload-header {\n  text-align: center;\n  margin-bottom: 2rem;\n  padding: 2rem;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.upload-title {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 0.5rem;\n}\n\n.upload-icon {\n  font-size: 2.5rem;\n}\n\n.upload-description {\n  color: #6b7280;\n  font-size: 1rem;\n  margin: 0;\n}\n\n.error-message {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 8px;\n  color: #dc2626;\n  margin-bottom: 1.5rem;\n}\n\n.error-message svg {\n  width: 1.25rem;\n  height: 1.25rem;\n  flex-shrink: 0;\n}\n\n.upload-section {\n  background: white;\n  border-radius: 12px;\n  padding: 2rem;\n  margin-bottom: 1.5rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.section-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 1rem;\n}\n\n.required {\n  color: #ef4444;\n}\n\n.drop-zone {\n  border: 2px dashed #d1d5db;\n  border-radius: 8px;\n  padding: 3rem 2rem;\n  text-align: center;\n  transition: all 0.3s ease;\n  background-color: #fafafa;\n  cursor: pointer;\n}\n\n.drop-zone:hover,\n.drop-zone.drag-over {\n  border-color: #3b82f6;\n  background-color: #eff6ff;\n}\n\n.drop-zone-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n}\n\n.upload-icon-large {\n  width: 3rem;\n  height: 3rem;\n  color: #9ca3af;\n}\n\n.drop-text {\n  font-size: 1rem;\n  color: #374151;\n  margin: 0;\n}\n\n.browse-link {\n  color: #3b82f6;\n  text-decoration: underline;\n  cursor: pointer;\n  font-weight: 500;\n}\n\n.browse-link:hover {\n  color: #1d4ed8;\n}\n\n.drop-subtext {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n.file-input-hidden {\n  display: none;\n}\n\n.selected-files {\n  margin-top: 1.5rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.files-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 1rem;\n}\n\n.files-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.file-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 0.75rem;\n  background-color: #f9fafb;\n  border: 1px solid #e5e7eb;\n  border-radius: 6px;\n}\n\n.file-icon {\n  font-size: 1.25rem;\n  flex-shrink: 0;\n}\n\n.file-name {\n  flex: 1;\n  font-size: 0.875rem;\n  color: #374151;\n  font-weight: 500;\n}\n\n.file-size {\n  font-size: 0.75rem;\n  color: #6b7280;\n  flex-shrink: 0;\n}\n\n.remove-button {\n  width: 1.5rem;\n  height: 1.5rem;\n  border: none;\n  background-color: #ef4444;\n  color: white;\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1rem;\n  font-weight: bold;\n  transition: background-color 0.2s ease;\n}\n\n.remove-button:hover {\n  background-color: #dc2626;\n}\n\n.json-upload-area {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  background-color: #fafafa;\n  transition: border-color 0.2s ease;\n}\n\n.json-upload-area:hover {\n  border-color: #3b82f6;\n}\n\n.json-upload-label {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  cursor: pointer;\n  flex: 1;\n}\n\n.json-icon {\n  width: 1.5rem;\n  height: 1.5rem;\n  color: #3b82f6;\n  flex-shrink: 0;\n}\n\n.json-text {\n  font-size: 0.875rem;\n  color: #374151;\n  font-weight: 500;\n}\n\n.remove-json-button {\n  width: 1.5rem;\n  height: 1.5rem;\n  border: none;\n  background-color: #ef4444;\n  color: white;\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1rem;\n  font-weight: bold;\n  transition: background-color 0.2s ease;\n  flex-shrink: 0;\n}\n\n.remove-json-button:hover {\n  background-color: #dc2626;\n}\n\n.json-error {\n  color: #ef4444;\n  font-size: 0.75rem;\n  margin: 0.5rem 0 0 0;\n}\n\n.json-success {\n  color: #059669;\n  font-size: 0.75rem;\n  margin: 0.5rem 0 0 0;\n  font-weight: 500;\n}\n\n.json-info {\n  color: #6b7280;\n  font-size: 0.75rem;\n  margin: 0.5rem 0 0 0;\n  line-height: 1.4;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding: 2rem;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.back-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background-color: #f3f4f6;\n  color: #374151;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.back-button:hover:not(:disabled) {\n  background-color: #e5e7eb;\n  border-color: #9ca3af;\n}\n\n.back-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.proceed-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 2rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.proceed-button:hover:not(:disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);\n}\n\n.proceed-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.spinner {\n  width: 1rem;\n  height: 1rem;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .individual-zip-upload {\n    padding: 1rem;\n  }\n  \n  .upload-header {\n    padding: 1.5rem;\n  }\n  \n  .upload-section {\n    padding: 1.5rem;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .back-button,\n  .proceed-button {\n    width: 100%;\n    justify-content: center;\n  }\n  \n  .drop-zone {\n    padding: 2rem 1rem;\n  }\n}\n", "/* Processing Page Styles */\n.processing-page {\n  min-height: 100vh;\n  background: #f9fafb;\n}\n\n/* Header */\n.processing-header {\n  background: #ffffff;\n  border-bottom: 1px solid #e5e7eb;\n  padding: 1rem 0;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.back-to-upload {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background: #f3f4f6;\n  border: 1px solid #d1d5db;\n  border-radius: 0.5rem;\n  color: #374151;\n  text-decoration: none;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.back-to-upload:hover {\n  background: #e5e7eb;\n  color: #111827;\n}\n\n.page-title h1 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #374151;\n  margin: 0 0 0.25rem 0;\n}\n\n.article-id {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n.zip-processor-notice {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.75rem;\n  color: #3b82f6;\n  margin: 0.25rem 0 0 0;\n  font-weight: 500;\n}\n\n.info-icon {\n  width: 0.875rem;\n  height: 0.875rem;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.75rem;\n}\n\n.complete-processing-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #10b981;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.complete-processing-button:hover {\n  background-color: #059669;\n}\n\n.complete-processing-button svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Main Content */\n.processing-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem 1.5rem;\n}\n\n/* Reference Input Section */\n.reference-input-section {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n  align-items: start;\n}\n\n.input-container {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 1rem;\n  padding: 2rem;\n}\n\n.input-header {\n  margin-bottom: 1.5rem;\n}\n\n.input-header h2 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #374151;\n  margin: 0 0 0.5rem 0;\n}\n\n.input-header p {\n  color: #6b7280;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n.manual-entry-icon {\n  margin-right: 0.5rem;\n}\n\n.manual-entry-notice {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-top: 0.75rem;\n  padding: 0.75rem;\n  background: #fef3c7;\n  border: 1px solid #f59e0b;\n  border-radius: 0.5rem;\n  color: #92400e;\n  font-size: 0.875rem;\n}\n\n.manual-entry-notice svg {\n  width: 1rem;\n  height: 1rem;\n  flex-shrink: 0;\n}\n\n/* Textarea */\n.textarea-container {\n  margin-bottom: 1.5rem;\n}\n\n.references-textarea {\n  width: 100%;\n  min-height: 300px;\n  padding: 1rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.75rem;\n  font-family: 'Courier New', monospace;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  resize: vertical;\n  transition: border-color 0.2s ease;\n}\n\n.references-textarea:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 1px #3b82f6;\n}\n\n.references-textarea::placeholder {\n  color: #9ca3af;\n  font-style: italic;\n}\n\n.textarea-info {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 0.5rem;\n  font-size: 0.75rem;\n  color: #6b7280;\n}\n\n/* Input Actions */\n.input-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n}\n\n.clear-button,\n.process-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.clear-button {\n  background: #f3f4f6;\n  color: #374151;\n  border: 1px solid #d1d5db;\n}\n\n.clear-button:hover:not(:disabled) {\n  background: #e5e7eb;\n}\n\n.process-button {\n  background: #3b82f6;\n  color: white;\n}\n\n.process-button:hover:not(:disabled) {\n  background: #1d4ed8;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.clear-button:disabled,\n.process-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* Help Section */\n.help-section {\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 1rem;\n  padding: 1.5rem;\n}\n\n.help-section h3 {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #374151;\n  margin: 0 0 1rem 0;\n}\n\n.help-steps {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.help-step {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 0.875rem;\n  color: #475569;\n}\n\n.step-number {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 1.5rem;\n  height: 1.5rem;\n  background: #3b82f6;\n  color: white;\n  border-radius: 50%;\n  font-size: 0.75rem;\n  font-weight: 600;\n  flex-shrink: 0;\n}\n\n/* PubMed Section */\n.pubmed-section {\n  background: #ffffff;\n  border-radius: 1rem;\n  padding: 1rem;\n  border: 1px solid #e5e7eb;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .reference-input-section {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n  \n  .help-section {\n    order: -1;\n  }\n}\n\n@media (max-width: 768px) {\n  .processing-content {\n    padding: 1rem;\n  }\n  \n  .input-container {\n    padding: 1.5rem;\n  }\n  \n  .header-content {\n    padding: 0 1rem;\n  }\n  \n  .header-left {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.75rem;\n  }\n  \n  .input-actions {\n    flex-direction: column;\n  }\n  \n  .clear-button,\n  .process-button {\n    justify-content: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .references-textarea {\n    min-height: 200px;\n    font-size: 0.75rem;\n  }\n  \n  .textarea-info {\n    flex-direction: column;\n    gap: 0.25rem;\n  }\n  \n  .help-step {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n    text-align: left;\n  }\n}\n\n/* Article ID Section Styles */\n.article-id-section {\n  margin-top: 20px;\n  padding: 16px;\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n}\n\n.article-id-label {\n  display: block;\n  font-size: 14px;\n  font-weight: 500;\n  color: #374151;\n  margin-bottom: 8px;\n}\n\n.article-id-input {\n  width: 100%;\n  padding: 10px 12px;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  font-size: 14px;\n  transition: border-color 0.2s, box-shadow 0.2s;\n}\n\n.article-id-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* Save Status Styles */\n.save-status {\n  margin-top: 12px;\n  padding: 12px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.save-status.success {\n  background: #dcfce7;\n  color: #166534;\n  border: 1px solid #bbf7d0;\n}\n\n.save-status.error {\n  background: #fef2f2;\n  color: #dc2626;\n  border: 1px solid #fecaca;\n}\n\n.save-status.info {\n  background: #dbeafe;\n  color: #1d4ed8;\n  border: 1px solid #bfdbfe;\n}\n\n/* Admin Save Section in Results */\n.admin-save-section {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n}\n\n.save-controls {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.article-id-input-results {\n  flex: 1;\n  padding: 10px 12px;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  font-size: 14px;\n  transition: border-color 0.2s, box-shadow 0.2s;\n}\n\n.article-id-input-results:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.save-button-results {\n  padding: 10px 20px;\n  background: #10b981;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  white-space: nowrap;\n}\n\n.save-button-results:hover:not(:disabled) {\n  background: #059669;\n}\n\n.save-button-results:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .processing-page {\n    padding: 0;\n  }\n\n  .header-content {\n    padding: 0 1rem;\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n\n  .header-left {\n    align-items: center;\n  }\n\n  .page-title h1 {\n    font-size: 1.25rem;\n  }\n\n  .header-actions {\n    justify-content: center;\n  }\n\n  .complete-processing-button {\n    width: 100%;\n    justify-content: center;\n  }\n\n  .processing-content {\n    padding: 1rem;\n  }\n\n  .input-container {\n    padding: 1.5rem;\n  }\n\n  .reference-textarea {\n    min-height: 200px;\n  }\n\n  .input-actions {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .process-button,\n  .clear-button {\n    width: 100%;\n    justify-content: center;\n  }\n}\n", ".admin-login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1rem;\n}\n\n.admin-login-card {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  padding: 2rem;\n  width: 100%;\n  max-width: 400px;\n  animation: slideUp 0.5s ease-out;\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.admin-login-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.admin-login-icon {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 1rem;\n}\n\n.admin-login-title {\n  font-size: 1.875rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 0.5rem;\n}\n\n.admin-login-subtitle {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.admin-login-form {\n  space-y: 1.5rem;\n}\n\n.form-group {\n  margin-bottom: 0rem;\n}\n\n.form-label {\n  display: block;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n\n.input-wrapper {\n  position: relative;\n}\n\n.input-icon {\n  position: absolute;\n  left: 0.75rem;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 1.25rem;\n  height: 1.25rem;\n  color: #9ca3af;\n  pointer-events: none;\n}\n\n.form-input {\n  width: 100%;\n  padding: 0.75rem 0.75rem 0.75rem 2.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n  background-color: white;\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.form-input:disabled {\n  background-color: #f9fafb;\n  color: #6b7280;\n  cursor: not-allowed;\n}\n\n.form-input::placeholder {\n  color: #9ca3af;\n}\n\n.error-message {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem;\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 8px;\n  color: #dc2626;\n  font-size: 0.875rem;\n  margin-bottom: 1rem;\n}\n\n.error-icon {\n  width: 1rem;\n  height: 1rem;\n  flex-shrink: 0;\n}\n\n.login-button {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.login-button:hover:not(:disabled) {\n  background-color: #2563eb;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\n}\n\n.login-button:disabled {\n  background-color: #9ca3af;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.button-icon {\n  width: 1rem;\n  height: 1rem;\n}\n\n.loading-spinner {\n  width: 1rem;\n  height: 1rem;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.admin-login-footer {\n  margin-top: 2rem;\n  text-align: center;\n}\n\n.footer-text {\n  font-size: 0.75rem;\n  color: #6b7280;\n  background-color: #f9fafb;\n  padding: 0.5rem;\n  border-radius: 6px;\n  border: 1px solid #e5e7eb;\n}\n\n/* Responsive design */\n@media (max-width: 480px) {\n  .admin-login-container {\n    padding: 0.5rem;\n  }\n  \n  .admin-login-card {\n    padding: 1.5rem;\n  }\n  \n  .admin-login-title {\n    font-size: 1.5rem;\n  }\n}\n", ".admin-dashboard {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  min-height: 100vh;\n  background-color: #f8fafc;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n}\n\n.dashboard-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0;\n}\n\n.dashboard-controls {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.date-range-select {\n  padding: 0.5rem 1rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  background-color: white;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: border-color 0.2s ease;\n}\n\n.date-range-select:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.dashboard-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  gap: 1rem;\n}\n\n.loading-spinner {\n  width: 2rem;\n  height: 2rem;\n  border: 3px solid #e5e7eb;\n  border-top: 3px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.error-banner {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 8px;\n  color: #dc2626;\n  margin-bottom: 2rem;\n}\n\n.error-icon {\n  width: 1.25rem;\n  height: 1.25rem;\n  flex-shrink: 0;\n}\n\n/* Statistics Grid */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.stat-card-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.stat-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.stat-card-title {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #6b7280;\n  margin: 0;\n}\n\n.stat-card-icon {\n  width: 1.5rem;\n  height: 1.5rem;\n}\n\n.stat-card-blue .stat-card-icon { color: #3b82f6; }\n.stat-card-green .stat-card-icon { color: #10b981; }\n.stat-card-purple .stat-card-icon { color: #8b5cf6; }\n.stat-card-orange .stat-card-icon { color: #f59e0b; }\n\n.stat-card-value {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0;\n}\n\n/* Chart Section */\n.chart-section {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.section-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 1.5rem 0;\n}\n\n.chart-container {\n  height: 200px;\n  display: flex;\n  align-items: end;\n  padding: 1rem 0;\n}\n\n.chart-bars {\n  display: flex;\n  align-items: end;\n  gap: 0.5rem;\n  width: 100%;\n  height: 100%;\n}\n\n.chart-bar-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  height: 100%;\n}\n\n.chart-bar {\n  background: linear-gradient(to top, #3b82f6, #60a5fa);\n  border-radius: 4px 4px 0 0;\n  width: 100%;\n  min-height: 4px;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.chart-bar:hover {\n  background: linear-gradient(to top, #2563eb, #3b82f6);\n}\n\n.chart-label {\n  font-size: 0.75rem;\n  color: #6b7280;\n  margin-top: 0.5rem;\n  text-align: center;\n}\n\n/* Articles Section */\n.articles-section {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.search-container {\n  position: relative;\n  width: 300px;\n}\n\n.search-icon {\n  position: absolute;\n  left: 0.75rem;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 1rem;\n  height: 1rem;\n  color: #9ca3af;\n  pointer-events: none;\n}\n\n.search-input {\n  width: 100%;\n  padding: 0.5rem 0.75rem 0.5rem 2.5rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  transition: border-color 0.2s ease;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.articles-table-container {\n  overflow-x: auto;\n}\n\n.articles-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.articles-table th {\n  text-align: left;\n  padding: 0.75rem;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 2px solid #e5e7eb;\n  background-color: #f9fafb;\n}\n\n.articles-table td {\n  padding: 0.75rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.articles-table tr:hover {\n  background-color: #f9fafb;\n}\n\n.article-id-cell {\n  font-weight: 500;\n}\n\n.article-id {\n  color: #3b82f6;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.875rem;\n}\n\n.reference-count-cell {\n  text-align: center;\n}\n\n.reference-count {\n  background-color: #dbeafe;\n  color: #1e40af;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.date-cell {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n/* Enhanced Articles Data Table Customization */\n.articles-data-table .article-id {\n  color: #3b82f6;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n/* Clickable Article ID */\n.clickable-article-id {\n  background: none;\n  border: none;\n  padding: 0;\n  cursor: pointer;\n  text-decoration: none;\n  transition: all 0.2s ease;\n  border-radius: 4px;\n  padding: 0.25rem 0.5rem;\n  margin: -0.25rem -0.5rem;\n}\n\n.clickable-article-id:hover {\n  background-color: #dbeafe;\n  color: #1e40af;\n  text-decoration: underline;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);\n}\n\n.clickable-article-id:active {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);\n}\n\n.article-id-cell {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.journal-name {\n  font-size: 0.75rem;\n  color: #6b7280;\n  font-style: italic;\n}\n\n/* Reference Summary */\n.reference-summary {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.reference-count-main {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.quality-breakdown {\n  display: flex;\n  gap: 0.25rem;\n}\n\n/* Quality Badges */\n.quality-badge {\n  padding: 0.125rem 0.375rem;\n  border-radius: 8px;\n  font-size: 0.625rem;\n  font-weight: 600;\n  text-align: center;\n  min-width: 1.5rem;\n}\n\n.quality-badge-high {\n  background-color: #dcfce7;\n  color: #166534;\n}\n\n.quality-badge-review {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.quality-badge-empty {\n  background-color: #f3f4f6;\n  color: #6b7280;\n}\n\n/* Quality Score Cell */\n.quality-score-cell {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.quality-score {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.quality-rates {\n  display: flex;\n  gap: 0.375rem;\n  font-size: 0.625rem;\n}\n\n.success-rate {\n  color: #059669;\n  font-weight: 500;\n}\n\n.review-rate {\n  color: #d97706;\n  font-weight: 500;\n}\n\n/* Source Breakdown */\n.source-breakdown {\n  display: flex;\n  flex-direction: column;\n  gap: 0.125rem;\n}\n\n.source-badge {\n  padding: 0.125rem 0.25rem;\n  border-radius: 4px;\n  font-size: 0.625rem;\n  font-weight: 500;\n  text-align: center;\n}\n\n.source-pubmed {\n  background-color: #dbeafe;\n  color: #1e40af;\n}\n\n.source-crossref {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.source-not-found {\n  background-color: #fee2e2;\n  color: #dc2626;\n}\n\n/* Status and Priority Badges */\n.status-cell {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.status-badge, .priority-badge {\n  padding: 0.125rem 0.375rem;\n  border-radius: 12px;\n  font-size: 0.625rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  text-align: center;\n}\n\n.status-badge-green {\n  background-color: #dcfce7;\n  color: #166534;\n}\n\n.status-badge-blue {\n  background-color: #dbeafe;\n  color: #1e40af;\n}\n\n.status-badge-yellow {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.status-badge-red {\n  background-color: #fee2e2;\n  color: #dc2626;\n}\n\n.status-badge-orange {\n  background-color: #fed7aa;\n  color: #c2410c;\n}\n\n.status-badge-gray {\n  background-color: #f3f4f6;\n  color: #6b7280;\n}\n\n.priority-badge-red {\n  background-color: #fee2e2;\n  color: #dc2626;\n}\n\n.priority-badge-yellow {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.priority-badge-green {\n  background-color: #dcfce7;\n  color: #166534;\n}\n\n.priority-badge-gray {\n  background-color: #f3f4f6;\n  color: #6b7280;\n}\n\n/* Processing Info */\n.processing-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.125rem;\n}\n\n.processed-by {\n  font-size: 0.75rem;\n  font-weight: 500;\n  color: #374151;\n}\n\n.processing-source {\n  font-size: 0.625rem;\n  color: #6b7280;\n  font-style: italic;\n}\n\n.articles-data-table .date-cell {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admin-dashboard {\n    padding: 1rem;\n  }\n\n  .dashboard-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .section-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n\n  .search-container {\n    width: 100%;\n  }\n\n  .chart-bars {\n    gap: 0.25rem;\n  }\n\n  .chart-label {\n    font-size: 0.625rem;\n  }\n}\n", ".admin-navigation {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1rem 0;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.nav-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 2rem;\n  margin-bottom: 1rem;\n}\n\n.nav-brand {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.brand-icon {\n  width: 2rem;\n  height: 2rem;\n  color: white;\n}\n\n.brand-text {\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: white;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.user-name {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.logout-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.5rem;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  color: white;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.logout-button:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.logout-icon {\n  width: 1rem;\n  height: 1rem;\n}\n\n.nav-menu {\n  display: flex;\n  justify-content: center;\n  gap: 2rem;\n  padding: 0 2rem;\n}\n\n.nav-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  border-radius: 8px;\n  text-decoration: none;\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 500;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n  border: 1px solid transparent;\n}\n\n.nav-item:hover {\n  color: white;\n  background: rgba(255, 255, 255, 0.1);\n  border-color: rgba(255, 255, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.nav-item-active {\n  color: white;\n  background: rgba(255, 255, 255, 0.15);\n  border-color: rgba(255, 255, 255, 0.3);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.nav-icon {\n  width: 1.25rem;\n  height: 1.25rem;\n  flex-shrink: 0;\n}\n\n.nav-label {\n  white-space: nowrap;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .nav-header {\n    padding: 0 1rem;\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .nav-menu {\n    flex-direction: column;\n    gap: 0.5rem;\n    padding: 0 1rem;\n  }\n  \n  .nav-item {\n    justify-content: center;\n    padding: 1rem;\n  }\n  \n  .brand-text {\n    font-size: 1rem;\n  }\n  \n  .user-info {\n    order: -1;\n  }\n}\n\n@media (max-width: 480px) {\n  .nav-header {\n    padding: 0 0.5rem;\n  }\n  \n  .nav-menu {\n    padding: 0 0.5rem;\n  }\n  \n  .nav-item {\n    padding: 0.75rem;\n    font-size: 0.8rem;\n  }\n  \n  .nav-label {\n    display: none;\n  }\n  \n  .nav-icon {\n    width: 1.5rem;\n    height: 1.5rem;\n  }\n}\n", ".admin-upload-page {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.upload-page-header {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n\n.upload-page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 0.5rem;\n}\n\n.upload-page-description {\n  color: #6b7280;\n  font-size: 1rem;\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n.upload-mode-selector {\n  margin-bottom: 2rem;\n}\n\n.mode-options {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.mode-option {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.5rem;\n  background: white;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-align: left;\n}\n\n.mode-option:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.mode-option.active {\n  border-color: #3b82f6;\n  background: #eff6ff;\n  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);\n}\n\n.mode-icon {\n  font-size: 2rem;\n  flex-shrink: 0;\n}\n\n.mode-content h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 0.25rem 0;\n}\n\n.mode-content p {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n.mode-option.active .mode-content h3 {\n  color: #1d4ed8;\n}\n\n.mode-option.active .mode-content p {\n  color: #3b82f6;\n}\n\n.upload-page-content {\n  background: white;\n  border-radius: 12px;\n  padding: 2rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admin-upload-page {\n    padding: 1rem;\n  }\n  \n  .upload-page-content {\n    padding: 1rem;\n  }\n  \n  .upload-page-title {\n    font-size: 1.5rem;\n  }\n}\n", ".admin-journals-page {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.journals-page-header {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n\n.journals-page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 0.5rem;\n}\n\n.journals-page-description {\n  color: #6b7280;\n  font-size: 1rem;\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n.journals-page-content {\n  background: white;\n  border-radius: 12px;\n  padding: 2rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admin-journals-page {\n    padding: 1rem;\n  }\n  \n  .journals-page-content {\n    padding: 1rem;\n  }\n  \n  .journals-page-title {\n    font-size: 1.5rem;\n  }\n}\n", "/* User Management Styles - Consistent with Admin Dashboard */\n.user-management {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  min-height: 100vh;\n  background-color: #f8fafc;\n}\n\n/* Header - Consistent with Dashboard */\n.user-management-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n}\n\n.header-content h1 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.header-icon {\n  width: 2rem;\n  height: 2rem;\n  color: #3b82f6;\n}\n\n.header-content p {\n  margin: 0.5rem 0 0 0;\n  color: #6b7280;\n  font-size: 1rem;\n}\n\n/* Access Denied */\n.access-denied {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #6b7280;\n}\n\n.access-denied-icon {\n  width: 4rem;\n  height: 4rem;\n  color: #ef4444;\n  margin-bottom: 1rem;\n}\n\n.access-denied h2 {\n  margin: 0 0 1rem 0;\n  color: #1f2937;\n}\n\n/* Alerts */\n.alert {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1.5rem;\n  position: relative;\n}\n\n.alert-error {\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n}\n\n.alert-success {\n  background-color: #f0fdf4;\n  border: 1px solid #bbf7d0;\n  color: #16a34a;\n}\n\n.alert-icon {\n  width: 1.25rem;\n  height: 1.25rem;\n  flex-shrink: 0;\n}\n\n.alert-close {\n  position: absolute;\n  right: 0.75rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  font-size: 1.25rem;\n  cursor: pointer;\n  color: inherit;\n  opacity: 0.7;\n}\n\n.alert-close:hover {\n  opacity: 1;\n}\n\n/* Filters */\n.filters-section {\n  background: #f9fafb;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.5rem;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.filters-row {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr;\n  gap: 1.5rem;\n  align-items: end;\n}\n\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.filter-group label {\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.875rem;\n}\n\n.filter-input,\n.filter-select {\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  transition: border-color 0.2s;\n}\n\n.filter-input:focus,\n.filter-select:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* Users Table */\n.users-table-container {\n  background: white;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.5rem;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.users-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.users-table th {\n  background: #f9fafb;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n  font-size: 0.875rem;\n}\n\n.users-table td {\n  padding: 1rem;\n  border-bottom: 1px solid #f3f4f6;\n  vertical-align: middle;\n}\n\n.user-row:hover {\n  background-color: #f9fafb;\n}\n\n.user-row.user-inactive {\n  opacity: 0.6;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.username {\n  font-weight: 500;\n  color: #1f2937;\n}\n\n.super-admin-icon {\n  width: 1rem;\n  height: 1rem;\n  color: #f59e0b;\n}\n\n/* Role Badges */\n.role-badge {\n  display: inline-block;\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.role-super-admin {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.role-admin {\n  background-color: #dbeafe;\n  color: #1e40af;\n}\n\n.role-coordinator {\n  background-color: #e0e7ff;\n  color: #3730a3;\n}\n\n.role-te {\n  background-color: #d1fae5;\n  color: #065f46;\n}\n\n.role-ce {\n  background-color: #fce7f3;\n  color: #be185d;\n}\n\n.role-default {\n  background-color: #f3f4f6;\n  color: #374151;\n}\n\n/* Status Badges */\n.status-badge {\n  display: inline-block;\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.status-active {\n  background-color: #d1fae5;\n  color: #065f46;\n}\n\n.status-inactive {\n  background-color: #fee2e2;\n  color: #991b1b;\n}\n\n/* Action Buttons */\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2rem;\n  height: 2rem;\n  border: none;\n  border-radius: 0.375rem;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.action-btn svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.edit-btn {\n  background-color: #dbeafe;\n  color: #1e40af;\n}\n\n.edit-btn:hover {\n  background-color: #bfdbfe;\n}\n\n.activate-btn {\n  background-color: #d1fae5;\n  color: #065f46;\n}\n\n.activate-btn:hover {\n  background-color: #a7f3d0;\n}\n\n.deactivate-btn {\n  background-color: #fee2e2;\n  color: #991b1b;\n}\n\n.deactivate-btn:hover {\n  background-color: #fecaca;\n}\n\n.password-btn {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.password-btn:hover {\n  background-color: #fde68a;\n}\n\n/* Loading State */\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  color: #6b7280;\n}\n\n.loading-icon {\n  width: 2rem;\n  height: 2rem;\n  margin-bottom: 1rem;\n}\n\n.spinning {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Pagination */\n.pagination {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.5rem;\n  background: #f9fafb;\n  border-top: 1px solid #e5e7eb;\n}\n\n.pagination-btn {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.375rem;\n  background: white;\n  color: #374151;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.pagination-btn:hover:not(:disabled) {\n  background-color: #f3f4f6;\n  border-color: #9ca3af;\n}\n\n.pagination-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.pagination-btn svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.pagination-info {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n/* Modal Styles */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 1rem;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 0.75rem;\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n  width: 100%;\n  max-width: 600px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.modal-small {\n  max-width: 400px;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.modal-header h2 {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.modal-icon {\n  width: 1.25rem;\n  height: 1.25rem;\n  color: #3b82f6;\n}\n\n.modal-close {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2rem;\n  height: 2rem;\n  border: none;\n  border-radius: 0.375rem;\n  background: #f3f4f6;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.modal-close:hover {\n  background: #e5e7eb;\n  color: #374151;\n}\n\n.modal-close svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.modal-body {\n  padding: 1.5rem;\n  color: #6b7280;\n}\n\n.modal-form {\n  padding: 0rem;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-group label {\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.875rem;\n}\n\n.form-group input,\n.form-group select {\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  transition: border-color 0.2s;\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.form-group input.error,\n.form-group select.error {\n  border-color: #ef4444;\n}\n\n.form-group input:disabled,\n.form-group select:disabled {\n  background-color: #f9fafb;\n  color: #6b7280;\n  cursor: not-allowed;\n}\n\n.error-text {\n  color: #ef4444;\n  font-size: 0.75rem;\n  margin-top: 0.25rem;\n}\n\n.form-help {\n  color: #6b7280;\n  font-size: 0.75rem;\n  margin-top: 0.25rem;\n}\n\n.checkbox-label {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  cursor: pointer;\n}\n\n.checkbox-label input[type=\"checkbox\"] {\n  width: auto;\n  margin: 0;\n}\n\n.checkbox-text {\n  font-weight: 500;\n  color: #374151;\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.75rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e5e7eb;\n  background: #f9fafb;\n}\n\n/* Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.btn-icon {\n  width: 1rem;\n  height: 1rem;\n}\n\n.btn-primary {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #2563eb;\n}\n\n.btn-secondary {\n  background-color: #f3f4f6;\n  color: #374151;\n  border: 1px solid #d1d5db;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: #e5e7eb;\n}\n\n.btn-danger {\n  background-color: #ef4444;\n  color: white;\n}\n\n.btn-danger:hover:not(:disabled) {\n  background-color: #dc2626;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .user-management {\n    padding: 1rem;\n  }\n\n  .user-management-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n\n  .filters-row {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .users-table-container {\n    overflow-x: auto;\n  }\n\n  .users-table {\n    min-width: 800px;\n  }\n\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n\n  .modal-content {\n    margin: 1rem;\n    max-width: none;\n  }\n\n  .pagination {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n\n  .action-buttons {\n    flex-wrap: wrap;\n  }\n}\n"], "names": [], "sourceRoot": ""}