import React from "react";

const ArticleIdSaveBox = ({
  articleId,
  setArticleId,
  handleSaveClick,
  saveStatus,
  adminMode,
  loading = false,
  hasData = false,
}) => (
  <>
    <div
      style={{
        margin: "16px 0",
        padding: 8,
        // background: "#f8fafc",
        borderRadius: 8,
        display: "flex",
        alignItems: "center",
      }}
    >
      {adminMode && (
        <>
          <label style={{ fontWeight: 500, marginRight: 8 }}>
            <span>Article ID:</span>
            <input
              type="text"
              value={articleId}
              onChange={(e) => setArticleId(e.target.value)}
              placeholder="PMC123456"
              disabled
              style={{
                marginLeft: 8,
                padding: 4,
                borderRadius: 4,
                border: "1px solid #ccc",
                width: 200,
              }}
            />
          </label>
          <button
            onClick={handleSaveClick}
            disabled={!articleId || loading || !hasData}
            style={{
              marginLeft: 16,
              padding: "6px 18px",
              borderRadius: 4,
              background: (!articleId || loading || !hasData) ? "#9ca3af" : "#2563eb",
              color: "white",
              border: "none",
              fontWeight: 500,
              cursor: (!articleId || loading || !hasData) ? "not-allowed" : "pointer",
              opacity: (!articleId || loading || !hasData) ? 0.6 : 1,
            }}
            title={
              !articleId ? "Article ID required" :
              loading ? "Processing references..." :
              !hasData ? "No references to save" :
              "Save references to database"
            }
          >
            Save References
          </button>
          <span style={{ marginLeft: 16, color: "#888", fontSize: 13 }}>
            (Required to tag references in database)
          </span>
        </>
      )}
    </div>
    {saveStatus && (
      <div
        style={{
          margin: "8px 0 0 0",
          color: saveStatus.includes("success")
            ? "green"
            : saveStatus.includes("Saving")
            ? "#2563eb"
            : "red",
          fontWeight: 500,
        }}
      >
        {saveStatus}
      </div>
    )}
  </>
);

export default ArticleIdSaveBox;
