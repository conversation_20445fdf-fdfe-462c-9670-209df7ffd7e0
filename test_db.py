#!/usr/bin/env python3
import sqlite3
import time

def test_database():
    print('=== DATABASE CONNECTION TEST ===')
    
    try:
        print('1. Connecting to database...')
        start = time.time()
        conn = sqlite3.connect('article_references.db')
        print(f'   ✅ Connected in {time.time() - start:.3f}s')

        print('2. Testing User table query...')
        start = time.time()
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM users')
        count = cursor.fetchone()[0]
        print(f'   ✅ User count query completed in {time.time() - start:.3f}s: {count} users')

        print('3. Testing specific user query...')
        start = time.time()
        cursor.execute('SELECT username, role, is_active FROM users WHERE username = ? AND is_active = 1', ('pratik',))
        result = cursor.fetchone()
        print(f'   ✅ Specific user query completed in {time.time() - start:.3f}s: {result}')

        print('4. Testing password hash query...')
        start = time.time()
        cursor.execute('SELECT password_hash FROM users WHERE username = ?', ('pratik',))
        hash_result = cursor.fetchone()
        print(f'   ✅ Password hash query completed in {time.time() - start:.3f}s: {"Found" if hash_result else "Not found"}')

        print('5. Testing table schema...')
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        print(f'   ✅ User table has {len(columns)} columns: {[col[1] for col in columns]}')

        conn.close()
        print('✅ Database test completed successfully')
        return True
        
    except Exception as e:
        print(f'❌ Database test failed: {str(e)}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_database()
