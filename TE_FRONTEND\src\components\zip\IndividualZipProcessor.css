/* Individual ZIP Processor Styles */
.zip-processor-container {
  min-height: 100vh;
  background-color: #f9fafb;
}

.zip-processor-header {
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  padding: .5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-to-queue-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-to-queue-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.back-to-queue-button svg {
  width: 1.25rem;
  height: 1.25rem;
}

.zip-info {
  flex: 1;
}

.zip-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.zip-icon {
  font-size: 1.25rem;
}

.zip-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.process-refs-button,
.mark-processed-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.process-refs-button {
  background-color: #3b82f6;
  color: white;
}

.process-refs-button:hover {
  background-color: #2563eb;
}

.mark-processed-button {
  background-color: #10b981;
  color: white;
}

.mark-processed-button:hover {
  background-color: #059669;
}

.process-refs-button svg,
.mark-processed-button svg {
  width: 1rem;
  height: 1rem;
}

/* Processing Indicator */
.processing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 0.5rem;
  margin: 1rem 2rem;
  color: #1d4ed8;
}

.processing-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #bfdbfe;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main Content */
.zip-processor-content {
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
}

/* Loading States */
.zip-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
}

.zip-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.zip-loading p {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

/* Error States */
.zip-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.zip-error-state h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 0.5rem;
}

.zip-error-state p {
  color: #6b7280;
  margin-bottom: 2rem;
  max-width: 400px;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.reupload-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reupload-button:hover {
  background-color: #2563eb;
}

.reupload-button svg {
  width: 1rem;
  height: 1rem;
}

.zip-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #dc2626;
}

.zip-error svg {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
}

.zip-error p {
  font-size: 1rem;
  margin: 0;
}

/* Step Navigation */
.step-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.step-item.active {
  background-color: #3b82f6;
  color: white;
}

.step-item.completed {
  background-color: #10b981;
  color: white;
}

.step-item.inactive {
  background-color: #f3f4f6;
  color: #6b7280;
}

.step-separator {
  width: 1rem;
  height: 1px;
  background-color: #d1d5db;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  position: sticky;
  bottom: 0;
}

.action-left,
.action-right {
  display: flex;
  gap: 0.75rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button.primary {
  background-color: #3b82f6;
  color: white;
}

.action-button.primary:hover {
  background-color: #2563eb;
}

.action-button.secondary {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.action-button.secondary:hover {
  background-color: #e5e7eb;
}

.action-button.success {
  background-color: #10b981;
  color: white;
}

.action-button.success:hover {
  background-color: #059669;
}

.action-button svg {
  width: 1rem;
  height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .zip-processor-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-left {
    align-items: center;
  }
  
  .zip-title {
    font-size: 1.25rem;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .zip-processor-content {
    padding: 1rem;
  }
  
  .processing-indicator {
    margin: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 1rem;
  }
  
  .action-left,
  .action-right {
    width: 100%;
    justify-content: center;
  }
}

/* Validation Error Styles */
.validation-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
  text-align: center;
  background-color: white;
  border-radius: 0.75rem;
  margin: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.validation-error .error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.validation-error h2 {
  color: #dc2626;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.validation-details {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
  max-width: 600px;
  width: 100%;
}

.validation-details p {
  margin-bottom: 0.5rem;
  color: #374151;
}

.validation-details strong {
  color: #111827;
  font-weight: 600;
}

.error-explanation {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #fecaca;
}

.error-explanation ul {
  margin-top: 0.5rem;
  padding-left: 1.5rem;
  color: #6b7280;
}

.error-explanation li {
  margin-bottom: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.validation-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.proceed-anyway-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: #f59e0b;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.proceed-anyway-button:hover {
  background-color: #d97706;
  transform: translateY(-1px);
}

.proceed-anyway-button:active {
  transform: translateY(0);
}
