#!/usr/bin/env python3
"""
Database initialization script for EDITINK
Creates all tables and optionally migrates data from journalMap.json
"""
import os
import sys
from datetime import datetime

# Add the current directory to Python path to import app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db

def init_database():
    """
    Initialize the database with all required tables
    """
    print("🚀 Initializing EDITINK database...")
    
    try:
        with app.app_context():
            # Drop all tables (for fresh start)
            print("🗑️  Dropping existing tables...")
            db.drop_all()
            
            # Create all tables
            print("🏗️  Creating database tables...")
            db.create_all()
            
            print("✅ Database tables created successfully!")
            
            # Print table information
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            print(f"\n📊 Created tables:")
            for table in tables:
                columns = inspector.get_columns(table)
                print(f"   - {table} ({len(columns)} columns)")
                for col in columns:
                    print(f"     • {col['name']} ({col['type']})")
            
            return True
            
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        return False

def create_sample_data():
    """
    Create some sample journal data for testing
    """
    print("\n📝 Creating sample journal data...")
    
    try:
        from app import JournalAbbreviation
        
        with app.app_context():
            sample_journals = [
                {
                    'full_name': 'Nature',
                    'abbreviation': 'Nature',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 'init_script'
                },
                {
                    'full_name': 'Science',
                    'abbreviation': 'Science',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 'init_script'
                },
                {
                    'full_name': 'Cell',
                    'abbreviation': 'Cell',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 'init_script'
                },
                {
                    'full_name': 'The New England Journal of Medicine',
                    'abbreviation': 'N. Engl. J. Med.',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 'init_script'
                },
                {
                    'full_name': 'Journal of the American Medical Association',
                    'abbreviation': 'JAMA',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 'init_script'
                }
            ]
            
            for journal_data in sample_journals:
                journal = JournalAbbreviation(**journal_data)
                db.session.add(journal)
            
            db.session.commit()
            
            print(f"✅ Created {len(sample_journals)} sample journal entries")
            return True
            
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return False

def verify_setup():
    """
    Verify the database setup is working correctly
    """
    print("\n🔍 Verifying database setup...")
    
    try:
        from app import JournalAbbreviation, ArticleReferences
        
        with app.app_context():
            # Test journal table
            journal_count = JournalAbbreviation.query.count()
            print(f"   - Journal abbreviations table: {journal_count} entries")
            
            # Test article references table
            article_count = ArticleReferences.query.count()
            print(f"   - Article references table: {article_count} entries")
            
            # Test a simple query
            sample_journal = JournalAbbreviation.query.first()
            if sample_journal:
                print(f"   - Sample journal: {sample_journal.full_name} -> {sample_journal.abbreviation}")
            
            print("✅ Database verification completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Error verifying database: {e}")
        return False

if __name__ == "__main__":
    print("🎯 EDITINK Database Initialization")
    print("=" * 50)
    
    success = True
    
    # Initialize database
    if not init_database():
        success = False
    
    # Create sample data
    if success and not create_sample_data():
        success = False
    
    # Verify setup
    if success and not verify_setup():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Database initialization completed successfully!")
        print("\nNext steps:")
        print("1. Run the migration script: python migrate_journals.py")
        print("2. Start the Flask server: python app.py")
        print("3. Access admin interface: http://localhost:3000?admin=true")
    else:
        print("💥 Database initialization failed!")
        sys.exit(1)
