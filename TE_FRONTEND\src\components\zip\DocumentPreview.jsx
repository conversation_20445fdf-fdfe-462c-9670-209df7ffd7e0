import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import mammoth from 'mammoth';
import { useArticle } from '../../context/ArticleContext';
import { useRole } from '../../context/AuthContext';
import { Icons } from '../common';
import ArticleMetadataPanel from './ArticleMetadataPanel';
import RaiseQueryButton from './RaiseQueryButton';
import './DocumentPreview.css';

const DocumentPreview = ({ file, onBack, onCopyReferences, zipId, zipFile, authors, articleId, onZipModified, onValidationQuerySent }) => {
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedText, setSelectedText] = useState('');
  const [showAuthorsSidebar, setShowAuthorsSidebar] = useState(true);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const navigate = useNavigate();
  const { setExtractedContent, articleData } = useArticle();
  const { isAdmin } = useRole();

  useEffect(() => {
    if (file) {
      loadFileContent();
    }
  }, [file]);

  // Handle scroll events to show/hide scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setShowScrollToTop(scrollTop > 300); // Show button after scrolling 300px
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const loadFileContent = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading file content for:', file.name);
      console.log('File object:', file);
      console.log('ZipEntry exists:', !!file.zipEntry);
      console.log('ZipEntry type:', typeof file.zipEntry);

      if (!file.zipEntry) {
        throw new Error('No zipEntry found in file object');
      }

      const arrayBuffer = await file.zipEntry.async('arraybuffer');
      console.log('ArrayBuffer loaded, size:', arrayBuffer.byteLength);
      
      if (file.type === 'docx' || file.type === 'doc') {
        try {
          const result = await mammoth.convertToHtml({ arrayBuffer });
          setContent(result.value);
          setExtractedContent(result.value);

          // Log any warnings from mammoth
          if (result.messages && result.messages.length > 0) {
            console.warn('Mammoth conversion warnings:', result.messages);
          }
        } catch (docError) {
          console.error('Error converting document:', docError);
          // Fallback: try to extract as plain text
          try {
            const text = new TextDecoder('utf-8', { ignoreBOM: true }).decode(arrayBuffer);
            const cleanText = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '').trim();
            if (cleanText.length > 100) {
              setContent(`<pre>${cleanText}</pre>`);
              setExtractedContent(cleanText);
              console.log('Fallback: Extracted as plain text');
            } else {
              throw new Error('Unable to extract meaningful content');
            }
          } catch (fallbackError) {
            throw new Error(`Cannot process ${file.type.toUpperCase()} file. Please try converting to .docx format first.`);
          }
        }
      } else if (file.type === 'txt') {
        const text = new TextDecoder().decode(arrayBuffer);
        setContent(`<pre>${text}</pre>`);
        setExtractedContent(text);
      } else {
        setError(`Unsupported file type: ${file.type.toUpperCase()}. Supported formats: .docx, .doc, .txt`);
      }
    } catch (err) {
      console.error('Error loading file content:', err);
      console.error('Error details:', err.message);
      console.error('File object at error:', file);

      let errorMessage = 'Failed to load file content';
      if (err.message.includes('zipEntry')) {
        errorMessage = 'File data is not properly loaded. Please try going back and selecting the file again.';
      } else if (err.message.includes('async')) {
        errorMessage = 'Unable to extract file from ZIP archive. The file may be corrupted.';
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTextSelection = () => {
    const selection = window.getSelection();
    const text = selection.toString().trim();
    setSelectedText(text);
  };

  const processSelectedText = () => {
    if (selectedText) {
      // Check multiple indicators that this is an admin session
      const hasAdminParam = window.location.search.includes('admin=true');
      const hasAdminPath = window.location.pathname.includes('/admin');
      const hasAdminSession = sessionStorage.getItem('adminContext') === 'true';
      const referrerIsAdmin = document.referrer.includes('/admin');

      // Use admin route if any admin indicator is present OR if we're clearly in admin workflow
      const useAdminRoute = isAdmin || hasAdminParam || hasAdminPath || hasAdminSession || referrerIsAdmin ||
                           window.location.href.includes('admin') || // Current URL has admin
                           document.referrer.includes('admin');      // Came from admin page

      const processRoute = useAdminRoute ? '/admin/process' : '/process';

      navigate(processRoute, {
        state: {
          references: selectedText,
          articleId: articleData?.articleId,
          fromZipProcessor: true,
          manualEntry: false,
          isAdminContext: useAdminRoute, // Pass admin context explicitly
          zipId: zipId // Pass the zipId for auto-completion
        }
      });
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="document-preview-container">
      {/* Header */}
      <div className="preview-header">
        <div className="preview-title-section">
          <button onClick={onBack} className="back-button">
            <Icons.ChevronLeftIcon />
          </button>
          <div className="preview-file-info">
            <h2 className="preview-title">
              <span className="file-icon">{file.icon}</span>
              {file.name}
            </h2>
            <p className="preview-subtitle">
              {file.type.toUpperCase()} • Document Preview
            </p>
          </div>
        </div>

        <div className="preview-actions">
          {/* Raise Query Button */}
          <RaiseQueryButton
            queryType="general"
            articleId={articleId}
            zipFile={zipFile}
            buttonText="Raise Query"
            buttonIcon="📧"
            variant="primary"
            size="small"
            onQuerySent={(id) => {
              console.log('Query sent for article:', id);
              // Mark ZIP with validationQuerySent flag for Leena assignment
              if (onValidationQuerySent) {
                onValidationQuerySent(id);
              }
            }}
          />

          {authors && authors.length > 0 && (
            <button
              onClick={() => setShowAuthorsSidebar(!showAuthorsSidebar)}
              className="toggle-sidebar-button"
              title={showAuthorsSidebar ? "Hide Authors" : "Show Authors"}
            >
              <Icons.SettingsIcon />
            </button>
          )}


          {selectedText && (
            <button onClick={processSelectedText} className="process-button selected">
              <Icons.ArrowRightIcon />
              Process Selected
            </button>
          )}
        </div>
      </div>

      {/* Main Content with Sidebar Layout */}
      <div className={`preview-with-sidebar ${!showAuthorsSidebar ? 'sidebar-hidden' : ''}`}>
        {/* Left Side - Document Content */}
        <div className="preview-content-wrapper">
        {isLoading && (
          <div className="preview-loading">
            <div className="loading-spinner"></div>
            <p>Loading document...</p>
          </div>
        )}

        {error && (
          <div className="preview-error">
            <Icons.ExclamationIcon />
            <div>
              <h3>Error Loading Document</h3>
              <p>{error}</p>
              {error.includes('.DOC') && (
                <div className="error-suggestion">
                  💡 <strong>Tip:</strong> For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.
                </div>
              )}
            </div>
          </div>
        )}

        {!isLoading && !error && content && (
          <div className="preview-content-container">
            <div
              className="preview-content"
              dangerouslySetInnerHTML={{ __html: content }}
              onMouseUp={handleTextSelection}
              onKeyUp={handleTextSelection}
            />
          </div>
        )}

        {/* Instructions */}
        <div className="preview-instructions">
          <div className="instruction-item">
            <span className="instruction-icon">🖱️</span>
            <span>Select text to process specific sections</span>
          </div>
          <div className="instruction-item">
            <span className="instruction-icon">🔄</span>
            <span>Processing will take you directly to the reference processing screen</span>
          </div>
        </div>

        {/* Selection Info */}
        {selectedText && (
          <div className="selection-info">
            <div className="selection-header">
              <span className="selection-icon">✂️</span>
              <span>Selected Text ({selectedText.length} characters)</span>
            </div>
            <div className="selection-preview">
              {selectedText.substring(0, 200)}
              {selectedText.length > 200 && '...'}
            </div>
          </div>
        )}
      </div>

      {/* Right Side - System Authors Sidebar */}
      {showAuthorsSidebar && (
        <div className="authors-sidebar">
          {authors && authors.length > 0 && (
            <ArticleMetadataPanel
              articleId={articleId}
              authors={authors}
              skipApiCall={true}
              alwaysExpanded={true}
              zipFiles={articleData?.zipFiles || []}
              onZipModified={onZipModified}
              onQueryCreated={() => {
                console.log('Query created for article:', articleId);
              }}
            />
          )}
        </div>
      )}

      {/* Scroll to Top Button */}
      {showScrollToTop && (
        <button
          onClick={scrollToTop}
          className="scroll-to-top-button"
          title="Scroll to top"
          aria-label="Scroll to top"
        >
          <Icons.ChevronUpIcon />
        </button>
      )}
    </div>
    </div>
  );
};

export default DocumentPreview;
