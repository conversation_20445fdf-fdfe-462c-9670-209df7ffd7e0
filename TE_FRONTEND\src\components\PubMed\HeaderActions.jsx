import React from "react";
import { getCurrentRole } from "../../utils/appUtils";

/**
 * HeaderActions component for PubMed interface
 * Contains buttons for toggling diff view, downloading documents, and showing statistics
 */
const HeaderActions = ({
  isDiffViewerOpen,
  toggleDiffViewer,
  showStatistics,
  toggleStatistics,
  onDownloadDocx,
  onDownloadExcel,
}) => {
  const currentRole = getCurrentRole();
  const isAdminMode = currentRole === 'admin';
  return (
    <div className="action-icons">
      <button
        className={`icon-button${isDiffViewerOpen ? ' active' : ''}`}
        onClick={toggleDiffViewer}
        title={isDiffViewerOpen ? "Hide Diff" : "Show Diff"}
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
          />
        </svg>
      </button>
      <button
        className="icon-button"
        onClick={onDownloadDocx}
        title="Download DOCX"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      </button>
      {isAdminMode && (
        <button
          className="icon-button"
          onClick={onDownloadExcel}
          title="Download Report"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </button>
      )}
      <button
        className={`icon-button${showStatistics ? ' active' : ''}`}
        onClick={toggleStatistics}
        title={showStatistics ? 'Hide Statistics' : 'Show Statistics'}
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      </button>
    </div>
  );
};

export default HeaderActions;