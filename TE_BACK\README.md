# 🚀 EDITINK Backend - AI-Powered Reference Enhancement & TE Automation API

## 📋 Overview

EDITINK Backend is a comprehensive Flask-based API server that provides:

### Core Features
- **Journal Abbreviation Management**: PostgreSQL-backed journal database with fuzzy matching
- **Article Reference Processing**: AI-powered reference enhancement and validation
- **User Management**: Role-based authentication with JWT tokens
- **TE Automation**: Complete Technical Editor workflow automation
- **ZIP Processing**: Automated article processing from ZIP files
- **Google Drive Integration**: Automatic file sharing and collaboration
- **Email Notifications**: SMTP-based notification system
- **Analytics & Reporting**: Comprehensive statistics and reporting

### Advanced Capabilities
- **Multi-Database Search**: PubMed, CrossRef, Google Scholar integration
- **AI Enhancement**: OpenAI GPT for reference improvement
- **Manuscript Validation**: Intelligent ZIP file structure validation
- **Batch Processing**: Efficient handling of multiple articles
- **Real-time Queue Management**: Live status tracking and updates
- **RESTful API**: Complete REST API for frontend integration

## 🏗️ Architecture

### System Components
- **Flask Application**: Main API server with blueprint organization
- **Database Layer**: SQLAlchemy ORM with SQLite/PostgreSQL support
- **Service Layer**: Modular services for external integrations
- **Authentication**: JWT-based auth with role-based access control
- **File Processing**: Document parsing and ZIP handling
- **External APIs**: Integration with multiple external services

### Database Schema
- **users**: User accounts with role-based permissions (SuperAdmin, Admin, Coordinator, TE, CE)
- **article_files**: Article metadata and processing status
- **zip_queue**: ZIP file processing queue and status tracking
- **te_assignments**: TE assignment batches with Google Drive links
- **journals**: Journal abbreviation database with fuzzy matching
- **article_references**: Processed reference storage with quality scores
- **daily_statistics**: Analytics and reporting data

## 🗄️ Database

### Development (Default)
- **Type**: SQLite
- **File**: `article_references.db`
- **Setup**: Automatic initialization via `init_db.py`
- **Benefits**: No external dependencies, easy setup

### Production (Optional)
- **Type**: PostgreSQL
- **Configuration**: Via `DATABASE_URL` environment variable
- **Benefits**: Better performance, concurrent access, scalability

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- pip (Python package manager)
- Virtual environment (recommended)

### Installation & Setup

```bash
# 1. Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 2. Install dependencies
pip install -r requirements.txt

# 3. Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# 4. Initialize database
python init_db.py

# 5. Create super admin user
python create_super_admin.py

# 6. Start development server
python app.py
```

### Environment Configuration

Create a `.env` file with the following variables:

```bash
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Google Drive Integration (for TE automation)
GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE=service_account.json
GOOGLE_DRIVE_PARENT_FOLDER_ID=your_drive_folder_id

# Email Service Configuration
SMTP_SERVER=smtpout.secureserver.net
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=True

# Flask Configuration
SECRET_KEY=your-super-secure-secret-key
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration (optional - SQLite used by default)
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name
```
## 🔧 API Endpoints

### Authentication & User Management
```
POST   /api/auth/login              # User authentication
POST   /api/auth/logout             # User logout
GET    /api/admin/users             # List all users (Admin only)
POST   /api/admin/users             # Create new user
PUT    /api/admin/users/:id         # Update user details
DELETE /api/admin/users/:id         # Delete user (SuperAdmin only)
```

### Reference Processing
```
POST   /api/upload                  # Upload and process documents
GET    /api/references              # Retrieve processed references
POST   /api/enhance                 # AI enhancement of references
POST   /api/search                  # Search external databases
GET    /api/autocomplete            # Article ID autocomplete
POST   /api/save-references         # Save references to database
GET    /api/saved-references        # Retrieve saved references
```

### Journal Management
```
GET    /api/journals                # List all journals
POST   /api/journals                # Create new journal entry
PUT    /api/journals/:id            # Update journal entry
DELETE /api/journals/:id            # Delete journal entry
POST   /api/journals/search         # Search journal abbreviations
POST   /api/journals/bulk-import    # Bulk import journals
```

### TE Automation & ZIP Processing
```
POST   /api/admin/zip-upload        # Upload ZIP files for processing
GET    /api/admin/zip-queue         # Get ZIP processing queue
POST   /api/admin/zip-queue/:id/process  # Process specific ZIP
PUT    /api/admin/zip-queue/:id     # Update ZIP status
DELETE /api/admin/zip-queue/:id     # Delete ZIP from queue
GET    /api/admin/te-assignments    # List TE assignments
POST   /api/admin/te-assignments    # Create new TE assignment
GET    /api/admin/available-tes     # Get available Technical Editors
PUT    /api/admin/te-assignments/:id # Update assignment status
```

### Analytics & Reporting
```
GET    /api/admin/statistics        # Comprehensive system statistics
GET    /api/admin/daily-stats       # Daily processing statistics
GET    /api/admin/user-activity     # User activity logs
GET    /api/health                  # System health check
```

## 🛠️ Technology Stack

### Core Technologies
- **Flask 3.0**: Modern Python web framework
- **SQLAlchemy 2.0**: Advanced ORM with async support
- **Flask-CORS**: Cross-origin resource sharing
- **Werkzeug**: WSGI utilities and security
- **Gunicorn**: Production WSGI server

### Document Processing
- **python-docx**: Microsoft Word document processing
- **unicodedata2**: Unicode text normalization
- **zipfile**: ZIP archive handling

### External Integrations
- **google-api-python-client**: Google Drive API integration
- **google-auth**: Google authentication
- **requests**: HTTP client for external APIs
- **email-validator**: Email validation utilities

### Development & Testing
- **python-dotenv**: Environment variable management
- **pytest**: Testing framework
- **logging**: Comprehensive logging system

## 🔒 Security Features

### Authentication & Authorization
- **JWT Tokens**: Secure stateless authentication
- **Password Hashing**: Bcrypt encryption for passwords
- **Role-Based Access Control**: Granular permissions by user role
- **Session Management**: Secure session handling

### Data Protection
- **Input Validation**: Comprehensive request validation
- **File Upload Security**: Secure file handling with validation
- **SQL Injection Prevention**: Parameterized queries via SQLAlchemy
- **CORS Configuration**: Controlled cross-origin access

### Environment Security
- **Environment Variables**: Secure credential management
- **Secret Key Management**: Cryptographically secure secret keys
- **Production Configuration**: Security-hardened production settings

## 🧪 Testing

### Test Suite
```bash
# Run all tests
python -m pytest

# Run specific test files
python test_te_assignment.py
python test_te_endpoints.py
python test_zip_upload.py

# Test with verbose output
python -m pytest -v
```

### Test Coverage
- **Authentication Tests**: Login, logout, token validation
- **API Endpoint Tests**: All REST endpoints
- **Database Tests**: Model relationships and queries
- **Service Tests**: External service integrations
- **File Processing Tests**: ZIP and document handling

## 📦 Deployment

### Development Deployment
```bash
# Start development server
python app.py

# Server runs on http://localhost:4999
# Debug mode enabled for development
```

### Production Deployment

#### AWS EC2 (Automated)
The application includes automated deployment via GitHub Actions:

```bash
# Automatic deployment on push to main branch
# Manual deployment via GitHub Actions workflow_dispatch
```

#### Manual Production Setup
```bash
# 1. Clone repository
git clone <repository-url>
cd TE_BACK

# 2. Create virtual environment
python3 -m venv venv
source venv/bin/activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Set up environment
cp .env.production .env
# Edit .env with production values

# 5. Initialize database
python init_db.py

# 6. Create admin user
python create_super_admin.py

# 7. Start with Gunicorn
gunicorn --bind 0.0.0.0:4999 --workers 2 --timeout 120 app:app
```
#### Systemd Service (Linux)
```bash
# Create service file
sudo nano /etc/systemd/system/editink-backend.service

# Service configuration
[Unit]
Description=EDITINK Backend API
After=network.target

[Service]
Type=exec
User=your-user
Group=your-group
WorkingDirectory=/path/to/TE_BACK
Environment=PATH=/path/to/TE_BACK/venv/bin
ExecStart=/path/to/TE_BACK/venv/bin/gunicorn --bind 0.0.0.0:4999 --workers 2 --timeout 120 app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable editink-backend
sudo systemctl start editink-backend
```

## 🔧 Configuration

### Environment Variables Reference

#### Required Variables
```bash
SECRET_KEY=your-super-secure-secret-key
OPENAI_API_KEY=your-openai-api-key
```

#### Google Drive Integration
```bash
GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE=service_account.json
GOOGLE_DRIVE_PARENT_FOLDER_ID=your-folder-id
```

#### Email Configuration
```bash
SMTP_SERVER=your-smtp-server
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_USE_TLS=True
```

#### Database Configuration
```bash
# SQLite (default)
# No configuration needed

# PostgreSQL (production)
DATABASE_URL=postgresql://username:password@host:port/database
```

#### Flask Configuration
```bash
FLASK_ENV=production
FLASK_DEBUG=False
MAX_CONTENT_LENGTH=********
UPLOAD_FOLDER=/tmp/uploads
```

## 🚀 Performance & Scaling

### Performance Optimizations
- **Database Indexing**: Optimized indexes for frequent queries
- **Connection Pooling**: Efficient database connection management
- **Caching**: Strategic caching for frequently accessed data
- **Async Processing**: Background task processing for heavy operations

### Scaling Considerations
- **Horizontal Scaling**: Multiple Gunicorn workers
- **Database Scaling**: PostgreSQL for production workloads
- **File Storage**: External storage for large file handling
- **Load Balancing**: Nginx reverse proxy for multiple instances

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a virtual environment
3. Install dependencies: `pip install -r requirements.txt`
4. Set up environment variables
5. Initialize database: `python init_db.py`
6. Run tests: `python -m pytest`
7. Start development server: `python app.py`

### Code Standards
- **PEP 8**: Python code style guidelines
- **Type Hints**: Use type annotations where appropriate
- **Documentation**: Comprehensive docstrings for functions
- **Testing**: Write tests for new functionality
- **Security**: Follow security best practices

### Git Workflow
1. Create feature branch from main
2. Make changes with descriptive commits
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request with detailed description

## 🔧 Recent Improvements & Bug Fixes

### TE Assignment Email System Enhancements (v3.2.0)
- **Author Queries Integration**: Emails now include author queries from database with HTML formatting
- **Queries File Attachment**: Automatic generation and attachment of detailed queries files (.txt)
- **Flask Context Fixes**: Resolved SQLAlchemy context errors in database operations
- **Variable Initialization**: Fixed crashes due to uninitialized variables in email service
- **Enhanced Email Templates**: Improved HTML formatting with author query summary tables
- **Robust Error Handling**: Better error handling for email sending and file generation

### Service Architecture Improvements
- **Modular Services**: Added dedicated `drive_service.py` and `email_service.py`
- **Clean Codebase**: Removed test files and debug scripts for production readiness
- **Template Consistency**: Synchronized query templates between frontend and backend

---

**Version**: 3.2.0
**Last Updated**: October 2025
**License**: Proprietary - EDITINK System
**Maintainer**: EDITINK Development Team