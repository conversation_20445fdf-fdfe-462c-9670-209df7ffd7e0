import React from "react";
import { LoaderSpinner, ProgressBadge } from "../common";

/**
 * ProgressSteps component for PubMed interface
 * Displays loading spinner, progress bar, and step indicators
 */
const ProgressSteps = ({ loading, progress, progressStep, totalCount }) => {
  if (!loading) return null;

  return (
    <div className="loading-container">
      <LoaderSpinner />
      <ProgressBadge progress={progress} />
      <div className="progress-details">
        <p className="progress-main">
          {/* Show dynamic progress for each step if available */}
          {progressStep && progressStep.step && progressStep.total > 0 && progressStep.current >= 0
            ? `${progressStep.description}`
            : progressStep.description || `Processing ${totalCount} references...`}
        </p>
        {progressStep.step && (
          <div className="progress-steps">
            {/* Only show 3 main steps: Extracting, PubMed, CrossRef */}
            <div
              className={`progress-step ${progressStep.step === "extracting" ? "active" : ["pubmed", "crossref"].includes(progressStep.step) ? "completed" : ""}`}
            >
              <span className="step-icon">🤖</span>
              <span className="step-text">Extracting references{progressStep.step === 'extracting' && progressStep.total > 0 ? ` (${progressStep.current}/${progressStep.total})` : ''}</span>
            </div>
            <div
              className={`progress-step ${progressStep.step === "pubmed" ? "active" : progressStep.step === "crossref" ? "completed" : ""}`}
            >
              <span className="step-icon">🔬</span>
              <span className="step-text">Searching PubMed{progressStep.step === 'pubmed' && progressStep.total > 0 ? ` (${progressStep.current}/${progressStep.total})` : ''}</span>
            </div>
            <div
              className={`progress-step ${progressStep.step === "crossref" ? "active" : ""}`}
            >
              <span className="step-icon">📚</span>
              <span className="step-text">Searching CrossRef{progressStep.step === 'crossref' && progressStep.total > 0 ? ` (${progressStep.current}/${progressStep.total})` : ''}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressSteps;