import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { Icons } from '../common';
import './AdminNavigation.css';

const AdminNavigation = () => {
  const location = useLocation();
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  const navigationItems = [
    {
      path: '/admin/dashboard',
      label: 'Dashboard',
      icon: Icons.DashboardIcon,
    },
    {
      path: '/admin/upload',
      label: 'ZIP Upload & Processing',
      icon: Icons.UploadIcon,
    },
    {
      path: '/admin/journals',
      label: 'Journal Administration',
      icon: Icons.SettingsIcon,
    },
    // Only show User Management for SuperAdmin
    ...(user?.role === 'SuperAdmin' ? [{
      path: '/admin/users',
      label: 'User Management',
      icon: Icons.UsersIcon,
    }] : []),
  ];

  const isActivePath = (path) => {
    return location.pathname === path;
  };

  return (
    <nav className="admin-navigation">
      <div className="nav-header">
        <div className="nav-brand">
          <Icons.UserIcon className="brand-icon" />
          <span className="brand-text">Admin Panel</span>
        </div>
        <div className="user-info">
          <span className="user-name">{user?.username}</span>
          <button onClick={handleLogout} className="logout-button" title="Logout">
            <Icons.LogoutIcon className="logout-icon" />
          </button>
        </div>
      </div>

      <div className="nav-menu">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.path}
              to={item.path}
              className={`nav-item ${isActivePath(item.path) ? 'nav-item-active' : ''}`}
            >
              <Icon className="nav-icon" />
              <span className="nav-label">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
};

export default AdminNavigation;
