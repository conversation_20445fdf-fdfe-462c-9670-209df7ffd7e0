{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Work\\\\MAIN_TE\\\\TE_FRONTEND\\\\src\\\\components\\\\zip\\\\DocumentPreview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport mammoth from 'mammoth';\nimport { useArticle } from '../../context/ArticleContext';\nimport { useRole } from '../../context/AuthContext';\nimport { Icons } from '../common';\nimport ArticleMetadataPanel from './ArticleMetadataPanel';\nimport RaiseQueryButton from './RaiseQueryButton';\nimport { extractMetadataFromZip, extractAuthorsFromZip } from '../../utils/zipMetadataUtils';\nimport './DocumentPreview.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DocumentPreview = ({\n  file,\n  onBack,\n  onCopyReferences,\n  zipId,\n  zipFile,\n  authors,\n  articleId,\n  onZipModified,\n  onValidationQuerySent\n}) => {\n  _s();\n  const [content, setContent] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAuthorsSidebar, setShowAuthorsSidebar] = useState(true);\n  const [showScrollToTop, setShowScrollToTop] = useState(false);\n  const [embeddedMetadata, setEmbeddedMetadata] = useState(null);\n  const [embeddedAuthors, setEmbeddedAuthors] = useState([]);\n  const navigate = useNavigate();\n  const {\n    setExtractedContent,\n    articleData\n  } = useArticle();\n  const {\n    isAdmin\n  } = useRole();\n  useEffect(() => {\n    if (file) {\n      loadFileContent();\n    }\n  }, [file]);\n\n  // Extract metadata from ZIP file if available\n  useEffect(() => {\n    const extractZipMetadata = async () => {\n      if (zipFile && zipFile instanceof File) {\n        try {\n          const metadata = await extractMetadataFromZip(zipFile);\n          const authorsFromZip = await extractAuthorsFromZip(zipFile);\n          if (metadata) {\n            setEmbeddedMetadata(metadata);\n            console.log('📄 Found embedded metadata:', metadata.article_id);\n          }\n          if (authorsFromZip && authorsFromZip.length > 0) {\n            setEmbeddedAuthors(authorsFromZip);\n            console.log('👥 Found embedded authors:', authorsFromZip.length);\n          }\n        } catch (error) {\n          console.error('❌ Error extracting ZIP metadata:', error);\n        }\n      }\n    };\n    extractZipMetadata();\n  }, [zipFile]);\n\n  // Handle scroll events to show/hide scroll-to-top button\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      setShowScrollToTop(scrollTop > 300); // Show button after scrolling 300px\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const loadFileContent = async () => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('Loading file content for:', file.name);\n      console.log('File object:', file);\n      console.log('ZipEntry exists:', !!file.zipEntry);\n      console.log('ZipEntry type:', typeof file.zipEntry);\n      if (!file.zipEntry) {\n        throw new Error('No zipEntry found in file object');\n      }\n      const arrayBuffer = await file.zipEntry.async('arraybuffer');\n      console.log('ArrayBuffer loaded, size:', arrayBuffer.byteLength);\n      if (file.type === 'docx' || file.type === 'doc') {\n        try {\n          const result = await mammoth.convertToHtml({\n            arrayBuffer\n          });\n          setContent(result.value);\n          setExtractedContent(result.value);\n\n          // Log any warnings from mammoth\n          if (result.messages && result.messages.length > 0) {\n            console.warn('Mammoth conversion warnings:', result.messages);\n          }\n        } catch (docError) {\n          console.error('Error converting document:', docError);\n          // Fallback: try to extract as plain text\n          try {\n            const text = new TextDecoder('utf-8', {\n              ignoreBOM: true\n            }).decode(arrayBuffer);\n            const cleanText = text.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]/g, '').trim();\n            if (cleanText.length > 100) {\n              setContent(`<pre>${cleanText}</pre>`);\n              setExtractedContent(cleanText);\n              console.log('Fallback: Extracted as plain text');\n            } else {\n              throw new Error('Unable to extract meaningful content');\n            }\n          } catch (fallbackError) {\n            throw new Error(`Cannot process ${file.type.toUpperCase()} file. Please try converting to .docx format first.`);\n          }\n        }\n      } else if (file.type === 'txt') {\n        const text = new TextDecoder().decode(arrayBuffer);\n        setContent(`<pre>${text}</pre>`);\n        setExtractedContent(text);\n      } else {\n        setError(`Unsupported file type: ${file.type.toUpperCase()}. Supported formats: .docx, .doc, .txt`);\n      }\n    } catch (err) {\n      console.error('Error loading file content:', err);\n      console.error('Error details:', err.message);\n      console.error('File object at error:', file);\n      let errorMessage = 'Failed to load file content';\n      if (err.message.includes('zipEntry')) {\n        errorMessage = 'File data is not properly loaded. Please try going back and selecting the file again.';\n      } else if (err.message.includes('async')) {\n        errorMessage = 'Unable to extract file from ZIP archive. The file may be corrupted.';\n      }\n      setError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    setSelectedText(text);\n  };\n  const processSelectedText = () => {\n    if (selectedText) {\n      // Check multiple indicators that this is an admin session\n      const hasAdminParam = window.location.search.includes('admin=true');\n      const hasAdminPath = window.location.pathname.includes('/admin');\n      const hasAdminSession = sessionStorage.getItem('adminContext') === 'true';\n      const referrerIsAdmin = document.referrer.includes('/admin');\n\n      // Use admin route if any admin indicator is present OR if we're clearly in admin workflow\n      const useAdminRoute = isAdmin || hasAdminParam || hasAdminPath || hasAdminSession || referrerIsAdmin || window.location.href.includes('admin') ||\n      // Current URL has admin\n      document.referrer.includes('admin'); // Came from admin page\n\n      const processRoute = useAdminRoute ? '/admin/process' : '/process';\n      navigate(processRoute, {\n        state: {\n          references: selectedText,\n          articleId: articleData === null || articleData === void 0 ? void 0 : articleData.articleId,\n          fromZipProcessor: true,\n          manualEntry: false,\n          isAdminContext: useAdminRoute,\n          // Pass admin context explicitly\n          zipId: zipId // Pass the zipId for auto-completion\n        }\n      });\n    }\n  };\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"document-preview-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-button\",\n          children: /*#__PURE__*/_jsxDEV(Icons.ChevronLeftIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-file-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"preview-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"file-icon\",\n              children: file.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), file.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"preview-subtitle\",\n            children: [file.type.toUpperCase(), \" \\u2022 Document Preview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-actions\",\n        children: [/*#__PURE__*/_jsxDEV(RaiseQueryButton, {\n          queryType: \"general\",\n          articleId: articleId,\n          zipFile: zipFile,\n          buttonText: \"Raise Query\",\n          buttonIcon: \"\\uD83D\\uDCE7\",\n          variant: \"primary\",\n          size: \"small\",\n          onQuerySent: id => {\n            console.log('Query sent for article:', id);\n            // Mark ZIP with validationQuerySent flag for Leena assignment\n            if (onValidationQuerySent) {\n              onValidationQuerySent(id);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), authors && authors.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAuthorsSidebar(!showAuthorsSidebar),\n          className: \"toggle-sidebar-button\",\n          title: showAuthorsSidebar ? \"Hide Authors\" : \"Show Authors\",\n          children: /*#__PURE__*/_jsxDEV(Icons.SettingsIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), selectedText && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: processSelectedText,\n          className: \"process-button selected\",\n          children: [/*#__PURE__*/_jsxDEV(Icons.ArrowRightIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), \"Process Selected\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `preview-with-sidebar ${!showAuthorsSidebar ? 'sidebar-hidden' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-content-wrapper\",\n        children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading document...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-error\",\n          children: [/*#__PURE__*/_jsxDEV(Icons.ExclamationIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Error Loading Document\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), error.includes('.DOC') && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-suggestion\",\n              children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tip:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 22\n              }, this), \" For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), !isLoading && !error && content && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-content-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"preview-content\",\n            dangerouslySetInnerHTML: {\n              __html: content\n            },\n            onMouseUp: handleTextSelection,\n            onKeyUp: handleTextSelection\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"instruction-icon\",\n              children: \"\\uD83D\\uDDB1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Select text to process specific sections\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"instruction-icon\",\n              children: \"\\uD83D\\uDD04\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Processing will take you directly to the reference processing screen\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 9\n        }, this), selectedText && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selection-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selection-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"selection-icon\",\n              children: \"\\u2702\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Selected Text (\", selectedText.length, \" characters)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selection-preview\",\n            children: [selectedText.substring(0, 200), selectedText.length > 200 && '...']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), showAuthorsSidebar && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"authors-sidebar\",\n        children: authors && authors.length > 0 && /*#__PURE__*/_jsxDEV(ArticleMetadataPanel, {\n          articleId: articleId,\n          authors: authors,\n          skipApiCall: true,\n          alwaysExpanded: true,\n          zipFiles: (articleData === null || articleData === void 0 ? void 0 : articleData.zipFiles) || [],\n          onZipModified: onZipModified,\n          onQueryCreated: () => {\n            console.log('Query created for article:', articleId);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), showScrollToTop && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: scrollToTop,\n        className: \"scroll-to-top-button\",\n        title: \"Scroll to top\",\n        \"aria-label\": \"Scroll to top\",\n        children: /*#__PURE__*/_jsxDEV(Icons.ChevronUpIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentPreview, \"Agna5cBx62FuemKDp4/MndwL1dE=\", false, function () {\n  return [useNavigate, useArticle, useRole];\n});\n_c = DocumentPreview;\nexport default DocumentPreview;\nvar _c;\n$RefreshReg$(_c, \"DocumentPreview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "mammoth", "useArticle", "useRole", "Icons", "ArticleMetadataPanel", "RaiseQuery<PERSON><PERSON>on", "extractMetadataFromZip", "extractAuthorsFromZip", "jsxDEV", "_jsxDEV", "DocumentPreview", "file", "onBack", "onCopyReferences", "zipId", "zipFile", "authors", "articleId", "onZipModified", "onValidationQuerySent", "_s", "content", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "setIsLoading", "error", "setError", "selectedText", "setSelectedText", "showAuthorsSidebar", "setShowAuthorsSidebar", "showScrollToTop", "setShowScrollToTop", "embeddedMetadata", "setEmbeddedMetadata", "embeddedAuthors", "setEmbeddedAuthors", "navigate", "setExtractedContent", "articleData", "isAdmin", "loadFileContent", "extractZipMetadata", "File", "metadata", "authors<PERSON>rom<PERSON><PERSON>", "console", "log", "article_id", "length", "handleScroll", "scrollTop", "window", "pageYOffset", "document", "documentElement", "addEventListener", "removeEventListener", "name", "zipEntry", "Error", "arrayBuffer", "async", "byteLength", "type", "result", "convertToHtml", "value", "messages", "warn", "doc<PERSON><PERSON><PERSON>", "text", "TextDecoder", "ignoreBOM", "decode", "cleanText", "replace", "trim", "fallback<PERSON><PERSON>r", "toUpperCase", "err", "message", "errorMessage", "includes", "handleTextSelection", "selection", "getSelection", "toString", "processSelectedText", "hasAdminParam", "location", "search", "has<PERSON>d<PERSON><PERSON><PERSON>", "pathname", "hasAdminSession", "sessionStorage", "getItem", "referrerIsAdmin", "referrer", "useAdminRoute", "href", "processRoute", "state", "references", "fromZipProcessor", "manualEntry", "isAdminContext", "scrollToTop", "scrollTo", "top", "behavior", "className", "children", "onClick", "ChevronLeftIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "queryType", "buttonText", "buttonIcon", "variant", "size", "onQuerySent", "id", "title", "SettingsIcon", "ArrowRightIcon", "ExclamationIcon", "dangerouslySetInnerHTML", "__html", "onMouseUp", "onKeyUp", "substring", "skipApiCall", "alwaysExpanded", "zipFiles", "onQ<PERSON>yCreated", "ChevronUpIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Work/MAIN_TE/TE_FRONTEND/src/components/zip/DocumentPreview.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport mammoth from 'mammoth';\nimport { useArticle } from '../../context/ArticleContext';\nimport { useRole } from '../../context/AuthContext';\nimport { Icons } from '../common';\nimport ArticleMetadataPanel from './ArticleMetadataPanel';\nimport RaiseQueryButton from './RaiseQueryButton';\nimport { extractMetadataFromZip, extractAuthorsFromZip } from '../../utils/zipMetadataUtils';\nimport './DocumentPreview.css';\n\nconst DocumentPreview = ({ file, onBack, onCopyReferences, zipId, zipFile, authors, articleId, onZipModified, onValidationQuerySent }) => {\n  const [content, setContent] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAuthorsSidebar, setShowAuthorsSidebar] = useState(true);\n  const [showScrollToTop, setShowScrollToTop] = useState(false);\n  const [embeddedMetadata, setEmbeddedMetadata] = useState(null);\n  const [embeddedAuthors, setEmbeddedAuthors] = useState([]);\n  const navigate = useNavigate();\n  const { setExtractedContent, articleData } = useArticle();\n  const { isAdmin } = useRole();\n\n  useEffect(() => {\n    if (file) {\n      loadFileContent();\n    }\n  }, [file]);\n\n  // Extract metadata from ZIP file if available\n  useEffect(() => {\n    const extractZipMetadata = async () => {\n      if (zipFile && zipFile instanceof File) {\n        try {\n          const metadata = await extractMetadataFromZip(zipFile);\n          const authorsFromZip = await extractAuthorsFromZip(zipFile);\n\n          if (metadata) {\n            setEmbeddedMetadata(metadata);\n            console.log('📄 Found embedded metadata:', metadata.article_id);\n          }\n\n          if (authorsFromZip && authorsFromZip.length > 0) {\n            setEmbeddedAuthors(authorsFromZip);\n            console.log('👥 Found embedded authors:', authorsFromZip.length);\n          }\n        } catch (error) {\n          console.error('❌ Error extracting ZIP metadata:', error);\n        }\n      }\n    };\n\n    extractZipMetadata();\n  }, [zipFile]);\n\n  // Handle scroll events to show/hide scroll-to-top button\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      setShowScrollToTop(scrollTop > 300); // Show button after scrolling 300px\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const loadFileContent = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('Loading file content for:', file.name);\n      console.log('File object:', file);\n      console.log('ZipEntry exists:', !!file.zipEntry);\n      console.log('ZipEntry type:', typeof file.zipEntry);\n\n      if (!file.zipEntry) {\n        throw new Error('No zipEntry found in file object');\n      }\n\n      const arrayBuffer = await file.zipEntry.async('arraybuffer');\n      console.log('ArrayBuffer loaded, size:', arrayBuffer.byteLength);\n      \n      if (file.type === 'docx' || file.type === 'doc') {\n        try {\n          const result = await mammoth.convertToHtml({ arrayBuffer });\n          setContent(result.value);\n          setExtractedContent(result.value);\n\n          // Log any warnings from mammoth\n          if (result.messages && result.messages.length > 0) {\n            console.warn('Mammoth conversion warnings:', result.messages);\n          }\n        } catch (docError) {\n          console.error('Error converting document:', docError);\n          // Fallback: try to extract as plain text\n          try {\n            const text = new TextDecoder('utf-8', { ignoreBOM: true }).decode(arrayBuffer);\n            const cleanText = text.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]/g, '').trim();\n            if (cleanText.length > 100) {\n              setContent(`<pre>${cleanText}</pre>`);\n              setExtractedContent(cleanText);\n              console.log('Fallback: Extracted as plain text');\n            } else {\n              throw new Error('Unable to extract meaningful content');\n            }\n          } catch (fallbackError) {\n            throw new Error(`Cannot process ${file.type.toUpperCase()} file. Please try converting to .docx format first.`);\n          }\n        }\n      } else if (file.type === 'txt') {\n        const text = new TextDecoder().decode(arrayBuffer);\n        setContent(`<pre>${text}</pre>`);\n        setExtractedContent(text);\n      } else {\n        setError(`Unsupported file type: ${file.type.toUpperCase()}. Supported formats: .docx, .doc, .txt`);\n      }\n    } catch (err) {\n      console.error('Error loading file content:', err);\n      console.error('Error details:', err.message);\n      console.error('File object at error:', file);\n\n      let errorMessage = 'Failed to load file content';\n      if (err.message.includes('zipEntry')) {\n        errorMessage = 'File data is not properly loaded. Please try going back and selecting the file again.';\n      } else if (err.message.includes('async')) {\n        errorMessage = 'Unable to extract file from ZIP archive. The file may be corrupted.';\n      }\n\n      setError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    setSelectedText(text);\n  };\n\n  const processSelectedText = () => {\n    if (selectedText) {\n      // Check multiple indicators that this is an admin session\n      const hasAdminParam = window.location.search.includes('admin=true');\n      const hasAdminPath = window.location.pathname.includes('/admin');\n      const hasAdminSession = sessionStorage.getItem('adminContext') === 'true';\n      const referrerIsAdmin = document.referrer.includes('/admin');\n\n      // Use admin route if any admin indicator is present OR if we're clearly in admin workflow\n      const useAdminRoute = isAdmin || hasAdminParam || hasAdminPath || hasAdminSession || referrerIsAdmin ||\n                           window.location.href.includes('admin') || // Current URL has admin\n                           document.referrer.includes('admin');      // Came from admin page\n\n      const processRoute = useAdminRoute ? '/admin/process' : '/process';\n\n      navigate(processRoute, {\n        state: {\n          references: selectedText,\n          articleId: articleData?.articleId,\n          fromZipProcessor: true,\n          manualEntry: false,\n          isAdminContext: useAdminRoute, // Pass admin context explicitly\n          zipId: zipId // Pass the zipId for auto-completion\n        }\n      });\n    }\n  };\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <div className=\"document-preview-container\">\n      {/* Header */}\n      <div className=\"preview-header\">\n        <div className=\"preview-title-section\">\n          <button onClick={onBack} className=\"back-button\">\n            <Icons.ChevronLeftIcon />\n          </button>\n          <div className=\"preview-file-info\">\n            <h2 className=\"preview-title\">\n              <span className=\"file-icon\">{file.icon}</span>\n              {file.name}\n            </h2>\n            <p className=\"preview-subtitle\">\n              {file.type.toUpperCase()} • Document Preview\n            </p>\n          </div>\n        </div>\n\n        <div className=\"preview-actions\">\n          {/* Raise Query Button */}\n          <RaiseQueryButton\n            queryType=\"general\"\n            articleId={articleId}\n            zipFile={zipFile}\n            buttonText=\"Raise Query\"\n            buttonIcon=\"📧\"\n            variant=\"primary\"\n            size=\"small\"\n            onQuerySent={(id) => {\n              console.log('Query sent for article:', id);\n              // Mark ZIP with validationQuerySent flag for Leena assignment\n              if (onValidationQuerySent) {\n                onValidationQuerySent(id);\n              }\n            }}\n          />\n\n          {authors && authors.length > 0 && (\n            <button\n              onClick={() => setShowAuthorsSidebar(!showAuthorsSidebar)}\n              className=\"toggle-sidebar-button\"\n              title={showAuthorsSidebar ? \"Hide Authors\" : \"Show Authors\"}\n            >\n              <Icons.SettingsIcon />\n            </button>\n          )}\n\n\n          {selectedText && (\n            <button onClick={processSelectedText} className=\"process-button selected\">\n              <Icons.ArrowRightIcon />\n              Process Selected\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Main Content with Sidebar Layout */}\n      <div className={`preview-with-sidebar ${!showAuthorsSidebar ? 'sidebar-hidden' : ''}`}>\n        {/* Left Side - Document Content */}\n        <div className=\"preview-content-wrapper\">\n        {isLoading && (\n          <div className=\"preview-loading\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading document...</p>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"preview-error\">\n            <Icons.ExclamationIcon />\n            <div>\n              <h3>Error Loading Document</h3>\n              <p>{error}</p>\n              {error.includes('.DOC') && (\n                <div className=\"error-suggestion\">\n                  💡 <strong>Tip:</strong> For better compatibility, try converting your .doc file to .docx format using Microsoft Word or an online converter.\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {!isLoading && !error && content && (\n          <div className=\"preview-content-container\">\n            <div\n              className=\"preview-content\"\n              dangerouslySetInnerHTML={{ __html: content }}\n              onMouseUp={handleTextSelection}\n              onKeyUp={handleTextSelection}\n            />\n          </div>\n        )}\n\n        {/* Instructions */}\n        <div className=\"preview-instructions\">\n          <div className=\"instruction-item\">\n            <span className=\"instruction-icon\">🖱️</span>\n            <span>Select text to process specific sections</span>\n          </div>\n          <div className=\"instruction-item\">\n            <span className=\"instruction-icon\">🔄</span>\n            <span>Processing will take you directly to the reference processing screen</span>\n          </div>\n        </div>\n\n        {/* Selection Info */}\n        {selectedText && (\n          <div className=\"selection-info\">\n            <div className=\"selection-header\">\n              <span className=\"selection-icon\">✂️</span>\n              <span>Selected Text ({selectedText.length} characters)</span>\n            </div>\n            <div className=\"selection-preview\">\n              {selectedText.substring(0, 200)}\n              {selectedText.length > 200 && '...'}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Right Side - System Authors Sidebar */}\n      {showAuthorsSidebar && (\n        <div className=\"authors-sidebar\">\n          {authors && authors.length > 0 && (\n            <ArticleMetadataPanel\n              articleId={articleId}\n              authors={authors}\n              skipApiCall={true}\n              alwaysExpanded={true}\n              zipFiles={articleData?.zipFiles || []}\n              onZipModified={onZipModified}\n              onQueryCreated={() => {\n                console.log('Query created for article:', articleId);\n              }}\n            />\n          )}\n        </div>\n      )}\n\n      {/* Scroll to Top Button */}\n      {showScrollToTop && (\n        <button\n          onClick={scrollToTop}\n          className=\"scroll-to-top-button\"\n          title=\"Scroll to top\"\n          aria-label=\"Scroll to top\"\n        >\n          <Icons.ChevronUpIcon />\n        </button>\n      )}\n    </div>\n    </div>\n  );\n};\n\nexport default DocumentPreview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,KAAK,QAAQ,WAAW;AACjC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,sBAAsB,EAAEC,qBAAqB,QAAQ,8BAA8B;AAC5F,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,gBAAgB;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,SAAS;EAAEC,aAAa;EAAEC;AAAsB,CAAC,KAAK;EAAAC,EAAA;EACxI,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAMwC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuC,mBAAmB;IAAEC;EAAY,CAAC,GAAGtC,UAAU,CAAC,CAAC;EACzD,MAAM;IAAEuC;EAAQ,CAAC,GAAGtC,OAAO,CAAC,CAAC;EAE7BJ,SAAS,CAAC,MAAM;IACd,IAAIa,IAAI,EAAE;MACR8B,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC9B,IAAI,CAAC,CAAC;;EAEV;EACAb,SAAS,CAAC,MAAM;IACd,MAAM4C,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI3B,OAAO,IAAIA,OAAO,YAAY4B,IAAI,EAAE;QACtC,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMtC,sBAAsB,CAACS,OAAO,CAAC;UACtD,MAAM8B,cAAc,GAAG,MAAMtC,qBAAqB,CAACQ,OAAO,CAAC;UAE3D,IAAI6B,QAAQ,EAAE;YACZV,mBAAmB,CAACU,QAAQ,CAAC;YAC7BE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEH,QAAQ,CAACI,UAAU,CAAC;UACjE;UAEA,IAAIH,cAAc,IAAIA,cAAc,CAACI,MAAM,GAAG,CAAC,EAAE;YAC/Cb,kBAAkB,CAACS,cAAc,CAAC;YAClCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,cAAc,CAACI,MAAM,CAAC;UAClE;QACF,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdqB,OAAO,CAACrB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF;IACF,CAAC;IAEDiB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC3B,OAAO,CAAC,CAAC;;EAEb;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMoD,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACJ,SAAS;MAC1EnB,kBAAkB,CAACmB,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAEDC,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAC/C,OAAO,MAAME,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMT,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCjB,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFoB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEpC,IAAI,CAAC+C,IAAI,CAAC;MACnDZ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEpC,IAAI,CAAC;MACjCmC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAACpC,IAAI,CAACgD,QAAQ,CAAC;MAChDb,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOpC,IAAI,CAACgD,QAAQ,CAAC;MAEnD,IAAI,CAAChD,IAAI,CAACgD,QAAQ,EAAE;QAClB,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;MACrD;MAEA,MAAMC,WAAW,GAAG,MAAMlD,IAAI,CAACgD,QAAQ,CAACG,KAAK,CAAC,aAAa,CAAC;MAC5DhB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEc,WAAW,CAACE,UAAU,CAAC;MAEhE,IAAIpD,IAAI,CAACqD,IAAI,KAAK,MAAM,IAAIrD,IAAI,CAACqD,IAAI,KAAK,KAAK,EAAE;QAC/C,IAAI;UACF,MAAMC,MAAM,GAAG,MAAMjE,OAAO,CAACkE,aAAa,CAAC;YAAEL;UAAY,CAAC,CAAC;UAC3DvC,UAAU,CAAC2C,MAAM,CAACE,KAAK,CAAC;UACxB7B,mBAAmB,CAAC2B,MAAM,CAACE,KAAK,CAAC;;UAEjC;UACA,IAAIF,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACG,QAAQ,CAACnB,MAAM,GAAG,CAAC,EAAE;YACjDH,OAAO,CAACuB,IAAI,CAAC,8BAA8B,EAAEJ,MAAM,CAACG,QAAQ,CAAC;UAC/D;QACF,CAAC,CAAC,OAAOE,QAAQ,EAAE;UACjBxB,OAAO,CAACrB,KAAK,CAAC,4BAA4B,EAAE6C,QAAQ,CAAC;UACrD;UACA,IAAI;YACF,MAAMC,IAAI,GAAG,IAAIC,WAAW,CAAC,OAAO,EAAE;cAAEC,SAAS,EAAE;YAAK,CAAC,CAAC,CAACC,MAAM,CAACb,WAAW,CAAC;YAC9E,MAAMc,SAAS,GAAGJ,IAAI,CAACK,OAAO,CAAC,wCAAwC,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;YACnF,IAAIF,SAAS,CAAC1B,MAAM,GAAG,GAAG,EAAE;cAC1B3B,UAAU,CAAE,QAAOqD,SAAU,QAAO,CAAC;cACrCrC,mBAAmB,CAACqC,SAAS,CAAC;cAC9B7B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD,CAAC,MAAM;cACL,MAAM,IAAIa,KAAK,CAAC,sCAAsC,CAAC;YACzD;UACF,CAAC,CAAC,OAAOkB,aAAa,EAAE;YACtB,MAAM,IAAIlB,KAAK,CAAE,kBAAiBjD,IAAI,CAACqD,IAAI,CAACe,WAAW,CAAC,CAAE,qDAAoD,CAAC;UACjH;QACF;MACF,CAAC,MAAM,IAAIpE,IAAI,CAACqD,IAAI,KAAK,KAAK,EAAE;QAC9B,MAAMO,IAAI,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACE,MAAM,CAACb,WAAW,CAAC;QAClDvC,UAAU,CAAE,QAAOiD,IAAK,QAAO,CAAC;QAChCjC,mBAAmB,CAACiC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL7C,QAAQ,CAAE,0BAAyBf,IAAI,CAACqD,IAAI,CAACe,WAAW,CAAC,CAAE,wCAAuC,CAAC;MACrG;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZlC,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAEuD,GAAG,CAAC;MACjDlC,OAAO,CAACrB,KAAK,CAAC,gBAAgB,EAAEuD,GAAG,CAACC,OAAO,CAAC;MAC5CnC,OAAO,CAACrB,KAAK,CAAC,uBAAuB,EAAEd,IAAI,CAAC;MAE5C,IAAIuE,YAAY,GAAG,6BAA6B;MAChD,IAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;QACpCD,YAAY,GAAG,uFAAuF;MACxG,CAAC,MAAM,IAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxCD,YAAY,GAAG,qEAAqE;MACtF;MAEAxD,QAAQ,CAACwD,YAAY,CAAC;IACxB,CAAC,SAAS;MACR1D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM4D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,SAAS,GAAGjC,MAAM,CAACkC,YAAY,CAAC,CAAC;IACvC,MAAMf,IAAI,GAAGc,SAAS,CAACE,QAAQ,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IACxCjD,eAAe,CAAC2C,IAAI,CAAC;EACvB,CAAC;EAED,MAAMiB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI7D,YAAY,EAAE;MAChB;MACA,MAAM8D,aAAa,GAAGrC,MAAM,CAACsC,QAAQ,CAACC,MAAM,CAACR,QAAQ,CAAC,YAAY,CAAC;MACnE,MAAMS,YAAY,GAAGxC,MAAM,CAACsC,QAAQ,CAACG,QAAQ,CAACV,QAAQ,CAAC,QAAQ,CAAC;MAChE,MAAMW,eAAe,GAAGC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,MAAM;MACzE,MAAMC,eAAe,GAAG3C,QAAQ,CAAC4C,QAAQ,CAACf,QAAQ,CAAC,QAAQ,CAAC;;MAE5D;MACA,MAAMgB,aAAa,GAAG3D,OAAO,IAAIiD,aAAa,IAAIG,YAAY,IAAIE,eAAe,IAAIG,eAAe,IAC/E7C,MAAM,CAACsC,QAAQ,CAACU,IAAI,CAACjB,QAAQ,CAAC,OAAO,CAAC;MAAI;MAC1C7B,QAAQ,CAAC4C,QAAQ,CAACf,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAM;;MAE/D,MAAMkB,YAAY,GAAGF,aAAa,GAAG,gBAAgB,GAAG,UAAU;MAElE9D,QAAQ,CAACgE,YAAY,EAAE;QACrBC,KAAK,EAAE;UACLC,UAAU,EAAE5E,YAAY;UACxBV,SAAS,EAAEsB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEtB,SAAS;UACjCuF,gBAAgB,EAAE,IAAI;UACtBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAEP,aAAa;UAAE;UAC/BrF,KAAK,EAAEA,KAAK,CAAC;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM6F,WAAW,GAAGA,CAAA,KAAM;IACxBvD,MAAM,CAACwD,QAAQ,CAAC;MACdC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACErG,OAAA;IAAKsG,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAEzCvG,OAAA;MAAKsG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BvG,OAAA;QAAKsG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCvG,OAAA;UAAQwG,OAAO,EAAErG,MAAO;UAACmG,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC9CvG,OAAA,CAACN,KAAK,CAAC+G,eAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACT7G,OAAA;UAAKsG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCvG,OAAA;YAAIsG,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC3BvG,OAAA;cAAMsG,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAErG,IAAI,CAAC4G;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC7C3G,IAAI,CAAC+C,IAAI;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACL7G,OAAA;YAAGsG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAC5BrG,IAAI,CAACqD,IAAI,CAACe,WAAW,CAAC,CAAC,EAAC,0BAC3B;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BvG,OAAA,CAACJ,gBAAgB;UACfmH,SAAS,EAAC,SAAS;UACnBvG,SAAS,EAAEA,SAAU;UACrBF,OAAO,EAAEA,OAAQ;UACjB0G,UAAU,EAAC,aAAa;UACxBC,UAAU,EAAC,cAAI;UACfC,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,OAAO;UACZC,WAAW,EAAGC,EAAE,IAAK;YACnBhF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+E,EAAE,CAAC;YAC1C;YACA,IAAI3G,qBAAqB,EAAE;cACzBA,qBAAqB,CAAC2G,EAAE,CAAC;YAC3B;UACF;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDtG,OAAO,IAAIA,OAAO,CAACiC,MAAM,GAAG,CAAC,iBAC5BxC,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;UAC1DkF,SAAS,EAAC,uBAAuB;UACjCgB,KAAK,EAAElG,kBAAkB,GAAG,cAAc,GAAG,cAAe;UAAAmF,QAAA,eAE5DvG,OAAA,CAACN,KAAK,CAAC6H,YAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACT,EAGA3F,YAAY,iBACXlB,OAAA;UAAQwG,OAAO,EAAEzB,mBAAoB;UAACuB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACvEvG,OAAA,CAACN,KAAK,CAAC8H,cAAc;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAE1B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7G,OAAA;MAAKsG,SAAS,EAAG,wBAAuB,CAAClF,kBAAkB,GAAG,gBAAgB,GAAG,EAAG,EAAE;MAAAmF,QAAA,gBAEpFvG,OAAA;QAAKsG,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GACvCzF,SAAS,iBACRd,OAAA;UAAKsG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvG,OAAA;YAAKsG,SAAS,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC7G,OAAA;YAAAuG,QAAA,EAAG;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACN,EAEA7F,KAAK,iBACJhB,OAAA;UAAKsG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvG,OAAA,CAACN,KAAK,CAAC+H,eAAe;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzB7G,OAAA;YAAAuG,QAAA,gBACEvG,OAAA;cAAAuG,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/B7G,OAAA;cAAAuG,QAAA,EAAIvF;YAAK;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACb7F,KAAK,CAAC0D,QAAQ,CAAC,MAAM,CAAC,iBACrB1E,OAAA;cAAKsG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAC,eAC7B,eAAAvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yHAC1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAAC/F,SAAS,IAAI,CAACE,KAAK,IAAIJ,OAAO,iBAC9BZ,OAAA;UAAKsG,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCvG,OAAA;YACEsG,SAAS,EAAC,iBAAiB;YAC3BoB,uBAAuB,EAAE;cAAEC,MAAM,EAAE/G;YAAQ,CAAE;YAC7CgH,SAAS,EAAEjD,mBAAoB;YAC/BkD,OAAO,EAAElD;UAAoB;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGD7G,OAAA;UAAKsG,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCvG,OAAA;YAAKsG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BvG,OAAA;cAAMsG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C7G,OAAA;cAAAuG,QAAA,EAAM;YAAwC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN7G,OAAA;YAAKsG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BvG,OAAA;cAAMsG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5C7G,OAAA;cAAAuG,QAAA,EAAM;YAAoE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL3F,YAAY,iBACXlB,OAAA;UAAKsG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvG,OAAA;YAAKsG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BvG,OAAA;cAAMsG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1C7G,OAAA;cAAAuG,QAAA,GAAM,iBAAe,EAACrF,YAAY,CAACsB,MAAM,EAAC,cAAY;YAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACN7G,OAAA;YAAKsG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/BrF,YAAY,CAAC4G,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAC9B5G,YAAY,CAACsB,MAAM,GAAG,GAAG,IAAI,KAAK;UAAA;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLzF,kBAAkB,iBACjBpB,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BhG,OAAO,IAAIA,OAAO,CAACiC,MAAM,GAAG,CAAC,iBAC5BxC,OAAA,CAACL,oBAAoB;UACnBa,SAAS,EAAEA,SAAU;UACrBD,OAAO,EAAEA,OAAQ;UACjBwH,WAAW,EAAE,IAAK;UAClBC,cAAc,EAAE,IAAK;UACrBC,QAAQ,EAAE,CAAAnG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmG,QAAQ,KAAI,EAAG;UACtCxH,aAAa,EAAEA,aAAc;UAC7ByH,cAAc,EAAEA,CAAA,KAAM;YACpB7F,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE9B,SAAS,CAAC;UACtD;QAAE;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAvF,eAAe,iBACdtB,OAAA;QACEwG,OAAO,EAAEN,WAAY;QACrBI,SAAS,EAAC,sBAAsB;QAChCgB,KAAK,EAAC,eAAe;QACrB,cAAW,eAAe;QAAAf,QAAA,eAE1BvG,OAAA,CAACN,KAAK,CAACyI,aAAa;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAAClG,EAAA,CAjUIV,eAAe;EAAA,QASFX,WAAW,EACiBE,UAAU,EACnCC,OAAO;AAAA;AAAA2I,EAAA,GAXvBnI,eAAe;AAmUrB,eAAeA,eAAe;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}