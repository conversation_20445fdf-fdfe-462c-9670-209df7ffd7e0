/* Document Preview Styles */
.document-preview-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 1.5rem;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Sidebar Layout */
.preview-with-sidebar {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 1.5rem;
  flex: 1;
  min-height: 0;
  align-items: start;
}

.preview-with-sidebar.sidebar-hidden {
  grid-template-columns: 1fr;
}

.authors-sidebar {
  position: sticky;
  top: 80px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

/* Header */
.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.preview-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #e5e7eb;
  color: #111827;
}

.preview-file-info {
  flex: 1;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.25rem 0;
}

.file-icon {
  font-size: 1.125rem;
}

.preview-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* Actions */
.preview-actions {
  display: flex;
  gap: 0.75rem;
}

.toggle-sidebar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-sidebar-button:hover {
  background: #e5e7eb;
  color: #374151;
  border-color: #d1d5db;
}

.toggle-sidebar-button svg {
  width: 1.25rem;
  height: 1.25rem;
}

.process-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #059669;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.process-button:hover {
  background: #047857;
}

.process-button.selected {
  background: #7c3aed;
}

.process-button.selected:hover {
  background: #6d28d9;
}

/* Content Wrapper */
.preview-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-bottom: 1.5rem;
}

/* Loading State */
.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 1rem;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preview-loading p {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

/* Error State */
.preview-error {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.75rem;
  color: #dc2626;
}

.preview-error h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.preview-error p {
  margin: 0;
  font-size: 0.875rem;
}

.error-suggestion {
  margin-top: 1rem;
  padding: 1rem;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 0.5rem;
  color: #92400e;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Content Container */
.preview-content-container {
  flex: 1;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  line-height: 1.6;
  font-family: 'Georgia', serif;
  user-select: text;
}

/* Content Styling */
.preview-content h1,
.preview-content h2,
.preview-content h3,
.preview-content h4,
.preview-content h5,
.preview-content h6 {
  color: #374151;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.preview-content h1 { font-size: 1.5rem; }
.preview-content h2 { font-size: 1.25rem; }
.preview-content h3 { font-size: 1.125rem; }

.preview-content p {
  margin-bottom: 1rem;
  color: #374151;
}

.preview-content pre {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
}

.preview-content ::selection {
  background: #dbeafe;
  color: #1d4ed8;
}

/* Instructions */
.preview-instructions {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #475569;
}

.instruction-item:last-child {
  margin-bottom: 0;
}

.instruction-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

/* Selection Info */
.selection-info {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 0.75rem;
  padding: 1rem;
  flex-shrink: 0;
}

.selection-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1d4ed8;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.selection-icon {
  font-size: 1rem;
}

.selection-preview {
  font-size: 0.875rem;
  color: #374151;
  background: #ffffff;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  font-family: 'Courier New', monospace;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .preview-with-sidebar {
    grid-template-columns: 1fr;
  }

  .authors-sidebar {
    position: static;
    max-height: none;
    order: -1; /* Show authors above content on mobile */
  }
}

@media (max-width: 768px) {
  .document-preview-container {
    padding: 1rem;
  }

  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .preview-title-section {
    width: 100%;
  }

  .preview-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .preview-content {
    padding: 1rem;
  }

  .process-button {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}
