/* ZIP Workflow Styles */
.zip-workflow-container {
  min-height: 100vh;
  background: #f9fafb;
  position: relative;
}

/* Progress Indicator */
.workflow-progress {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.progress-steps {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2rem;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.step-circle {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: #f3f4f6;
  border: 2px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.progress-step.active .step-circle {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.progress-step.completed .step-circle {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.step-number {
  font-weight: 600;
  font-size: 0.875rem;
}

.step-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-align: center;
}

.progress-step.active .step-label {
  color: #3b82f6;
}

.progress-step.completed .step-label {
  color: #10b981;
}

.progress-line {
  width: 4rem;
  height: 2px;
  background: #e5e7eb;
  margin: 0 1rem;
}

.progress-step.completed + .progress-line {
  background: #10b981;
}

/* Main Content */
.workflow-content {
  padding: 2rem 0;
  min-height: calc(100vh - 120px);
}

/* Floating Action Panel */
.floating-action-panel {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 50;
  max-width: 400px;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.action-panel-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
}

.action-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.action-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.action-text {
  flex: 1;
}

.action-title {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 0.125rem;
}

.action-subtitle {
  font-size: 0.75rem;
  color: #6b7280;
}

.process-button {
  background: #059669;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.process-button:hover {
  background: #047857;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .workflow-progress {
    padding: 1rem 0;
  }
  
  .progress-steps {
    padding: 0 1rem;
  }
  
  .step-circle {
    width: 2rem;
    height: 2rem;
  }
  
  .step-number {
    font-size: 0.75rem;
  }
  
  .step-label {
    font-size: 0.625rem;
  }
  
  .progress-line {
    width: 2rem;
    margin: 0 0.5rem;
  }
  
  .workflow-content {
    padding: 1rem 0;
  }
  
  .floating-action-panel {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
  
  .action-panel-content {
    padding: 1rem;
  }
  
  .process-button {
    padding: 0.625rem 1.25rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .progress-steps {
    flex-direction: column;
    gap: 1rem;
  }
  
  .progress-line {
    width: 2px;
    height: 2rem;
    margin: 0;
  }
  
  .action-panel-content {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .action-info {
    justify-content: center;
  }
}
