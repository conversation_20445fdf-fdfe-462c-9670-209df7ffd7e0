/* Manual Entry Section Styles */
.manual-entry-section {
  margin-bottom: 1.5rem;
}

.manual-entry-card {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.manual-entry-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.manual-entry-icon {
  color: #d97706;
  flex-shrink: 0;
}

.manual-entry-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.manual-entry-text {
  flex: 1;
}

.manual-entry-text h4 {
  color: #92400e;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.manual-entry-text p {
  color: #a16207;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.4;
}

.manual-entry-button {
  background: #f59e0b;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.manual-entry-button:hover {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}
