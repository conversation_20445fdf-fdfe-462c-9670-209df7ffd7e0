import React from 'react';
import FolderZipWorkflow from '../zip/FolderZipWorkflow';
import './AdminUploadPage.css';

const AdminUploadPage = () => {
  return (
    <div className="admin-upload-page">
      <div className="upload-page-header">
        <h1 className="upload-page-title">ZIP Upload & Reference Processing</h1>
        <p className="upload-page-description">
          Upload single ZIP files or folders containing multiple ZIP files for batch processing.
        </p>
      </div>

      <div className="upload-page-content">
        <FolderZipWorkflow />
      </div>
    </div>
  );
};

export default AdminUploadPage;
