.author-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.75rem;
  color: #374151;
}

.author-summary.compact {
  padding: 4px 8px;
  gap: 6px;
  background-color: transparent;
  border: none;
}

.author-summary.loading,
.author-summary.error,
.author-summary.no-data {
  color: #6b7280;
  font-style: italic;
}

.author-summary.error {
  color: #dc2626;
}

.summary-icon {
  width: 14px;
  height: 14px;
  color: #6b7280;
  flex-shrink: 0;
}

.summary-icon.error {
  color: #dc2626;
}

.author-count {
  font-weight: 500;
  color: #111827;
}

.copyright-indicators {
  display: flex;
  gap: 4px;
  align-items: center;
}

.indicator {
  font-size: 0.65rem;
  font-weight: 500;
  padding: 1px 4px;
  border-radius: 8px;
  line-height: 1;
}

.indicator.yes {
  background-color: #dcfce7;
  color: #166534;
}

.indicator.no {
  background-color: #fef2f2;
  color: #dc2626;
}

.indicator.unknown {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Non-compact version */
.summary-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

.summary-title {
  font-weight: 500;
  color: #111827;
}

.summary-details {
  display: flex;
  align-items: center;
}

.copyright-breakdown {
  display: flex;
  gap: 8px;
  align-items: center;
}

.breakdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.breakdown-item .count {
  font-weight: 600;
  font-size: 0.875rem;
}

.breakdown-item .label {
  font-size: 0.625rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.breakdown-item.yes .count {
  color: #166534;
}

.breakdown-item.yes .label {
  color: #16a34a;
}

.breakdown-item.no .count {
  color: #dc2626;
}

.breakdown-item.no .label {
  color: #ef4444;
}

.breakdown-item.unknown .count {
  color: #6b7280;
}

.breakdown-item.unknown .label {
  color: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .author-summary:not(.compact) {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  
  .copyright-breakdown {
    gap: 6px;
  }
  
  .breakdown-item .count {
    font-size: 0.75rem;
  }
  
  .breakdown-item .label {
    font-size: 0.6rem;
  }
}
