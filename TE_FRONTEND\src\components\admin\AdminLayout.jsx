import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { Icons } from '../common';

const AdminLayout = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  const menuSections = [
    {
      title: 'Overview',
      items: [
        {
          path: '/admin/dashboard',
          icon: Icons.HomeIcon,
          label: 'Dashboard',
          description: 'Overview & Quick Stats'
        },
        // {
        //   path: '/admin/statistics',
        //   icon: Icons.BarChart3,
        //   label: 'Statistics',
        //   description: 'Comprehensive Analytics'
        // }
      ]
    },
    {
      title: 'Reference Processing',
      items: [
        {
          path: '/admin/search',
          icon: Icons.SearchIcon,
          label: 'Search References',
          description: 'Find Article References'
        },
        {
          path: '/admin/zip-upload',
          icon: Icons.UploadIcon,
          label: 'Single ZIP Upload',
          description: 'Upload Individual ZIP Files'
        },
        {
          path: '/admin/folder-upload',
          icon: Icons.FolderIcon,
          label: 'Folder Upload',
          description: 'Upload ZIP Folders'
        },
        {
          path: '/admin/zip-queue?admin=true',
          icon: Icons.CollectionIcon,
          label: 'ZIP Queue',
          description: 'Manage Processing Queue'
        }
      ]
    },
    {
      title: 'Administration',
      items: [
        {
          path: '/admin/journals',
          icon: Icons.DocumentIcon,
          label: 'Journal Admin',
          description: 'Manage Abbreviations'
        },
        // Only show User Management for SuperAdmin
        ...(user?.role === 'SuperAdmin' ? [{
          path: '/admin/users',
          icon: Icons.UsersIcon,
          label: 'User Management',
          description: 'Create & Manage Users'
        }] : [])
      ]
    }
  ];
     console.log(user?.role, 'user role');

  // Flatten for backward compatibility
  const menuItems = menuSections.flatMap(section => section.items);

  const isActivePath = (path) => {
    // Handle exact matches
    if (location.pathname === path) {
      return true;
    }

    // Handle ZIP queue related paths (both admin and non-admin)
    if ((path === '/zip-queue' || path === '/admin/zip-queue') && (
      location.pathname === '/zip-queue' ||
      location.pathname === '/admin/zip-queue' ||
      location.pathname.startsWith('/process-zip/')
    )) {
      return true;
    }

    // Handle folder upload related paths (both admin and non-admin)
    if ((path === '/folder-upload' || path === '/admin/folder-upload') && (
      location.pathname === '/folder-upload' ||
      location.pathname === '/admin/folder-upload'
    )) {
      return true;
    }

    return false;
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 bg-white shadow-xl border-r border-gray-100 transform transition-all duration-300 ease-in-out lg:static lg:inset-0 ${
        isSidebarOpen ? 'w-72 translate-x-0' : 'w-16 translate-x-0'
      }`}>

        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
              <Icons.DocumentIcon className="w-5 h-5 text-white" />
            </div>
            {isSidebarOpen && (
              <div className="ml-3">
                <span className="text-lg font-bold text-gray-900 tracking-tight">EDITINK</span>
                <div className="text-xs text-gray-600 font-medium">Admin Panel</div>
              </div>
            )}
          </div>
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            {isSidebarOpen ? (
              <Icons.ChevronLeftIcon className="w-5 h-5" />
            ) : (
              <Icons.MenuIcon className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Navigation Menu */}
        <nav className="mt-8 px-2">
          <div className="space-y-6">
            {menuSections.map((section, sectionIndex) => (
              <div key={section.title}>
                {isSidebarOpen && (
                  <div className="px-4 mb-3">
                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                      {section.title}
                    </h3>
                  </div>
                )}
                <div className="space-y-2">
                  {section.items.map((item) => {
                    const isActive = isActivePath(item.path);
                    return (
                      <button
                        key={item.path}
                        onClick={() => {
                          navigate(item.path);
                        }}
                        className={`w-full group flex items-center ${isSidebarOpen ? 'px-4 py-3.5' : 'px-2 py-3 justify-center'} text-left rounded-xl transition-all duration-200 ${
                          isActive
                            ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/25'
                            : 'text-gray-700 hover:bg-gray-50 hover:shadow-md'
                        }`}
                        title={!isSidebarOpen ? item.label : ''}
                      >
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${isSidebarOpen ? 'mr-3' : ''} transition-colors ${
                          isActive
                            ? 'bg-white/20'
                            : 'bg-gray-100 group-hover:bg-gray-200'
                        }`}>
                          <item.icon className={`w-5 h-5 ${
                            isActive ? 'text-white' : 'text-gray-600 group-hover:text-gray-700'
                          }`} />
                        </div>
                        {isSidebarOpen && (
                          <div className="flex-1">
                            <div className={`text-sm font-semibold tracking-wide ${
                              isActive ? 'text-white' : 'text-gray-900'
                            }`}>
                              {item.label}
                            </div>
                            <div className={`text-xs ${
                              isActive ? 'text-blue-100' : 'text-gray-500'
                            }`}>
                              {item.description}
                            </div>
                          </div>
                        )}
                        {isActive && isSidebarOpen && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </nav>

        {/* User Info & Logout */}
        <div className="absolute bottom-0 left-0 right-0 p-2 border-t border-gray-100 bg-gray-50/50">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            {isSidebarOpen ? (
              <div className="p-4">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-xl flex items-center justify-center">
                    <Icons.UserIcon className="w-5 h-5 text-white" />
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="text-sm font-semibold text-gray-900">{user?.username}</div>
                    <div className="text-xs text-gray-500 font-medium">Administrator</div>
                  </div>
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                </div>
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center justify-center px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors border border-gray-200"
                >
                  <Icons.LogoutIcon className="w-4 h-4 mr-2" />
                  Sign Out
                </button>
              </div>
            ) : (
              <div className="p-2 flex flex-col items-center space-y-2">
                <div className="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-xl flex items-center justify-center">
                  <Icons.UserIcon className="w-5 h-5 text-white" />
                </div>
                <button
                  onClick={handleLogout}
                  className="w-10 h-10 flex items-center justify-center text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Sign Out"
                >
                  <Icons.LogoutIcon className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        {/* Page Content */}
        <main className="p-6 lg:p-8 max-w-7xl mx-auto">
          {children}
        </main>
      </div>


    </div>
  );
};

export default AdminLayout;
