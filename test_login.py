#!/usr/bin/env python3
import logging
import time
from flask import Flask, request, jsonify, session
from flask_cors import CORS
from werkzeug.security import check_password_hash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta
import os

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_login.log'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
CORS(app, supports_credentials=True)

# Configure session
app.config['SECRET_KEY'] = 'test-secret-key'
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SECURE'] = False
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# Database configuration
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(BASE_DIR, 'article_references.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{DB_PATH}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# User model
class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'email': self.email,
            'isActive': self.is_active,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'lastLogin': self.last_login.isoformat() if self.last_login else None
        }

@app.route('/api/auth/login', methods=['POST'])
def test_login():
    start_time = time.time()
    request_id = f"login_{int(start_time * 1000)}"
    
    print(f"[{request_id}] === LOGIN REQUEST STARTED ===")
    
    try:
        # Step 1: Parse request
        print(f"[{request_id}] Step 1: Parsing request...")
        data = request.get_json()
        print(f"[{request_id}] Request data: {data}")
        
        if not data:
            print(f"[{request_id}] No JSON data")
            return jsonify({'error': 'No data provided'}), 400
            
        username = data.get('username')
        password = data.get('password')
        
        print(f"[{request_id}] Username: {username}, Password provided: {password is not None}")
        
        if not username or not password:
            print(f"[{request_id}] Missing credentials")
            return jsonify({'error': 'Username and password required'}), 400

        # Step 2: Database query
        print(f"[{request_id}] Step 2: Querying database...")
        query_start = time.time()
        
        user = User.query.filter_by(username=username, is_active=True).first()
        
        query_time = time.time() - query_start
        print(f"[{request_id}] Database query took {query_time:.3f}s")
        print(f"[{request_id}] User found: {user is not None}")
        
        if not user:
            print(f"[{request_id}] User not found")
            return jsonify({'error': 'Invalid credentials'}), 401

        # Step 3: Password check
        print(f"[{request_id}] Step 3: Checking password...")
        password_start = time.time()
        
        password_valid = user.check_password(password)
        
        password_time = time.time() - password_start
        print(f"[{request_id}] Password check took {password_time:.3f}s")
        print(f"[{request_id}] Password valid: {password_valid}")
        
        if not password_valid:
            print(f"[{request_id}] Invalid password")
            return jsonify({'error': 'Invalid credentials'}), 401

        # Step 4: Session setup
        print(f"[{request_id}] Step 4: Setting up session...")
        session_start = time.time()
        
        session['user_id'] = user.id
        session['username'] = user.username
        session['user_role'] = user.role
        session.permanent = True
        
        session_time = time.time() - session_start
        print(f"[{request_id}] Session setup took {session_time:.3f}s")

        # Step 5: Update last login
        print(f"[{request_id}] Step 5: Updating last login...")
        update_start = time.time()
        
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        update_time = time.time() - update_start
        print(f"[{request_id}] Database update took {update_time:.3f}s")

        # Step 6: Response
        print(f"[{request_id}] Step 6: Preparing response...")
        response_start = time.time()
        
        response_data = {
            'message': 'Login successful',
            'user': user.to_dict()
        }
        
        response_time = time.time() - response_start
        total_time = time.time() - start_time
        
        print(f"[{request_id}] Response preparation took {response_time:.3f}s")
        print(f"[{request_id}] === LOGIN COMPLETED in {total_time:.3f}s ===")
        
        return jsonify(response_data), 200
        
    except Exception as e:
        error_time = time.time() - start_time
        print(f"[{request_id}] === LOGIN FAILED after {error_time:.3f}s ===")
        print(f"[{request_id}] Error: {str(e)}")
        import traceback
        traceback.print_exc()
        
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/auth/check', methods=['GET'])
def test_check():
    print("[AUTH_CHECK] Checking authentication...")
    
    if 'user_id' in session:
        user = User.query.get(session['user_id'])
        if user and user.is_active:
            print(f"[AUTH_CHECK] User authenticated: {user.username}")
            return jsonify({
                'authenticated': True,
                'user': user.to_dict()
            }), 200
    
    print("[AUTH_CHECK] User not authenticated")
    return jsonify({'authenticated': False}), 200

@app.route('/test', methods=['GET'])
def test_endpoint():
    return jsonify({'message': 'Test endpoint working', 'timestamp': datetime.utcnow().isoformat()}), 200

if __name__ == '__main__':
    print("Starting test login server...")
    app.run(host='0.0.0.0', port=5002, debug=True)
