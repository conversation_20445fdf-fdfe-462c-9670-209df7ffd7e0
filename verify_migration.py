#!/usr/bin/env python3
"""
Comprehensive database migration verification script
Run this on the server to verify all data was migrated correctly
"""
import sqlite3
import os
from datetime import datetime

def check_database_file():
    """Check if database file exists and get basic info"""
    db_path = "article_references.db"
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    size = os.path.getsize(db_path)
    size_mb = size / (1024 * 1024)
    print(f"✅ Database file found: {size_mb:.2f} MB ({size:,} bytes)")
    return True

def get_table_info(cursor):
    """Get all tables and their row counts"""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = cursor.fetchall()
    
    table_info = {}
    for table in tables:
        table_name = table[0]
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        table_info[table_name] = count
    
    return table_info

def verify_users(cursor):
    """Verify user accounts"""
    print("\n=== USER VERIFICATION ===")
    cursor.execute("SELECT id, username, role, email, is_active FROM users ORDER BY id")
    users = cursor.fetchall()
    
    print(f"Total users: {len(users)}")
    print("ID | Username | Role       | Email                  | Active")
    print("-" * 65)
    
    expected_users = {"rakhi", "pratik"}
    found_users = set()
    
    for user in users:
        user_id, username, role, email, is_active = user
        active_str = "Yes" if is_active else "No"
        print(f"{user_id:<2} | {username:<8} | {role:<10} | {email:<22} | {active_str}")
        found_users.add(username)
    
    # Check if expected users exist
    missing_users = expected_users - found_users
    if missing_users:
        print(f"❌ Missing users: {missing_users}")
        return False
    else:
        print("✅ All expected users found")
        return True

def verify_articles(cursor):
    """Verify article data"""
    print("\n=== ARTICLE DATA VERIFICATION ===")
    
    # Check article_files
    cursor.execute("SELECT COUNT(*) FROM article_files")
    article_count = cursor.fetchone()[0]
    print(f"Article files: {article_count}")
    
    # Check article_references
    cursor.execute("SELECT COUNT(*) FROM article_references")
    ref_count = cursor.fetchone()[0]
    print(f"Article references: {ref_count}")
    
    # Check if counts match (they should)
    if article_count == ref_count:
        print("✅ Article files and references counts match")
    else:
        print("❌ Article files and references counts don't match")
        return False
    
    # Sample some article data
    cursor.execute("""
        SELECT af.filename, ar.total_references, ar.processed_references 
        FROM article_files af 
        JOIN article_references ar ON af.id = ar.article_file_id 
        LIMIT 5
    """)
    samples = cursor.fetchall()
    
    print("\nSample articles:")
    print("Filename                    | Total Refs | Processed")
    print("-" * 55)
    for sample in samples:
        filename, total, processed = sample
        print(f"{filename[:25]:<25} | {total:<10} | {processed}")
    
    return True

def verify_journals(cursor):
    """Verify journal abbreviations"""
    print("\n=== JOURNAL ABBREVIATIONS VERIFICATION ===")
    
    cursor.execute("SELECT COUNT(*) FROM journal_abbreviations")
    journal_count = cursor.fetchone()[0]
    print(f"Total journal abbreviations: {journal_count}")
    
    # Check for some common journals
    test_journals = ['Nature', 'Science', 'Cell', 'NEJM']
    found_journals = []
    
    for journal in test_journals:
        cursor.execute("SELECT COUNT(*) FROM journal_abbreviations WHERE full_name LIKE ?", (f"%{journal}%",))
        count = cursor.fetchone()[0]
        if count > 0:
            found_journals.append(journal)
    
    print(f"Found common journals: {found_journals}")
    
    # Sample some journal data
    cursor.execute("SELECT full_name, abbreviation LIMIT 5")
    samples = cursor.fetchall()
    
    print("\nSample journal abbreviations:")
    print("Full Name                           | Abbreviation")
    print("-" * 60)
    for sample in samples:
        full_name, abbrev = sample
        print(f"{full_name[:35]:<35} | {abbrev}")
    
    return journal_count > 3000  # Should have over 3000 journals

def verify_new_features(cursor):
    """Verify new schema features"""
    print("\n=== NEW FEATURES VERIFICATION ===")
    
    # Check TE assignments
    cursor.execute("SELECT COUNT(*) FROM te_assignments")
    te_count = cursor.fetchone()[0]
    print(f"TE assignments: {te_count}")
    
    # Check author queries
    cursor.execute("SELECT COUNT(*) FROM author_queries")
    query_count = cursor.fetchone()[0]
    print(f"Author queries: {query_count}")
    
    # Sample TE assignments
    if te_count > 0:
        cursor.execute("""
            SELECT article_id, te_email, status, created_at 
            FROM te_assignments 
            ORDER BY created_at DESC 
            LIMIT 3
        """)
        te_samples = cursor.fetchall()
        
        print("\nSample TE assignments:")
        print("Article ID | TE Email           | Status    | Created")
        print("-" * 60)
        for sample in te_samples:
            article_id, te_email, status, created = sample
            created_short = created[:10] if created else "N/A"
            print(f"{article_id:<10} | {te_email[:18]:<18} | {status:<9} | {created_short}")
    
    return True

def verify_data_integrity(cursor):
    """Check data integrity and relationships"""
    print("\n=== DATA INTEGRITY VERIFICATION ===")
    
    # Check for orphaned records
    cursor.execute("""
        SELECT COUNT(*) FROM article_references ar 
        LEFT JOIN article_files af ON ar.article_file_id = af.id 
        WHERE af.id IS NULL
    """)
    orphaned_refs = cursor.fetchone()[0]
    
    if orphaned_refs > 0:
        print(f"❌ Found {orphaned_refs} orphaned article references")
        return False
    else:
        print("✅ No orphaned article references found")
    
    # Check for duplicate articles
    cursor.execute("""
        SELECT filename, COUNT(*) as count 
        FROM article_files 
        GROUP BY filename 
        HAVING COUNT(*) > 1
    """)
    duplicates = cursor.fetchall()
    
    if duplicates:
        print(f"❌ Found {len(duplicates)} duplicate filenames:")
        for dup in duplicates[:5]:  # Show first 5
            print(f"   {dup[0]} (appears {dup[1]} times)")
        return False
    else:
        print("✅ No duplicate filenames found")
    
    return True

def test_password_hashes(cursor):
    """Test that password hashes are in correct format"""
    print("\n=== PASSWORD HASH VERIFICATION ===")
    
    cursor.execute("SELECT username, password_hash FROM users")
    users = cursor.fetchall()
    
    for username, password_hash in users:
        if password_hash.startswith(('pbkdf2:', 'scrypt:')):
            print(f"✅ {username}: Werkzeug hash format")
        else:
            print(f"❌ {username}: Invalid hash format")
            return False
    
    return True

def main():
    """Main verification function"""
    print("=== DATABASE MIGRATION VERIFICATION ===")
    print(f"Verification started at: {datetime.now()}")
    
    # Check database file
    if not check_database_file():
        return
    
    try:
        # Connect to database
        conn = sqlite3.connect("article_references.db")
        cursor = conn.cursor()
        
        # Get table overview
        print("\n=== TABLE OVERVIEW ===")
        table_info = get_table_info(cursor)
        
        total_rows = 0
        for table_name, count in table_info.items():
            print(f"{table_name:<20}: {count:>8,} rows")
            total_rows += count
        
        print(f"{'Total':<20}: {total_rows:>8,} rows")
        
        # Run all verification checks
        checks = [
            ("Users", verify_users),
            ("Articles", verify_articles),
            ("Journals", verify_journals),
            ("New Features", verify_new_features),
            ("Data Integrity", verify_data_integrity),
            ("Password Hashes", test_password_hashes)
        ]
        
        passed_checks = 0
        total_checks = len(checks)
        
        for check_name, check_func in checks:
            try:
                if check_func(cursor):
                    passed_checks += 1
                else:
                    print(f"❌ {check_name} verification failed")
            except Exception as e:
                print(f"❌ {check_name} verification error: {e}")
        
        conn.close()
        
        # Final summary
        print(f"\n=== VERIFICATION SUMMARY ===")
        print(f"Checks passed: {passed_checks}/{total_checks}")
        
        if passed_checks == total_checks:
            print("🎉 ALL VERIFICATIONS PASSED!")
            print("✅ Database migration was successful")
            print("✅ All data integrity checks passed")
            print("✅ New features are working")
            print("✅ Ready for production use")
        else:
            print("❌ Some verifications failed")
            print("⚠️  Please review the issues above")
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")

if __name__ == "__main__":
    main()
