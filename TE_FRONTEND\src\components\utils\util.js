import { extract } from "../../constants/urls";
import {
  formatText,
  abbreviatePageNumbers,
  toSentenceCase,
  cleanTextForSearch,
} from "../../services/styling";
// Removed journalMap.json - now using database-backed system
import stopWords from "./stopWords";
export async function extractDetailsFromAPI(content, Prompt) {
  try {
    // Fetch data from the API
    const response = await fetch(extract, {
      method: "POST",
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ content: `${Prompt(content)}` }),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    // Parse the JSON response
    const data = await response.json();
    // Extract citation details
    return data;
  } catch (error) {
    // Handle error silently
    return null;
  }
}

export const extractFormattedData = async (
  extractedData,
  skipJournalLookup = false
) => {
  // Preserve original title for search, apply sentence case for display
  const original_article_title = extractedData?.article_title;
  const article_title = formatText(
    toSentenceCase(extractedData?.article_title)
  );
  const authors = extractedData?.authors;
  const year = extractedData?.year;

  // Skip journal abbreviation lookup for PubMed (it provides journal data)
  // Only do lookup for CrossRef searches
  const journal_title = skipJournalLookup
    ? extractedData?.journal_title
    : await getJournalNameWithFallback(extractedData?.journal_title);

  const pages = abbreviatePageNumbers(extractedData?.pages);
  const volume = extractedData?.volume;

  // Create data object with original title preserved for search
  const dataForSearch = {
    ...extractedData,
    original_article_title,
    original_term: extractedData?.original_term,
  };

  // Generate search strings for PubMed (3 strategies)
  const searchStr = generatePrimarySearchString(dataForSearch);
  const searchStr2 = generateSecondarySearchString(dataForSearch);
  const searchStr3 = generateTertiarySearchString(dataForSearch);
  const searchStr4 = generateQuaternarySearchString(dataForSearch);

  return {
    finalStr: `${authors} ${article_title} ${journal_title} ${
      year ? year + ";" : ""
    }${volume ? volume : ""}${pages ? ":" + pages : ""}`,
    extractedData,
    searchStr,
    searchStr2,
    searchStr3,
    searchStr4,
  };
};

const pubmedStopwords = new Set(stopWords);

function getPartialTitleWordsFromArray(words, percentage = 100) {
  const count = Math.ceil(words.length * (percentage / 100));
  return words.slice(0, count).join(" ");
}

function generateSearchString(data, level = "primary") {
  if (!data) return "";

  const parts = [];

  const originalTitle = data.original_article_title || data.article_title;
  const cleanedTitle = cleanTextForSearch(originalTitle || "");

  const filteredWords = cleanedTitle
    .split(/\s+/)
    .filter((word) => !pubmedStopwords.has(word.toLowerCase()));

  const partialTitles = {
    secondary: getPartialTitleWordsFromArray(filteredWords, 80),
    tertiary: getPartialTitleWordsFromArray(filteredWords, 60),
    quaternary: getPartialTitleWordsFromArray(filteredWords, 50),
  };

  // 🔹 Title logic by level
  if (level === "primary") {
    if (cleanedTitle) parts.push(`${cleanedTitle}[Title]`);
  } else if (partialTitles[level]) {
    parts.push(`${partialTitles[level]}[Title]`);
  }

  // 🔹 Journal logic for all levels
  if (level !== "secondary" && data.journal_title) {
    const cleanedJournal = cleanTextForSearch(data.journal_title);
    if (cleanedJournal) {
      parts.push(`${cleanedJournal}[Journal]`);
    }
  }

  // 🔹 Author logic for all levels
  if (level !== "tertiary" && data.authors) {
    const authors = data.authors
      .split(",")
      .map((a) => a.trim())
      .filter((a) => a && !a.toLowerCase().includes("et al"));

    if (authors.length > 0) {
      parts.push(`${authors[0]}[Author]`);
    }
  }

  return parts.join("+");
}


// Wrapper functions for clarity if needed

function generatePrimarySearchString(data) {
  return generateSearchString(data, "primary");
}

function generateSecondarySearchString(data) {
  return generateSearchString(data, "secondary");
}

function generateTertiarySearchString(data) {
  return generateSearchString(data, "tertiary");
}
function generateQuaternarySearchString(data) {
  return generateSearchString(data, "quaternary");
}

export async function getJournalNameWithFallback(searchTerm) {
  const journal = await getJournalAbbreviation(searchTerm);
  if (journal) {
    return journal;
  }
  const journalWithAllFields = await getJournalAbbreviation(searchTerm, true);
  return journalWithAllFields || searchTerm;
}

// DEPRECATED: This function is no longer needed with database-backed system
// Journal mappings are now handled automatically by journalService
export async function updateJournalMapping(
  originalJournal,
  abbreviatedJournal
) {
  console.warn('updateJournalMapping is deprecated - journals are now managed via database');
  // This function is kept for backward compatibility but does nothing
  return;
}

export async function getJournalAbbreviation(searchTerm) {
  // Use the existing journalMap.json and GenAI system
  const { searchJournalInGenAI } = await import(
    "../../services/fetchFromGenAI"
  );
  const journalFromMapOrGenAI = await searchJournalInGenAI(searchTerm);

  if (journalFromMapOrGenAI && journalFromMapOrGenAI !== searchTerm) {
    return journalFromMapOrGenAI;
  }
  // Fallback to original term if no mapping found
  return searchTerm;
}

// --- Reference scoring logic (moved from ReferenceEditor.js) ---
export function checkQuality(ref) {
  const originalText =
    ref.term !== undefined && ref.term !== null ? ref.term : "";
  const finalText =
    ref.finalStr !== undefined && ref.finalStr !== null
      ? ref.finalStr
      : ref.term !== undefined && ref.term !== null
      ? ref.term
      : "";

  // Calculate quality based on improvements made
  const improvementScore = calculateImprovementScore(originalText, finalText);
  const structuralScore = calculateStructuralScore(finalText);
  const completenessScore = calculateCompletenessScore(finalText);

  // Weighted average of different quality aspects
  const overallScore = Math.round(
    improvementScore * 0.4 + structuralScore * 0.3 + completenessScore * 0.3
  );

  const issues = [];
  const suggestions = [];

  // Analyze issues based on scores
  if (improvementScore < 70) {
    issues.push("Significant changes needed");
    suggestions.push("Original reference required major corrections");
  }

  if (structuralScore < 70) {
    issues.push("Formatting issues");
    suggestions.push("Check punctuation and structure");
  }

  if (completenessScore < 70) {
    issues.push("Missing information");
    suggestions.push("Some required fields may be missing");
  }

  return {
    issues,
    suggestions,
    score: overallScore,
    breakdown: {
      improvement: improvementScore,
      structure: structuralScore,
      completeness: completenessScore,
    },
  };
}

function calculateImprovementScore(original, final) {
  if (!original || !final) return 50;

  const changes = calculateTextChanges(original, final);
  const changeRatio = changes / Math.max(original.length, final.length);

  // Less changes = higher quality (original was already good)
  // More changes = lower quality (original needed lots of work)
  if (changeRatio < 0.1) return 95; // Very few changes needed
  if (changeRatio < 0.2) return 85; // Minor changes
  if (changeRatio < 0.4) return 70; // Moderate changes
  if (changeRatio < 0.6) return 55; // Major changes
  return 40; // Extensive changes needed
}

function calculateStructuralScore(text) {
  if (!text) return 0;

  let score = 100;

  // Check for proper punctuation
  if (!text.includes(".")) score -= 20;
  if (text.match(/\.{2,}|,{2,}|;{2,}/)) score -= 15; // Duplicate punctuation

  // Check for proper capitalization
  if (!text.match(/^[A-Z]/)) score -= 10; // Should start with capital

  // Check for balanced parentheses
  const openParens = (text.match(/\(/g) || []).length;
  const closeParens = (text.match(/\)/g) || []).length;
  if (openParens !== closeParens) score -= 15;

  // Check for reasonable length
  if (text.length < 30) score -= 20; // Too short
  if (text.length > 500) score -= 10; // Too long

  return Math.max(0, score);
}

function calculateCompletenessScore(text) {
  if (!text) return 0;

  let score = 0;
  const maxScore = 100;

  // Check for authors (names with initials or full names)
  if (text.match(/[A-Z][a-z]+,?\s+[A-Z]/)) score += 25;

  // Check for year
  if (text.match(/\b(19|20)\d{2}\b/)) score += 20;

  // Check for journal/source
  if (text.match(/[A-Z][a-z]+.*[A-Z]/)) score += 20;

  // Check for volume/pages
  if (text.match(/\d+:\d+/) || text.match(/\d+-\d+/)) score += 15;

  // Check for title (sentence case with proper ending)
  if (text.match(/[A-Z][a-z].*\./)) score += 20;

  return Math.min(score, maxScore);
}

function calculateTextChanges(str1, str2) {
  if (!str1 || !str2) return Math.max(str1?.length || 0, str2?.length || 0);

  const len1 = str1.length;
  const len2 = str2.length;
  const matrix = Array(len1 + 1)
    .fill()
    .map(() => Array(len2 + 1).fill(0));

  // Initialize first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;

  // Fill the matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1, // deletion
          matrix[i][j - 1] + 1, // insertion
          matrix[i - 1][j - 1] + 1 // substitution
        );
      }
    }
  }

  return matrix[len1][len2];
}
